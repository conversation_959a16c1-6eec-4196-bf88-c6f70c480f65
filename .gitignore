# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log
!src/app/api/logs
application-*.log

# env files (can opt-in for commiting if needed)
.env*
!.env.sample
!.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
/.idea/

translations.csv
*storybook.log


prisma/generated

scripts/book-data
scripts/image-process/input/*
scripts/image-process/output/*
scripts/image-process/data/*