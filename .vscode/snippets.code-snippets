{"GET API Route": {"prefix": "@GET", "body": ["import withRoute, { auth } from \"@/lib/error/withRouteErrorHandling\";", "import { successResponse } from \"@/services/response\";", "import { NextResponse } from \"next/server\";", "", "export const GET = withRoute(auth)(async (req, { params, t }) => {", "  $1", "  return NextResponse.json(successResponse({$2}));", "});"], "description": "创建GET API路由处理程序"}, "POST API Route": {"prefix": "@POST", "body": ["import withRoute, { auth } from \"@/lib/error/withRouteErrorHandling\";", "import { successResponse } from \"@/services/response\";", "import { NextResponse } from \"next/server\";", "", "export const POST = withRoute(auth)(async (req, { params, t }) => {", "  const body = await req.json();", "  $1", "  return NextResponse.json(successResponse({$2}));", "});"], "description": "创建POST API路由处理程序"}, "PUT API Route": {"prefix": "@PUT", "body": ["import withRoute, { auth } from \"@/lib/error/withRouteErrorHandling\";", "import { successResponse } from \"@/services/response\";", "import { NextResponse } from \"next/server\";", "", "export const PUT = withRoute(auth)(async (req, { params, t }) => {", "  const body = await req.json();", "  $1", "  return NextResponse.json(successResponse({$2}));", "});"], "description": "创建PUT API路由处理程序"}, "DELETE API Route": {"prefix": "@DELETE", "body": ["import withRoute, { auth } from \"@/lib/error/withRouteErrorHandling\";", "import { successResponse } from \"@/services/response\";", "import { NextResponse } from \"next/server\";", "", "export const DELETE = withRoute(auth)(async (req, { params, t }) => {", "  $1", "  return NextResponse.json(successResponse({$2}));", "});"], "description": "创建DELETE API路由处理程序"}}