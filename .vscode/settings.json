{"cSpell.words": ["coloringbook", "datamodel", "De<PERSON>ch", "DMMF", "dont", "gsap", "imageanalysis", "jspdf", "<PERSON><PERSON>", "PDFFAQ", "Português", "RABBITMQ", "scrollspy", "signup", "tailwindcss", "Tesseract", "turbopack", "usehooks", "uuidv", "zustand"], "i18n-ally.localesPaths": ["messages", "src/i18n"], "i18n-ally.keystyle": "nested", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "typescript.tsdk": "node_modules/typescript/lib", "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}}