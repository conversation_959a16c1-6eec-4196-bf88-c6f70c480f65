-- 公共服务sql脚本
CREATE TABLE `daily_users`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `date` date NULL DEFAULT NULL COMMENT '日期',
  `total` bigint NULL DEFAULT NULL COMMENT '总会员数',
  `new_users` bigint NULL DEFAULT NULL COMMENT '新会员数',
  `active_users` bigint NULL DEFAULT NULL COMMENT '活跃会员数',
  `active_user_ids` json NULL COMMENT '活跃会员id',
  `inactive_users` bigint NULL DEFAULT NULL COMMENT '未活跃会员数',
  `active_seniors` bigint NULL DEFAULT NULL COMMENT '老会员活跃数',
  `active_seniors_rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '老会员活跃率',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_daily.date`(`date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '每日用户统计' ROW_FORMAT = DYNAMIC;


CREATE TABLE `distribution_code`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT 'user_id',
  `distribution_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分销码',
  `rank_data` json NULL COMMENT '套餐佣金',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `commission_rate` int NULL DEFAULT NULL COMMENT '佣金比例',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `distribution_code`(`distribution_code`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '子分销码' ROW_FORMAT = DYNAMIC;


CREATE TABLE `distribution_commission`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `type` int NULL DEFAULT NULL COMMENT '类型 1=>结算, 2=>收入',
  `money` decimal(10, 2) NULL DEFAULT NULL COMMENT '金额',
  `current_assets` decimal(10, 2) NULL DEFAULT NULL COMMENT '当前资产',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '佣金记录表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `distribution_info`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `distribution_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分销码',
  `customize_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '定制码',
  `status` int NULL DEFAULT NULL COMMENT '状态  1=>等待处理 2=>分销中 3=>不可分销',
  `application_reason` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '申请理由',
  `application_time` int NULL DEFAULT NULL COMMENT '申请时间',
  `review_time` int NULL DEFAULT NULL COMMENT '审核时间',
  `paypal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'PayPal账号',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `order_effect_num` int NULL DEFAULT 0 COMMENT '订单有效数',
  `order_invalid_num` int UNSIGNED NULL DEFAULT 0 COMMENT '订单无效数',
  `confirmed_commission` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '已确认佣金',
  `not_confirmed_commission` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '未确认佣金',
  `assets` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '当前资产',
  `cancel_commission` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '已取消佣金',
  `settlement_commission` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '已结算佣金',
  `pay_customer` int NULL DEFAULT 0 COMMENT '付费客户数',
  `click_num` int NULL DEFAULT 0 COMMENT '点击数',
  `register_num` int NULL DEFAULT 0 COMMENT '注册数',
  `income_num` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '收入数',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  `commission_rate` int NULL DEFAULT NULL COMMENT '佣金比例',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`) USING BTREE,
  UNIQUE INDEX `distribution_code`(`distribution_code`) USING BTREE,
  INDEX `customize_code`(`customize_code`) USING BTREE,
  INDEX `review_time`(`review_time`) USING BTREE,
  INDEX `application_time`(`application_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分销信息表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `distribution_record`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint NULL DEFAULT NULL COMMENT '订单id',
  `pay_status` int NULL DEFAULT NULL COMMENT '支付状态 1=>已支付 2=>已退款',
  `sure_time` int NULL DEFAULT NULL COMMENT '确认时间',
  `commission_rate` int NULL DEFAULT NULL COMMENT '佣金比例',
  `commission` decimal(10, 2) NULL DEFAULT NULL COMMENT '佣金',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  `distribution_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分销码',
  `sure_status` int NULL DEFAULT NULL COMMENT '是否已经确认结算 1=>没确认 2=>已确认',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `sure_time`(`sure_time`) USING BTREE,
  INDEX `distribution_code`(`distribution_code`, `sure_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分销记录表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `distribution_settlement`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `pay_money` decimal(10, 2) NULL DEFAULT NULL COMMENT '结算金额',
  `pay_time` int NULL DEFAULT NULL COMMENT '结算时间',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态 1=> 已付款 2=>已作废',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `pay_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '付款账号',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  `void_time` int NULL DEFAULT NULL COMMENT '作废时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `created_at`(`created_at`) USING BTREE,
  INDEX `pay_time`(`pay_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分销结算表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `order_refunds`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NULL DEFAULT NULL COMMENT '订单id',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
  `method` tinyint NULL DEFAULT NULL COMMENT '退款方式',
  `cancel_vip` tinyint NULL DEFAULT NULL COMMENT '是否退会员',
  `cancel_subscription` tinyint NULL DEFAULT NULL COMMENT '是否取消订阅',
  `admin_id` bigint NULL DEFAULT NULL COMMENT '管理员id',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_refunds.order_id`(`order_id`) USING BTREE,
  INDEX `order_refunds.admin_id`(`admin_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单退款记录' ROW_FORMAT = DYNAMIC;


CREATE TABLE `orders`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单号',
  `rank_id` tinyint NULL DEFAULT NULL COMMENT '等级套餐id',
  `rank_duration` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '套餐时长',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `first_time` tinyint NULL DEFAULT NULL COMMENT '是否首次购买',
  `order_status` tinyint NULL DEFAULT NULL COMMENT '订单状态',
  `payment_platform` tinyint NULL DEFAULT NULL COMMENT '支付平台',
  `subscription_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订阅编号',
  `order_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单金额（订阅金额）',
  `paid_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `refunded_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退款金额',
  `profit_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '利润额',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注信息',
  `created_at` int NULL DEFAULT NULL COMMENT '下单时间',
  `paid_at` int NULL DEFAULT NULL COMMENT '支付时间',
  `refunded_at` int NULL DEFAULT NULL COMMENT '退款时间',
  `stripe_invoice` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'stripe账单id',
  `hosted_invoice_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `invoice_pdf` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `transaction_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易号',
  `distribution_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分销码',
  `prev_plan_reamin_days` int NULL DEFAULT NULL COMMENT '升级套餐时，上一套餐折算后剩余天数',
  `trial_days` int DEFAULT NULL COMMENT '试用天数',
  `type` tinyint DEFAULT NULL COMMENT '订单类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `orders.order_sn`(`order_sn`) USING BTREE,
  INDEX `orders.user_id`(`user_id`) USING BTREE,
  INDEX `orders.rank_id`(`rank_id`) USING BTREE,
  INDEX `orders.rank_duration`(`rank_duration`) USING BTREE,
  INDEX `orders.first_time`(`first_time`) USING BTREE,
  INDEX `orders.order_status`(`order_status`) USING BTREE,
  INDEX `orders.created_at`(`created_at`) USING BTREE,
  INDEX `orders.paid_at`(`paid_at`) USING BTREE,
  INDEX `orders.refunded_at`(`refunded_at`) USING BTREE,
  INDEX `orders.subscription_sn`(`subscription_sn`) USING BTREE,
  INDEX `orders.user_stripe_invoice`(`user_id`, `stripe_invoice`) USING BTREE,
  INDEX `orders.refunded_amount`(`refunded_amount`) USING BTREE,
  INDEX `orders.profit_amount`(`profit_amount`) USING BTREE,
  INDEX `orders.transaction_number`(`transaction_number`) USING BTREE,
  INDEX `order.user_platform_transaction_number`(`user_id`, `payment_platform`, `transaction_number`) USING BTREE,
  INDEX `distribution_code`(`distribution_code`) USING BTREE,
  INDEX `type` (`type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单' ROW_FORMAT = DYNAMIC;


CREATE TABLE `ranks`  (
  `id` tinyint NOT NULL AUTO_INCREMENT,
  `rank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '套餐名称',
  `duration` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '套餐时长',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `first_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '首购价格',
  `trial_days` int DEFAULT NULL COMMENT '试用天数（首次购买）',
  `allowed_buy` tinyint DEFAULT NULL COMMENT '允许购买（前台）',
  `is_visibled` tinyint DEFAULT NULL COMMENT '前台展示',
  `max_online_users` int DEFAULT NULL COMMENT '最大同时在线人数',
  `max_team_members` int NULL DEFAULT NULL COMMENT '团队人数上限',
  `permission` json NULL COMMENT '权限',
  `stripe_product_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'stripe中的产品id',
  `stripe_price_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'stripe中的价格id',
  `stripe_coupon_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'stripe中的优惠券id',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品名称（自动生成）',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  `remark` text NULL COMMENT '备注',
  `apple_product_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'apple中的产品 id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ranks.stripe_product_id`(`stripe_product_id`) USING BTREE,
  INDEX `ranks.rank_name`(`rank_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '等级套餐' ROW_FORMAT = DYNAMIC;
INSERT INTO `ranks` (`id`, `rank_name`, `duration`, `price`, `original_price`, `first_price`, `trial_days`, `allowed_buy`, `is_visibled`, `max_online_users`, `max_team_members`, `permission`, `stripe_product_id`, `stripe_price_id`, `stripe_coupon_id`, `product_name`, `created_at`, `updated_at`) VALUES (1, 'Free', 'forever', 0.00, 0.00, NULL, 0, 1, 0, NULL, 1, '{}', NULL, NULL, NULL, NULL, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());


CREATE TABLE `stripe_customer`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `stripe_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `pm_type` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `pm_last_four` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `stripe_customer.user_id`(`user_id`) USING BTREE,
  INDEX `stripe_id`(`stripe_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'stripe客户表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `stripe_payment_intents`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payment_intent` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付意图',
  `customer` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户id',
  `invoice` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账单id',
  `payment_method` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式id',
  `payment_method_details` json NULL COMMENT '支付方式明细',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `stripe_payment_intents.pi`(`payment_intent`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'stripe支付意图' ROW_FORMAT = DYNAMIC;


CREATE TABLE `stripe_subscription_items`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `subscription_id` bigint NOT NULL,
  `stripe_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_product` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_price` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `subscription_id_stripe_price`(`subscription_id`, `stripe_price`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'stripe订阅明细表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `stripe_subscriptions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_price` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `quantity` int NULL DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `stripe_subscriptions.stripe_id`(`stripe_id`) USING BTREE,
  INDEX `user_id_stripe_status`(`user_id`, `stripe_status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'stripe订阅表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `subscriptions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `payment_platform` tinyint NULL DEFAULT NULL COMMENT '订阅平台',
  `subscription_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订阅编号',
  `subscription_status` tinyint NULL DEFAULT NULL COMMENT '订阅状态',
  `platform_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '平台源状态',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订阅产品',
  `start_date` int NULL DEFAULT NULL COMMENT '订阅开始时间',
  `next_period_start` int NULL DEFAULT NULL COMMENT '下一个账单时间',
  `next_period_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '下一账单金额',
  `canceled_handler` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '取消来源',
  `canceled_at` int NULL DEFAULT NULL COMMENT '取消时间',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `subscriptions.subscription`(`payment_platform`, `subscription_sn`) USING BTREE,
  INDEX `subscriptions.user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订阅' ROW_FORMAT = DYNAMIC;


CREATE TABLE `user_quota`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `permission_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限名称',
  `limit` bigint NULL DEFAULT NULL COMMENT '限制次数',
  `remaining` bigint NULL DEFAULT NULL COMMENT '剩余次数',
  `used` bigint NULL DEFAULT NULL COMMENT '已用次数',
  `reset_at` int NULL DEFAULT NULL COMMENT '重置时间',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_permission`(`user_id`, `permission_name`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `remaining`(`remaining`) USING BTREE,
  INDEX `reset_at`(`reset_at`) USING BTREE,
  INDEX `used`(`used`) USING BTREE,
  INDEX `limit`(`limit`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户权限配额' ROW_FORMAT = DYNAMIC;


CREATE TABLE `user_socialites`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '第三方登录类型',
  `socialite_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方登录唯一id',
  `socialite_raw_data` json NULL COMMENT '第三方登录原始数据',
  `user_id` bigint NULL DEFAULT NULL COMMENT '会员id',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_socialites.unique`(`type`, `socialite_id`) USING BTREE,
  INDEX `user_socialites.user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '会员第三方登录' ROW_FORMAT = DYNAMIC;


CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `email_verified_at` int NULL DEFAULT NULL COMMENT '邮箱认证时间',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号码',
  `phone_region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机归属地',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密码',
  `salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `rank_id` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '等级id',
  `last_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后一次登录的ip',
  `last_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后一次登录的位置',
  `last_at` int NULL DEFAULT NULL COMMENT '最后一次登录的时间',
  `vip_started_at` int NULL DEFAULT NULL COMMENT '会员开通时间',
  `vip_expired_at` bigint NULL DEFAULT NULL COMMENT '会员到期时间',
  `subscription_status` tinyint NULL DEFAULT NULL COMMENT '订阅状态',
  `subscription_platform` tinyint NULL DEFAULT NULL COMMENT '订阅平台',
  `subscription_started_at` int NULL DEFAULT NULL COMMENT '订阅开始时间',
  `subscription_next_deduct_at` int NULL DEFAULT NULL COMMENT '订阅下次扣费时间',
  `card_last4` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '卡号后四位',
  `source` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源',
  `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '语言',
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '时区',
  `device_language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备语言',
  `device_timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备时区',
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司名称',
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家/地区',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `postal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话号码',
  `vat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '增值税号',
  `distribution_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '推荐人的分销码',
  `user_value` decimal(10,2) DEFAULT NULL COMMENT '会员价值',
  `account_status` tinyint NULL DEFAULT NULL COMMENT '账号状态',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注信息',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  `delete_task_created_at` int NULL COMMENT '发起删除时间',
  `delete_task_plan_executed_at` int NULL COMMENT '计划执行删除时间',
  `delete_task_executed_at` int NULL COMMENT '执行删除时间',
  `avatar` varchar(500) NULL COMMENT '头像链接',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `users.account`(`account`) USING BTREE,
  INDEX `users.rank_id`(`rank_id`) USING BTREE,
  INDEX `users.last_at`(`last_at`) USING BTREE,
  INDEX `users.vip_started_at`(`vip_started_at`) USING BTREE,
  INDEX `users.vip_expired_at`(`vip_expired_at`) USING BTREE,
  INDEX `users.subscription_status`(`subscription_status`) USING BTREE,
  INDEX `users.subscription_started_at`(`subscription_started_at`) USING BTREE,
  INDEX `users.subscription_next_deduct_at`(`subscription_next_deduct_at`) USING BTREE,
  INDEX `users.created_at`(`created_at`) USING BTREE,
  INDEX `users.email`(`email`) USING BTREE,
  INDEX `users.phone_number`(`phone_number`) USING BTREE,
  INDEX `users.phone_region`(`phone_region`) USING BTREE,
  INDEX `users.last_ip`(`last_ip`) USING BTREE,
  INDEX `users.last_location`(`last_location`) USING BTREE,
  INDEX `users.language`(`language`) USING BTREE,
  INDEX `users.timezone`(`timezone`) USING BTREE,
  INDEX `users.account_status`(`account_status`) USING BTREE,
  INDEX `users.device_language`(`device_language`) USING BTREE,
  INDEX `users.device_timezone`(`device_timezone`) USING BTREE,
  INDEX `card_last4`(`card_last4`) USING BTREE,
  INDEX `user_value` (`user_value`) USING BTREE,
  INDEX `delete_task_plan_executed_at`(`delete_task_plan_executed_at` DESC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '会员' ROW_FORMAT = DYNAMIC;

CREATE TABLE `teams`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '团队名称',
  `member_count` int NULL DEFAULT NULL COMMENT '成员数',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队' ROW_FORMAT = Dynamic;

CREATE TABLE `team_roles`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `team_id` bigint NULL DEFAULT NULL COMMENT '团队id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色名称',
  `permission` json NULL COMMENT '权限',
  `is_default` tinyint NULL DEFAULT NULL COMMENT '是否默认',
  `is_admin` tinyint NULL DEFAULT NULL COMMENT '是否管理员',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `team_id`(`team_id`) USING BTREE,
  INDEX `default_role`(`team_id`, `is_default`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队角色' ROW_FORMAT = Dynamic;

CREATE TABLE `team_members`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `team_id` bigint NULL DEFAULT NULL COMMENT '团队id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `role_id` bigint NULL DEFAULT NULL COMMENT '角色id',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态',
  `invite_id` bigint NULL DEFAULT NULL COMMENT '邀请id',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `team_member`(`team_id`, `user_id`) USING BTREE,
  INDEX `team_id`(`team_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `invite_id`(`invite_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队成员' ROW_FORMAT = Dynamic;

CREATE TABLE `team_invites`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `team_id` bigint NULL DEFAULT NULL COMMENT '团队id',
  `role_id` bigint NULL DEFAULT NULL COMMENT '角色id',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邀请码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '被邀请人的邮箱',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `expired_at` int NULL DEFAULT NULL COMMENT '失效时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code`) USING BTREE,
  INDEX `team_id`(`team_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '团队邀请码' ROW_FORMAT = Dynamic;

CREATE TABLE `frontend_api_secret_keys`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `secret_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密钥',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `created_at` int NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `secret_key`(`secret_key`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '前台接口密钥' ROW_FORMAT = Dynamic;

CREATE TABLE `feedbacks` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '反馈内容',
  `created_at` int DEFAULT NULL COMMENT '创建时间',
  `updated_at` int DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈';

CREATE TABLE `open_apis` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
  `api` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '接口',
  `method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方式',
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述说明',
  `rpm` int DEFAULT NULL COMMENT '每分钟请求次数限制',
  `billing_method` tinyint DEFAULT NULL COMMENT '计费方式',
  `cpr` int DEFAULT NULL COMMENT '每次请求消耗次数',
  `created_at` int DEFAULT NULL COMMENT '创建时间',
  `updated_at` int DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique` (`api`,`method`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开放平台-接口';

CREATE TABLE `open_users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id（兼容pipiads的）',
  `account` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户账号（兼容pipiads的）',
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `quota` bigint DEFAULT NULL COMMENT '总次数',
  `used_quota` bigint DEFAULT NULL COMMENT '已使用次数',
  `remaining_quota` bigint GENERATED ALWAYS AS ((`quota` - `used_quota`)) STORED COMMENT '剩余次数',
  `created_at` int DEFAULT NULL COMMENT '创建时间',
  `updated_at` int DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开放平台-用户';

CREATE TABLE `open_user_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id（兼容pipiads的）',
  `source_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源类型',
  `source_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源id（兼容pipiads的）',
  `quota` bigint DEFAULT '0' COMMENT '次数',
  `created_at` int DEFAULT NULL COMMENT '创建时间',
  `updated_at` int DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique` (`user_id`,`source_type`,`source_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开放平台-修改日志';

CREATE TABLE `open_api_secret_keys` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `secret_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密钥',
  `user_id` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id（兼容pipiads的）',
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `created_at` int DEFAULT NULL COMMENT '创建时间',
  `updated_at` int DEFAULT NULL COMMENT '修改时间',
  `deleted_at` int DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `secret_key` (`secret_key`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='开放平台-密钥';

CREATE TABLE `apple_notifications`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户 id',
  `original_transaction_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '购买的原始交易标识符',
  `transaction_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易的唯一标识符',
  `notification_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '事件类型',
  `subtype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '事件子类型',
  `web_order_line_item_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跨设备订阅购买事件的唯一标识符',
  `product_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用内购买的产品标识符',
  `purchase_date` int UNSIGNED NULL DEFAULT NULL COMMENT '购买时间',
  `original_purchase_date` int UNSIGNED NULL DEFAULT NULL COMMENT '原始交易购买时间',
  `storefront` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家或地区',
  `price` int UNSIGNED NULL DEFAULT NULL COMMENT '订阅*1000',
  `auto_renew_status` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '续订订阅的续订状态: 0=自动续订已关闭,客户已关闭订阅自动续订当前订阅期结束后不会续订。 1=自动续订已开启,订阅将在当前订阅期结束时续订。',
  `environment` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务器环境：Sandbox、Production',
  `signed_transaction_info` json NULL COMMENT '交易信息',
  `signed_renewal_info` json NULL COMMENT '阅续订信息',
  `signed_payload` json NULL COMMENT '交易和订阅续订详细信息',
  `raw` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '原始信息',
  `created_at` int UNSIGNED NULL DEFAULT NULL,
  `updated_at` int UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ original_transaction_id`(`original_transaction_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_ transaction_id`(`transaction_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'apple通知表' ROW_FORMAT = Dynamic;
