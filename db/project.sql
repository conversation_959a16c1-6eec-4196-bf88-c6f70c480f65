-- 项目mysql脚本
-- 语言表
CREATE TABLE `languages` (
  `language_code` VARCHAR(5) NOT NULL,
  `name` VARCHAR(50) NOT NULL COMMENT '语言名称（英文）',
  `native_name` VARCHAR(50) NOT NULL COMMENT '语言名称（本地语言）',
  `is_active` TINYINT DEFAULT 1 COMMENT '是否激活',
  PRIMARY KEY (`language_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '语言' ROW_FORMAT = Dynamic;

-- 插入默认英文语言数据
INSERT INTO `languages` (`language_code`, `name`, `native_name`, `is_active`)
VALUES ('en', 'English', 'English', 1);

-- 出版商表
CREATE TABLE `publishers` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '出版商唯一标识符',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '出版商' ROW_FORMAT = Dynamic;

-- 核心表：书籍
CREATE TABLE `books` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '书籍唯一标识符',
  `rawid` VARCHAR(50) DEFAULT NULL COMMENT '数据源导入的id',
  `content_type` VARCHAR(50) DEFAULT NULL COMMENT '内容类型',
  `binding` VARCHAR(50) DEFAULT NULL COMMENT '装订类型',
  `isbn` VARCHAR(100) DEFAULT NULL COMMENT '国际标准书号',
  `asin` VARCHAR(100) DEFAULT NULL COMMENT '亚马逊标准识别号',
  `isbn13` VARCHAR(100) DEFAULT NULL COMMENT 'ISBN-13',
  `issn` VARCHAR(100) DEFAULT NULL COMMENT '国际标准期刊号',
  `publication_year` INT DEFAULT NULL COMMENT '出版年份',
  `publisher_id` INT DEFAULT NULL COMMENT '出版商ID，外键关联publishers表',
  `original_language_id` VARCHAR(5) DEFAULT NULL COMMENT '原始语言ID，外键关联languages表',
  `file_size_pdf` FLOAT DEFAULT NULL COMMENT 'PDF文件大小（MB）',
  `file_size_epub` FLOAT DEFAULT NULL COMMENT 'EPUB文件大小（MB）',
  `pdf_url` VARCHAR(255) DEFAULT NULL COMMENT 'PDF文件URL',
  `rate_score` DECIMAL(3,2) DEFAULT NULL COMMENT '平均评分（0.00-5.00）',
  `total_ratings` INT UNSIGNED DEFAULT 0 COMMENT '评分总数',
  `epub_url` VARCHAR(255) DEFAULT NULL COMMENT 'EPUB文件URL',
  `ipfs_cid` VARCHAR(100) DEFAULT NULL COMMENT 'IPFS内容标识符',
  `ipfs_cid_blake2b` VARCHAR(100) DEFAULT NULL COMMENT 'IPFS Blake2b哈希值',
  `is_published` TINYINT DEFAULT 0 COMMENT '是否已发布',
  `published_at` TIMESTAMP NULL DEFAULT NULL COMMENT '发布时间',
  `reading_time_minutes` INT DEFAULT NULL COMMENT '阅读时长（分钟）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `publisher_id`(`publisher_id`) USING BTREE,
  INDEX `original_language_id`(`original_language_id`) USING BTREE,
  CONSTRAINT `books_ibfk_1` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `books_ibfk_2` FOREIGN KEY (`original_language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍' ROW_FORMAT = Dynamic;

-- 书籍本地化信息
CREATE TABLE `book_translations` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '翻译记录唯一标识符',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `title` VARCHAR(255) NOT NULL COMMENT '书籍标题',
  `subtitle` VARCHAR(255) DEFAULT NULL COMMENT '书籍副标题',
  `description` TEXT DEFAULT NULL COMMENT '书籍描述',
  `plot_summary` TEXT DEFAULT NULL COMMENT '情节摘要',
  `review_summary` TEXT DEFAULT NULL COMMENT '书籍评论摘要',
  `best_quote` TEXT DEFAULT NULL COMMENT '最佳引用',
  `synopsis` TEXT DEFAULT NULL COMMENT '内容概要',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为默认翻译',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `book_language`(`book_id`, `language_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `book_translations_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `book_translations_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍翻译' ROW_FORMAT = Dynamic;

-- 作者表
CREATE TABLE `authors` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '作者唯一标识符',
  `rawid` VARCHAR(50) DEFAULT NULL COMMENT '数据源导入的id',
  `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `website` VARCHAR(500) DEFAULT NULL COMMENT '个人网站',
  `twitter_account` VARCHAR(100) DEFAULT NULL COMMENT 'X(Twitter)账号',
  `born` VARCHAR(255) DEFAULT NULL COMMENT '出生地',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '作者' ROW_FORMAT = Dynamic;

-- 作者本地化信息
CREATE TABLE `author_translations` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '翻译记录唯一标识符',
  `author_id` INT DEFAULT NULL COMMENT '作者ID，外键关联authors表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `name` VARCHAR(255) NOT NULL COMMENT '作者姓名',
  `biography` TEXT DEFAULT NULL COMMENT '作者传记',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为默认翻译',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `author_language`(`author_id`, `language_id`) USING BTREE,
  INDEX `author_id`(`author_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `author_translations_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `author_translations_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '作者翻译' ROW_FORMAT = Dynamic;

-- 书籍-作者关联表
CREATE TABLE `book_authors` (
  `book_id` INT NOT NULL COMMENT '书籍ID，外键关联books表',
  `author_id` INT NOT NULL COMMENT '作者ID，外键关联authors表',
  `author_order` INT DEFAULT 1 COMMENT '作者排序（用于多作者书籍）',
  PRIMARY KEY (`book_id`, `author_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `author_id`(`author_id`) USING BTREE,
  CONSTRAINT `book_authors_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `book_authors_ibfk_2` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍-作者关联' ROW_FORMAT = Dynamic;

-- 分类表
CREATE TABLE `categories` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '分类唯一标识符',
  `rawid` VARCHAR(50) DEFAULT NULL COMMENT '数据源导入的id',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分类' ROW_FORMAT = Dynamic;

-- 分类本地化信息
CREATE TABLE `category_translations` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '翻译记录唯一标识符',
  `category_id` INT DEFAULT NULL COMMENT '分类ID，外键关联categories表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `description` TEXT DEFAULT NULL COMMENT '分类描述',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为默认翻译',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `category_language`(`category_id`, `language_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `category_translations_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `category_translations_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分类翻译' ROW_FORMAT = Dynamic;

-- 书籍-分类关联表
CREATE TABLE `book_categories` (
  `book_id` INT NOT NULL COMMENT '书籍ID，外键关联books表',
  `category_id` INT NOT NULL COMMENT '分类ID，外键关联categories表',
  PRIMARY KEY (`book_id`, `category_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  CONSTRAINT `book_categories_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `book_categories_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍-分类关联' ROW_FORMAT = Dynamic;

-- 出版商本地化信息
CREATE TABLE `publisher_translations` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '翻译记录唯一标识符',
  `publisher_id` INT DEFAULT NULL COMMENT '出版商ID，外键关联publishers表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `name` VARCHAR(255) NOT NULL COMMENT '出版商名称',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否为默认翻译',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `publisher_language`(`publisher_id`, `language_id`) USING BTREE,
  INDEX `publisher_id`(`publisher_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `publisher_translations_ibfk_1` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `publisher_translations_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '出版商翻译' ROW_FORMAT = Dynamic;

-- 创建新的 book_chapters 表
CREATE TABLE `book_chapters` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '书籍章节唯一标识符',
  `book_id` INT NOT NULL COMMENT '书籍ID，外键关联books表',
  `language_id` VARCHAR(5) NOT NULL COMMENT '语言ID，外键关联languages表',
  `content` JSON COMMENT '章节内容JSON数组',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `book_language` (`book_id`, `language_id`) USING BTREE,
  INDEX `book_id` (`book_id`) USING BTREE,
  INDEX `language_id` (`language_id`) USING BTREE,
  CONSTRAINT `book_chapters_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `book_chapters_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍章节内容' ROW_FORMAT = Dynamic;

-- 评分表
CREATE TABLE `ratings` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '评分唯一标识符',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID，外键关联users表',
  `score` DECIMAL(2,1) NOT NULL COMMENT '评分（0-5分）',
  `review_text` TEXT DEFAULT NULL COMMENT '评论内容',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `book_user`(`book_id`, `user_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `ratings_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ratings_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ratings_score_check` CHECK (`score` >= 0 AND `score` <= 5)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评分' ROW_FORMAT = Dynamic;

-- 书籍封面图片
CREATE TABLE `book_covers` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '封面唯一标识符',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `image_url` VARCHAR(255) NOT NULL COMMENT '图片URL',
  `is_primary` TINYINT DEFAULT 0 COMMENT '是否为主封面',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `book_covers_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `book_covers_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '书籍封面' ROW_FORMAT = Dynamic;

-- 用户收藏表
CREATE TABLE `user_favorites` (
  `user_id` BIGINT NOT NULL COMMENT '用户ID，外键关联users表',
  `book_id` INT NOT NULL COMMENT '书籍ID，外键关联books表',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`user_id`, `book_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  CONSTRAINT `user_favorites_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `user_favorites_ibfk_2` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户收藏' ROW_FORMAT = Dynamic;

-- 用户阅读历史
CREATE TABLE `user_reading_history` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '历史记录唯一标识符',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID，外键关联users表',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `last_read_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后阅读时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
   -- 复合唯一索引：确保同一用户对同一本书只有一条记录
  UNIQUE INDEX `user_book_reading_unique`(`user_id`, `book_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `last_read_at`(`last_read_at`) USING BTREE,
  CONSTRAINT `user_reading_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `user_reading_history_ibfk_2` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户阅读历史' ROW_FORMAT = Dynamic;

-- 用户音频播放进度表 (完整版 - 支持播放进度管理)
CREATE TABLE `user_audio_progress` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '进度记录唯一标识符',
  `user_id` BIGINT NOT NULL COMMENT '用户ID，外键关联users表',
  `book_id` INT NOT NULL COMMENT '书籍ID，外键关联books表',

  -- 播放进度信息
  `position_seconds` DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '当前播放位置(秒)',
  `duration_seconds` DECIMAL(10,3) DEFAULT NULL COMMENT '音频总时长(秒)',
  `progress_percentage` DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '播放进度百分比(0-100)',

  -- 播放设置
  `playback_rate` DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT '播放倍速(0.5-2.0)',
  `volume` DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT '音量(0-1)',

  -- 播放状态
  `is_completed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否播放完成',
  `last_listened_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后播放时间',

  -- 元数据
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 主键和索引
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_book`(`user_id`, `book_id`) USING BTREE COMMENT '用户书籍唯一约束',
  INDEX `idx_user_id`(`user_id`) USING BTREE COMMENT '用户ID索引',
  INDEX `idx_book_id`(`book_id`) USING BTREE COMMENT '书籍ID索引',
  INDEX `idx_last_listened_at`(`last_listened_at`) USING BTREE COMMENT '最后播放时间索引',
  INDEX `idx_user_recent`(`user_id`, `last_listened_at` DESC) USING BTREE COMMENT '用户最近播放索引',
  INDEX `idx_progress_percentage`(`progress_percentage`) USING BTREE COMMENT '播放进度索引',
  INDEX `idx_is_completed`(`is_completed`) USING BTREE COMMENT '完成状态索引',

  -- 外键约束
  CONSTRAINT `user_audio_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_audio_progress_ibfk_2` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,

  -- 检查约束
  CONSTRAINT `chk_position_seconds` CHECK (`position_seconds` >= 0),
  CONSTRAINT `chk_duration_seconds` CHECK (`duration_seconds` IS NULL OR `duration_seconds` >= 0),
  CONSTRAINT `chk_progress_percentage` CHECK (`progress_percentage` >= 0 AND `progress_percentage` <= 100),
  CONSTRAINT `chk_playback_rate` CHECK (`playback_rate` >= 0.5 AND `playback_rate` <= 2.0),
  CONSTRAINT `chk_volume` CHECK (`volume` >= 0 AND `volume` <= 1)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户音频播放进度表' ROW_FORMAT = Dynamic;

-- 音频文件
CREATE TABLE `audio_files` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '音频文件唯一标识符',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `language_id` VARCHAR(5) DEFAULT NULL COMMENT '语言ID，外键关联languages表',
  `file_url` VARCHAR(255) NOT NULL COMMENT '文件URL',
  `duration_seconds` INT DEFAULT NULL COMMENT '时长（秒）',
  `file_size_mb` FLOAT DEFAULT NULL COMMENT '文件大小（MB）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `book_language`(`book_id`, `language_id`) USING BTREE,
  INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `language_id`(`language_id`) USING BTREE,
  CONSTRAINT `audio_files_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `audio_files_ibfk_2` FOREIGN KEY (`language_id`) REFERENCES `languages` (`language_code`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '音频文件' ROW_FORMAT = Dynamic;

-- 访问统计
CREATE TABLE `view_statistics` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '统计记录唯一标识符',
  `book_id` INT DEFAULT NULL COMMENT '书籍ID，外键关联books表',
  `view_count` BIGINT DEFAULT 0 COMMENT '浏览次数',
  `last_viewed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后浏览时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `book_id`(`book_id`) USING BTREE,
  INDEX `idx_view_count`(`view_count`) USING BTREE COMMENT '优化按浏览量排序的查询',
  CONSTRAINT `view_statistics_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '访问统计' ROW_FORMAT = Dynamic;

-- 作者访问统计
CREATE TABLE `author_view_statistics` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '统计记录唯一标识符',
  `author_id` INT DEFAULT NULL COMMENT '作者ID，外键关联authors表',
  `view_count` BIGINT DEFAULT 0 COMMENT '浏览次数',
  `last_viewed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后浏览时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `author_id`(`author_id`) USING BTREE,
  CONSTRAINT `author_view_statistics_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '作者访问统计' ROW_FORMAT = Dynamic;
