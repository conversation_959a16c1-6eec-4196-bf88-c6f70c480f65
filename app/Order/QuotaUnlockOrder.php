<?php

namespace App\Order;

use App\Constants\OrderType;
use Hyperf\DbConnection\Db;
use Lete\MongoDB\MongoClient\MongoDb;
use Website\Common\Service\OrderService;
use Carbon\Carbon;
use Website\Common\Utils\Website;

class QuotaUnlockOrder extends \Lete\Base\Abstraction\Model
{
    /**
     * 支付成功后的处理逻辑
     */
    public static function paid($order_id, $others = [])
    {
        Website::logger()->debug('QuotaUnlockOrder::paid called starting ...', [
            'order_id' => $order_id,
            'others' => $others
        ]);

        // 1. 查询订单信息并验证状态
        $order = Db::table('orders')->find($order_id);
        if (!$order || $order->order_status !== OrderService::STATUS_PAID) {
            Website::logger()->debug('Order not found or not paid', ['order_id' => $order_id]);
            return;
        }

        try {
            Website::logger()->debug('Processing quota unlock for order', [
                'order_id' => $order_id,
                'user_id' => $order->user_id
            ]);

            Db::beginTransaction();

            // 2. 获取用户账号
            $user_account = Db::table('users')
                ->where('id', $order->user_id)
                ->value('account');

            if (!$user_account) {
                throw new \Exception('User account not found');
            }

            // 3. 查询用户当前的额度信息
            $current_quota = Db::table('chat_message_quota')
                ->where('user_account', $user_account)
                ->first();

            // 计算新的 reset_at 值
            $now = Carbon::now();
            $new_reset_at = $now->addHours(24)->timestamp;

            // 判断用户之前是否是付费用户，并且额度未过期
            if ($current_quota &&
                $current_quota->remaining_count > 50 &&
                $current_quota->reset_at > time()) {
                // 如果是付费用户且未过期，在原有 reset_at 基础上增加24小时
                $new_reset_at = $current_quota->reset_at + (24 * 3600);
                Website::logger()->debug('Adding 24 hours to existing reset_at', [
                    'user_account' => $user_account,
                    'old_reset_at' => $current_quota->reset_at,
                    'new_reset_at' => $new_reset_at
                ]);
            }

            // 更新用户额度表
            Db::table('chat_message_quota')
                ->updateOrInsert(
                    ['user_account' => $user_account],
                    [
                        'remaining_count' => 999999,
                        'reset_at' => $new_reset_at,
                        'updated_at' => time(),
                        'created_at' => Db::raw('COALESCE(created_at, ' . time() . ')')
                    ]
                );

            Db::commit();

            Website::logger()->debug('Quota updated successfully', [
                'user_account' => $user_account,
                'order_id' => $order_id
            ]);


        } catch (\Throwable $e) {
            if (isset($db_transaction_started) && $db_transaction_started) {
                Db::rollBack();
            }
            Website::logger()->debug('Error processing quota unlock', [
                'order_id' => $order_id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}