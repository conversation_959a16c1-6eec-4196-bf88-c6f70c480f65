<?php

declare(strict_types=1);

namespace App\Frontend\Controller;

use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\Utils\Str;
use Lete\Mail\Mail;
use Website\Common\FrontendApi\Controller\AbstractController;
use Website\Common\Utils\Website;

/**
 * @ApiServer(name="frontend")
 * @ApiController(prefix="preview-email", tag="邮件预览", server="website-frontend")
 */
class PreviewEmailController extends AbstractController
{
    /**
     * @GetA<PERSON>(path="forgot-password", description="忘记密码")
     * @Query(key="email|email", rule="email")
     */
    public function forgotPassword()
    {
        $validator_data = Context::get('validator.data');

        $code = Str::random(32);
        $email = $validator_data['email'] ?? '';
        $data = [
            'email' => $email,
            'subject' => 'Reset Password',
            'template' => 'mail/forgot-password',
            'view_data' => [
                'email' => $email,
                'code' => $code,
                'reset_url' => env('WEBSITE_FRONTEND_BASE_URI') . "/resetPassword?". \GuzzleHttp\Psr7\Query::build([
                    'type' => 'forget',
                    'email' => $email,
                    'code' => $code,
                ]),
            ],
        ];
        if (isset($validator_data['email'])) {
            go(function () use ($data) {
                $this->sendEmail($data);
            });
        }
        return (string) \Hyperf\ViewEngine\view($data['template'], $data['view_data']);
    }

    /**
     * @GetApi(path="activate-code", description="账号激活")
     * @Query(key="email|email", rule="email")
     */
    public function activateCode()
    {
        $validator_data = Context::get('validator.data');

        $email = $validator_data['email'] ?? '';
        $data = [
            'email' => $email,
            'subject' => 'Complete your registration',
            'template' => 'mail/activate-code',
            'view_data' => [
                'activate_code' => rand(1000, 9999),
            ],
        ];
        if (isset($validator_data['email'])) {
            go(function () use ($data) {
                $this->sendEmail($data);
            });
        }
        return (string) \Hyperf\ViewEngine\view($data['template'], $data['view_data']);
    }

    /**
     * @GetApi(path="account-activation-successful", description="账号激活")
     * @Query(key="email|email", rule="email")
     */
    public function accountActivationSuccessful()
    {
        $validator_data = Context::get('validator.data');

        $email = $validator_data['email'] ?? '';
        $data = [
            'email' => $email,
            'subject' => 'Welcome to ' . Website::config()->get('website.name', ''),
            'template' => 'mail/account-activation-successful',
            'view_data' => [],
        ];
        if (isset($validator_data['email'])) {
            go(function () use ($data) {
                $this->sendEmail($data);
            });
        }
        return (string) \Hyperf\ViewEngine\view($data['template'], $data['view_data']);
    }

    /**
     * 发邮件
     * @param $data
     */
    protected function sendEmail($data)
    {
        $Mail = (new Mail())
            ->to($data['email'])
            ->subject($data['subject']);
        if (isset($data['template'])) {
            $Mail->render($data['template'], $data['view_data'] ?? []);
        } elseif (isset($data['html'])) {
            $Mail->html($data['html']);
        } elseif (isset($data['text'])) {
            $Mail->text($data['text']);
        }
        if (!$Mail->send()) {
            throw $Mail->exception;
        } else {
            var_dump('邮件发送成功');
        }
    }
}
