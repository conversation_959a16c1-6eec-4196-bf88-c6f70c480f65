<?php

declare(strict_types=1);

namespace App\Frontend\Controller;

use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Website\Common\FrontendApi\Controller\AbstractController;
use Website\Common\Model\Rank;
use Website\Common\Service\RankService;
use Website\Common\Service\WebsiteSettingService;

/**
 * @ApiServer(name="website-frontend")
 * @ApiController(prefix="", tag="首页", server="website-frontend")
 */
class IndexController extends AbstractController
{
    /**
     * @param string $view
     * @param array $data
     * @return string
     */
    protected function view(string $view, $data = [])
    {
        $base_uri = env('WEBSITE_FRONTEND_BASE_URI');
        $base_uri = Str::replaceLast('/', '', $base_uri);
        $google = json_decode(env('GOOGLE_CLIENT_SECRET'), true);
        $merge_data = [
            // 当前运行环境
            'app_env' => env('APP_ENV'),
            // 静态资源版本号
            'static_version' => WebsiteSettingService::getStaticVersion(),
            'base_uri' => $base_uri,
            'current_uri' => $this->request->getUri(),
            'current_path' => $this->request->getAttribute('current-path'),
            'locale' => $this->request->getAttribute('locale'),
            'is_logged_in' => $this->authGuard()->check(),
            'google' => [
                'client_id' => $google['web']['client_id']
            ],
            'languages' => self::languages(),
        ];
        $merge_data = array_merge($merge_data, []);
        return (string) \Hyperf\ViewEngine\view($view, $data, $merge_data);
    }

    /**
     * @GetApi(path="/[{lang:zh|tw|ko|ja|pt|es|de|fr|vi}]", description="首页")
     */
    public function index()
    {
        return $this->view('frontend/home');
    }

    /**
     * @GetApi(path="terms-of-service", description="Terms Of Service")
     */
    public function termsOfService()
    {
        return $this->view('frontend/terms-of-service');
    }

    /**
     * @GetApi(path="{lang:zh|tw|ko|ja|pt|es|de|fr|vi}/terms-of-service", description="Terms Of Service")
     */
    public function localeTermsOfService()
    {
        return $this->termsOfService();
    }

    /**
     * @GetApi(path="privacy-policy", description="Privacy Policy")
     */
    public function privacyPolicy()
    {
        return $this->view('frontend/privacy-policy');
    }

    /**
     * @GetApi(path="{lang:zh|tw|ko|ja|pt|es|de|fr|vi}/privacy-policy", description="Privacy Policy")
     */
    public function localePrivacyPolicy()
    {
        return $this->privacyPolicy();
    }

    /**
     * @GetApi(path="subscription-refund-policy", description="Subscription&Refund Policy")
     */
    public function subscriptionRefundPolicy()
    {
        return $this->view('frontend/subscription-refund-policy');
    }

    /**
     * @GetApi(path="{lang:zh|tw|ko|ja|pt|es|de|fr|vi}/subscription-refund-policy", description="Subscription&Refund Policy")
     */
    public function localeSubscriptionRefundPolicy()
    {
        return $this->subscriptionRefundPolicy();
    }

    /**
     * @GetApi(path="/rank", description="价格页")
     */
    public function pricingPlan()
    {
        $data = [
            'groups' => [],
        ];
        $RankCollection = Rank::findManyFromCache(RankService::getPlanIds());
        foreach ($RankCollection->toArray() as $rank) {
            $plan = Arr::only($rank, ['id', 'rank_name', 'duration', 'price', 'original_price', 'first_price', 'permission', 'discounts']);
            $data['groups'][$plan['rank_name']][$plan['duration']] = $plan;
        }
        return $this->view('frontend/pricing-plan', $data);
    }

    /**
     * @GetApi(path="{lang:zh|tw|ko|ja|pt|es|de|fr|vi}/rank", description="价格页")
     */
    public function localePricingPlan()
    {
        return $this->pricingPlan();
    }

    /**
     * @GetApi(path="/affiliate", description="分销首页")
     */
    public function affiliate()
    {
        return $this->view('frontend/affiliate');
    }

    /**
     * @GetApi(path="{lang:zh|tw|ko|ja|pt|es|de|fr|vi}/affiliate", description="分销首页")
     */
    public function localeAffiliate()
    {
        return $this->affiliate();
    }
}
