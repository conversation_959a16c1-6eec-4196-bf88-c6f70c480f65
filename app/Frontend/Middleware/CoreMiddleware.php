<?php

declare(strict_types=1);

namespace App\Frontend\Middleware;

use Website\Common\FrontendApi\Controller\AbstractController;
use Carbon\Carbon;
use Hyperf\Contract\TranslatorInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpMessage\Cookie\Cookie;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Context\Context;
use Lete\Base\Utils\IP;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class CoreMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var \Hyperf\HttpServer\Contract\ResponseInterface
     */
    protected $response;

    /**
     * @Inject
     * @var TranslatorInterface
     */
    protected $translator;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 语言优先级：链接语言>本地保存的语言>浏览器语言（浏览器语言不为网站的语言时忽略）>默认语言
        $mapping = [
            'zh' => 'zh_CN',
        ];
        $locales = AbstractController::locales();
        // 默认语言
        $locale = 'en';
        // cookie保存的语言
        $locale_cookie_name = 'locale' . (env('APP_ENV') === 'prod' ? '' : ('-' . env('APP_ENV')));
        $cookie_locale = $this->request->cookie($locale_cookie_name);
        $cookie_locale = in_array($cookie_locale, $locales) ? $cookie_locale : null;
        // 匹配浏览器的语言
        $accept_language_mapping = [
            'zh-CN' => 'zh',
            'zh-HK' => 'tw',
            'zh-TW' => 'tw',
        ];
        $accept_language = $this->request->header('accept-language') ? explode(',', $this->request->header('accept-language'))[0] : null;
        $device_locale = $accept_language_mapping[$accept_language] ?? $accept_language;
        $device_locale = in_array($device_locale, $locales) ? $device_locale : null;
        // 匹配链接上的语言
        $uri_path = $this->request->getUri()->getPath();
        $uri_query = $this->request->getUri()->getQuery();
        preg_match('/^\/(' . implode('|', $locales) . ')*(\/)*.*$/', $uri_path, $matches);
        $uri_locale = isset($matches[1]) && in_array($matches[1], $locales) ? $matches[1] : null;
        if ($uri_locale) {
            $locale = $uri_locale;
        } elseif ($cookie_locale || $device_locale) {
            $locale = $cookie_locale ? $cookie_locale : $device_locale;
        }

        // 当前语言 和 cookie的不一致，则更新
        if ($locale !== $cookie_locale) {
            $uri = $this->request->getUri();
            $domain = env('FRONTEND_COOKIE_DOMAIN');
            if (!$domain) {
                $domain = isset($this->request->getHeaders()['x-forwarded-host']) ? explode(':', $this->request->getHeaders()['x-forwarded-host'][0])[0] : $uri->getHost();
            }
            $cookie = new Cookie(
                $locale_cookie_name,
                $locale,
                Carbon::now()->addYears(2)->timestamp,
                '/',
                $domain,
                strtolower($uri->getScheme()) === 'https',
                false
            );
            $response = Context::get(ResponseInterface::class);
            if (!method_exists($response, 'withCookie')) {
                $response = $response->withHeader('Set-Cookie', (string) $cookie);
            } else {
                /* @var \Hyperf\HttpMessage\Server\Response $response */
                $response = $response->withCookie($cookie);
            }
            Context::set(ResponseInterface::class, $response);
        }

        // 当链接上不包含语言，且当前语言非英文，则进行重定向
        if (!$uri_locale && $locale !== 'en') {
            return $this->response->redirect("/{$locale}" . ($uri_path === '/' ? '' : $uri_path) . ($uri_query ? "?{$uri_query}" : ''));
        }

        // 剔除语言后的路径
        $current_path = preg_replace("/^\/{$locale}\/*/", '/', $uri_path);
        // 当链接上的语言为英文时，重定向到无语言的链接
        if ($uri_locale === 'en') {
            return $this->response->redirect($current_path . ($uri_query ? "?{$uri_query}" : ''));
        }
        $ServerRequestInterface = $request->withAttribute('current-path',  $current_path);

        $this->translator->setLocale($mapping[$locale] ?? $locale);
        $ServerRequestInterface = $ServerRequestInterface->withAttribute('locale',  $locale);

        //用户ip
        $request = Context::set(ServerRequestInterface::class, $ServerRequestInterface->withAttribute('client-ip', IP::get($this->request)));

        return $handler->handle($request);
    }
}
