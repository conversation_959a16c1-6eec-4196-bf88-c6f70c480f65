<?php

declare(strict_types=1);

namespace App\Command;

use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Symfony\Component\Console\Input\InputArgument;

/**
 * @Command
 */
class TestCommand extends HyperfCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $name = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试';

    /**
     * @return array
     */
    protected function getArguments()
    {
        return [
            ['method', InputArgument::REQUIRED, '方法不能为空'],
            ['arg1', InputArgument::OPTIONAL, '参数1'],
            ['arg2', InputArgument::OPTIONAL, '参数2'],
            ['arg3', InputArgument::OPTIONAL, '参数3'],
            ['arg4', InputArgument::OPTIONAL, '参数4'],
        ];
    }

    /**
     * 执行命令
     * @throws \Exception
     */
    public function handle()
    {
        $method = Str::studly($this->input->getArgument('method'));
        if (!method_exists($this, $method)) {
            throw new \Exception('Method not exists.');
        }
        $method_args = $this->input->getArguments();
        Arr::forget($method_args, ['command', 'method']);
        $method_args = array_map(function ($value) {
            if (in_array($value, ['true', 'false'])) {
                $value = $value === 'true' ? true : false;
            }
            return $value;
        }, $method_args);
        $this->{$method}(...array_values($method_args));
        print_r("脚本执行完成\n");
    }
}
