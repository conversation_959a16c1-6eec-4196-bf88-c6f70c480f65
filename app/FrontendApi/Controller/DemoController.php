<?php

declare(strict_types=1);

namespace App\FrontendApi\Controller;

use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\Query;
use Website\Common\FrontendApi\Controller\AbstractController;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Context\Context;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="demo", tag="Demo", server="website-self-frontend-api")
 */
class DemoController extends AbstractController
{
    /**
     * @GetApi(path="", description="列表")
     * @Query(key="order_by|order by", rule="")
     * @Query(key="direction|direction", rule="in:desc,asc")
     * @Query(key="page|page", rule="integer|min:1")
     * @Query(key="per_page|per page", rule="integer|max:100")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');

        $data = [
            'demo' => 'this is a demo',
            'validator_data' => $validator_data,
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @PostApi(path="", description="创建")
     * @FormData(key="name|名称", rule="required|string|max:255")
     */
    public function store()
    {
        try {
            $result = self::save(Context::get('validator.data'));
            return $this->response(200, 'success', $result);
        } catch (\Throwable $e) {
            return $this->response(400, $e->getMessage());
        }
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|id", rule="required|integer")
     * @FormData(key="name|name", rule="required|string|max:255")
     */
    public function update()
    {
        try {
            $result = self::save(Context::get('validator.data'));
            return $this->response(200, 'success', $result);
        } catch (\Throwable $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 保存
     * @param $validator_data
     * @return array
     * @throws \Exception
     */
    public static function save($validator_data)
    {
        if (isset($validator_data['id'])) {
            $Demo = null;
            if (!$Demo) {
                throw new \Exception('Demo not found', 404);
            }
        }

        $attributes = $validator_data;
        return [
            'demo' => (object) [],
        ];
    }

    /**
     * @GetApi(path="{id:\d+}", description="查看")
     * @Path(key="id|Id", rule="required|integer")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');


        $data = [
            'demo' => (object) [],
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @DeleteApi(path="", description="批量删除")
     * @FormData(key="ids|ids", rule="required|array")
     */
    public function batchDelete()
    {
        $validator_data = Context::get('validator.data');

        return $this->response(200, 'success');
    }
}
