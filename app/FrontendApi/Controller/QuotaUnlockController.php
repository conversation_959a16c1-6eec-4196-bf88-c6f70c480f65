<?php

declare(strict_types=1);

namespace App\FrontendApi\Controller;

use App\Constants\ErrorCode;
use App\Constants\OrderType;
use App\Order\QuotaUnlockOrder;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Lete\MongoDB\MongoClient\MongoDb;
use Lete\Pay\Cashier;
use Website\Common\Exception\OrderException;
use Website\Common\FrontendApi\Controller\AbstractController;
use Website\Common\Service\OrderService;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="quota-unlock", tag="额度解锁", server="website-self-frontend-api")
 */
class QuotaUnlockController extends AbstractController
{
    /**
     * @PostApi(path="", description="创建额度解锁订单")
     * @FormData(key="success_url|success url", rule="required|url")
     * @FormData(key="cancel_url|cancel url", rule="url")
     */
    public function store()
    {
        $user = $this->user();
        $validator_data = Context::get('validator.data');

        // 固定金额 4 美元
        $amount = 4;

        try {
            Db::beginTransaction();

            // 使用自定义订单类型，添加备注信息
            $OrderService = OrderService::create(
                $user['id'],
                OrderType::CHAT_UNLOCK,
                $amount,
                ['remark' => '24小时免费用户']
            );

            Db::commit();

        } catch (\Throwable $e) {
            Db::rollBack();

            if ($e instanceof OrderException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }

        Website::logger()->debug('QuotaUnlockController::create called', [
            'order_id' => $OrderService->id,
            'user_id' => $user['id'],
            'amount' => $amount,
            'order_sn' => $OrderService->order_sn,
            'order_type' => $OrderService->type
        ]);

        try {
            // 创建支付会话
            $StripeCheckoutSession = $OrderService->checkout(
                OrderService::PAYMENT_PLATFORM_STRIPE,
                $validator_data['success_url'],
                $validator_data['cancel_url'] ?? null
            );

            return $this->response(200, 'success', [
                'stripe' => [
                    'url' => $StripeCheckoutSession->url,
                ],
            ]);
        } catch (\Throwable $e) {
            // 记录支付会话创建失败日志
            MongoDb::collection('quota_unlock_logs')->insertOne([
                'user_id' => $user['id'],
                'order_id' => $OrderService->id,
                'error_message' => $e->getMessage(),
                'status' => 'checkout_failed',
                'created_at' => time(),
            ]);

            throw $e;
        }
    }
}
