<?php

declare(strict_types=1);

namespace App\BackendApi\Controller;

use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Website\Common\BackendApi\Controller\AdminController;

/**
 * @ApiController(prefix="demo", tag="示例", server="website-self-backend-api")
 */
class DemoController extends AdminController
{
    /**
     * @GetApi(path="", description="列表")
     * @Query(key="order_by|排序字段", rule="")
     * @Query(key="direction|排序方式", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|分页大小", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');

        $data = [
            'demo' => 'this is a demo',
            'validator_data' => $validator_data,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PostApi(path="", description="创建")
     * @FormData(key="name|名称", rule="required|string|max:255")
     */
    public function store()
    {
        try {
            $result = self::save(Context::get('validator.data'));
            return $this->adminResponse(200, '请求成功', $result);
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|Id", rule="required|integer")
     * @FormData(key="name|名称", rule="required|string|max:255")
     */
    public function update()
    {
        try {
            $result = self::save(Context::get('validator.data'));
            return $this->adminResponse(200, '请求成功', $result);
        } catch (\Throwable $e) {
            return $this->adminResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 保存
     * @param $validator_data
     * @return array
     * @throws \Exception
     */
    public static function save($validator_data)
    {
        if (isset($validator_data['id'])) {
            $Demo = null;
            if (!$Demo) {
                throw new \Exception('Demo不存在', 404);
            }
        }

        $attributes = $validator_data;
        return [
            'demo' => (object) [],
        ];
    }

    /**
     * @GetApi(path="{id:\d+}", description="查看")
     * @Path(key="id|Id", rule="required|integer")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');


        $data = [
            'demo' => (object) [],
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @DeleteApi(path="", description="批量删除")
     * @FormData(key="ids|Ids", rule="required|array")
     */
    public function batchDelete()
    {
        $validator_data = Context::get('validator.data');

        return $this->adminResponse(200, '请求成功');
    }
}
