{"title": "Book Summary", "description": "A book summary website", "keywords": "book, summary, review, analysis", "Metadata": {"title": "Book Summary", "description": "A book summary website", "keywords": "book, summary, review, analysis", "brandName": "15Minutes", "bookDetail": {"defaultTitle": "Book Summary & Analysis | 15Minutes", "defaultDescription": "Explore book summaries & analysis in 15Minutes. Enjoy 15‑min reads, or listen to our podcast and audiobooks. Quick plots, reviews & chapter synopses."}, "register": {"title": "Register", "description": "Register"}, "resetPassword": {"title": "Reset Password", "description": "Reset Password"}, "privacy": {"title": "Privacy Policy", "description": "Privacy Policy"}, "terms": {"title": "Terms of Use", "description": "Terms of Use"}, "pricing": {"title": "Pricing", "description": "Pricing"}, "profile": {"title": "Profile", "description": "Manage your profile and account settings"}}, "NotFound": {"title": "Not Found", "message": "The page you are looking for does not exist.", "backToHome": "Back to Home"}, "Avatar": {"profile": "Profile", "terms": "Terms", "privacy": "Privacy", "signIn": "Sign In", "logout": "Logout"}, "Register": {"emailSentMessage": "We've sent an activation code to your email : ", "pleaseActivate": "Please activate your account to continue.", "finishRegister": "Finish Register", "activationCodePlaceholder": "Activation Code", "activationCodeLabel": "Activation Code", "activationCodeRequired": "Activation code is required", "activationCodeFormat": "Activation code must be 4 digits", "activateButton": "Activate", "resendButton": "Resend Activation Code"}, "RedisTest": {"title": "Redis Test", "description": "Test Redis key-value operations", "key": "Key", "keyPlaceholder": "Enter key", "value": "Value", "valuePlaceholder": "Enter value", "set": "Set", "get": "Get", "delete": "Delete", "loading": "Loading...", "keyValueRequired": "Both key and value are required", "keyRequired": "Key is required", "unknownError": "An unknown error occurred", "requestFailed": "Request failed", "setSuccess": "Successfully set value for key: {key}", "getValue": "Value for key {key}: {value}", "keyNotFound": "No value found for key: {key}", "deleteSuccess": "Successfully deleted key: {key}"}, "PaySuccess": {"paymentSuccess": "Payment Successful!", "getStarted": "Get Started", "downloadVoucher": "Download Invoice"}, "Price": {"redirectingToManageSubscription": "Redirecting to subscription management...", "unsubscribeBeforePurchasing": "You need to cancel your current subscription before purchasing a new one."}, "resetPassword": {"title": "Reset Password", "emailPlaceholder": "Email", "emailLabel": "Email", "emailRequired": "Email is required", "emailFormat": "<PERSON><PERSON> is invalid", "sendButton": "Send", "rememberPassword": "Remember your password?", "signIn": "Sign In"}, "Auth": {"email": "Email", "buttons": {"resetPassword": "Reset Password", "signIn": "Sign In", "signUp": "Sign Up"}, "password": "Password", "confirmPassword": "Confirm Password", "emailRequired": "Email is required", "emailInvalid": "<PERSON><PERSON> is invalid", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 6 characters"}, "resetPasswordSent": {"title": "Reset Password", "emailSentMessage": "We've sent a password reset email to {email}. Please check your inbox and click on the reset password link to continue.", "loginButton": "<PERSON><PERSON>", "signUpButton": "Sign Up", "emailRequired": "Email is required"}, "API": {"Error": {"InternalServerError": "Internal server error", "Unauthorized": "Unauthorized access", "userNotLoggedIn": "User not logged in", "invalidBookId": "Invalid book ID", "bookIdRequired": "Book ID is required", "audioServiceConfigError": "Audio service configuration error", "methodNotAllowed": "Method not allowed", "fieldMustBeANumber": "Field {field} must be a number", "invalidJsonPayload": "Invalid JSON payload", "fieldIsRequired": "Field {field} is required"}}, "UserInfo": {"expiredAt": "Expires at", "changePlan": "Change Plan"}, "TokenUsage": {"title": "Token Usage", "monthlyUsage": "Monthly Usage", "nextReset": "Next Reset", "usedQuota": "Used Quota"}, "PaymentRecords": {"title": "Payment Records", "invoiceNumber": "Invoice Number", "status": "Status", "created": "Created", "actions": "Actions", "paid": "Paid", "unpaid": "Unpaid", "refunded": "Refunded", "noRecords": "No payment records found", "results": "results", "prev": "Prev", "next": "Next"}, "Profile": {"InvoiceInfo": {"title": "Invoice Information", "email": "Email", "emailPlaceholder": "Enter your email", "username": "Username", "usernamePlaceholder": "Enter your username", "company": "Company", "companyPlaceholder": "Enter your company name", "countryRegion": "Country/Region", "phone": "Phone", "phonePlaceholder": "Enter your phone number", "address": "Address", "addressPlaceholder": "Enter your address", "vat": "VAT Number", "vatPlaceholder": "Enter your VAT number", "city": "City", "cityPlaceholder": "Enter your city", "province": "Province/State", "provincePlaceholder": "Enter your province or state", "postal": "Postal Code", "postalPlaceholder": "Enter your postal code", "saveButton": "Save Changes"}}, "DownloadInvoice": {"downloadButton": "Download Invoice", "invoiceTitle": "Invoice", "invoiceNumber": "Invoice number", "dateOfIssue": "Date of issue", "dateDue": "Date due", "billTo": "Bill to", "payOnline": "Pay online", "description": "Description", "qty": "Qty", "unitPrice": "Unit price", "amount": "Amount", "subtotal": "Subtotal", "total": "Total", "amountDue": "Amount due", "pageInfo": "Page 1 of 1", "planName": "15Minutes {planName} Plan"}}