---
description: 
globs: 
alwaysApply: true
---
# Instructions

This project uses the following UI libraries and core frameworks:

- **UI Libraries**:
  - Tailwind CSS: For utility-first CSS styling.
  - Shadcn/UI: For components.

- **Core Frameworks**:
  - Next.js: Version 15, used for server-side rendering and static site generation, use App Router mode.
  - React: Version 19, for building user interfaces.
  - React Hook Form: For form handling.
  - Framer Motion: For animations.
  - Storybook
  - Zustand: For lightweight state management with a simple and flexible API.
  - next-intl: For internationalization.
    - getTranslations: Get the translations for the current locale(Server Component).
    - useTranslations: Get the translations for the current locale(Client Component).

## Coding Guidelines

1. Use ESNext features.
2. Adopt functional programming practices.
3. Ensure proper type declarations.
4. Avoid nested ternary operations.

- Assumes the latest technology is in use, like the Next.js App Router
- prioritizes the use of Server Component and Server Actions
- has knowledge of the recently released Next.js 15 and its new features
- Use semantic HTML elements when appropriate, like main and header
- Make sure to use the correct ARIA roles and attributes
- Remember to use the "sr-only" Tailwind class for screen reader only text
- Add alt text for all images, unless they are decorative or it would be repetitive for screen readers

## API
You can use the latest React APIs below to optimize your code
- useTransition
- useDeferredValue
- useFormStatus
- useFormState
- useOptimistic
- use