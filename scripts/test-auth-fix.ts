#!/usr/bin/env bun

/**
 * 测试认证修复效果的脚本
 * 
 * 使用方法：
 * bun scripts/test-auth-fix.ts
 */

import { getRedisClient } from '../src/lib/redis'
import { getTokenKey, getAppName, getRedisTokenKey, validateAuthConfig } from '../src/lib/auth/config'

async function testAuthFix() {
  console.log('🔍 开始测试认证修复效果...\n')
  
  // 1. 验证配置
  console.log('1️⃣ 验证配置:')
  const configValidation = validateAuthConfig()
  console.log('配置验证结果:', configValidation)
  
  if (!configValidation.valid) {
    console.error('❌ 配置验证失败，请检查环境变量')
    process.exit(1)
  }
  
  console.log('✅ 配置验证通过\n')
  
  // 2. 测试配置函数
  console.log('2️⃣ 测试配置函数:')
  const tokenKey = getTokenKey()
  const appName = getAppName()
  console.log('Token Key:', tokenKey)
  console.log('App Name:', appName)
  console.log('Redis Token Key 示例:', getRedisTokenKey('123'))
  console.log('✅ 配置函数正常\n')
  
  // 3. 测试 Redis 连接
  console.log('3️⃣ 测试 Redis 连接:')
  try {
    const redis1 = getRedisClient(1) // 数据库1
    const redis0 = getRedisClient(0) // 数据库0
    
    // 测试连接
    await redis1.ping()
    await redis0.ping()
    
    console.log('✅ Redis 连接正常')
    
    // 测试写入和读取
    const testKey = `${appName}:test:${Date.now()}`
    await redis1.set(testKey, 'test-value', 'EX', 10)
    const value = await redis1.get(testKey)
    
    if (value === 'test-value') {
      console.log('✅ Redis 读写测试通过')
      await redis1.del(testKey) // 清理测试数据
    } else {
      console.error('❌ Redis 读写测试失败')
    }
  } catch (error) {
    console.error('❌ Redis 连接失败:', error)
    process.exit(1)
  }
  
  console.log('')
  
  // 4. 检查现有 token
  console.log('4️⃣ 检查现有 token:')
  try {
    const redis1 = getRedisClient(1)
    
    // 扫描所有 token key
    const pattern = `${appName}:u:token:*`
    const keys = await redis1.keys(pattern)
    
    console.log(`找到 ${keys.length} 个 token key:`)
    for (const key of keys.slice(0, 5)) { // 只显示前5个
      const exists = await redis1.exists(key)
      console.log(`  ${key}: ${exists ? '存在' : '不存在'}`)
    }
    
    if (keys.length > 5) {
      console.log(`  ... 还有 ${keys.length - 5} 个`)
    }
  } catch (error) {
    console.error('❌ 检查 token 失败:', error)
  }
  
  console.log('')
  
  // 5. 总结
  console.log('📋 修复总结:')
  console.log('✅ 统一了 Token Key 配置')
  console.log('✅ 修复了 Redis 数据库选择问题（使用数据库1）')
  console.log('✅ 统一了应用名称配置')
  console.log('✅ 添加了详细的调试日志')
  console.log('✅ 创建了调试 API 端点')
  
  console.log('\n🎉 认证修复测试完成！')
  console.log('\n📝 下一步建议:')
  console.log('1. 访问 /api/debug/auth 查看详细的认证状态')
  console.log('2. 测试用户登录和 Server Action 调用')
  console.log('3. 检查浏览器开发者工具的网络请求')
  console.log('4. 查看服务器日志中的认证相关信息')
  
  process.exit(0)
}

// 运行测试
testAuthFix().catch(error => {
  console.error('❌ 测试失败:', error)
  process.exit(1)
})
