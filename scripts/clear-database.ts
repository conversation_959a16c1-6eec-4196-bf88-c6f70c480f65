#!/usr/bin/env bun

import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

// 日志函数
const logger = {
  info: (msg: string) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  success: (msg: string) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`),
  warn: (msg: string) => console.log(`[WARN] ${new Date().toISOString()} - ${msg}`),
  error: (msg: string, error?: any) => {
    console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`)
    if (error) console.error(error)
  }
}

async function clearDatabase() {
  logger.info('开始清理数据库...')
  
  try {
    // 删除所有关联表数据（按依赖关系顺序）
    logger.info('删除书籍分类关联...')
    await prisma.book_categories.deleteMany({})
    
    logger.info('删除书籍作者关联...')
    await prisma.book_authors.deleteMany({})
    
    logger.info('删除书籍封面...')
    await prisma.book_covers.deleteMany({})
    
    logger.info('删除书籍章节...')
    await prisma.book_chapters.deleteMany({})
    
    logger.info('删除书籍翻译...')
    await prisma.book_translations.deleteMany({})
    
    logger.info('删除评分...')
    await prisma.ratings.deleteMany({})
    
    // 删除主表数据
    logger.info('删除书籍...')
    await prisma.books.deleteMany({})
    
    logger.info('删除作者翻译...')
    await prisma.author_translations.deleteMany({})
    
    logger.info('删除作者...')
    await prisma.authors.deleteMany({})
    
    logger.info('删除分类翻译...')
    await prisma.category_translations.deleteMany({})
    
    logger.info('删除分类...')
    await prisma.categories.deleteMany({})
    
    logger.info('删除出版商翻译...')
    await prisma.publisher_translations.deleteMany({})
    
    logger.info('删除出版商...')
    await prisma.publishers.deleteMany({})
    
    // 验证清理结果
    const counts = {
      books: await prisma.books.count(),
      authors: await prisma.authors.count(),
      categories: await prisma.categories.count(),
      book_translations: await prisma.book_translations.count(),
      book_chapters: await prisma.book_chapters.count(),
      book_categories: await prisma.book_categories.count(),
      book_authors: await prisma.book_authors.count()
    }
    
    logger.info('数据库清理完成！各表记录数：')
    Object.entries(counts).forEach(([table, count]) => {
      logger.info(`  ${table}: ${count} 条记录`)
    })
    
    // 清理进度文件
    const progressFile = path.join(process.cwd(), 'scripts', 'import-progress.json')
    if (fs.existsSync(progressFile)) {
      fs.unlinkSync(progressFile)
      logger.info('已删除导入进度文件')
    }
    
    logger.success('数据库清理成功完成！')
    
  } catch (error) {
    logger.error('数据库清理失败', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--confirm') || args.includes('-y')) {
    await clearDatabase()
  } else {
    console.log('⚠️  警告：此操作将删除数据库中的所有书籍相关数据！')
    console.log('如果确定要继续，请使用以下命令：')
    console.log('bun scripts/clear-database.ts --confirm')
  }
}

main().catch(console.error)
