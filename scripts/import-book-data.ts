/**
 * 书籍数据导入脚本
 * 将JSON格式的书籍数据导入到数据库中
 * 特别处理review_summary字段的导入
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { PrismaClient } from '@prisma/client'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

// 定义数据类型
interface BookData {
  BookID: string
  'book title': string
  'book subtitle'?: string
  Author: string
  Genre: string
  'Best Quotes'?: string
  'Review Summary'?: string
  'PDF file'?: string
  'EUPB FIle'?: string // 注意这里的拼写错误，保持与原始数据一致
  'Rate Score'?: number
  Ratings?: number
  'Book Cover'?: string
  BookSummary?: string
}

/**
 * 数据验证函数
 * @param bookData 书籍数据
 * @returns 验证结果
 */
function validateBookData(bookData: BookData): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // 检查必填字段
  if (!bookData.BookID) errors.push('缺少BookID')
  if (!bookData['book title']) errors.push('缺少书籍标题')
  if (!bookData.Author) errors.push('缺少作者信息')
  if (!bookData.Genre) errors.push('缺少分类信息')

  // 检查数据格式
  if (bookData['Rate Score'] && (isNaN(Number(bookData['Rate Score'])) || Number(bookData['Rate Score']) < 0 || Number(bookData['Rate Score']) > 5)) {
    errors.push('评分格式不正确，应为0-5之间的数字')
  }

  if (bookData.Ratings && (isNaN(Number(bookData.Ratings)) || Number(bookData.Ratings) < 0)) {
    errors.push('评分数量格式不正确，应为非负数字')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 主导入函数
 * @param filePath JSON文件路径
 */
async function importBookData(filePath: string) {
  try {
    // 读取JSON文件
    console.log(`正在读取文件: ${filePath}`)
    const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8')) as BookData[]

    console.log(`读取到 ${jsonData.length} 条书籍数据`)

    // 导入数据
    let successCount = 0
    let errorCount = 0
    const errors: { bookId: string; title: string; error: string }[] = []

    for (const bookData of jsonData) {
      try {
        // 验证数据
        const validation = validateBookData(bookData)
        if (!validation.valid) {
          throw new Error(`数据验证失败: ${validation.errors.join(', ')}`)
        }

        // 使用事务处理
        await prisma.$transaction(async (tx) => {
          // 1. 处理作者数据
          const authorIds = bookData.Author.split(',')
          const authorEntities = []

          for (const authorRawId of authorIds) {
            // 查找或创建作者
            let author = await tx.authors.findFirst({
              where: {
                OR: [
                  { id: parseInt(authorRawId.trim()) || 0 },
                  {
                    author_translations: {
                      some: { name: { contains: authorRawId.trim() } }
                    }
                  }
                ]
              }
            })

            if (!author) {
              // 创建新作者
              author = await tx.authors.create({
                data: {
                  // 作者其他信息将在后续导入
                  avatar_url: null
                }
              })

              // 创建作者翻译
              await tx.author_translations.create({
                data: {
                  author_id: author.id,
                  language_id: 'en',
                  name: authorRawId.trim(),
                  is_default: 1
                }
              })
            }
            authorEntities.push(author)
          }

          // 2. 处理分类数据
          const genreIds = bookData.Genre.split(',')
          const categoryEntities = []

          for (const genreRawId of genreIds) {
            // 查找或创建分类
            let category = await tx.categories.findFirst({
              where: {
                category_translations: {
                  some: {
                    name: { contains: genreRawId.trim() }
                  }
                }
              }
            })

            if (!category) {
              // 创建新分类
              category = await tx.categories.create({
                data: {}
              })

              // 创建分类翻译
              await tx.category_translations.create({
                data: {
                  category_id: category.id,
                  language_id: 'en',
                  name: genreRawId.trim(),
                  is_default: 1
                }
              })
            }
            categoryEntities.push(category)
          }

          // 3. 检查书籍是否已存在
          const existingBook = await tx.books.findFirst({
            where: {
              OR: [
                { id: parseInt(bookData.BookID) || 0 },
                {
                  book_translations: {
                    some: {
                      title: bookData['book title']
                    }
                  }
                }
              ]
            }
          })

          let book;

          if (existingBook) {
            // 更新现有书籍
            book = await tx.books.update({
              where: { id: existingBook.id },
              data: {
                pdf_url: bookData['PDF file'] || existingBook.pdf_url,
                epub_url: bookData['EUPB FIle'] || existingBook.epub_url,
                is_published: 1, // 默认发布
              }
            })

            // 更新书籍翻译
            const existingTranslation = await tx.book_translations.findFirst({
              where: {
                book_id: book.id,
                language_id: 'en'
              }
            })

            if (existingTranslation) {
              await tx.book_translations.update({
                where: { id: existingTranslation.id },
                data: {
                  title: bookData['book title'],
                  subtitle: bookData['book subtitle'] || existingTranslation.subtitle,
                  review_summary: bookData['Review Summary'] || existingTranslation.review_summary,
                  best_quote: bookData['Best Quotes'] || existingTranslation.best_quote,
                }
              })
            } else {
              await tx.book_translations.create({
                data: {
                  book_id: book.id,
                  language_id: 'en',
                  title: bookData['book title'],
                  subtitle: bookData['book subtitle'] || null,
                  review_summary: bookData['Review Summary'] || null,
                  best_quote: bookData['Best Quotes'] || null,
                  is_default: 1,
                }
              })
            }
          } else {
            // 创建新书籍
            book = await tx.books.create({
              data: {
                pdf_url: bookData['PDF file'],
                epub_url: bookData['EUPB FIle'],
                is_published: 1, // 默认发布

                // 4. 创建书籍翻译记录
                book_translations: {
                  create: {
                    language_id: 'en', // 默认英语
                    title: bookData['book title'],
                    subtitle: bookData['book subtitle'] || null,
                    review_summary: bookData['Review Summary'] || null,
                    best_quote: bookData['Best Quotes'] || null,
                    is_default: 1, // 设为默认翻译
                  }
                },
              }
            })
          }

          // 5. 处理书籍封面
          if (bookData['Book Cover']) {
            const existingCover = await tx.book_covers.findFirst({
              where: {
                book_id: book.id,
                language_id: 'en',
                is_primary: 1
              }
            })

            if (existingCover) {
              await tx.book_covers.update({
                where: { id: existingCover.id },
                data: {
                  image_url: bookData['Book Cover'],
                }
              })
            } else {
              await tx.book_covers.create({
                data: {
                  book_id: book.id,
                  language_id: 'en',
                  image_url: bookData['Book Cover'],
                  is_primary: 1,
                }
              })
            }
          }

          // 6. 关联作者
          for (const author of authorEntities) {
            // 检查关联是否已存在
            const existingRelation = await tx.book_authors.findFirst({
              where: {
                book_id: book.id,
                author_id: author.id
              }
            })

            if (!existingRelation) {
              await tx.book_authors.create({
                data: {
                  book_id: book.id,
                  author_id: author.id,
                }
              })
            }
          }

          // 7. 关联分类
          for (const category of categoryEntities) {
            // 检查关联是否已存在
            const existingRelation = await tx.book_categories.findFirst({
              where: {
                book_id: book.id,
                category_id: category.id
              }
            })

            if (!existingRelation) {
              await tx.book_categories.create({
                data: {
                  book_id: book.id,
                  category_id: category.id
                }
              })
            }
          }

          // 8. 处理评分数据
          if (bookData['Rate Score']) {
            // 这里简化处理，实际应该考虑更复杂的评分逻辑
            console.log(`书籍 ${bookData['book title']} 的评分: ${bookData['Rate Score']}, 评分数量: ${bookData.Ratings || 0}`)
          }
        })

        successCount++
        console.log(`成功导入书籍: ${bookData['book title']} (ID: ${bookData.BookID})`)
      } catch (error) {
        errorCount++
        const errorMessage = error instanceof Error ? error.message : String(error)
        errors.push({
          bookId: bookData.BookID,
          title: bookData['book title'],
          error: errorMessage
        })
        console.error(`导入书籍失败: ${bookData['book title']} (ID: ${bookData.BookID})`, errorMessage)
      }
    }

    console.log(`导入完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`)
    if (errors.length > 0) {
      console.log('错误详情:')
      console.log(errors)
    }

    return {
      total: jsonData.length,
      success: successCount,
      error: errorCount,
      errors
    }
  } catch (error) {
    console.error('导入过程中出错:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 命令行参数处理
async function main() {
  const args = process.argv.slice(2)

  if (args.length < 1) {
    console.log('用法: bun scripts/import-book-data.ts <JSON文件路径>')
    console.log('默认使用: scripts/book-data/Main Content and Summary.json')

    // 使用默认文件路径
    const defaultPath = path.join(__dirname, 'book-data', 'Main Content and Summary.json')
    await importBookData(defaultPath)
  } else {
    const inputPath = path.resolve(args[0])
    await importBookData(inputPath)
  }
}

// 如果直接运行此脚本（而不是作为模块导入）
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

// 导出函数供其他模块使用
export { importBookData }
