#!/usr/bin/env bun

import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

// 数据类型定义
interface BookDetailData {
  BookID: string
  'brief analysis'?: string
  Author?: string
  Content?: string
  Genre?: string
  Binding?: string
  Publisher?: string
  'Publish Date'?: string
  ISBN?: string
  ISBN13?: string
  ASIN?: string
}

interface ImportResult {
  success: number
  failed: number
  skipped: number
  errors: Array<{ id: string, error: string }>
}

// 工具函数
function cleanString(str: string | undefined | null): string | null {
  if (!str || typeof str !== 'string') return null
  const cleaned = str.trim()
  return cleaned === '' ? null : cleaned
}

function extractYearFromDate(dateStr: string | undefined | null): number | null {
  if (!dateStr || typeof dateStr !== 'string') return null

  const cleaned = dateStr.trim()
  if (!cleaned) return null

  // 尝试提取4位数年份
  const yearMatch = cleaned.match(/\b(19|20)\d{2}\b/)
  if (yearMatch) {
    const year = parseInt(yearMatch[0])
    if (year >= 1900 && year <= new Date().getFullYear() + 5) {
      return year
    }
  }

  return null
}

function validateRequiredFields(data: any, requiredFields: string[]): string[] {
  const missing = []
  for (const field of requiredFields) {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      missing.push(field)
    }
  }
  return missing
}

// 单独导入书籍详情
async function importBookDetailsOnly(): Promise<ImportResult> {
  console.log('📑 开始单独导入书籍详情数据')

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const dataBasePath = path.join(__dirname, 'book-data')
  const detailFile = path.join(dataBasePath, 'Brief Analysis and Book editions.json')

  // 检查文件是否存在
  if (!fs.existsSync(detailFile)) {
    console.error(`❌ 书籍详情文件不存在: ${detailFile}`)
    return result
  }

  console.log(`📖 读取书籍详情文件: ${detailFile}`)

  let detailData: BookDetailData[]
  try {
    const fileContent = fs.readFileSync(detailFile, 'utf8')
    detailData = JSON.parse(fileContent)
    console.log(`📊 找到 ${detailData.length} 条书籍详情记录`)
  } catch (error) {
    console.error('❌ 读取或解析文件失败:', error)
    return result
  }

  const startTime = Date.now()
  let processed = 0

  for (const detail of detailData) {
    try {
      // 验证必填字段
      const missingFields = validateRequiredFields(detail, ['BookID'])
      if (missingFields.length > 0) {
        result.failed++
        result.errors.push({ id: detail.BookID || 'unknown', error: `缺少必填字段: ${missingFields.join(', ')}` })
        continue
      }

      await prisma.$transaction(async (tx) => {
        // 查找对应的书籍
        const book = await tx.books.findFirst({
          where: { rawid: detail.BookID }
        })

        if (!book) {
          result.failed++
          result.errors.push({ id: detail.BookID, error: `找不到对应的书籍记录` })
          return
        }

        // 更新书籍详情
        await tx.books.update({
          where: { id: book.id },
          data: {
            isbn: cleanString(detail.ISBN),
            isbn13: cleanString(detail.ISBN13),
            asin: cleanString(detail.ASIN),
            publication_year: extractYearFromDate(detail['Publish Date']),
            content_type: cleanString(detail.Content),
            binding: cleanString(detail.Binding)
          }
        })

        // 更新翻译表中的 plot_summary
        await tx.book_translations.updateMany({
          where: {
            book_id: book.id,
            language_id: 'en'
          },
          data: {
            plot_summary: cleanString(detail['brief analysis'])
          }
        })

        // 处理出版商关系
        if (detail.Publisher) {
          const publisherName = cleanString(detail.Publisher)
          if (publisherName) {
            // 查找现有出版商（通过翻译表）
            const existingPublisher = await tx.publisher_translations.findFirst({
              where: {
                name: publisherName,
                language_id: 'en'
              }
            })

            let publisherId: number

            if (existingPublisher && existingPublisher.publisher_id) {
              publisherId = existingPublisher.publisher_id
            } else {
              // 创建新出版商
              const newPublisher = await tx.publishers.create({
                data: {}
              })

              // 创建出版商翻译记录
              await tx.publisher_translations.create({
                data: {
                  publisher_id: newPublisher.id,
                  language_id: 'en',
                  name: publisherName,
                  is_default: 1
                }
              })

              publisherId = newPublisher.id
            }

            // 更新书籍的出版商关联
            await tx.books.update({
              where: { id: book.id },
              data: { publisher_id: publisherId }
            })
          }
        }

        result.success++
        console.log(`✅ 成功更新书籍详情: ${detail.BookID}`)
      })

    } catch (error) {
      result.failed++
      const errorMessage = error instanceof Error ? error.message : String(error)
      result.errors.push({ id: detail.BookID, error: errorMessage })
      console.error(`❌ 导入书籍详情失败: ${detail.BookID}`, error)
    }

    processed++
    if (processed % 50 === 0 || processed === detailData.length) {
      const elapsed = Date.now() - startTime
      const rate = processed / (elapsed / 1000)
      const remaining = Math.round((detailData.length - processed) / rate)
      console.log(`📊 书籍详情导入: ${processed}/${detailData.length} (${((processed / detailData.length) * 100).toFixed(1)}%) | ${rate.toFixed(1)}条/秒 | 剩余${remaining}秒`)
    }
  }

  console.log(`✅ 书籍详情导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)

  if (result.errors.length > 0) {
    console.log('\n❌ 错误详情:')
    result.errors.slice(0, 10).forEach(error => {
      console.log(`  - ${error.id}: ${error.error}`)
    })
    if (result.errors.length > 10) {
      console.log(`  ... 还有 ${result.errors.length - 10} 个错误`)
    }
  }

  return result
}

// 主函数
async function main() {
  try {
    console.log('🚀 开始单独导入书籍详情...')
    const result = await importBookDetailsOnly()

    console.log('\n📊 导入结果汇总:')
    console.log(`✅ 成功: ${result.success} 条`)
    console.log(`❌ 失败: ${result.failed} 条`)
    console.log(`⏭️  跳过: ${result.skipped} 条`)

  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
if (require.main === module) {
  main()
}
