#!/usr/bin/env node
import prompts from 'prompts'
import fs from 'fs'
import path from 'path'
import fsExtra from 'fs-extra'
import { Project, SyntaxKind, ArrayLiteralExpression } from 'ts-morph'

// 定义可用的语言选项
const availableLocales = ['de', 'en', 'es', 'fr', 'ja', 'ko', 'pt', 'tw', 'vi', 'zh']

// 定义路径
const messagesDir = path.join(process.cwd(), 'src', 'messages')
const routingFilePath = path.join(process.cwd(), 'src', 'i18n', 'routing.ts')
const packageJsonPath = path.join(process.cwd(), 'package.json')

// 询问项目名称、语言选择和端口
async function askQuestions() {
  const responses = await prompts([
    {
      type: 'text',
      name: 'projectName',
      message: '请输入项目名称（仅限英文字符）:',
      validate: (value) => {
        // 检查是否是有效的npm包名
        if (!/^[a-zA-Z0-9-_]+$/.test(value)) {
          return '请只使用英文字母、数字、连字符和下划线'
        }
        if (value.length < 1) {
          return '名称不能为空'
        }
        if (value.startsWith('.') || value.startsWith('_')) {
          return '名称不能以.或_开头'
        }
        return true
      }
    },
    {
      type: 'multiselect',
      name: 'selectedLocales',
      message: '选择需要包含的i18n语言:',
      choices: availableLocales.map((locale) => ({
        title: locale,
        value: locale,
        selected: locale === 'en' // 默认选中英语
      })),
      min: 1,
      hint: '- 空格选择，回车确认'
    },
    {
      type: 'number',
      name: 'port',
      message: '请输入开发服务器端口号:',
      initial: 3000,
      validate: (value) => {
        if (!Number.isInteger(value)) {
          return '请输入整数'
        }
        if (value < 1 || value > 65535) {
          return '端口号必须在1-65535之间'
        }
        return true
      }
    }
  ])

  return responses
}

// 创建语言JSON文件
function createLocaleFiles(selectedLocales: string[]) {
  // 确保messages目录存在
  fsExtra.ensureDirSync(messagesDir)

  // 为每个选定的语言创建JSON文件
  selectedLocales.forEach((locale) => {
    const filePath = path.join(messagesDir, `${locale}.json`)
    // 创建一个基本的JSON模板
    const template = {
      app: {
        title: 'Next.js Project',
        description: 'A Next.js project with i18n support'
      },
      common: {
        welcome: '欢迎',
        home: '首页',
        about: '关于'
      }
    }

    fs.writeFileSync(filePath, JSON.stringify(template, null, 2), 'utf8')
    console.log(`已创建 ${locale}.json`)
  })
}

// 使用AST更新routing.ts文件
async function updateRoutingFile(selectedLocales: string[]) {
  try {
    // 确保目录存在
    const dirPath = path.dirname(routingFilePath)
    fsExtra.ensureDirSync(dirPath)

    // 检查文件是否存在，如果不存在则创建一个基本的routing.ts
    if (!fs.existsSync(routingFilePath)) {
      const newContent = `// i18n配置\nexport const locales = ${JSON.stringify(selectedLocales)};`
      fs.writeFileSync(routingFilePath, newContent, 'utf8')
      console.log('已创建 src/i18n/routing.ts')
      return
    }

    // 使用ts-morph解析文件
    const project = new Project()
    const sourceFile = project.addSourceFileAtPath(routingFilePath)

    // 寻找locales变量声明
    let found = false
    const variableDeclarations = sourceFile.getVariableDeclarations()

    for (const declaration of variableDeclarations) {
      if (declaration.getName() === 'locales') {
        found = true
        const initializer = declaration.getInitializer()

        // 检查初始化器是否为数组字面量
        if (initializer && initializer.getKind() === SyntaxKind.ArrayLiteralExpression) {
          const arrayLiteral = initializer as ArrayLiteralExpression

          // 使用 replaceWithText 方法替换整个数组
          arrayLiteral.replaceWithText(
            `[${selectedLocales.map((locale) => `"${locale}"`).join(', ')}]`
          )

          // 保存更改
          await sourceFile.save()
          console.log('已更新 src/i18n/routing.ts')
          break
        }
      }
    }

    // 如果没有找到locales变量，则添加它
    if (!found) {
      const content = sourceFile.getText()
      const newContent = content + `\n\nexport const locales = ${JSON.stringify(selectedLocales)};`
      fs.writeFileSync(routingFilePath, newContent, 'utf8')
      console.log('已添加locales变量到 src/i18n/routing.ts')
    }
  } catch (error) {
    console.error('更新routing文件时出错:', error)
    throw error
  }
}

// 更新package.json
function updatePackageJson(projectName: string, port: number) {
  try {
    // 检查package.json是否存在
    if (fs.existsSync(packageJsonPath)) {
      // 读取package.json
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

      // 保存旧名称以便报告
      const oldName = packageJson.name || '未知'

      // 保存旧端口以便报告
      let oldPort = 3333 // 默认值
      if (packageJson.scripts && packageJson.scripts.dev) {
        const match = packageJson.scripts.dev.match(/--port=(\d+)/)
        if (match && match[1]) {
          oldPort = parseInt(match[1], 10)
        }
      }

      // 更新名称
      packageJson.name = projectName

      // 更新dev脚本的端口
      if (packageJson.scripts) {
        if (packageJson.scripts.dev) {
          // 如果dev脚本已存在，更新端口
          packageJson.scripts.dev = packageJson.scripts.dev.replace(/--port=\d+/, `--port=${port}`)

          // 如果dev脚本不包含port参数，则添加它
          if (!packageJson.scripts.dev.includes('--port=')) {
            packageJson.scripts.dev = `${packageJson.scripts.dev} --port=${port}`
          }
        } else {
          // 如果dev脚本不存在，创建一个
          packageJson.scripts.dev = `next dev --turbopack --port=${port}`
        }
      } else {
        // 如果scripts对象不存在，创建一个
        packageJson.scripts = {
          dev: `next dev --turbopack --port=${port}`
        }
      }

      // 写回更新后的package.json
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8')
      console.log(`已更新package.json: 项目名称从"${oldName}"更改为"${projectName}"`)
      console.log(`已更新package.json: 开发服务器端口从"${oldPort}"更改为"${port}"`)
    } else {
      console.error('找不到package.json，无法更新项目名称和端口。')
      throw new Error('找不到package.json')
    }
  } catch (error) {
    console.error('更新package.json时出错:', error)
    throw error
  }
}

// 主函数
async function main() {
  console.log('=== Next.js项目初始化 ===')

  try {
    // 询问问题
    const { projectName, selectedLocales, port } = await askQuestions()

    if (!projectName || !selectedLocales || selectedLocales.length === 0) {
      console.log('设置已取消或提供的信息不完整。')
      return
    }

    console.log('\n正在应用配置...')

    // 创建语言文件
    createLocaleFiles(selectedLocales)

    // 更新routing.ts
    await updateRoutingFile(selectedLocales)

    // 更新package.json
    updatePackageJson(projectName, port)

    console.log('\n✅ 设置完成！您的Next.js项目已准备就绪。')
    console.log(`项目名称: ${projectName}`)
    console.log(`已配置的语言: ${selectedLocales.join(', ')}`)
    console.log(`开发服务器端口: ${port}`)
  } catch (error) {
    console.error('\n❌ 设置失败:', error instanceof Error ? error.message : '未知错误')
    process.exit(1)
  }
}

// 运行脚本
main()
