# 数据库导入优化方案

## 问题分析

您遇到的导入速度问题主要原因：

### 网络环境差异
- **测试环境**: 局域网 (192.168.3.x) - 延迟 ~1ms
- **正式环境**: 公网 (************) - 延迟 ~50-200ms

### 原始脚本的性能瓶颈
1. **逐条处理**: 每条记录单独事务，大量网络往返
2. **频繁查询**: 每次都要查询关联数据
3. **无批量操作**: 没有利用数据库批量插入能力
4. **无网络优化**: 没有针对远程连接优化

## 优化方案

### 1. 自动环境检测
```typescript
// 自动检测是本地还是远程数据库
function detectEnvironment() {
  const dbUrl = process.env.DATABASE_URL || ''
  const isLocal = /192\.168\.|localhost|127\.0\.0\.1/.test(dbUrl)
  
  return {
    isLocal,
    batchSize: isLocal ? 100 : 20,        // 本地大批次，远程小批次
    timeout: isLocal ? 30000 : 120000,    // 远程更长超时
    maxRetries: isLocal ? 3 : 5           // 远程更多重试
  }
}
```

### 2. 批量处理策略
- **本地环境**: 批次大小 100，超时 30秒
- **远程环境**: 批次大小 20，超时 2分钟
- **智能重试**: 指数退避算法，远程环境更多重试

### 3. 数据库操作优化
- **批量查询**: 一次查询多条记录
- **批量创建**: 使用 `createMany` 减少网络往返
- **预加载映射**: 提前加载作者/分类映射关系
- **并发处理**: 使用 `Promise.all` 并发执行操作

### 4. 错误处理增强
- **重试机制**: 网络错误自动重试
- **优雅降级**: 批量失败时回退到单条处理
- **详细日志**: 实时进度报告和错误追踪

## 使用方法

### 新增命令
```bash
# 使用优化版本导入（推荐）
pnpm db:import:all:fast

# 原始版本（保留作为备用）
pnpm db:import:all
```

### 性能对比预期

| 环境 | 原始版本 | 优化版本 | 提升倍数 |
|------|----------|----------|----------|
| 本地环境 | 2分钟 | 30秒 | 4x |
| 远程环境 | 2-3小时 | 15-30分钟 | 6-12x |

### 优化效果

#### 网络往返次数减少
- **原始**: 每条记录 5-10 次数据库查询
- **优化**: 每批次 3-5 次数据库查询

#### 事务优化
- **原始**: 每条记录一个事务
- **优化**: 每批次一个大事务

#### 并发处理
- **原始**: 串行处理所有操作
- **优化**: 并发处理翻译、封面、关联关系

## 监控和调试

### 实时进度显示
```
🌐 检测到远程数据库环境
⚙️  配置: 批次=20, 超时=120000ms, 重试=5次
👥 开始导入作者数据: 1500 条
📊 作者导入: 100/1500 (6.7%) | 2.5条/秒 | 剩余560秒
```

### 错误处理
- 自动重试网络错误
- 详细错误日志
- 批量失败时自动降级

## 进一步优化建议

### 1. 数据库连接池优化
```bash
# 在 .env 中添加连接池配置
DATABASE_URL="mysql://user:pass@host:port/db?connection_limit=10&pool_timeout=20"
```

### 2. 分时段导入
```bash
# 可以分别导入不同类型的数据
pnpm db:import:authors    # 仅导入作者
pnpm db:import:categories # 仅导入分类  
pnpm db:import:books      # 仅导入书籍
```

### 3. 数据预处理
- 提前验证数据格式
- 去除重复数据
- 优化 JSON 文件结构

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加超时时间配置

2. **内存不足**
   - 减小批次大小
   - 分批次导入

3. **数据冲突**
   - 检查数据唯一性
   - 使用 `skipDuplicates` 选项

### 回退方案
如果优化版本出现问题，可以随时回退到原始版本：
```bash
pnpm db:import:all  # 使用原始版本
```

## 总结

通过这些优化，远程环境的导入速度应该能提升 6-12 倍，从几小时缩短到 15-30 分钟。主要改进包括：

1. ✅ 自动环境检测和配置优化
2. ✅ 批量处理减少网络往返
3. ✅ 智能重试机制
4. ✅ 并发操作提升效率
5. ✅ 实时进度监控
6. ✅ 优雅的错误处理

建议先在测试环境验证优化效果，确认无误后再在正式环境使用。
