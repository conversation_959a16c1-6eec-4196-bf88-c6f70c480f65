/**
 * 通用工具函数
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import type { ImageFile, BookMetadataFromDB, ProcessingConfig } from './types.js'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 配置常量
export const CONFIG: ProcessingConfig = {
  BATCH_SIZE: 1,  // 修改为1，确保每张图片独立处理
  MAX_CONCURRENT_POLLS: 3,
  MAX_CONCURRENT_DOWNLOADS: 3,
  POLL_INTERVAL_NEW: 30000,        // 新任务30秒
  POLL_INTERVAL_GENERATING: 60000,  // 生成中60秒
  POLL_INTERVAL_NEAR_COMPLETE: 30000, // 接近完成30秒
  MAX_RETRY_ATTEMPTS: 3,
  TASK_TIMEOUT_MINUTES: 60
}

// API配置
export const API_CONFIG = {
  BASE_URL: 'https://kieai.erweima.ai/api/v1/gpt4o-image',
  CALLBACK_URL: 'https://open.feishu.cn/open-apis/bot/v2/hook/cc2825e1-d5a9-48d9-a692-750e0e33cc76'
}

// 目录配置
export const DIRS = {
  INPUT: path.join(__dirname, '../input'),
  OUTPUT: path.join(__dirname, '../output'),
  LOGS: path.join(__dirname, '../logs'),
  DATA: path.join(__dirname, '../data')
}

// TLS配置
export const TLS_CONFIG = {
  rejectUnauthorized: false,
  checkServerIdentity: () => undefined,
  secureProtocol: 'TLSv1_2_method'
}

/**
 * 简单的日志工具
 */
export class Logger {
  private logFile: string

  constructor(prefix: string = 'processing') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(DIRS.LOGS, `${prefix}-${timestamp}.log`)

    // 确保日志目录存在
    if (!fs.existsSync(DIRS.LOGS)) {
      fs.mkdirSync(DIRS.LOGS, { recursive: true })
    }
  }

  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${level}] ${timestamp} - ${message}`

    // 控制台输出
    console.log(logMessage)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }

    // 文件输出
    let fileMessage = logMessage
    if (data) {
      fileMessage += '\n' + JSON.stringify(data, null, 2)
    }
    fileMessage += '\n'

    fs.appendFileSync(this.logFile, fileMessage, 'utf8')
  }

  info(message: string, data?: any): void {
    this.log('INFO', message, data)
  }

  warn(message: string, data?: any): void {
    this.log('WARN', message, data)
  }

  error(message: string, data?: any): void {
    this.log('ERROR', message, data)
  }

  success(message: string, data?: any): void {
    this.log('SUCCESS', message, data)
  }
}

/**
 * 信号量实现
 */
export class Semaphore {
  private permits: number
  private waitQueue: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.permits > 0) {
        this.permits--
        resolve()
      } else {
        this.waitQueue.push(resolve)
      }
    })
  }

  release(): void {
    this.permits++
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!
      this.permits--
      resolve()
    }
  }
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试装饰器
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = CONFIG.MAX_RETRY_ATTEMPTS,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      if (attempt === maxAttempts) {
        throw lastError
      }

      const delayMs = baseDelay * Math.pow(2, attempt - 1)
      console.warn(`操作失败，${delayMs}ms后重试 (${attempt}/${maxAttempts}): ${lastError.message}`)
      await delay(delayMs)
    }
  }

  throw lastError!
}

/**
 * 加载书籍元数据
 */
export function loadBookMetadata(): Record<string, BookMetadataFromDB> {
  try {
    const metadataPath = path.join(DIRS.DATA, 'book-metadata.json')
    if (fs.existsSync(metadataPath)) {
      const data = fs.readFileSync(metadataPath, 'utf8')
      const metadata = JSON.parse(data)
      console.log(`成功加载 ${Object.keys(metadata).length} 条书籍元数据`)
      return metadata
    }
  } catch (error) {
    console.warn('加载书籍元数据失败:', error)
  }

  return {}
}

/**
 * 扫描输入目录，获取待处理的图片文件
 * @param skipExisting 是否跳过已存在的输出文件
 */
export function scanInputDirectory(skipExisting: boolean = false): ImageFile[] {
  const bookMetadata = loadBookMetadata()

  if (Object.keys(bookMetadata).length === 0) {
    console.warn('没有找到书籍元数据')
    return []
  }

  console.log(`从数据库元数据中发现 ${Object.keys(bookMetadata).length} 条记录`)

  const allImages = Object.values(bookMetadata)
    .filter(metadata => {
      return metadata.imageUrl && metadata.title && metadata.authors
    })
    .map(metadata => {
      return {
        filePath: '',
        fileName: metadata.imageFilename,
        title: metadata.title,
        author: metadata.authors.length > 0 ? metadata.authors.join(', ') : 'Unknown Author',
        imageUrl: metadata.imageUrl
      }
    })

  if (!skipExisting) {
    return allImages
  }

  // 检查输出目录，跳过已存在的文件
  const filteredImages = allImages.filter(imageFile => {
    const outputPath = path.join(DIRS.OUTPUT, imageFile.fileName)
    const exists = fs.existsSync(outputPath)

    if (exists) {
      console.log(`跳过已存在的文件: ${imageFile.fileName} (${imageFile.title})`)
      return false
    }

    return true
  })

  const skippedCount = allImages.length - filteredImages.length
  if (skippedCount > 0) {
    console.log(`跳过了 ${skippedCount} 个已存在的文件，剩余 ${filteredImages.length} 个待处理`)
  }

  return filteredImages
}

/**
 * 创建批次
 */
export function createBatches<T>(items: T[], batchSize: number): T[][] {
  const batches: T[][] = []
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize))
  }
  return batches
}

/**
 * 生成批次ID
 */
export function generateBatchId(): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `batch-${timestamp}-${random}`
}

/**
 * 生成提示词
 */
export function generatePrompt(title: string, author: string): string {
  const template = `Generate a book cover image with the following specifications:

Book Title: {title}
Author: {author}
Cover Style: Modern、Symbolic/Conceptual Graphics、Limited and High-Contrast Color Palettes、Clear Visual Hierarchy、Flat Design Style

**Color Palette Instruction:**
* **Crucially, derive the color palette for this new cover *directly and exclusively* from the colors present in the provided reference image.**
* Strive for a high-contrast application of these extracted colors.`

  return template.replace('{title}', title).replace('{author}', author)
}

/**
 * 格式化时间
 */
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${Math.round(size * 100) / 100}${units[unitIndex]}`
}
