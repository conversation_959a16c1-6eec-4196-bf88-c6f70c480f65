/**
 * 分离式图片处理的类型定义
 */

export interface ImageFile {
  filePath: string
  fileName: string
  title: string
  author: string
  imageUrl: string
}

export interface TaskRecord {
  taskId: string
  batchId: string
  imageFiles: ImageFile[]
  status: 'PENDING' | 'GENERATING' | 'SUCCESS' | 'FAILED' | 'DOWNLOADED'
  createdAt: number
  lastChecked: number
  completedAt?: number
  retryCount: number
  errorMessage?: string
  resultUrls?: string[]
  downloadPaths?: string[]
  estimatedCompletionTime?: number
}

export interface TaskStats {
  total: number
  pending: number
  generating: number
  success: number
  failed: number
  downloaded: number
}

export interface TaskQueue {
  tasks: Record<string, TaskRecord>
  stats: TaskStats
  lastUpdated: number
  version: string
}

export interface BatchInfo {
  batchId: string
  taskId: string
  imageFiles: ImageFile[]
  createdAt: number
  estimatedCompletionTime: number
}

export interface ProcessingReport {
  timestamp: number
  phase: 'CREATE' | 'POLL' | 'DOWNLOAD' | 'COMPLETE'
  totalTasks: number
  stats: TaskStats
  duration: number
  errors: Array<{
    taskId: string
    error: string
    timestamp: number
  }>
}

export interface BookMetadataFromDB {
  imageFilename: string
  title: string
  authors: string[]
  imageUrl: string
}

// API响应类型
export interface APITaskResult {
  taskId: string
  paramJson: string
  completeTime: number | null
  response: {
    resultUrls?: string[]
  } | null
  successFlag: number
  status: 'GENERATING' | 'SUCCESS' | 'CREATE_TASK_FAILED' | 'GENERATE_FAILED'
  errorCode: number | null
  errorMessage: string | null
  createTime: number
  progress: string
}

// 配置类型
export interface ProcessingConfig {
  BATCH_SIZE: number
  MAX_CONCURRENT_POLLS: number
  MAX_CONCURRENT_DOWNLOADS: number
  POLL_INTERVAL_NEW: number
  POLL_INTERVAL_GENERATING: number
  POLL_INTERVAL_NEAR_COMPLETE: number
  MAX_RETRY_ATTEMPTS: number
  TASK_TIMEOUT_MINUTES: number
}
