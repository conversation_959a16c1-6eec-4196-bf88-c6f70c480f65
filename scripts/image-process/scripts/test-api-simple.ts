/**
 * 简单的API测试脚本
 */

// import fs from 'node:fs'
// import path from 'node:path'

// 强制禁用SSL验证
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

const API_KEY = process.env.GPT4O_API_KEY || 'b0575c44ad4ea11c2c8c477140627caa'
const API_BASE_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image'

// TLS配置
const TLS_CONFIG = {
  rejectUnauthorized: false,
  checkServerIdentity: () => undefined,
  secureProtocol: 'TLSv1_2_method'
}

async function testBatchGenerateAPI() {
  console.log('='.repeat(50))
  console.log('测试批量图片生成API (3张图片)')
  console.log('='.repeat(50))

  try {
    // 使用多个真实的图片URL进行测试
    const testImageUrls = [
      'https://picsum.photos/400/600?random=1',
      'https://picsum.photos/400/600?random=2',
      'https://picsum.photos/400/600?random=3'
    ]

    const requestBody = JSON.stringify({
      filesUrl: testImageUrls,
      prompt: "Generate beautiful book cover designs for: Book 1 by Author 1; Book 2 by Author 2; Book 3 by Author 3",
      size: "2:3",
      callBackUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/cc2825e1-d5a9-48d9-a692-750e0e33cc76",
      isEnhance: false,
      uploadCn: false,
      nVariants: 4
    })

    console.log('请求体:', requestBody)

    const fetchOptions: any = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: requestBody,
      tls: TLS_CONFIG
    }

    console.log('发送请求到:', `${API_BASE_URL}/generate`)

    const response = await fetch(`${API_BASE_URL}/generate`, fetchOptions)

    console.log('响应状态:', response.status)
    console.log('响应头:', Object.fromEntries(response.headers.entries()))

    const responseText = await response.text()
    console.log('响应内容:', responseText)

    if (response.ok) {
      try {
        const data = JSON.parse(responseText)
        console.log('解析后的响应:', JSON.stringify(data, null, 2))

        if (data.code === 200 && data.data && data.data.taskId) {
          console.log('✅ 批量任务创建成功! TaskId:', data.data.taskId)
          console.log('📊 预计生成时间: 约3-5分钟 (3张图片)')
          return data.data.taskId
        } else {
          console.log('❌ 响应格式不正确')
          return null
        }
      } catch (parseError) {
        console.log('❌ JSON解析失败:', parseError)
        return null
      }
    } else {
      console.log('❌ API请求失败')
      return null
    }
  } catch (error) {
    console.log('❌ 请求异常:', error)
    return null
  }
}

async function testRecordInfoAPI(taskId: string) {
  console.log('\n' + '='.repeat(50))
  console.log('测试任务状态查询API')
  console.log('='.repeat(50))

  let attempts = 0
  const maxAttempts = 12 // 最多等待1分钟

  while (attempts < maxAttempts) {
    try {
      const fetchOptions: any = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Accept': 'application/json'
        },
        tls: TLS_CONFIG
      }

      const url = `${API_BASE_URL}/record-info?taskId=${taskId}`
      console.log(`发送请求到 (第${attempts + 1}次):`, url)

      const response = await fetch(url, fetchOptions)

      console.log('响应状态:', response.status)

      const responseText = await response.text()
      console.log('响应内容:', responseText)

      if (response.ok) {
        try {
          const data = JSON.parse(responseText)
          console.log('解析后的响应:', JSON.stringify(data, null, 2))

          if (data.code === 200 && data.data) {
            console.log('✅ 任务状态查询成功!')
            console.log('任务状态:', data.data.status)
            console.log('进度:', data.data.progress)

            if (data.data.status === 'SUCCESS') {
              console.log('🎉 批量任务完成!')
              if (data.data.response && data.data.response.resultUrls) {
                console.log(`生成了 ${data.data.response.resultUrls.length} 张图片:`)
                data.data.response.resultUrls.forEach((url: string, index: number) => {
                  console.log(`  图片 ${index + 1}: ${url}`)
                })
              }
              return data.data
            } else if (data.data.status === 'GENERATE_FAILED' || data.data.status === 'CREATE_TASK_FAILED') {
              console.log('❌ 任务失败!')
              console.log('错误码:', data.data.errorCode)
              console.log('错误信息:', data.data.errorMessage)
              return data.data
            } else if (data.data.status === 'GENERATING') {
              console.log('⏳ 任务生成中，5秒后重试...')
              await new Promise(resolve => setTimeout(resolve, 5000))
              attempts++
              continue
            }
          } else {
            console.log('❌ 响应格式不正确')
            return null
          }
        } catch (parseError) {
          console.log('❌ JSON解析失败:', parseError)
          return null
        }
      } else {
        console.log('❌ API请求失败')
        return null
      }
    } catch (error) {
      console.log('❌ 请求异常:', error)
      return null
    }
  }

  console.log('⏰ 轮询超时')
  return null
}

async function main() {
  console.log('开始API测试...')
  console.log('API Key:', API_KEY ? API_KEY.substring(0, 10) + '...' : '未设置')

  // 测试批量生成API
  const taskId = await testBatchGenerateAPI()

  if (taskId) {
    // 等待一下再查询状态
    console.log('\n等待3秒后查询任务状态...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 测试状态查询API
    await testRecordInfoAPI(taskId)
  }

  console.log('\n测试完成!')
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('测试失败:', error)
    process.exit(1)
  })
}

export { main as testAPI }
