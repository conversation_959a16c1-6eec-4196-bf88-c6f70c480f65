#!/usr/bin/env bun

/**
 * 下载生成的图片脚本
 * 下载所有成功生成的图片
 */

import fs from 'node:fs'
import path from 'node:path'
import { Logger, CONFIG, DIRS, withRetry, Semaphore, formatFileSize } from './utils.js'
import { getTasksByStatus, updateTaskRecord, saveProcessingReport, getTaskQueueStats } from './task-queue.js'
import type { TaskRecord, ProcessingReport, ImageFile } from './types.js'

// 强制禁用SSL验证
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

const logger = new Logger('download-images')

/**
 * 下载单个图片
 */
async function downloadSingleImage(imageUrl: string, originalImageFile: ImageFile, taskId: string): Promise<string> {
  // 生成输出文件名 - 保持原始文件格式
  const originalExtension = path.extname(originalImageFile.fileName)  // 保持原始扩展名
  const baseName = path.basename(originalImageFile.fileName, originalExtension)
  const outputFileName = `${baseName}${originalExtension}`  // 直接使用原始文件名
  const outputPath = path.join(DIRS.OUTPUT, outputFileName)

  logger.info(`[下载] 开始下载: ${imageUrl} -> ${outputFileName} (${originalImageFile.title})`)

  try {
    // 确保输出目录存在
    if (!fs.existsSync(DIRS.OUTPUT)) {
      fs.mkdirSync(DIRS.OUTPUT, { recursive: true })
    }

    // 首先尝试使用fetch下载
    try {
      const response = await fetch(imageUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      if (!response.ok) {
        throw new Error(`下载失败: HTTP ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      fs.writeFileSync(outputPath, buffer)

      const fileSize = fs.statSync(outputPath).size
      logger.success(`[下载] 下载成功: ${outputFileName} (${formatFileSize(fileSize)})`)

      return outputPath
    } catch (fetchError) {
      logger.warn(`[下载] fetch下载失败，尝试curl: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`)

      // 使用curl作为备用方案
      const curlResult = await curlDownload(imageUrl, outputPath)
      if (curlResult) {
        const fileSize = fs.statSync(outputPath).size
        logger.success(`[下载] curl下载成功: ${outputFileName} (${formatFileSize(fileSize)})`)
        return outputPath
      } else {
        throw new Error('curl下载也失败')
      }
    }
  } catch (error) {
    logger.error(`[下载] 下载失败: ${imageUrl} - ${error instanceof Error ? error.message : String(error)}`)
    throw error
  }
}

/**
 * curl下载实现
 */
async function curlDownload(url: string, outputPath: string): Promise<boolean> {
  const { spawn } = await import('node:child_process')

  return new Promise((resolve) => {
    const curlArgs = [
      '-k', '--insecure', '-s', '-S', '-L',
      '--tlsv1.2', '--ssl-no-revoke',
      '--connect-timeout', '30',
      '--max-time', '300',
      '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      '-o', outputPath,
      url
    ]

    const curl = spawn('curl', curlArgs)
    let stderr = ''

    curl.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    curl.on('close', (code) => {
      if (code === 0 && fs.existsSync(outputPath)) {
        resolve(true)
      } else {
        logger.error(`curl下载失败 (code: ${code}): ${stderr}`)
        resolve(false)
      }
    })

    curl.on('error', (error) => {
      logger.error(`curl进程错误: ${error.message}`)
      resolve(false)
    })
  })
}

/**
 * 下载任务的所有图片
 */
async function downloadTaskImages(task: TaskRecord): Promise<void> {
  if (!task.resultUrls || task.resultUrls.length === 0) {
    throw new Error('任务没有可下载的图片URL')
  }

  logger.info(`[下载任务] 开始下载任务 ${task.taskId} 的 ${task.resultUrls.length} 张图片`)

  const downloadPaths: string[] = []
  const errors: string[] = []

  // 现在每个任务只有一张图片，所以只下载第一个URL
  const imageUrl = task.resultUrls[0]  // 只取第一个结果
  const originalImageFile = task.imageFiles[0]  // 原始图片文件信息

  try {
    const downloadPath = await withRetry(
      () => downloadSingleImage(imageUrl, originalImageFile, task.taskId),
      2, // 最多重试2次
      3000 // 3秒延迟
    )

    downloadPaths.push(downloadPath)
    logger.success(`[下载任务] 任务 ${task.taskId} 图片下载完成 (${originalImageFile.title})`)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`[下载任务] 下载失败 (${originalImageFile.title}): ${errorMessage}`)
    errors.push(`下载失败: ${errorMessage}`)
  }

  // 更新任务状态
  if (downloadPaths.length > 0) {
    const updates: Partial<TaskRecord> = {
      downloadPaths,
      status: 'DOWNLOADED',  // 现在只有一张图片，下载成功就是完成
      completedAt: Date.now()
    }

    if (errors.length > 0) {
      updates.errorMessage = `部分下载失败: ${errors.join('; ')}`
    }

    updateTaskRecord(task.taskId, updates)
  } else {
    // 所有下载都失败了
    updateTaskRecord(task.taskId, {
      status: 'FAILED',
      errorMessage: `所有图片下载失败: ${errors.join('; ')}`
    })
    throw new Error(`任务 ${task.taskId} 所有图片下载失败`)
  }
}

/**
 * 主函数：下载所有成功的图片
 */
async function downloadAllImages(): Promise<void> {
  const startTime = Date.now()
  logger.info('开始下载生成的图片')

  try {
    // 1. 获取成功的任务
    const successTasks = getTasksByStatus('SUCCESS')
    logger.info(`发现 ${successTasks.length} 个成功任务待下载`)

    if (successTasks.length === 0) {
      logger.info('没有可下载的图片')
      logger.info('提示：请先运行 "pnpm image:poll" 检查任务状态')
      return
    }

    // 2. 显示当前统计
    const stats = getTaskQueueStats()
    logger.info(`当前状态: 总计 ${stats.total}, 成功 ${stats.success}, 已下载 ${stats.downloaded}`)

    // 3. 计算总图片数（现在每个任务只有1张图片）
    const totalImages = successTasks.length
    logger.info(`总共需要下载 ${totalImages} 张图片`)

    // 4. 并发下载
    const semaphore = new Semaphore(CONFIG.MAX_CONCURRENT_DOWNLOADS)
    let successCount = 0
    let failedCount = 0
    const errors: Array<{ taskId: string; error: string }> = []

    const promises = successTasks.map(async (task) => {
      await semaphore.acquire()
      try {
        await downloadTaskImages(task)
        successCount++
        logger.success(`任务 ${task.taskId} 下载完成`)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.error(`任务 ${task.taskId} 下载失败: ${errorMessage}`)
        errors.push({ taskId: task.taskId, error: errorMessage })
        failedCount++
      } finally {
        semaphore.release()
      }
    })

    await Promise.allSettled(promises)

    // 5. 生成报告
    const duration = Date.now() - startTime
    const finalStats = getTaskQueueStats()

    const report: ProcessingReport = {
      timestamp: Date.now(),
      phase: 'DOWNLOAD',
      totalTasks: successTasks.length,
      stats: finalStats,
      duration,
      errors: errors.map(e => ({
        taskId: e.taskId,
        error: e.error,
        timestamp: Date.now()
      }))
    }

    const reportPath = saveProcessingReport(report)

    // 6. 输出摘要
    logger.info('='.repeat(60))
    logger.info('下载完成摘要:')
    logger.info(`处理任务数: ${successTasks.length}`)
    logger.info(`下载成功: ${successCount}`)
    logger.info(`下载失败: ${failedCount}`)
    logger.info(`总图片数: ${totalImages}`)
    logger.info(`最终状态: 已下载 ${finalStats.downloaded}`)
    logger.info(`处理时间: ${Math.round(duration / 1000)}秒`)
    logger.info(`输出目录: ${DIRS.OUTPUT}`)
    logger.info(`报告文件: ${reportPath}`)
    logger.info('='.repeat(60))

    if (successCount > 0) {
      logger.success('图片下载完成！')
      logger.info(`请查看输出目录: ${DIRS.OUTPUT}`)
    }

    if (failedCount > 0) {
      logger.warn(`有 ${failedCount} 个任务下载失败，请检查错误日志`)
    }

  } catch (error) {
    logger.error('下载图片失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  downloadAllImages().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
