#!/usr/bin/env bun

/**
 * 轮询任务状态脚本
 * 智能轮询所有待处理任务的状态
 */

import { Logger, CONFIG, API_CONFIG, TLS_CONFIG, delay, Semaphore } from './utils.js'
import { getPendingTasks, updateTaskRecord, saveProcessingReport, getTaskQueueStats } from './task-queue.js'
import type { TaskRecord, ProcessingReport, APITaskResult } from './types.js'

// 强制禁用SSL验证
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

const logger = new Logger('poll-tasks')

/**
 * 轮询单个任务状态
 */
async function pollTaskStatus(taskId: string): Promise<APITaskResult> {
  let attempts = 0
  const maxAttempts = 3

  while (attempts < maxAttempts) {
    try {
      // 首先尝试使用fetch
      try {
        const fetchOptions: any = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
            'Accept': 'application/json'
          },
          tls: TLS_CONFIG
        }

        const fetchResponse = await fetch(`${API_CONFIG.BASE_URL}/record-info?taskId=${taskId}`, fetchOptions)

        if (!fetchResponse.ok) {
          throw new Error(`轮询请求失败: ${fetchResponse.status}`)
        }

        const responseData = await fetchResponse.json()
        logger.info(`[轮询] 任务 ${taskId} API响应: ${JSON.stringify(responseData)}`)

        if (responseData.code === 200 && responseData.data) {
          const result = responseData.data as APITaskResult
          logger.info(`[轮询] 任务 ${taskId} 状态: ${result.status}, 进度: ${result.progress}`)
          return result
        } else {
          throw new Error(`API响应错误: ${JSON.stringify(responseData)}`)
        }
      } catch (fetchError) {
        // 使用curl作为备用方案
        const curlOptions = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
            'Accept': 'application/json'
          }
        }

        const curlResponseData = await curlRequest(`${API_CONFIG.BASE_URL}/record-info?taskId=${taskId}`, curlOptions)
        logger.info(`[轮询-curl] 任务 ${taskId} API响应: ${JSON.stringify(curlResponseData)}`)

        if (curlResponseData.code === 200 && curlResponseData.data) {
          const result = curlResponseData.data as APITaskResult
          logger.info(`[轮询-curl] 任务 ${taskId} 状态: ${result.status}, 进度: ${result.progress}`)
          return result
        } else {
          throw new Error(`curl API响应错误: ${JSON.stringify(curlResponseData)}`)
        }
      }
    } catch (error) {
      attempts++
      logger.warn(`轮询任务状态失败 (${attempts}/${maxAttempts}): ${taskId} - ${error instanceof Error ? error.message : String(error)}`)

      if (attempts < maxAttempts) {
        await delay(5000)
      }
    }
  }

  throw new Error(`轮询任务状态失败，已重试 ${maxAttempts} 次`)
}

/**
 * curl请求实现
 */
async function curlRequest(url: string, options: any): Promise<any> {
  const { spawn } = await import('node:child_process')

  return new Promise((resolve, reject) => {
    const curlArgs = [
      '-k', '--insecure', '-s', '-S', '-L',
      '--tlsv1.2', '--ssl-no-revoke',
      '--connect-timeout', '30',
      '--max-time', '60',
      '-X', options.method || 'GET'
    ]

    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlArgs.push('-H', `${key}: ${value}`)
      })
    }

    curlArgs.push(url)

    const curl = spawn('curl', curlArgs)
    let stdout = ''
    let stderr = ''

    curl.stdout.on('data', (data) => {
      stdout += data.toString()
    })

    curl.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    curl.on('close', (code) => {
      if (code === 0) {
        try {
          const response = JSON.parse(stdout)
          resolve(response)
        } catch (error) {
          resolve(stdout)
        }
      } else {
        reject(new Error(`curl failed with code ${code}: ${stderr}`))
      }
    })

    curl.on('error', (error) => {
      reject(error)
    })
  })
}

/**
 * 计算轮询间隔
 */
function calculatePollInterval(task: TaskRecord): number {
  const now = Date.now()
  const taskAge = now - task.createdAt

  // 新任务（5分钟内）
  if (taskAge < 5 * 60 * 1000) {
    return CONFIG.POLL_INTERVAL_NEW
  }

  // 接近预估完成时间
  if (task.estimatedCompletionTime && now > task.estimatedCompletionTime - 2 * 60 * 1000) {
    return CONFIG.POLL_INTERVAL_NEAR_COMPLETE
  }

  // 默认间隔
  return CONFIG.POLL_INTERVAL_GENERATING
}

/**
 * 轮询单个任务
 */
async function pollSingleTask(task: TaskRecord): Promise<void> {
  try {
    // 检查是否需要轮询
    const now = Date.now()
    const timeSinceLastCheck = now - task.lastChecked
    const minInterval = calculatePollInterval(task)

    if (timeSinceLastCheck < minInterval) {
      logger.info(`[轮询] 任务 ${task.taskId} 跳过轮询，距离上次检查仅 ${Math.round(timeSinceLastCheck / 1000)}秒`)
      return
    }

    // 检查任务超时
    const taskAge = now - task.createdAt
    const timeoutMs = CONFIG.TASK_TIMEOUT_MINUTES * 60 * 1000
    if (taskAge > timeoutMs) {
      logger.warn(`[轮询] 任务 ${task.taskId} 已超时 (${Math.round(taskAge / 60000)}分钟)`)
      updateTaskRecord(task.taskId, {
        status: 'FAILED',
        errorMessage: `任务超时 (${CONFIG.TASK_TIMEOUT_MINUTES}分钟)`,
        lastChecked: now
      })
      return
    }

    // 执行轮询
    const result = await pollTaskStatus(task.taskId)

    // 更新任务状态
    const updates: Partial<TaskRecord> = {
      lastChecked: now
    }

    if (result.status === 'SUCCESS') {
      logger.success(`[轮询] 任务 ${task.taskId} 生成成功! 进度: ${result.progress}`)
      if (result.response && result.response.resultUrls) {
        logger.info(`[轮询] 任务 ${task.taskId} 生成的图片URL: ${result.response.resultUrls.join(', ')}`)
        updates.status = 'SUCCESS'
        updates.resultUrls = result.response.resultUrls
        updates.completedAt = now
      }
    } else if (result.status === 'GENERATING') {
      logger.info(`[轮询] 任务 ${task.taskId} 生成中... 进度: ${result.progress}`)
      updates.status = 'GENERATING'
    } else if (result.status === 'GENERATE_FAILED' || result.status === 'CREATE_TASK_FAILED') {
      logger.error(`[轮询] 任务 ${task.taskId} 生成失败! 错误码: ${result.errorCode}, 错误信息: ${result.errorMessage}`)
      updates.status = 'FAILED'
      updates.errorMessage = `${result.status}: ${result.errorMessage || '未知错误'}`
      updates.retryCount = task.retryCount + 1
    }

    updateTaskRecord(task.taskId, updates)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`[轮询] 任务 ${task.taskId} 轮询失败: ${errorMessage}`)

    updateTaskRecord(task.taskId, {
      lastChecked: Date.now(),
      retryCount: task.retryCount + 1,
      errorMessage: errorMessage
    })
  }
}

/**
 * 主函数：轮询所有任务
 */
async function pollAllTasks(): Promise<void> {
  const startTime = Date.now()
  logger.info('开始轮询任务状态')

  try {
    // 1. 检查API密钥
    if (!process.env.GPT4O_API_KEY) {
      throw new Error('GPT4O_API_KEY 环境变量未设置')
    }

    // 2. 获取待轮询的任务
    const pendingTasks = getPendingTasks()
    logger.info(`发现 ${pendingTasks.length} 个待轮询任务`)

    if (pendingTasks.length === 0) {
      logger.info('没有需要轮询的任务')
      return
    }

    // 3. 显示当前统计
    const stats = getTaskQueueStats()
    logger.info(`当前状态: 总计 ${stats.total}, 待处理 ${stats.pending}, 生成中 ${stats.generating}, 成功 ${stats.success}, 失败 ${stats.failed}, 已下载 ${stats.downloaded}`)

    // 4. 并发轮询任务
    const semaphore = new Semaphore(CONFIG.MAX_CONCURRENT_POLLS)

    const promises = pendingTasks.map(async (task) => {
      await semaphore.acquire()
      try {
        await pollSingleTask(task)
      } finally {
        semaphore.release()
      }
    })

    await Promise.allSettled(promises)

    // 5. 生成报告
    const duration = Date.now() - startTime
    const finalStats = getTaskQueueStats()

    const report: ProcessingReport = {
      timestamp: Date.now(),
      phase: 'POLL',
      totalTasks: pendingTasks.length,
      stats: finalStats,
      duration,
      errors: []
    }

    const reportPath = saveProcessingReport(report)

    // 6. 输出摘要
    logger.info('='.repeat(60))
    logger.info('轮询完成摘要:')
    logger.info(`轮询任务数: ${pendingTasks.length}`)
    logger.info(`当前状态: 总计 ${finalStats.total}, 待处理 ${finalStats.pending}, 生成中 ${finalStats.generating}, 成功 ${finalStats.success}, 失败 ${finalStats.failed}`)
    logger.info(`处理时间: ${Math.round(duration / 1000)}秒`)
    logger.info(`报告文件: ${reportPath}`)
    logger.info('='.repeat(60))

    if (finalStats.success > 0) {
      logger.success(`发现 ${finalStats.success} 个成功任务！`)
      logger.info('下一步：运行 "pnpm image:download" 下载生成的图片')
    }

    if (finalStats.generating > 0) {
      logger.info(`还有 ${finalStats.generating} 个任务正在生成中，请稍后再次运行轮询`)
    }

  } catch (error) {
    logger.error('轮询任务失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  pollAllTasks().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
