/**
 * 批量图片处理脚本
 * 使用 GPT-4o Image API 将现有图片转换为书籍封面图片
 * 参考 import-all-book-data.ts 的结构和风格
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
// const PROJECT_ROOT = path.resolve(__dirname, '../../..')

// 强制禁用SSL验证 - 在所有导入之后立即设置
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

// 尝试设置额外的环境变量来禁用SSL验证
process.env.HTTPS_PROXY = ''
process.env.HTTP_PROXY = ''

// 配置常量
const CONFIG = {
  // 目录配置
  INPUT_DIR: path.join(__dirname, '../input'),
  OUTPUT_DIR: path.join(__dirname, '../output'),
  LOG_DIR: path.join(__dirname, '../logs'),
  PROGRESS_FILE: path.join(__dirname, '../data/progress.json'),

  // API配置 - 使用正确的官方端点
  API_BASE_URL: 'https://kieai.erweima.ai/api/v1/gpt4o-image',

  // 批量处理配置
  BATCH_SIZE: 5, // 每批最多处理5张图片（API限制）
  MAX_CONCURRENT: 2, // 最大并发批次数（降低以避免API限制）
  RETRY_ATTEMPTS: 3,

  // 轮询配置
  POLL_INTERVAL: 10000, // 10秒轮询间隔（给生成更多时间）
  POLL_MAX_ATTEMPTS: 360, // 最大轮询次数（60分钟，因为一张图片约1分钟）

  // 支持的图片格式
  SUPPORTED_FORMATS: ['.jpg', '.jpeg', '.png', '.webp'],

  // 提示词模板
  PROMPT_TEMPLATE: `Generate a book cover image with the following specifications:

Book Title: {title}
Author: {author}
Cover Style: Modern、Symbolic/Conceptual Graphics、Limited and High-Contrast Color Palettes、Clear Visual Hierarchy、Flat Design Style

**Color Palette Instruction:**
* **Crucially, derive the color palette for this new cover *directly and exclusively* from the colors present in the provided reference image.**
* Strive for a high-contrast application of these extracted colors.`
}

// 确保必要的目录存在
function ensureDirectories(): void {
  const dirs = [CONFIG.INPUT_DIR, CONFIG.OUTPUT_DIR, CONFIG.LOG_DIR, path.dirname(CONFIG.PROGRESS_FILE)]

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
}

// 初始化目录
ensureDirectories()

// 日志文件
const logFile = path.join(CONFIG.LOG_DIR, `processing-${new Date().toISOString().replace(/[:.]/g, '-')}.log`)
const errorFile = path.join(CONFIG.LOG_DIR, `error-${new Date().toISOString().replace(/[:.]/g, '-')}.log`)
const logStream = fs.createWriteStream(logFile, { flags: 'a' })
const errorStream = fs.createWriteStream(errorFile, { flags: 'a' })

// 数据类型定义
interface ProcessingProgress {
  processedFiles: string[]
  failedFiles: Array<{ file: string; error: string }>
  totalFiles: number
  startTime: string
}

interface ImageFile {
  filePath: string
  fileName: string
  title: string
  author: string
  imageUrl: string
}

interface BookMetadataFromDB {
  imageFilename: string
  bookId: number
  title: string
  authors: string[]
  imageUrl: string
}

interface TaskResult {
  taskId: string
  paramJson: string
  completeTime: number | null
  response: {
    resultUrls?: string[]
  } | null
  successFlag: number
  status: 'GENERATING' | 'SUCCESS' | 'CREATE_TASK_FAILED' | 'GENERATE_FAILED'
  errorCode: number | null
  errorMessage: string | null
  createTime: number
  progress: string
}

interface ProcessingResult {
  success: number
  failed: number
  skipped: number
  errors: Array<{ file: string; error: string }>
}

// 日志工具（参考 import-all-book-data.ts）
const logger = {
  info: (message: string) => {
    const logMessage = `[INFO] ${new Date().toISOString()} - ${message}\n`
    console.log(logMessage.trim())
    logStream.write(logMessage)
  },

  warn: (message: string) => {
    const logMessage = `[WARN] ${new Date().toISOString()} - ${message}\n`
    console.warn(logMessage.trim())
    logStream.write(logMessage)
  },

  error: (message: string, error?: any) => {
    const errorDetails = error ? `\n${error.stack || error}` : ''
    const logMessage = `[ERROR] ${new Date().toISOString()} - ${message}${errorDetails}\n`
    console.error(logMessage.trim())
    logStream.write(logMessage)
    errorStream.write(logMessage)
  },

  success: (message: string) => {
    const logMessage = `[SUCCESS] ${new Date().toISOString()} - ${message}\n`
    console.log(logMessage.trim())
    logStream.write(logMessage)
  },

  close: () => {
    logStream.end()
    errorStream.end()
  }
}

// 工具函数
function loadProgress(): ProcessingProgress {
  try {
    if (fs.existsSync(CONFIG.PROGRESS_FILE)) {
      const data = fs.readFileSync(CONFIG.PROGRESS_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    logger.warn(`无法加载进度文件: ${error instanceof Error ? error.message : String(error)}`)
  }

  return {
    processedFiles: [],
    failedFiles: [],
    totalFiles: 0,
    startTime: new Date().toISOString()
  }
}

function saveProgress(progress: ProcessingProgress): void {
  try {
    fs.writeFileSync(CONFIG.PROGRESS_FILE, JSON.stringify(progress, null, 2), 'utf8')
  } catch (error) {
    logger.error(`保存进度文件失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 加载数据库导出的元数据
function loadBookMetadata(): Record<string, BookMetadataFromDB> {
  try {
    const metadataPath = path.join(__dirname, '../data/book-metadata.json')
    if (fs.existsSync(metadataPath)) {
      const data = fs.readFileSync(metadataPath, 'utf8')
      const metadata = JSON.parse(data)
      logger.info(`成功加载 ${Object.keys(metadata).length} 条书籍元数据`)
      return metadata
    } else {
      logger.warn(`书籍元数据文件不存在: ${metadataPath}`)
      logger.info('请先运行: bun run image:export')
    }
  } catch (error) {
    logger.warn(`无法加载书籍元数据文件: ${error instanceof Error ? error.message : String(error)}`)
  }
  return {}
}

// 验证环境配置
function validateEnvironment(): void {
  if (!process.env.GPT4O_API_KEY) {
    throw new Error('GPT4O_API_KEY environment variable is not set')
  }
}

// 从数据库元数据提取书籍信息
/*
function extractMetadataFromDatabase(filename: string, bookMetadata: Record<string, BookMetadataFromDB>): { title: string; author: string } {
  // 尝试直接匹配文件名
  if (bookMetadata[filename]) {
    const metadata = bookMetadata[filename]
    return {
      title: metadata.title,
      author: metadata.authors.length > 0 ? metadata.authors.join(', ') : 'Unknown Author'
    }
  }

  // 尝试匹配不带扩展名的文件名
  const nameWithoutExt = path.parse(filename).name
  const matchingKey = Object.keys(bookMetadata).find(key => {
    const keyWithoutExt = path.parse(key).name
    return keyWithoutExt === nameWithoutExt
  })

  if (matchingKey) {
    const metadata = bookMetadata[matchingKey]
    logger.info(`通过文件名匹配找到: ${filename} -> "${metadata.title}"`)
    return {
      title: metadata.title,
      author: metadata.authors.length > 0 ? metadata.authors.join(', ') : 'Unknown Author'
    }
  }

  // 降级到原有的文件名解析方法
  logger.warn(`未找到文件 ${filename} 的数据库元数据，使用文件名解析`)
  return extractMetadataFromFilename(filename)
}
*/

// 从文件名提取元数据（备用方法）
/*
function extractMetadataFromFilename(filename: string): { title: string; author: string } {
  const nameWithoutExt = path.parse(filename).name

  // 支持多种命名格式：
  // 1. "title_author.jpg"
  // 2. "title - author.jpg"
  // 3. "title by author.jpg"

  const separators = ['_', ' - ', ' by ']

  for (const sep of separators) {
    if (nameWithoutExt.includes(sep)) {
      const parts = nameWithoutExt.split(sep)
      if (parts.length >= 2) {
        return {
          title: parts[0].trim(),
          author: parts.slice(1).join(' ').trim()
        }
      }
    }
  }

  // 如果无法解析，使用默认值
  return {
    title: nameWithoutExt,
    author: 'Unknown Author'
  }
}
*/

// 生成提示词
/*
function generatePrompt(title: string, author: string): string {
  return CONFIG.PROMPT_TEMPLATE
    .replace('{title}', title)
    .replace('{author}', author)
}
*/

// 扫描书籍元数据
function scanInputDirectory(): ImageFile[] {
  const bookMetadata = loadBookMetadata() // 加载数据库元数据

  if (Object.keys(bookMetadata).length === 0) {
    logger.warn('没有找到书籍元数据')
    return []
  }

  logger.info(`从数据库元数据中发现 ${Object.keys(bookMetadata).length} 条记录`)

  return Object.values(bookMetadata)
    .filter(metadata => {
      // 确保有必要的字段
      return metadata.imageUrl && metadata.title && metadata.authors
    })
    .map(metadata => {
      return {
        filePath: '', // 不再需要本地文件路径
        fileName: metadata.imageFilename,
        title: metadata.title,
        author: metadata.authors.length > 0 ? metadata.authors.join(', ') : 'Unknown Author',
        imageUrl: metadata.imageUrl
      }
    })
}

// 过滤待处理文件
function filterPendingFiles(imageFiles: ImageFile[], progress: ProcessingProgress): ImageFile[] {
  return imageFiles.filter(file => !progress.processedFiles.includes(file.fileName))
}

// 过滤失败文件（用于重试）
function filterFailedFiles(imageFiles: ImageFile[], progress: ProcessingProgress): ImageFile[] {
  const failedFileNames = progress.failedFiles.map(f => f.file)
  return imageFiles.filter(file => failedFileNames.includes(file.fileName))
}

// 设置环境变量以禁用SSL证书验证（解决证书问题）
if (process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
  logger.warn('已禁用SSL证书验证以解决API连接问题')
}

// 为Bun设置额外的TLS配置
const TLS_CONFIG = {
  rejectUnauthorized: false,
  checkServerIdentity: () => undefined,
  secureProtocol: 'TLSv1_2_method'
}

// 使用curl作为备用HTTP客户端的函数
async function curlRequest(url: string, options: any): Promise<any> {
  const { spawn } = await import('node:child_process')

  return new Promise((resolve, reject) => {
    const curlArgs = [
      '-k', // 忽略SSL证书错误
      '--insecure', // 额外的SSL忽略选项
      '-s', // 静默模式
      '-S', // 显示错误
      '-L', // 跟随重定向
      '--tlsv1.2', // 强制使用TLS 1.2
      '--ssl-no-revoke', // 不检查证书撤销
      '-X', options.method || 'GET',
      '--max-time', '60', // 60秒超时
      '--connect-timeout', '30' // 连接超时
    ]

    // 添加headers
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlArgs.push('-H', `${key}: ${value}`)
      })
    }

    // 添加body数据
    if (options.body) {
      curlArgs.push('-d', options.body)
    }

    curlArgs.push(url)

    const curl = spawn('curl', curlArgs)
    let stdout = ''
    let stderr = ''

    curl.stdout.on('data', (data) => {
      stdout += data.toString()
    })

    curl.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    curl.on('close', (code) => {
      if (code === 0) {
        try {
          const response = JSON.parse(stdout)
          resolve(response)
        } catch (error) {
          resolve(stdout) // 如果不是JSON，返回原始文本
        }
      } else {
        reject(new Error(`curl failed with code ${code}: ${stderr}`))
      }
    })

    curl.on('error', (error) => {
      reject(error)
    })
  })
}

// 重试机制
async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = CONFIG.RETRY_ATTEMPTS
): Promise<T> {
  let lastError: Error = new Error('No attempts made')

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      if (attempt < maxAttempts) {
        const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // 指数退避
        logger.warn(`操作失败，${delayMs}ms后重试 (${attempt}/${maxAttempts}): ${lastError.message}`)
        await delay(delayMs)
      }
    }
  }

  throw lastError
}

// 创建批量图片生成任务（最多5张图片）
async function createBatchImageGenerationTask(
  imageFiles: ImageFile[]
): Promise<string> {

  if (imageFiles.length === 0 || imageFiles.length > CONFIG.BATCH_SIZE) {
    throw new Error(`批次大小必须在1-${CONFIG.BATCH_SIZE}之间，当前: ${imageFiles.length}`)
  }

  // 提取所有图片URL
  const filesUrl = imageFiles.map(file => file.imageUrl)

  // 生成组合提示词（包含所有书籍信息）
  const prompts = imageFiles.map(file => `${file.title} by ${file.author}`).join('; ')
  const combinedPrompt = `Generate book cover images for the following books: ${prompts}. ${CONFIG.PROMPT_TEMPLATE.replace('{title}', 'various books').replace('{author}', 'various authors')}`

  logger.info(`[批量创建] 处理 ${imageFiles.length} 张图片: ${imageFiles.map(f => f.fileName).join(', ')}`)

  // 根据官网API文档，使用真实的图片URL
  const requestBody = JSON.stringify({
    filesUrl: filesUrl,
    prompt: combinedPrompt,
    size: "2:3",
    callBackUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/cc2825e1-d5a9-48d9-a692-750e0e33cc76",
    isEnhance: false,
    uploadCn: false,
    nVariants: Math.min(4, Math.max(1, imageFiles.length <= 2 ? imageFiles.length : 4)) // API只支持1,2,4
  })

  // 首先尝试使用fetch
  try {
    const fetchOptions: any = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: requestBody,
      tls: TLS_CONFIG
    }

    const response = await fetch(`${CONFIG.API_BASE_URL}/generate`, fetchOptions)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API请求失败: ${response.status} ${errorText}`)
    }

    const data = await response.json()
    logger.info(`[创建] API响应数据: ${JSON.stringify(data, null, 2)}`)

    // 根据curl测试，响应格式是 {code: 200, msg: "success", data: {taskId: "..."}}
    if (data.code === 200 && data.data && data.data.taskId) {
      logger.success(`[创建] 任务创建成功 (fetch): ${data.data.taskId}`)
      return data.data.taskId
    } else {
      throw new Error(`API响应格式错误: ${JSON.stringify(data)}`)
    }
  } catch (fetchError) {
    logger.warn(`fetch请求失败，尝试使用curl: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`)

    // 使用curl作为备用方案
    try {
      const curlOptions = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: requestBody
      }

      const data = await curlRequest(`${CONFIG.API_BASE_URL}/generate`, curlOptions)
      logger.info(`[创建-curl] API响应数据: ${JSON.stringify(data, null, 2)}`)

      // 根据curl测试，响应格式是 {code: 200, msg: "success", data: {taskId: "..."}}
      if (data.code === 200 && data.data && data.data.taskId) {
        logger.success(`[创建-curl] 任务创建成功: ${data.data.taskId}`)
        return data.data.taskId
      } else {
        throw new Error(`curl API响应格式错误: ${JSON.stringify(data)}`)
      }
    } catch (curlError) {
      logger.error(`curl请求也失败: ${curlError instanceof Error ? curlError.message : String(curlError)}`)
      throw new Error(`所有HTTP客户端都失败: fetch(${fetchError instanceof Error ? fetchError.message : String(fetchError)}), curl(${curlError instanceof Error ? curlError.message : String(curlError)})`)
    }
  }
}

// 轮询任务状态
async function pollTaskStatus(taskId: string): Promise<TaskResult> {
  let attempts = 0

  while (attempts < CONFIG.POLL_MAX_ATTEMPTS) {
    try {
      let response: TaskResult

      // 首先尝试使用fetch
      try {
        const fetchOptions: any = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          tls: TLS_CONFIG
        }

        const fetchResponse = await fetch(`${CONFIG.API_BASE_URL}/record-info?taskId=${taskId}`, fetchOptions)

        if (!fetchResponse.ok) {
          throw new Error(`轮询请求失败: ${fetchResponse.status}`)
        }

        const responseData = await fetchResponse.json()
        logger.info(`[轮询] 任务 ${taskId} API响应: ${JSON.stringify(responseData, null, 2)}`)

        // 根据API响应格式解析任务结果
        if (responseData.code === 200 && responseData.data) {
          response = responseData.data as TaskResult
          logger.info(`[轮询] 任务 ${taskId} 状态: ${response.status}, 进度: ${response.progress}`)
        } else {
          throw new Error(`API响应错误: ${JSON.stringify(responseData)}`)
        }
      } catch (fetchError) {
        // 使用curl作为备用方案
        const curlOptions = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }

        const curlResponseData = await curlRequest(`${CONFIG.API_BASE_URL}/record-info?taskId=${taskId}`, curlOptions)
        logger.info(`[轮询-curl] 任务 ${taskId} API响应: ${JSON.stringify(curlResponseData, null, 2)}`)

        // 根据API响应格式解析任务结果
        if (curlResponseData.code === 200 && curlResponseData.data) {
          response = curlResponseData.data as TaskResult
          logger.info(`[轮询-curl] 任务 ${taskId} 状态: ${response.status}, 进度: ${response.progress}`)
        } else {
          throw new Error(`curl API响应错误: ${JSON.stringify(curlResponseData)}`)
        }
      }

      // 详细的状态日志
      if (response.status === 'SUCCESS') {
        logger.info(`[轮询] 任务 ${taskId} 生成成功! 进度: ${response.progress}`)
        if (response.response && response.response.resultUrls) {
          logger.info(`[轮询] 任务 ${taskId} 生成的图片URL: ${response.response.resultUrls.join(', ')}`)
        }
      } else if (response.status === 'GENERATING') {
        logger.info(`[轮询] 任务 ${taskId} 生成中... 进度: ${response.progress}`)
      } else if (response.status === 'GENERATE_FAILED') {
        logger.error(`[轮询] 任务 ${taskId} 生成失败! 错误码: ${response.errorCode}, 错误信息: ${response.errorMessage}`)
      } else if (response.status === 'CREATE_TASK_FAILED') {
        logger.error(`[轮询] 任务 ${taskId} 创建失败! 错误码: ${response.errorCode}, 错误信息: ${response.errorMessage}`)
      }

      if (response.status === 'SUCCESS' || response.status === 'GENERATE_FAILED' || response.status === 'CREATE_TASK_FAILED') {
        return response
      }

      // 等待后继续轮询
      await delay(CONFIG.POLL_INTERVAL)
      attempts++

    } catch (error) {
      logger.warn(`轮询任务状态失败 (${attempts + 1}/${CONFIG.POLL_MAX_ATTEMPTS}): ${taskId} - ${error instanceof Error ? error.message : String(error)}`)
      await delay(CONFIG.POLL_INTERVAL)
      attempts++
    }
  }

  throw new Error(`任务超时: ${taskId}`)
}

// 下载生成的图片
async function downloadGeneratedImage(
  imageUrl: string,
  originalFileName: string,
  taskId: string
): Promise<void> {

  try {
    // 获取直接下载链接 - 根据文档，需要taskId和url字段
    const downloadOptions: any = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        taskId: taskId,
        url: imageUrl
      }),
      tls: TLS_CONFIG
    }

    const downloadResponse = await fetch(`${CONFIG.API_BASE_URL}/download-url`, downloadOptions)

    if (!downloadResponse.ok) {
      throw new Error(`获取下载链接失败: ${downloadResponse.status}`)
    }

    const downloadData = await downloadResponse.json()
    logger.info(`[下载] 任务 ${taskId} 下载API响应: ${JSON.stringify(downloadData, null, 2)}`)

    // 根据API文档，响应格式是 {code: 200, msg: "success", data: "直接下载URL"}
    let downloadUrl: string
    if (downloadData.code === 200 && downloadData.data) {
      downloadUrl = downloadData.data
      logger.info(`[下载] 任务 ${taskId} 获取到直接下载URL: ${downloadUrl}`)
    } else {
      throw new Error(`无法获取下载URL: ${JSON.stringify(downloadData)}`)
    }

    // 下载图片
    const imageOptions: any = {
      method: 'GET',
      tls: TLS_CONFIG
    }

    logger.info(`[下载] 任务 ${taskId} 开始下载图片文件...`)
    const imageResponse = await fetch(downloadUrl, imageOptions)
    if (!imageResponse.ok) {
      throw new Error(`下载失败: ${imageResponse.status} ${imageResponse.statusText}`)
    }

    const imageBuffer = await imageResponse.arrayBuffer()
    const fileSizeKB = Math.round(imageBuffer.byteLength / 1024)
    logger.info(`[下载] 任务 ${taskId} 下载完成，文件大小: ${fileSizeKB}KB`)

    // 保存到输出目录
    const outputPath = path.join(CONFIG.OUTPUT_DIR, originalFileName)
    fs.writeFileSync(outputPath, Buffer.from(imageBuffer))

    logger.success(`[下载] 任务 ${taskId} 图片已保存: ${outputPath}`)

  } catch (error) {
    // 如果获取下载链接失败，尝试直接下载原始URL
    logger.warn(`[下载] 任务 ${taskId} 获取下载链接失败，尝试直接下载原始URL: ${error instanceof Error ? error.message : String(error)}`)

    const imageOptions: any = {
      method: 'GET',
      tls: TLS_CONFIG
    }

    logger.info(`[下载] 任务 ${taskId} 直接下载原始URL: ${imageUrl}`)
    const imageResponse = await fetch(imageUrl, imageOptions)
    if (!imageResponse.ok) {
      throw new Error(`直接下载失败: ${imageResponse.status} ${imageResponse.statusText}`)
    }

    const imageBuffer = await imageResponse.arrayBuffer()
    const fileSizeKB = Math.round(imageBuffer.byteLength / 1024)
    logger.info(`[下载] 任务 ${taskId} 直接下载完成，文件大小: ${fileSizeKB}KB`)

    const outputPath = path.join(CONFIG.OUTPUT_DIR, originalFileName)
    fs.writeFileSync(outputPath, Buffer.from(imageBuffer))

    logger.success(`[下载] 任务 ${taskId} 图片已保存: ${outputPath}`)
  }
}

// 并发控制信号量
class Semaphore {
  private permits: number
  private waiting: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--
      return
    }

    return new Promise<void>((resolve) => {
      this.waiting.push(resolve)
    })
  }

  release(): void {
    this.permits++
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!
      this.permits--
      resolve()
    }
  }
}

// 处理单个图片文件
async function processImageFile(
  imageFile: ImageFile,
  progress: ProcessingProgress
): Promise<{ success: boolean; error?: string }> {

  try {
    logger.info(`开始处理图片: ${imageFile.fileName}`)

    // 1. 验证图片URL
    if (!imageFile.imageUrl) {
      throw new Error(`图片URL不存在: ${imageFile.fileName}`)
    }
    logger.info(`使用图片URL: ${imageFile.imageUrl}`)

    // 2. 生成提示词
    // const prompt = generatePrompt(imageFile.title, imageFile.author)
    logger.info(`生成提示词: ${imageFile.title} by ${imageFile.author}`)

    // 3. 调用GPT-4o API创建任务（带重试）
    const taskId = await withRetry(() => createBatchImageGenerationTask([imageFile]))
    logger.info(`任务已创建: ${taskId}`)

    // 4. 轮询任务状态
    const taskResult = await pollTaskStatus(taskId)

    // 5. 下载生成的图片
    if (taskResult.status === 'SUCCESS') {
      // 检查是否有生成的图片URL
      if (!taskResult.response || !taskResult.response.resultUrls || taskResult.response.resultUrls.length === 0) {
        throw new Error(`任务成功但没有生成图片URL: ${taskId}`)
      }

      // 使用第一个生成的图片URL
      const imageUrl = taskResult.response.resultUrls[0]
      logger.info(`[处理] ${imageFile.fileName} 开始下载生成的图片: ${imageUrl}`)

      await withRetry(() => downloadGeneratedImage(imageUrl, imageFile.fileName, taskId))

      // 更新进度
      progress.processedFiles.push(imageFile.fileName)
      saveProgress(progress)

      logger.success(`[处理] 成功处理图片: ${imageFile.fileName}`)
      return { success: true }
    } else if (taskResult.status === 'GENERATE_FAILED' || taskResult.status === 'CREATE_TASK_FAILED') {
      throw new Error(`任务失败: ${taskResult.status}, 错误码: ${taskResult.errorCode}, 错误信息: ${taskResult.errorMessage}`)
    } else {
      throw new Error(`未知的任务状态: ${taskResult.status}`)
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`处理图片失败: ${imageFile.fileName}`, error)

    // 记录失败
    progress.failedFiles.push({
      file: imageFile.fileName,
      error: errorMessage
    })
    saveProgress(progress)

    return { success: false, error: errorMessage }
  }
}

// 处理批量图片文件（最多5张）
async function processBatchImageFiles(
  imageFiles: ImageFile[],
  progress: ProcessingProgress
): Promise<{ success: number; failed: number; errors: Array<{ file: string; error: string }> }> {

  const batchSize = imageFiles.length
  const fileNames = imageFiles.map(f => f.fileName).join(', ')

  try {
    logger.info(`[批量处理] 开始处理 ${batchSize} 张图片: ${fileNames}`)

    // 1. 验证所有图片URL
    for (const imageFile of imageFiles) {
      if (!imageFile.imageUrl) {
        throw new Error(`图片URL不存在: ${imageFile.fileName}`)
      }
    }

    // 2. 调用批量API创建任务（带重试）
    const taskId = await withRetry(() => createBatchImageGenerationTask(imageFiles))
    logger.info(`[批量处理] 任务已创建: ${taskId}`)

    // 3. 轮询任务状态
    const taskResult = await pollTaskStatus(taskId)

    // 4. 处理结果
    if (taskResult.status === 'SUCCESS') {
      // 检查是否有生成的图片URL
      if (!taskResult.response || !taskResult.response.resultUrls || taskResult.response.resultUrls.length === 0) {
        throw new Error(`任务成功但没有生成图片URL: ${taskId}`)
      }

      const resultUrls = taskResult.response.resultUrls
      logger.info(`[批量处理] 生成了 ${resultUrls.length} 张图片`)

      let successCount = 0
      let failedCount = 0
      const errors: Array<{ file: string; error: string }> = []

      // 下载每张生成的图片
      for (let i = 0; i < Math.min(imageFiles.length, resultUrls.length); i++) {
        const imageFile = imageFiles[i]
        const imageUrl = resultUrls[i]

        try {
          logger.info(`[批量处理] ${imageFile.fileName} 开始下载生成的图片: ${imageUrl}`)
          await withRetry(() => downloadGeneratedImage(imageUrl, imageFile.fileName, taskId))

          // 更新进度
          progress.processedFiles.push(imageFile.fileName)
          successCount++
          logger.success(`[批量处理] 成功处理图片: ${imageFile.fileName}`)
        } catch (downloadError) {
          const errorMessage = downloadError instanceof Error ? downloadError.message : String(downloadError)
          logger.error(`[批量处理] 下载图片失败: ${imageFile.fileName}`, downloadError)

          progress.failedFiles.push({ file: imageFile.fileName, error: errorMessage })
          errors.push({ file: imageFile.fileName, error: errorMessage })
          failedCount++
        }
      }

      // 保存进度
      saveProgress(progress)

      logger.info(`[批量处理] 批次完成: 成功 ${successCount}, 失败 ${failedCount}`)
      return { success: successCount, failed: failedCount, errors }

    } else if (taskResult.status === 'GENERATE_FAILED' || taskResult.status === 'CREATE_TASK_FAILED') {
      const errorMessage = `任务失败: ${taskResult.status}, 错误码: ${taskResult.errorCode}, 错误信息: ${taskResult.errorMessage}`
      throw new Error(errorMessage)
    } else {
      throw new Error(`未知的任务状态: ${taskResult.status}`)
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`[批量处理] 批次处理失败: ${fileNames}`, error)

    // 将所有文件标记为失败
    const errors: Array<{ file: string; error: string }> = []
    for (const imageFile of imageFiles) {
      progress.failedFiles.push({ file: imageFile.fileName, error: errorMessage })
      errors.push({ file: imageFile.fileName, error: errorMessage })
    }
    saveProgress(progress)

    return { success: 0, failed: imageFiles.length, errors }
  }
}

// 优化的批量处理图片（支持5张一批）
async function processBatchImages(
  imageFiles: ImageFile[],
  progress: ProcessingProgress
): Promise<ProcessingResult> {
  const result: ProcessingResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  if (imageFiles.length === 0) {
    logger.info('没有需要处理的图片文件')
    return result
  }

  logger.info(`开始批量处理 ${imageFiles.length} 个图片文件，每批最多 ${CONFIG.BATCH_SIZE} 张`)

  // 将文件分组，每组最多5张
  const batches: ImageFile[][] = []
  for (let i = 0; i < imageFiles.length; i += CONFIG.BATCH_SIZE) {
    batches.push(imageFiles.slice(i, i + CONFIG.BATCH_SIZE))
  }

  logger.info(`分为 ${batches.length} 个批次处理`)

  // 使用并发控制处理批次
  const semaphore = new Semaphore(CONFIG.MAX_CONCURRENT)

  const promises = batches.map(async (batch, batchIndex) => {
    await semaphore.acquire()
    try {
      logger.info(`处理批次: ${batchIndex + 1}/${batches.length} (${batch.length} 张图片)`)
      return await processBatchImageFiles(batch, progress)
    } finally {
      semaphore.release()
    }
  })

  const results = await Promise.allSettled(promises)

  // 统计结果
  results.forEach((promiseResult, batchIndex) => {
    if (promiseResult.status === 'fulfilled') {
      const batchResult = promiseResult.value
      result.success += batchResult.success
      result.failed += batchResult.failed
      result.errors.push(...batchResult.errors)

      logger.info(`批次 ${batchIndex + 1} 完成: 成功 ${batchResult.success}, 失败 ${batchResult.failed}`)
    } else {
      const batch = batches[batchIndex]
      result.failed += batch.length

      // 将整个批次标记为失败
      batch.forEach(imageFile => {
        const errorMessage = `批次处理失败: ${promiseResult.reason}`
        progress.failedFiles.push({ file: imageFile.fileName, error: errorMessage })
        result.errors.push({ file: imageFile.fileName, error: errorMessage })
      })

      logger.error(`批次 ${batchIndex + 1} 失败: ${promiseResult.reason}`)
    }
  })

  // 保存最终进度
  saveProgress(progress)

  return result
}

// 生成处理报告
function generateReport(result: ProcessingResult, startTime: string): void {
  const endTime = new Date().toISOString()
  const reportPath = path.join(CONFIG.LOG_DIR, `report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`)

  const totalFiles = result.success + result.failed + result.skipped
  const successRate = totalFiles > 0 ? ((result.success / totalFiles) * 100).toFixed(2) : '0.00'

  const report = {
    summary: {
      startTime,
      endTime,
      totalFiles,
      successfulFiles: result.success,
      failedFiles: result.failed,
      skippedFiles: result.skipped,
      successRate: `${successRate}%`,
      processingDuration: `${Math.round((new Date(endTime).getTime() - new Date(startTime).getTime()) / 1000)}s`
    },
    errors: result.errors,
    configuration: {
      batchSize: CONFIG.BATCH_SIZE,
      maxConcurrent: CONFIG.MAX_CONCURRENT,
      retryAttempts: CONFIG.RETRY_ATTEMPTS,
      pollInterval: CONFIG.POLL_INTERVAL,
      pollMaxAttempts: CONFIG.POLL_MAX_ATTEMPTS,
      supportedFormats: CONFIG.SUPPORTED_FORMATS
    }
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8')
  logger.info(`处理报告已生成: ${reportPath}`)

  // 打印摘要
  logger.info('='.repeat(50))
  logger.info('处理摘要:')
  logger.info(`总文件数: ${totalFiles}`)
  logger.info(`成功: ${result.success}`)
  logger.info(`失败: ${result.failed}`)
  logger.info(`跳过: ${result.skipped}`)
  logger.info(`成功率: ${successRate}%`)
  logger.info('='.repeat(50))
}

// 主函数
async function main() {
  const startTime = new Date().toISOString()
  logger.info('开始批量图片处理流程')

  try {
    // 验证环境配置
    validateEnvironment()

    // 加载进度
    const progress = loadProgress()
    logger.info(`已加载处理进度: 已处理 ${progress.processedFiles.length} 个文件, 失败 ${progress.failedFiles.length} 个文件`)

    // 扫描书籍元数据
    const imageFiles = scanInputDirectory()
    logger.info(`发现 ${imageFiles.length} 个书籍记录`)

    if (imageFiles.length === 0) {
      logger.warn(`没有找到有效的书籍元数据记录`)
      logger.info('请确保 book-metadata.json 文件存在且包含有效数据')
      return
    }

    // 过滤待处理的文件
    const pendingFiles = filterPendingFiles(imageFiles, progress)
    logger.info(`待处理文件: ${pendingFiles.length} 个`)

    if (pendingFiles.length === 0) {
      logger.info('所有文件都已处理完成')
      return
    }

    // 更新总文件数
    progress.totalFiles = imageFiles.length
    saveProgress(progress)

    // 批量处理图片
    const result = await processBatchImages(pendingFiles, progress)

    // 生成处理报告
    generateReport(result, startTime)

    logger.success('批量图片处理流程完成')
  } catch (error) {
    logger.error('处理过程中出错', error)
    throw error
  }
}

// 重试失败的文件
async function retryFailedFiles() {
  const startTime = new Date().toISOString()
  logger.info('开始重试失败的文件')

  try {
    validateEnvironment()

    const progress = loadProgress()
    const imageFiles = scanInputDirectory()
    const failedFiles = filterFailedFiles(imageFiles, progress)

    logger.info(`发现 ${failedFiles.length} 个失败的文件需要重试`)

    if (failedFiles.length === 0) {
      logger.info('没有失败的文件需要重试')
      return
    }

    // 清除失败记录，准备重试
    progress.failedFiles = progress.failedFiles.filter(f =>
      !failedFiles.some(file => file.fileName === f.file)
    )
    saveProgress(progress)

    const result = await processBatchImages(failedFiles, progress)
    generateReport(result, startTime)

    logger.success('重试处理完成')
  } catch (error) {
    logger.error('重试过程中出错', error)
    throw error
  }
}

// 重置进度
function resetProgress() {
  const emptyProgress: ProcessingProgress = {
    processedFiles: [],
    failedFiles: [],
    totalFiles: 0,
    startTime: new Date().toISOString()
  }

  saveProgress(emptyProgress)
  logger.info('处理进度已重置')
}

// 命令行参数处理
async function run() {
  const args = process.argv.slice(2)
  const command = args[0]

  try {
    if (command === 'reset') {
      // 重置进度
      resetProgress()
    } else if (command === 'retry') {
      // 重试失败的文件
      await retryFailedFiles()
    } else {
      // 执行完整处理流程
      await main()
    }
  } catch (error) {
    logger.error('脚本执行失败', error)
    process.exit(1)
  } finally {
    // 关闭日志
    logger.close()
  }
}

// 如果直接运行此脚本（而不是作为模块导入）
if (import.meta.url === `file://${process.argv[1]}`) {
  run().catch(error => {
    console.error('批量图片处理脚本执行失败:', error)
    process.exit(1)
  })
}

// 导出函数供其他模块使用
export {
  main,
  retryFailedFiles,
  resetProgress,
  scanInputDirectory,
  processImageFile,
  processBatchImages
}
