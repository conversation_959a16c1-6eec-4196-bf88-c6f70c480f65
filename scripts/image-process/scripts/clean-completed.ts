#!/usr/bin/env bun

/**
 * 清理已完成任务脚本
 * 清理已下载完成的任务记录
 */

import { cleanCompletedTasks } from './task-queue.js'
import { Logger } from './utils.js'

const logger = new Logger('clean-completed')

/**
 * 主函数
 */
function cleanCompleted(): void {
  console.log('🧹 清理已完成的任务')
  
  const cleanedCount = cleanCompletedTasks()
  
  if (cleanedCount > 0) {
    logger.success(`已清理 ${cleanedCount} 个已完成的任务记录`)
    logger.info('任务队列已优化，只保留未完成的任务')
  } else {
    logger.info('没有找到需要清理的已完成任务')
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanCompleted()
}
