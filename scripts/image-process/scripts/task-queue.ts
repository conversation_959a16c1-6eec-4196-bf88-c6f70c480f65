/**
 * 任务队列管理工具
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import type { TaskQueue, TaskRecord, TaskStats, ProcessingReport } from './types.js'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const TASK_QUEUE_FILE = path.join(__dirname, '../data/tasks-queue.json')
const REPORTS_DIR = path.join(__dirname, '../data/reports')

// 确保目录存在
if (!fs.existsSync(path.dirname(TASK_QUEUE_FILE))) {
  fs.mkdirSync(path.dirname(TASK_QUEUE_FILE), { recursive: true })
}
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true })
}

/**
 * 加载任务队列
 */
export function loadTaskQueue(): TaskQueue {
  try {
    if (fs.existsSync(TASK_QUEUE_FILE)) {
      const data = fs.readFileSync(TASK_QUEUE_FILE, 'utf8')
      const queue = JSON.parse(data) as TaskQueue

      // 版本兼容性检查
      if (!queue.version || queue.version !== '2.0') {
        console.log('检测到旧版本任务队列，正在升级...')
        return migrateTaskQueue(queue)
      }

      return queue
    }
  } catch (error) {
    console.warn('加载任务队列失败，创建新队列:', error)
  }

  return createEmptyTaskQueue()
}

/**
 * 保存任务队列
 */
export function saveTaskQueue(queue: TaskQueue): void {
  queue.lastUpdated = Date.now()
  queue.stats = calculateStats(queue.tasks)

  const data = JSON.stringify(queue, null, 2)
  fs.writeFileSync(TASK_QUEUE_FILE, data, 'utf8')
}

/**
 * 创建空的任务队列
 */
function createEmptyTaskQueue(): TaskQueue {
  return {
    tasks: {},
    stats: {
      total: 0,
      pending: 0,
      generating: 0,
      success: 0,
      failed: 0,
      downloaded: 0
    },
    lastUpdated: Date.now(),
    version: '2.0'
  }
}

/**
 * 计算统计信息
 */
function calculateStats(tasks: Record<string, TaskRecord>): TaskStats {
  const stats: TaskStats = {
    total: 0,
    pending: 0,
    generating: 0,
    success: 0,
    failed: 0,
    downloaded: 0
  }

  for (const task of Object.values(tasks)) {
    stats.total++
    switch (task.status) {
      case 'PENDING':
        stats.pending++
        break
      case 'GENERATING':
        stats.generating++
        break
      case 'SUCCESS':
        stats.success++
        break
      case 'FAILED':
        stats.failed++
        break
      case 'DOWNLOADED':
        stats.downloaded++
        break
    }
  }

  return stats
}

/**
 * 添加任务记录
 */
export function addTaskRecord(taskId: string, taskRecord: Omit<TaskRecord, 'taskId'>): void {
  const queue = loadTaskQueue()
  queue.tasks[taskId] = { ...taskRecord, taskId }
  saveTaskQueue(queue)
}

/**
 * 更新任务记录
 */
export function updateTaskRecord(taskId: string, updates: Partial<TaskRecord>): void {
  const queue = loadTaskQueue()
  if (queue.tasks[taskId]) {
    queue.tasks[taskId] = { ...queue.tasks[taskId], ...updates }
    saveTaskQueue(queue)
  }
}

/**
 * 获取特定状态的任务
 */
export function getTasksByStatus(status: TaskRecord['status']): TaskRecord[] {
  const queue = loadTaskQueue()
  return Object.values(queue.tasks).filter(task => task.status === status)
}

/**
 * 获取需要轮询的任务
 */
export function getPendingTasks(): TaskRecord[] {
  const queue = loadTaskQueue()
  return Object.values(queue.tasks).filter(task =>
    task.status === 'PENDING' || task.status === 'GENERATING'
  )
}

/**
 * 清理已完成的任务
 */
export function cleanCompletedTasks(): number {
  const queue = loadTaskQueue()
  const beforeCount = Object.keys(queue.tasks).length

  // 只保留未完成的任务
  const activeTasks: Record<string, TaskRecord> = {}
  for (const [taskId, task] of Object.entries(queue.tasks)) {
    if (task.status !== 'DOWNLOADED') {
      activeTasks[taskId] = task
    }
  }

  queue.tasks = activeTasks
  saveTaskQueue(queue)

  return beforeCount - Object.keys(queue.tasks).length
}

/**
 * 重置失败的任务
 */
export function resetFailedTasks(): number {
  const queue = loadTaskQueue()
  let resetCount = 0

  for (const task of Object.values(queue.tasks)) {
    if (task.status === 'FAILED') {
      task.status = 'PENDING'
      task.retryCount = 0
      task.errorMessage = undefined
      task.lastChecked = 0
      resetCount++
    }
  }

  if (resetCount > 0) {
    saveTaskQueue(queue)
  }

  return resetCount
}

/**
 * 保存处理报告
 */
export function saveProcessingReport(report: ProcessingReport): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const filename = `report-${report.phase.toLowerCase()}-${timestamp}.json`
  const filepath = path.join(REPORTS_DIR, filename)

  fs.writeFileSync(filepath, JSON.stringify(report, null, 2), 'utf8')
  return filepath
}

/**
 * 迁移旧版本任务队列
 */
function migrateTaskQueue(oldQueue: any): TaskQueue {
  const newQueue = createEmptyTaskQueue()

  // 如果有旧的任务数据，尝试迁移
  if (oldQueue.tasks) {
    for (const [taskId, task] of Object.entries(oldQueue.tasks as any)) {
      newQueue.tasks[taskId] = {
        ...(task as TaskRecord),
        batchId: `batch-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        estimatedCompletionTime: Date.now() + 300000 // 默认5分钟
      } as TaskRecord
    }
  }

  saveTaskQueue(newQueue)
  return newQueue
}

/**
 * 获取任务队列统计信息
 */
export function getTaskQueueStats(): TaskStats {
  const queue = loadTaskQueue()
  return queue.stats
}
