#!/usr/bin/env bun

/**
 * 智能重置和创建任务脚本
 * 1. 清理已完成的任务记录
 * 2. 重置失败的任务
 * 3. 为没有任务的图片创建新任务（跳过已存在的输出文件）
 */

import { Logger, scanInputDirectory } from './utils.js'
import { loadTaskQueue, cleanCompletedTasks, resetFailedTasks, getTaskQueueStats } from './task-queue.js'
import { spawn } from 'node:child_process'
import path from 'node:path'

const logger = new Logger('reset-failed-and-create')

/**
 * 运行子脚本
 */
async function runScript(scriptPath: string, args: string[] = []): Promise<boolean> {
  return new Promise((resolve) => {
    const fullPath = path.resolve(scriptPath)
    logger.info(`运行脚本: ${fullPath} ${args.join(' ')}`)
    
    const child = spawn('bun', [fullPath, ...args], {
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    child.on('close', (code) => {
      if (code === 0) {
        logger.success(`脚本执行成功: ${scriptPath}`)
        resolve(true)
      } else {
        logger.error(`脚本执行失败: ${scriptPath} (退出码: ${code})`)
        resolve(false)
      }
    })
    
    child.on('error', (error) => {
      logger.error(`脚本执行错误: ${scriptPath} - ${error.message}`)
      resolve(false)
    })
  })
}

/**
 * 获取已有任务的图片文件名
 */
function getExistingTaskImages(): Set<string> {
  const queue = loadTaskQueue()
  const existingImages = new Set<string>()
  
  for (const task of Object.values(queue.tasks)) {
    for (const imageFile of task.imageFiles) {
      existingImages.add(imageFile.fileName)
    }
  }
  
  return existingImages
}

/**
 * 主函数
 */
async function resetFailedAndCreate(): Promise<void> {
  const startTime = Date.now()
  logger.info('开始智能重置和创建任务')
  
  try {
    // 1. 显示当前状态
    logger.info('='.repeat(60))
    logger.info('步骤 1: 检查当前状态')
    logger.info('='.repeat(60))
    
    const initialStats = getTaskQueueStats()
    logger.info(`当前状态: 总计 ${initialStats.total}, 待处理 ${initialStats.pending}, 生成中 ${initialStats.generating}`)
    logger.info(`成功 ${initialStats.success}, 失败 ${initialStats.failed}, 已下载 ${initialStats.downloaded}`)
    
    // 2. 清理已完成的任务
    if (initialStats.downloaded > 0) {
      logger.info('='.repeat(60))
      logger.info('步骤 2: 清理已完成的任务记录')
      logger.info('='.repeat(60))
      
      const cleanedCount = cleanCompletedTasks()
      logger.success(`清理了 ${cleanedCount} 个已完成的任务记录`)
    }
    
    // 3. 重置失败的任务
    if (initialStats.failed > 0) {
      logger.info('='.repeat(60))
      logger.info('步骤 3: 重置失败的任务')
      logger.info('='.repeat(60))
      
      const resetCount = resetFailedTasks()
      logger.success(`重置了 ${resetCount} 个失败的任务`)
    }
    
    // 4. 检查是否需要创建新任务
    logger.info('='.repeat(60))
    logger.info('步骤 4: 检查是否需要创建新任务')
    logger.info('='.repeat(60))
    
    const allImages = scanInputDirectory(false) // 获取所有图片
    const existingTaskImages = getExistingTaskImages()
    const missingImages = allImages.filter(img => !existingTaskImages.has(img.fileName))
    
    logger.info(`总图片数: ${allImages.length}`)
    logger.info(`已有任务的图片: ${existingTaskImages.size}`)
    logger.info(`缺少任务的图片: ${missingImages.length}`)
    
    if (missingImages.length > 0) {
      logger.info('='.repeat(60))
      logger.info('步骤 5: 为缺少任务的图片创建新任务')
      logger.info('='.repeat(60))
      
      // 创建新任务（会自动跳过已存在的输出文件）
      const createSuccess = await runScript('scripts/image-process/scripts/create-tasks.ts')
      if (!createSuccess) {
        throw new Error('创建新任务失败')
      }
    } else {
      logger.info('所有图片都已有对应的任务，无需创建新任务')
    }
    
    // 5. 显示最终状态
    logger.info('='.repeat(60))
    logger.info('最终状态')
    logger.info('='.repeat(60))
    
    const finalStats = getTaskQueueStats()
    logger.info(`最终状态: 总计 ${finalStats.total}, 待处理 ${finalStats.pending}, 生成中 ${finalStats.generating}`)
    logger.info(`成功 ${finalStats.success}, 失败 ${finalStats.failed}, 已下载 ${finalStats.downloaded}`)
    
    const duration = Date.now() - startTime
    logger.info(`总处理时间: ${Math.round(duration / 1000)}秒`)
    
    // 6. 建议下一步操作
    logger.info('='.repeat(60))
    logger.info('建议的下一步操作')
    logger.info('='.repeat(60))
    
    if (finalStats.pending > 0 || finalStats.generating > 0) {
      logger.info('• 运行 "pnpm image:poll" 开始轮询任务状态')
    } else if (finalStats.success > 0) {
      logger.info('• 运行 "pnpm image:download" 下载生成的图片')
    } else {
      logger.info('• 所有任务已完成或无需处理')
    }
    
    logger.success('智能重置和创建任务完成！')
    
  } catch (error) {
    logger.error('智能重置和创建任务失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  resetFailedAndCreate().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
