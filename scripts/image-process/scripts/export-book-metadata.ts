#!/usr/bin/env bun

/**
 * 从数据库导出书籍元数据脚本
 * 生成图片文件名到书籍信息的映射JSON
 */

import { PrismaClient } from '@prisma/client'
import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const prisma = new PrismaClient({
  log: ['error', 'warn']
})

// 数据类型定义
interface BookMetadata {
  imageFilename: string;  // "40121378.jpg"
  bookId: number;
  title: string;
  authors: string[];
  imageUrl: string;
}

// 日志工具
const logger = {
  info: (message: string) => {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`)
  },
  
  warn: (message: string) => {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`)
  },
  
  error: (message: string, error?: any) => {
    const errorDetails = error ? `\n${error.stack || error}` : ''
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}${errorDetails}`)
  },
  
  success: (message: string) => {
    console.log(`[SUCCESS] ${new Date().toISOString()} - ${message}`)
  }
}

// 主导出函数
async function exportBookMetadata() {
  logger.info('开始导出书籍元数据...')
  
  try {
    // 查询所有有封面的书籍
    logger.info('查询数据库中的书籍封面信息...')
    
    const books = await prisma.book_covers.findMany({
      where: {
        is_primary: 1,
        image_url: {
          contains: '/book-cover/'
        }
      },
      include: {
        book: {
          include: {
            book_translations: {
              where: { language_id: 'en' },
              take: 1
            },
            book_authors: {
              orderBy: { author_order: 'asc' },
              include: {
                author: {
                  include: {
                    author_translations: {
                      where: { language_id: 'en' },
                      take: 1
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    logger.info(`找到 ${books.length} 条封面记录`)

    const metadata: Record<string, BookMetadata> = {}
    let processedCount = 0
    let skippedCount = 0
    
    books.forEach(cover => {
      // 检查必要数据是否存在
      if (!cover.book || !cover.book.book_translations[0]) {
        skippedCount++
        logger.warn(`跳过书籍 ID ${cover.book?.id || 'unknown'}: 缺少翻译信息`)
        return
      }
      
      // 提取图片文件名
      const urlParts = cover.image_url.split('/')
      const filename = urlParts[urlParts.length - 1] // "40121378.jpg"
      
      if (!filename || !filename.includes('.')) {
        skippedCount++
        logger.warn(`跳过无效的图片URL: ${cover.image_url}`)
        return
      }
      
      // 获取作者列表
      const authors = cover.book.book_authors
        .map(ba => ba.author.author_translations[0]?.name)
        .filter(Boolean)
      
      if (authors.length === 0) {
        logger.warn(`书籍 "${cover.book.book_translations[0].title}" 没有找到作者信息`)
      }
      
      metadata[filename] = {
        imageFilename: filename,
        bookId: cover.book.id,
        title: cover.book.book_translations[0].title,
        authors,
        imageUrl: cover.image_url
      }
      
      processedCount++
    })
    
    // 保存到JSON文件 - 修改为相对于当前脚本位置的路径
    const outputPath = path.join(__dirname, '../data/book-metadata.json')
    
    // 确保目录存在
    const outputDir = path.dirname(outputPath)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(metadata, null, 2), 'utf8')
    
    logger.success(`导出完成！`)
    logger.info(`📊 统计信息:`)
    logger.info(`  - 处理成功: ${processedCount} 条`)
    logger.info(`  - 跳过记录: ${skippedCount} 条`)
    logger.info(`  - 总计记录: ${books.length} 条`)
    logger.info(`📁 文件保存至: ${outputPath}`)
    
    // 显示示例数据
    const sampleKeys = Object.keys(metadata).slice(0, 5)
    if (sampleKeys.length > 0) {
      logger.info('\n📋 示例数据:')
      sampleKeys.forEach(key => {
        const item = metadata[key]
        const authorsText = item.authors.length > 0 ? item.authors.join(', ') : 'Unknown Author'
        logger.info(`  ${key} -> "${item.title}" by ${authorsText}`)
      })
    }
    
    // 生成统计报告
    generateStatisticsReport(metadata)
    
  } catch (error) {
    logger.error('导出过程中发生错误', error)
    throw error
  }
}

// 生成统计报告
function generateStatisticsReport(metadata: Record<string, BookMetadata>) {
  const stats = {
    totalBooks: Object.keys(metadata).length,
    booksWithAuthors: <AUTHORS>
    booksWithoutAuthors: <AUTHORS>
    authorDistribution: {} as Record<number, number>,
    fileExtensions: {} as Record<string, number>
  }
  
  Object.values(metadata).forEach(book => {
    // 统计作者情况
    if (book.authors.length > 0) {
      stats.booksWithAuthors++
      const authorCount = book.authors.length
      stats.authorDistribution[authorCount] = (stats.authorDistribution[authorCount] || 0) + 1
    } else {
      stats.booksWithoutAuthors++
    }
    
    // 统计文件扩展名
    const ext = path.extname(book.imageFilename).toLowerCase()
    stats.fileExtensions[ext] = (stats.fileExtensions[ext] || 0) + 1
  })
  
  // 保存统计报告
  const reportPath = path.join(__dirname, '../data/book-metadata-stats.json')
  fs.writeFileSync(reportPath, JSON.stringify(stats, null, 2), 'utf8')
  
  logger.info('\n📈 数据统计:')
  logger.info(`  - 有作者信息: ${stats.booksWithAuthors} 本`)
  logger.info(`  - 无作者信息: ${stats.booksWithoutAuthors} 本`)
  logger.info(`  - 文件格式分布: ${JSON.stringify(stats.fileExtensions)}`)
  logger.info(`📁 统计报告保存至: ${reportPath}`)
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  exportBookMetadata()
    .catch(error => {
      logger.error('脚本执行失败', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}

// 导出函数供其他模块使用
export { exportBookMetadata }
