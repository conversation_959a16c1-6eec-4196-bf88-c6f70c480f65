#!/usr/bin/env bun

/**
 * 创建图片生成任务脚本
 * 快速创建所有图片生成任务，不等待完成
 */

import { Logger, CONFIG, API_CONFIG, TLS_CONFIG, scanInputDirectory, createBatches, generateBatchId, withRetry, delay } from './utils.js'
import { addTaskRecord, saveProcessingReport, loadTaskQueue } from './task-queue.js'
import type { ImageFile, TaskRecord, ProcessingReport } from './types.js'

// 强制禁用SSL验证
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

const logger = new Logger('create-tasks')

/**
 * 创建单张图片生成任务
 */
async function createSingleImageGenerationTask(imageFile: ImageFile, batchId: string): Promise<string> {
  // 单张图片处理
  const filesUrl = [imageFile.imageUrl]  // 只包含一张图片

  // 生成单本书的提示词
  const singlePrompt = `Generate a book cover image with the following specifications:

Book Title: ${imageFile.title}
Author: ${imageFile.author}
Cover Style: Modern、Symbolic/Conceptual Graphics、Limited and High-Contrast Color Palettes、Clear Visual Hierarchy、Flat Design Style

**Color Palette Instruction:**
* **Crucially, derive the color palette for this new cover *directly and exclusively* from the colors present in the provided reference image.**
* Strive for a high-contrast application of these extracted colors.`

  logger.info(`[创建任务] 单张图片 ${batchId}: ${imageFile.fileName} (${imageFile.title})`)

  // 构建请求体
  const requestBody = JSON.stringify({
    filesUrl: filesUrl,
    prompt: singlePrompt,
    size: "2:3",
    callBackUrl: API_CONFIG.CALLBACK_URL,
    isEnhance: false,
    uploadCn: false,
    nVariants: 1  // 每张图片生成1个变体
  })

  // 首先尝试使用fetch
  try {
    const fetchOptions: any = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: requestBody,
      tls: TLS_CONFIG
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/generate`, fetchOptions)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API请求失败: ${response.status} ${errorText}`)
    }

    const data = await response.json()
    logger.info(`[创建任务] 批次 ${batchId} API响应:`, data)

    if (data.code === 200 && data.data && data.data.taskId) {
      logger.success(`[创建任务] 批次 ${batchId} 任务创建成功: ${data.data.taskId}`)
      return data.data.taskId
    } else {
      throw new Error(`API响应格式错误: ${JSON.stringify(data)}`)
    }
  } catch (fetchError) {
    logger.warn(`[创建任务] 批次 ${batchId} fetch请求失败，尝试使用curl: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`)

    // 使用curl作为备用方案
    try {
      const curlOptions = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.GPT4O_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: requestBody
      }

      const data = await curlRequest(`${API_CONFIG.BASE_URL}/generate`, curlOptions)
      logger.info(`[创建任务] 批次 ${batchId} curl API响应:`, data)

      if (data.code === 200 && data.data && data.data.taskId) {
        logger.success(`[创建任务] 批次 ${batchId} 任务创建成功 (curl): ${data.data.taskId}`)
        return data.data.taskId
      } else {
        throw new Error(`curl API响应格式错误: ${JSON.stringify(data)}`)
      }
    } catch (curlError) {
      logger.error(`[创建任务] 批次 ${batchId} curl请求也失败: ${curlError instanceof Error ? curlError.message : String(curlError)}`)
      throw new Error(`所有HTTP客户端都失败: fetch(${fetchError instanceof Error ? fetchError.message : String(fetchError)}), curl(${curlError instanceof Error ? curlError.message : String(curlError)})`)
    }
  }
}

/**
 * curl请求实现
 */
async function curlRequest(url: string, options: any): Promise<any> {
  const { spawn } = await import('node:child_process')

  return new Promise((resolve, reject) => {
    const curlArgs = [
      '-k', '--insecure', '-s', '-S', '-L',
      '--tlsv1.2', '--ssl-no-revoke',
      '--connect-timeout', '30',
      '--max-time', '60',
      '-X', options.method || 'GET'
    ]

    // 添加headers
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlArgs.push('-H', `${key}: ${value}`)
      })
    }

    // 添加body数据
    if (options.body) {
      curlArgs.push('-d', options.body)
    }

    curlArgs.push(url)

    const curl = spawn('curl', curlArgs)
    let stdout = ''
    let stderr = ''

    curl.stdout.on('data', (data) => {
      stdout += data.toString()
    })

    curl.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    curl.on('close', (code) => {
      if (code === 0) {
        try {
          const response = JSON.parse(stdout)
          resolve(response)
        } catch (error) {
          resolve(stdout)
        }
      } else {
        reject(new Error(`curl failed with code ${code}: ${stderr}`))
      }
    })

    curl.on('error', (error) => {
      reject(error)
    })
  })
}

/**
 * 主函数：创建所有任务
 */
async function createAllTasks(): Promise<void> {
  const startTime = Date.now()
  logger.info('开始创建图片生成任务')

  try {
    // 1. 检查API密钥
    if (!process.env.GPT4O_API_KEY) {
      throw new Error('GPT4O_API_KEY 环境变量未设置')
    }

    // 2. 扫描待处理图片（跳过已存在的输出文件）
    const skipExisting = !process.argv.includes('--force-all')
    const imageFiles = scanInputDirectory(skipExisting)

    if (skipExisting) {
      logger.info(`发现 ${imageFiles.length} 个待处理图片（已跳过已存在的文件）`)
      logger.info('如要重新处理所有文件，请使用 --force-all 参数')
    } else {
      logger.info(`发现 ${imageFiles.length} 个待处理图片（包含已存在的文件）`)
    }

    if (imageFiles.length === 0) {
      logger.warn('没有找到待处理的图片文件')
      return
    }

    // 3. 检查现有任务队列
    const existingQueue = loadTaskQueue()
    const existingTaskCount = Object.keys(existingQueue.tasks).length
    if (existingTaskCount > 0) {
      logger.warn(`发现 ${existingTaskCount} 个现有任务，建议先运行 'pnpm image:status' 查看状态`)
      const shouldContinue = process.argv.includes('--force')
      if (!shouldContinue) {
        logger.info('如要强制创建新任务，请使用 --force 参数')
        return
      }
    }

    // 4. 分批处理（每张图片独立处理）
    const batches = createBatches(imageFiles, CONFIG.BATCH_SIZE)
    logger.info(`分为 ${batches.length} 个独立任务，每个任务处理 1 张图片`)

    let successCount = 0
    let failedCount = 0
    const errors: Array<{ batchId: string; error: string }> = []

    // 5. 创建所有任务（每张图片独立处理）
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      const imageFile = batch[0]  // 现在每个批次只有一张图片
      const batchId = generateBatchId()

      try {
        logger.info(`处理图片 ${i + 1}/${batches.length}: ${batchId} (${imageFile.title})`)

        // 创建单张图片任务
        const taskId = await withRetry(() => createSingleImageGenerationTask(imageFile, batchId))

        // 保存任务记录
        const taskRecord: Omit<TaskRecord, 'taskId'> = {
          batchId,
          imageFiles: [imageFile],  // 只包含一张图片
          status: 'PENDING',
          createdAt: Date.now(),
          lastChecked: 0,
          retryCount: 0,
          estimatedCompletionTime: Date.now() + 120000 // 单张图片预计2分钟
        }

        addTaskRecord(taskId, taskRecord)
        successCount++

        logger.success(`图片 ${imageFile.fileName} 任务创建成功: ${taskId}`)

        // 避免API限制，稍微延迟
        if (i < batches.length - 1) {
          await delay(2000)
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.error(`图片 ${imageFile.fileName} 创建失败: ${errorMessage}`)
        errors.push({ batchId, error: errorMessage })
        failedCount++
      }
    }

    // 6. 生成报告
    const duration = Date.now() - startTime
    const report: ProcessingReport = {
      timestamp: Date.now(),
      phase: 'CREATE',
      totalTasks: batches.length,
      stats: {
        total: batches.length,
        pending: successCount,
        generating: 0,
        success: 0,
        failed: failedCount,
        downloaded: 0
      },
      duration,
      errors: errors.map(e => ({
        taskId: e.batchId,
        error: e.error,
        timestamp: Date.now()
      }))
    }

    const reportPath = saveProcessingReport(report)

    // 7. 输出摘要
    logger.info('='.repeat(60))
    logger.info('任务创建完成摘要:')
    logger.info(`总任务数: ${batches.length}`)
    logger.info(`成功创建: ${successCount}`)
    logger.info(`创建失败: ${failedCount}`)
    logger.info(`总图片数: ${imageFiles.length}`)
    logger.info(`预计生成: ${successCount} 张图片 (每个任务1个变体)`)
    logger.info(`处理时间: ${Math.round(duration / 1000)}秒`)
    logger.info(`报告文件: ${reportPath}`)
    logger.info('='.repeat(60))

    if (successCount > 0) {
      logger.success('任务创建完成！')
      logger.info('下一步：运行 "pnpm image:poll" 开始轮询任务状态')
    }

  } catch (error) {
    logger.error('创建任务失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  createAllTasks().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
