#!/usr/bin/env bun

/**
 * 一键完整处理脚本
 * 依次执行创建、轮询、下载的完整流程
 */

import { spawn } from 'node:child_process'
import { Logger, delay } from './utils.js'
import { getTaskQueueStats } from './task-queue.js'

const logger = new Logger('full-process')

/**
 * 执行子脚本
 */
async function runScript(scriptName: string, args: string[] = []): Promise<boolean> {
  return new Promise((resolve) => {
    logger.info(`执行脚本: ${scriptName}`)
    
    const child = spawn('bun', [scriptName, ...args], {
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    child.on('close', (code) => {
      if (code === 0) {
        logger.success(`脚本 ${scriptName} 执行成功`)
        resolve(true)
      } else {
        logger.error(`脚本 ${scriptName} 执行失败 (退出码: ${code})`)
        resolve(false)
      }
    })
    
    child.on('error', (error) => {
      logger.error(`脚本 ${scriptName} 执行错误: ${error.message}`)
      resolve(false)
    })
  })
}

/**
 * 等待任务完成
 */
async function waitForCompletion(maxWaitMinutes: number = 60): Promise<boolean> {
  const maxWaitMs = maxWaitMinutes * 60 * 1000
  const startTime = Date.now()
  const pollInterval = 30000 // 30秒轮询一次
  
  logger.info(`等待任务完成，最长等待 ${maxWaitMinutes} 分钟`)
  
  while (Date.now() - startTime < maxWaitMs) {
    const stats = getTaskQueueStats()
    
    logger.info(`当前状态: 待处理 ${stats.pending}, 生成中 ${stats.generating}, 成功 ${stats.success}, 失败 ${stats.failed}`)
    
    // 如果没有待处理和生成中的任务，说明都完成了
    if (stats.pending === 0 && stats.generating === 0) {
      if (stats.success > 0) {
        logger.success('所有任务已完成生成！')
        return true
      } else if (stats.failed > 0) {
        logger.warn('所有任务都失败了')
        return false
      } else {
        logger.info('没有任务需要处理')
        return true
      }
    }
    
    // 轮询任务状态
    logger.info('轮询任务状态...')
    await runScript('scripts/image-process/scripts/poll-tasks.ts')
    
    // 等待下次轮询
    await delay(pollInterval)
  }
  
  logger.warn(`等待超时 (${maxWaitMinutes} 分钟)，但任务可能仍在进行中`)
  return false
}

/**
 * 主函数：完整处理流程
 */
async function fullProcess(): Promise<void> {
  const startTime = Date.now()
  logger.info('开始完整图片处理流程')
  
  try {
    // 1. 创建任务
    logger.info('=' .repeat(60))
    logger.info('阶段 1: 创建图片生成任务')
    logger.info('=' .repeat(60))
    
    const createSuccess = await runScript('scripts/image-process/scripts/create-tasks.ts')
    if (!createSuccess) {
      throw new Error('创建任务失败')
    }
    
    // 2. 等待任务完成
    logger.info('=' .repeat(60))
    logger.info('阶段 2: 等待任务完成')
    logger.info('=' .repeat(60))
    
    const completionSuccess = await waitForCompletion(60)
    if (!completionSuccess) {
      logger.warn('任务未在预期时间内完成，但将继续尝试下载')
    }
    
    // 3. 下载图片
    logger.info('=' .repeat(60))
    logger.info('阶段 3: 下载生成的图片')
    logger.info('=' .repeat(60))
    
    const downloadSuccess = await runScript('scripts/image-process/scripts/download-images.ts')
    if (!downloadSuccess) {
      logger.warn('下载图片失败，请手动运行 "pnpm image:download"')
    }
    
    // 4. 显示最终状态
    logger.info('=' .repeat(60))
    logger.info('阶段 4: 最终状态')
    logger.info('=' .repeat(60))
    
    await runScript('scripts/image-process/scripts/show-status.ts')
    
    // 5. 输出摘要
    const duration = Date.now() - startTime
    const stats = getTaskQueueStats()
    
    logger.info('=' .repeat(60))
    logger.info('完整处理流程摘要:')
    logger.info(`总处理时间: ${Math.round(duration / 60000)} 分钟`)
    logger.info(`最终状态: 总计 ${stats.total}, 成功 ${stats.success}, 已下载 ${stats.downloaded}, 失败 ${stats.failed}`)
    
    if (stats.downloaded > 0) {
      logger.success('🎉 图片处理流程完成！')
      logger.info('请查看输出目录中的生成图片')
    } else if (stats.success > 0) {
      logger.info('图片生成成功，但下载失败')
      logger.info('请运行 "pnpm image:download" 手动下载')
    } else {
      logger.warn('图片处理流程完成，但没有成功生成图片')
      logger.info('请检查错误日志或运行 "pnpm image:status" 查看详情')
    }
    
    logger.info('=' .repeat(60))
    
  } catch (error) {
    logger.error('完整处理流程失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  fullProcess().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
