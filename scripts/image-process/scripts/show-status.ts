#!/usr/bin/env bun

/**
 * 显示任务状态脚本
 * 查看当前所有任务的状态和统计信息
 */

import { loadTaskQueue, getTaskQueueStats } from './task-queue.js'
import { formatDuration } from './utils.js'
import type { TaskRecord } from './types.js'

/**
 * 格式化任务状态显示
 */
function formatTaskStatus(task: TaskRecord): string {
  const now = Date.now()
  const age = formatDuration(now - task.createdAt)
  const lastChecked = task.lastChecked > 0 ? formatDuration(now - task.lastChecked) + '前' : '从未'
  
  let statusInfo = `${task.status}`
  
  if (task.status === 'GENERATING' && task.estimatedCompletionTime) {
    const remaining = task.estimatedCompletionTime - now
    if (remaining > 0) {
      statusInfo += ` (预计${formatDuration(remaining)}后完成)`
    } else {
      statusInfo += ` (已超过预计时间)`
    }
  }
  
  if (task.status === 'FAILED' && task.errorMessage) {
    statusInfo += ` - ${task.errorMessage}`
  }
  
  if (task.status === 'SUCCESS' && task.resultUrls) {
    statusInfo += ` (${task.resultUrls.length}张图片)`
  }
  
  if (task.status === 'DOWNLOADED' && task.downloadPaths) {
    statusInfo += ` (${task.downloadPaths.length}张已下载)`
  }
  
  return `${task.taskId.substr(0, 8)}... | ${statusInfo} | 创建: ${age}前 | 检查: ${lastChecked} | 重试: ${task.retryCount}`
}

/**
 * 显示任务统计
 */
function showTaskStats(): void {
  const stats = getTaskQueueStats()
  
  console.log('\n' + '='.repeat(80))
  console.log('📊 任务队列统计')
  console.log('='.repeat(80))
  console.log(`总任务数: ${stats.total}`)
  console.log(`待处理: ${stats.pending} | 生成中: ${stats.generating} | 成功: ${stats.success}`)
  console.log(`失败: ${stats.failed} | 已下载: ${stats.downloaded}`)
  
  if (stats.total > 0) {
    const successRate = ((stats.success + stats.downloaded) / stats.total * 100).toFixed(1)
    const completionRate = (stats.downloaded / stats.total * 100).toFixed(1)
    console.log(`成功率: ${successRate}% | 完成率: ${completionRate}%`)
  }
  
  console.log('='.repeat(80))
}

/**
 * 显示任务详情
 */
function showTaskDetails(status?: string, limit: number = 10): void {
  const queue = loadTaskQueue()
  const tasks = Object.values(queue.tasks)
  
  let filteredTasks = tasks
  if (status) {
    filteredTasks = tasks.filter(task => task.status === status.toUpperCase())
  }
  
  // 按创建时间排序（最新的在前）
  filteredTasks.sort((a, b) => b.createdAt - a.createdAt)
  
  if (filteredTasks.length === 0) {
    console.log(`\n没有找到${status ? `状态为 ${status} 的` : ''}任务`)
    return
  }
  
  const displayTasks = filteredTasks.slice(0, limit)
  const title = status ? `📋 ${status.toUpperCase()} 任务详情` : '📋 所有任务详情'
  
  console.log(`\n${title} (显示前${displayTasks.length}个，共${filteredTasks.length}个)`)
  console.log('-'.repeat(120))
  console.log('任务ID      | 状态信息                                    | 时间信息')
  console.log('-'.repeat(120))
  
  for (const task of displayTasks) {
    console.log(formatTaskStatus(task))
  }
  
  if (filteredTasks.length > limit) {
    console.log(`... 还有 ${filteredTasks.length - limit} 个任务未显示`)
  }
  console.log('-'.repeat(120))
}

/**
 * 显示图片信息
 */
function showImageInfo(): void {
  const queue = loadTaskQueue()
  const tasks = Object.values(queue.tasks)
  
  let totalImages = 0
  let successImages = 0
  let downloadedImages = 0
  
  for (const task of tasks) {
    totalImages += task.imageFiles.length
    if (task.status === 'SUCCESS' || task.status === 'DOWNLOADED') {
      successImages += task.resultUrls?.length || 0
    }
    if (task.status === 'DOWNLOADED') {
      downloadedImages += task.downloadPaths?.length || 0
    }
  }
  
  console.log('\n' + '='.repeat(80))
  console.log('🖼️  图片统计')
  console.log('='.repeat(80))
  console.log(`原始图片: ${totalImages}`)
  console.log(`生成成功: ${successImages}`)
  console.log(`已下载: ${downloadedImages}`)
  
  if (totalImages > 0) {
    const generationRate = (successImages / totalImages * 100).toFixed(1)
    const downloadRate = (downloadedImages / totalImages * 100).toFixed(1)
    console.log(`生成率: ${generationRate}% | 下载率: ${downloadRate}%`)
  }
  console.log('='.repeat(80))
}

/**
 * 显示建议的下一步操作
 */
function showNextSteps(): void {
  const stats = getTaskQueueStats()
  
  console.log('\n' + '='.repeat(80))
  console.log('💡 建议的下一步操作')
  console.log('='.repeat(80))
  
  if (stats.total === 0) {
    console.log('• 运行 "pnpm image:create" 创建新的图片生成任务')
  } else if (stats.pending > 0 || stats.generating > 0) {
    console.log('• 运行 "pnpm image:poll" 检查任务进度')
    if (stats.generating > 0) {
      console.log('• 有任务正在生成中，建议等待几分钟后再次轮询')
    }
  } else if (stats.success > 0) {
    console.log('• 运行 "pnpm image:download" 下载生成的图片')
  } else if (stats.failed > 0) {
    console.log('• 运行 "pnpm image:retry" 重试失败的任务')
    console.log('• 或运行 "pnpm image:reset" 重置所有任务')
  } else if (stats.downloaded > 0) {
    console.log('• 所有任务已完成！图片已下载到输出目录')
    console.log('• 运行 "pnpm image:clean" 清理已完成的任务')
  }
  
  console.log('• 运行 "pnpm image:report" 生成详细报告')
  console.log('='.repeat(80))
}

/**
 * 主函数
 */
function showStatus(): void {
  const args = process.argv.slice(2)
  const statusFilter = args.find(arg => !arg.startsWith('--'))
  const limitArg = args.find(arg => arg.startsWith('--limit='))
  const limit = limitArg ? parseInt(limitArg.split('=')[1]) : 10
  
  console.log('🔍 图片处理任务状态')
  
  // 显示统计信息
  showTaskStats()
  
  // 显示图片信息
  showImageInfo()
  
  // 显示任务详情
  if (statusFilter) {
    showTaskDetails(statusFilter, limit)
  } else {
    // 显示各种状态的任务概览
    const statuses = ['PENDING', 'GENERATING', 'SUCCESS', 'FAILED', 'DOWNLOADED']
    for (const status of statuses) {
      const queue = loadTaskQueue()
      const count = Object.values(queue.tasks).filter(task => task.status === status).length
      if (count > 0) {
        showTaskDetails(status, Math.min(5, limit))
      }
    }
  }
  
  // 显示建议操作
  showNextSteps()
  
  console.log('\n使用方法:')
  console.log('  pnpm image:status [状态] [--limit=数量]')
  console.log('  状态可选: pending, generating, success, failed, downloaded')
  console.log('  例如: pnpm image:status generating --limit=20')
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  showStatus()
}
