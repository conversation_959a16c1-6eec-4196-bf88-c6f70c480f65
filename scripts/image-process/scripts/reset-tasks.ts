#!/usr/bin/env bun

/**
 * 重置任务脚本
 * 重置失败的任务或清空所有任务
 */

import { loadTaskQueue, saveTaskQueue, resetFailedTasks } from './task-queue.js'
import { Logger } from './utils.js'

const logger = new Logger('reset-tasks')

/**
 * 重置所有任务
 */
function resetAllTasks(): void {
  const queue = loadTaskQueue()
  const beforeCount = Object.keys(queue.tasks).length
  
  // 清空所有任务
  queue.tasks = {}
  saveTaskQueue(queue)
  
  logger.success(`已清空所有任务 (${beforeCount} 个)`)
}

/**
 * 主函数
 */
function resetTasks(): void {
  const args = process.argv.slice(2)
  const isResetAll = args.includes('--all') || args.includes('all')
  
  console.log('🔄 重置图片处理任务')
  
  if (isResetAll) {
    console.log('⚠️  即将清空所有任务，包括已完成的任务')
    console.log('确认请输入 "yes"，取消请按 Ctrl+C')
    
    // 简单的确认机制
    const confirm = prompt('确认清空所有任务? (yes/no): ')
    if (confirm?.toLowerCase() === 'yes') {
      resetAllTasks()
    } else {
      console.log('操作已取消')
    }
  } else {
    const resetCount = resetFailedTasks()
    if (resetCount > 0) {
      logger.success(`已重置 ${resetCount} 个失败任务`)
      logger.info('这些任务将重新进入待处理状态')
      logger.info('运行 "pnpm image:poll" 重新开始处理')
    } else {
      logger.info('没有找到需要重置的失败任务')
    }
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  resetTasks()
}
