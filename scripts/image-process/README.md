# 图片处理模块

这个目录包含了所有与AI图片生成和处理相关的脚本、数据和配置文件。

## 📁 目录结构

```
image-process/
├── scripts/                    # 脚本文件
│   ├── export-book-metadata.ts    # 从数据库导出书籍元数据
│   ├── create-tasks.ts            # 创建图片生成任务
│   ├── poll-tasks.ts              # 轮询任务状态
│   ├── download-images.ts         # 下载生成的图片
│   ├── show-status.ts             # 显示任务状态
│   ├── reset-tasks.ts             # 重置任务
│   ├── clean-completed.ts         # 清理已完成任务
│   ├── full-process.ts            # 一键完整处理
│   ├── types.ts                   # 类型定义
│   ├── task-queue.ts              # 任务队列管理
│   ├── utils.ts                   # 通用工具函数
│   ├── batch-image-processing.ts  # 旧版批量处理脚本（兼容性）
│   └── test-api-simple.ts         # API测试脚本
├── data/                       # 数据文件
│   ├── book-metadata.json         # 书籍元数据（从数据库导出）
│   ├── book-metadata-stats.json   # 书籍元数据统计报告
│   ├── tasks-queue.json           # 任务队列状态
│   └── reports/                   # 处理报告目录
├── logs/                       # 日志文件
│   ├── create-tasks-*.log         # 创建任务日志
│   ├── poll-tasks-*.log           # 轮询日志
│   ├── download-images-*.log      # 下载日志
│   └── processing-*.log           # 其他处理日志
├── input/                      # 输入图片（如果需要）
│   └── *.jpg                     # 原始图片文件
├── output/                     # 生成的图片
│   └── *.jpg                     # AI生成的图片
└── README.md                   # 本说明文件
```

## 🚀 使用方法

### 🎯 分离式处理（推荐）

**完整流程**：

```bash
# 0. 先导出书籍元数据（必须先执行）
pnpm image:export

# 方式1：一键处理（自动执行完整流程）
pnpm image:process

# 方式2：分步处理（更灵活的控制）
pnpm image:create    # 1. 创建所有任务
pnpm image:poll      # 2. 轮询任务状态
pnpm image:download  # 3. 下载生成的图片
```

**状态管理**：

```bash
# 查看当前状态
pnpm image:status

# 查看特定状态的任务
pnpm image:status generating
pnpm image:status success
pnpm image:status failed

# 重置失败的任务
pnpm image:reset

# 清理已完成的任务
pnpm image:clean
```

### 🔧 兼容性命令

```bash
# 旧版批量处理（兼容性）
pnpm image:legacy

# 导出书籍元数据
pnpm image:export

# 测试API连接
pnpm image:test
```

## ⚙️ 配置说明

### 分离式处理配置

- **批次大小**: 每批最多5张图片（API限制）
- **并发轮询**: 最多3个任务并发轮询
- **并发下载**: 最多3个图片并发下载
- **轮询间隔**:
  - 新任务: 30秒
  - 生成中: 60秒
  - 接近完成: 30秒
- **任务超时**: 60分钟
- **最大重试**: 3次
- **图片尺寸**: 2:3 比例

### 智能轮询策略

- **动态间隔**: 根据任务状态和年龄调整轮询频率
- **超时检测**: 自动标记超时任务为失败
- **错误重试**: 指数退避重试机制
- **并发控制**: 避免API限制和资源占用

### 目录配置

所有路径都相对于 `scripts/image-process/` 目录：

- 输入目录: `input/`
- 输出目录: `output/`
- 日志目录: `logs/`
- 数据目录: `data/`

## 📊 分离式处理流程

### 阶段0: 导出元数据 (`pnpm image:export`)

1. **数据库查询**: 从数据库查询所有书籍封面信息
2. **数据处理**: 提取图片文件名、书名、作者、图片URL
3. **生成文件**: 保存到 `data/book-metadata.json`
4. **统计报告**: 生成 `data/book-metadata-stats.json`

### 阶段1: 创建任务 (`pnpm image:create`)

1. **扫描元数据**: 从 `data/book-metadata.json` 读取书籍信息
2. **单张处理**: 每张图片独立创建任务（避免批量问题）
3. **快速创建**: 并发创建所有API任务（2-3分钟完成）
4. **保存状态**: 任务信息保存到 `data/tasks-queue.json`

### 阶段2: 轮询状态 (`pnpm image:poll`)

1. **智能轮询**: 根据任务状态动态调整轮询频率
2. **并发检查**: 最多3个任务并发轮询
3. **状态更新**: 实时更新任务状态和进度
4. **超时处理**: 自动处理超时任务

### 阶段3: 下载图片 (`pnpm image:download`)

1. **筛选成功**: 只下载成功生成的图片
2. **并发下载**: 最多3个图片并发下载
3. **重试机制**: 下载失败自动重试
4. **文件命名**: 智能命名避免冲突

## 🔧 故障排除

### 常见问题

1. **API密钥错误**

   ```bash
   export GPT4O_API_KEY=your_actual_api_key
   ```

2. **权限问题**

   ```bash
   chmod +x image-process/scripts/*.ts
   ```

3. **目录不存在**
   ```bash
   mkdir -p image-process/{input,output,logs,data}
   ```

### 日志查看

- 处理日志: `logs/processing-*.log`
- 错误日志: `logs/error-*.log`
- 处理报告: `logs/report-*.json`

## 📈 性能优化

- **批量处理**: 5张图片一次API调用，减少80%请求数
- **并发控制**: 最多2个批次并发，避免API限制
- **智能重试**: 失败任务自动重试机制
- **断点续传**: 支持中断后继续处理

## 🔗 相关文件

- 主项目配置: `../../package.json`
- 环境变量: `../../.env`
- 数据库配置: `../../prisma/schema.prisma`
