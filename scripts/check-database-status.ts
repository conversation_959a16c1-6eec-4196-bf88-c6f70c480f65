/**
 * 检查数据库导入状态
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkDatabaseStatus() {
  try {
    console.log('🔍 检查数据库导入状态...\n')

    // 检查各表的记录数量
    const authorsCount = await prisma.authors.count()
    const categoriesCount = await prisma.categories.count()
    const booksCount = await prisma.books.count()
    const bookTranslationsCount = await prisma.book_translations.count()
    const bookChaptersCount = await prisma.book_chapters.count()
    const bookAuthorsCount = await prisma.book_authors.count()
    const bookCategoriesCount = await prisma.book_categories.count()
    const bookCoversCount = await prisma.book_covers.count()
    console.log('📊 数据库表记录统计:')
    console.log(`  - authors: ${authorsCount} 条`)
    console.log(`  - categories: ${categoriesCount} 条`)
    console.log(`  - books: ${booksCount} 条`)
    console.log(`  - book_translations: ${bookTranslationsCount} 条`)
    console.log(`  - book_chapters: ${bookChaptersCount} 条`)
    console.log(`  - book_authors: ${bookAuthorsCount} 条`)
    console.log(`  - book_categories: ${bookCategoriesCount} 条`)
    console.log(`  - book_covers: ${bookCoversCount} 条`)

    console.log('\n📖 检查具体的书籍记录:')

    // 检查前10本书籍
    const books = await prisma.books.findMany({
      take: 10,
      include: {
        book_translations: true,
        book_chapters: true,
        book_authors: {
          include: {
            author: {
              include: {
                author_translations: true
              }
            }
          }
        },
        book_categories: {
          include: {
            category: {
              include: {
                category_translations: true
              }
            }
          }
        }
      }
    })

    books.forEach((book, index) => {
      console.log(`\n${index + 1}. 书籍 ID: ${book.rawid}`)
      console.log(`   - 翻译记录: ${book.book_translations.length} 条`)
      console.log(`   - 章节记录: ${book.book_chapters.length} 条`)
      console.log(`   - 作者关联: ${book.book_authors.length} 条`)
      console.log(`   - 分类关联: ${book.book_categories.length} 条`)

      if (book.book_translations.length > 0) {
        console.log(`   - 标题: ${book.book_translations[0].title}`)
      }

      if (book.book_authors.length > 0) {
        const authorName = book.book_authors[0].author.author_translations[0]?.name || 'Unknown'
        console.log(`   - 作者: ${authorName}`)
      }
    })

    // 检查章节数据
    console.log('\n📚 检查章节数据:')
    const chapters = await prisma.book_chapters.findMany({
      take: 5,
      include: {
        book: {
          include: {
            book_translations: true
          }
        }
      }
    })

    chapters.forEach((chapter, index) => {
      console.log(`\n${index + 1}. 章节 - 书籍 ID: ${chapter.book.rawid}`)
      console.log(`   - 书籍标题: ${chapter.book.book_translations[0]?.title || 'Unknown'}`)
      console.log(`   - 章节内容长度: ${JSON.stringify(chapter.content).length} 字符`)

      // 尝试解析章节内容
      try {
        const content = typeof chapter.content === 'string' ? JSON.parse(chapter.content) : chapter.content
        if (Array.isArray(content)) {
          console.log(`   - 章节数量: ${content.length} 个`)
          if (content.length > 0) {
            console.log(`   - 第一章标题: ${content[0].title || 'Unknown'}`)
          }
        }
      } catch (error) {
        console.log(`   - 章节内容解析失败: ${(error as any).message}`)
      }
    })

    // 检查最近的导入记录
    console.log('\n🕒 检查最近导入的记录:')
    const recentBooks = await prisma.books.findMany({
      orderBy: {
        created_at: 'desc'
      },
      take: 5,
      include: {
        book_translations: true
      }
    })

    recentBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.rawid} - ${book.book_translations[0]?.title || 'No Title'} (${book.created_at})`)
    })

  } catch (error) {
    console.error('❌ 检查数据库状态失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabaseStatus()
