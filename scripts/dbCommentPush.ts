/**
 * 这个脚本会将Prisma Schema中的注释同步到MySQL数据库
 * 是原有MySQL到Prisma同步脚本的反向操作
 */

import * as mysql from 'mysql2/promise'
import * as fs from 'fs'
import dotenv from 'dotenv'
import path from 'path'

dotenv.config()

interface FieldInfo {
  fieldName: string
  dbColumn: string
  comment: string
}

interface ModelInfo {
  modelName: string
  tableName: string
  comment: string | null
  fields: FieldInfo[]
}

interface ColumnDefinition {
  Field: string
  Type: string
  Null: string
  Key: string
  Default: string | null
  Extra: string
}

async function syncPrismaCommentsToMySQL() {
  console.log('开始将Prisma Schema中的注释同步到MySQL...')

  // 1. 读取Prisma Schema文件
  const schemaPath = path.resolve(__dirname, '../prisma/schema.prisma')
  const schema = fs.readFileSync(schemaPath, 'utf8')

  // 2. 解析Schema中的模型和注释
  const models = parsePrismaSchema(schema)
  console.log(`成功解析 ${models.length} 个模型`)

  try {
    // 3. 连接MySQL数据库
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    })

    console.log('成功连接到MySQL数据库')

    // 4. 同步注释到MySQL
    let tablesUpdated = 0
    let columnsUpdated = 0

    for (const model of models) {
      // 4.1 更新表注释
      if (model.comment) {
        await connection.query(`ALTER TABLE \`${model.tableName}\` COMMENT = ?`, [model.comment])
        tablesUpdated++
        console.log(`更新表注释: ${model.tableName}`)
      }

      // 4.2 更新列注释
      for (const field of model.fields) {
        if (field.comment) {
          // 获取列的当前定义
          const [columns] = (await connection.query(
            `SHOW COLUMNS FROM \`${model.tableName}\` LIKE ?`,
            [field.dbColumn]
          )) as [ColumnDefinition[], any]

          if (columns.length > 0) {
            const column = columns[0]

            // 构建MODIFY COLUMN语句
            let nullStatement = column.Null === 'YES' ? 'NULL' : 'NOT NULL'
            let defaultStatement =
              column.Default === null ? '' : ` DEFAULT ${mysql.escape(column.Default)}`
            let extraStatement = column.Extra ? ` ${column.Extra}` : ''

            await connection.query(
              `ALTER TABLE \`${model.tableName}\` MODIFY COLUMN \`${field.dbColumn}\` ${column.Type} ${nullStatement}${defaultStatement}${extraStatement} COMMENT ?`,
              [field.comment]
            )

            columnsUpdated++
            console.log(`更新列注释: ${model.tableName}.${field.dbColumn}`)
          } else {
            console.warn(`警告: 未找到列 ${model.tableName}.${field.dbColumn}`)
          }
        }
      }
    }

    console.log(`完成注释同步! 更新了 ${tablesUpdated} 个表注释和 ${columnsUpdated} 个列注释`)
    await connection.end()
  } catch (error) {
    console.error('同步注释时出错:', error)
    throw error
  }
}

function parsePrismaSchema(schema: string): ModelInfo[] {
  const models: ModelInfo[] = []
  const lines = schema.split('\n')

  let currentModel: ModelInfo | null = null
  let inModelBlock = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // 检测模型定义开始
    const modelMatch = line.match(/^model\s+(\w+)\s+{/)
    if (modelMatch) {
      currentModel = {
        modelName: modelMatch[1],
        tableName: modelMatch[1], // 默认使用模型名作为表名
        comment: null,
        fields: []
      }
      inModelBlock = true

      // 检查表映射
      for (let j = i + 1; j < lines.length; j++) {
        const mapLine = lines[j].trim()
        const mapMatch = mapLine.match(/@map\s*\(\s*["']([^"']+)["']\s*\)/)
        if (mapMatch) {
          currentModel.tableName = mapMatch[1]
          break
        }
        // 如果遇到字段定义或块结束，停止寻找映射
        if (mapLine === '}' || mapLine.match(/^\w+\s/)) break
      }

      // 检查表注释
      for (let j = i + 1; j < i + 5; j++) {
        // 仅检查接下来的几行
        if (j >= lines.length) break
        const commentLine = lines[j].trim()
        const commentMatch = commentLine.match(/\/\/\/\s*@comment\s+(.+)$/)
        if (commentMatch) {
          currentModel.comment = commentMatch[1]
          break
        }
        // 如果遇到字段定义，停止寻找注释
        if (commentLine.match(/^\w+\s/)) break
      }

      continue
    }

    // 检测模型定义结束
    if (inModelBlock && line === '}') {
      if (currentModel) {
        models.push(currentModel)
      }
      inModelBlock = false
      currentModel = null
      continue
    }

    // 处理字段和注释
    if (inModelBlock && currentModel) {
      // 匹配字段定义（排除注释行和空行）
      const fieldMatch = line.match(/^(\w+)\s+.+/)
      if (fieldMatch && !line.startsWith('//') && line !== '') {
        const fieldName = fieldMatch[1]
        let dbColumn = fieldName // 默认使用字段名作为列名
        let comment = null

        // 检查列映射
        const dbNameMatch = line.match(/@map\s*\(\s*["']([^"']+)["']\s*\)/)
        if (dbNameMatch) {
          dbColumn = dbNameMatch[1]
        }

        // 检查行内注释
        const inlineCommentMatch = line.match(/\/\/\/\s*@comment\s+(.+)$/)
        if (inlineCommentMatch) {
          comment = inlineCommentMatch[1]
        } else {
          // 检查下一行是否有注释
          if (i + 1 < lines.length) {
            const nextLine = lines[i + 1].trim()
            const nextLineCommentMatch = nextLine.match(/\/\/\/\s*@comment\s+(.+)$/)
            if (nextLineCommentMatch) {
              comment = nextLineCommentMatch[1]
            }
          }
        }

        if (comment) {
          currentModel.fields.push({
            fieldName,
            dbColumn,
            comment
          })
        }
      }
    }
  }

  return models
}

// 执行同步
syncPrismaCommentsToMySQL().catch(console.error)
