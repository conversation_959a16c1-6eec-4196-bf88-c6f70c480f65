/**
 * 调试数据库状态
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function debugDatabase() {
  try {
    console.log('🔍 调试数据库状态...\n')

    // 检查数据库连接
    console.log('📡 测试数据库连接...')
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 数据库连接正常\n')

    // 检查各表的记录数量
    const authorsCount = await prisma.authors.count()
    const categoriesCount = await prisma.categories.count()
    const booksCount = await prisma.books.count()
    const bookTranslationsCount = await prisma.book_translations.count()
    const bookChaptersCount = await prisma.book_chapters.count()
    const bookAuthorsCount = await prisma.book_authors.count()
    const bookCategoriesCount = await prisma.book_categories.count()
    const bookCoversCount = await prisma.book_covers.count()

    console.log('📊 数据库表记录统计:')
    console.log(`  - authors: ${authorsCount} 条`)
    console.log(`  - categories: ${categoriesCount} 条`)
    console.log(`  - books: ${booksCount} 条`)
    console.log(`  - book_translations: ${bookTranslationsCount} 条`)
    console.log(`  - book_chapters: ${bookChaptersCount} 条`)
    console.log(`  - book_authors: ${bookAuthorsCount} 条`)
    console.log(`  - book_categories: ${bookCategoriesCount} 条`)
    console.log(`  - book_covers: ${bookCoversCount} 条`)

    // 检查 rawid 字段
    console.log('\n🔍 检查 rawid 字段:')
    const booksWithRawid = await prisma.books.findMany({
      where: {
        rawid: {
          not: null
        }
      },
      select: {
        id: true,
        rawid: true
      },
      take: 10
    })

    console.log(`有 rawid 的书籍数量: ${booksWithRawid.length}`)
    booksWithRawid.forEach((book, index) => {
      console.log(`${index + 1}. ID: ${book.id}, rawid: ${book.rawid}`)
    })

    // 检查特定的书籍ID
    console.log('\n🔍 检查特定书籍ID:')
    const testIds = ['15M000001', '15M000502', '15M000503', '15M001000', '15M005000']
    
    for (const testId of testIds) {
      const book = await prisma.books.findFirst({
        where: {
          rawid: testId
        },
        include: {
          book_translations: true
        }
      })
      
      if (book) {
        console.log(`✅ 找到书籍 ${testId}: ${book.book_translations[0]?.title || 'No Title'}`)
      } else {
        console.log(`❌ 未找到书籍 ${testId}`)
      }
    }

    // 检查最近创建的书籍
    console.log('\n🕒 检查最近创建的书籍:')
    const recentBooks = await prisma.books.findMany({
      orderBy: {
        created_at: 'desc'
      },
      take: 20,
      include: {
        book_translations: true
      }
    })

    recentBooks.forEach((book, index) => {
      console.log(`${index + 1}. ID: ${book.id}, rawid: ${book.rawid}, 标题: ${book.book_translations[0]?.title || 'No Title'}, 创建时间: ${book.created_at}`)
    })

    // 检查数据库事务状态
    console.log('\n🔄 检查数据库事务状态:')
    const result = await prisma.$queryRaw`SHOW PROCESSLIST` as any[]
    console.log(`当前活跃连接数: ${result.length}`)

  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugDatabase()
