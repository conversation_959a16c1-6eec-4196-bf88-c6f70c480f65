/**
 * 优化版数据导入脚本
 * 针对远程数据库环境进行网络优化
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { PrismaClient } from '@prisma/client'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 环境检测
function detectEnvironment() {
  const dbUrl = process.env.DATABASE_URL || ''
  const isLocal = /192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|localhost|127\.0\.0\.1/.test(dbUrl)

  return {
    isLocal,
    batchSize: isLocal ? 100 : 20,        // 本地大批次，远程小批次
    timeout: isLocal ? 30000 : 120000,    // 本地30秒，远程2分钟
    maxRetries: isLocal ? 3 : 5,          // 远程更多重试
    connectionLimit: isLocal ? 20 : 5      // 远程更少连接
  }
}

const config = detectEnvironment()
console.log(`🌐 检测到${config.isLocal ? '本地' : '远程'}数据库环境`)
console.log(`⚙️  配置: 批次=${config.batchSize}, 超时=${config.timeout}ms, 重试=${config.maxRetries}次`)

// 创建优化的 Prisma 客户端
const prisma = new PrismaClient({
  log: config.isLocal ? ['warn', 'error'] : ['error'],
  datasources: {
    db: { url: process.env.DATABASE_URL }
  }
})

// 重试机制
async function withRetry<T>(operation: () => Promise<T>, context = ''): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      if (attempt === config.maxRetries) {
        console.error(`❌ ${context} 最终失败:`, lastError.message)
        throw lastError
      }

      const delay = Math.min(1000 * Math.pow(1.5, attempt), 8000)
      console.log(`⚠️  ${context} 第${attempt}次失败，${delay}ms后重试: ${lastError.message}`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError!
}

// 批量处理工具
function createBatches<T>(items: T[]): T[][] {
  const batches: T[][] = []
  for (let i = 0; i < items.length; i += config.batchSize) {
    batches.push(items.slice(i, i + config.batchSize))
  }
  return batches
}

// 进度报告
function reportProgress(current: number, total: number, operation: string, startTime: number) {
  const percentage = Math.min(100, (current / total) * 100).toFixed(1)
  const elapsed = (Date.now() - startTime) / 1000
  const rate = current / elapsed
  const remaining = Math.max(0, total - current)
  const eta = remaining > 0 ? remaining / rate : 0

  console.log(`📊 ${operation}: ${current}/${total} (${percentage}%) | ${rate.toFixed(1)}条/秒 | 剩余${Math.round(eta)}秒`)
}

// 工具函数 - 与原始脚本保持完全一致
function cleanString(str?: string | null | undefined): string | null {
  if (!str || typeof str !== 'string') return null
  return str.trim()
}

// CDN基础URL - 与原始脚本保持一致
const CDN_BASE_URL = 'https://cdn.15minutes.ai'

function buildCdnUrl(filePath?: string): string | null {
  if (!filePath) return null

  // 清理路径：处理正斜杠和反斜杠，确保路径格式一致
  let cleanPath = filePath
    .replace(/^[\/\\]+/, '') // 去除前导的正斜杠或反斜杠
    .replace(/\\/g, '/') // 将所有反斜杠转换为正斜杠
    .trim()

  // 如果路径为空，返回null
  if (!cleanPath) return null

  // 构建完整的CDN URL
  return `${CDN_BASE_URL}/${cleanPath}`
}

// 补充缺失的工具函数
function extractYearFromDate(dateStr?: string): number | null {
  if (!dateStr) return null

  const match = dateStr.match(/(\d{4})/)
  if (match && match[1]) {
    return parseInt(match[1], 10)
  }

  return null
}

function validateRequiredFields(data: any, fields: string[]): string[] {
  const missingFields: string[] = []

  for (const field of fields) {
    if (!data[field]) {
      missingFields.push(field)
    }
  }

  return missingFields
}

// 数据类型定义 - 与原始脚本保持完全一致
interface AuthorData {
  AuthorID: string
  Author: string
  BookID: string // 补充缺失的字段
  'Author Avatar'?: string
  Website?: string
  'X Account'?: string
  'Author Desc'?: string
  Born?: string
}

interface CategoryData {
  GenreID: string
  Genre: string
  BookID: string // 补充缺失的字段
  GenreDesc?: string
}

interface MainContentData {
  BookID: string
  'book title': string
  'book subtitle'?: string
  Author: string
  Genre: string
  'Best Quotes'?: string
  'Review Summary'?: string
  'PDF file'?: string
  'EUPB FIle'?: string
  'Rate Score'?: number
  Ratings?: number
  'Book Cover'?: string
  BookSummary?: string
}

// 补充缺失的接口定义
interface BookDetailData {
  BookID: string
  'brief analysis'?: string
  Author?: string
  Content?: string
  Genre?: string
  Binding?: string
  Publisher?: string
  ASIN?: string
  ISBN?: string
  ISBN13?: string
  'Publish Date'?: string
  Language?: string
  File?: string
}



interface ImportResult {
  success: number
  failed: number
  skipped: number
  errors: Array<{ id: string, error: string }>
}







// 优化的作者导入 - 添加数据验证和错误处理
async function importAuthorsOptimized(authorData: AuthorData[]): Promise<ImportResult> {
  console.log(`👥 开始导入作者数据: ${authorData.length} 条`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const batches = createBatches(authorData)
  const startTime = Date.now()
  let processed = 0

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    try {
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          // 1. 批量查询现有作者
          const existing = await tx.authors.findMany({
            where: { rawid: { in: batch.map(a => a.AuthorID) } } as any,
            select: { id: true, rawid: true }
          })

          const existingAuthorsMap = new Map(existing.map(a => [a.rawid, a]))

          // 2. 数据验证和准备
          const authorsToCreate = []
          const authorsToUpdate = []

          for (const author of batch) {
            // 验证必填字段
            const missingFields = validateRequiredFields(author, ['AuthorID', 'Author'])
            if (missingFields.length > 0) {
              result.failed++
              result.errors.push({ id: author.AuthorID, error: `缺少必填字段: ${missingFields.join(', ')}` })
              continue
            }

            const existingAuthor = existingAuthorsMap.get(author.AuthorID)
            const authorData = {
              rawid: author.AuthorID,
              avatar_url: buildCdnUrl(author['Author Avatar']),
              website: cleanString(author.Website),
              twitter_account: cleanString(author['X Account']),
              born: cleanString(author.Born)
            }

            if (!existingAuthor) {
              authorsToCreate.push(authorData)
            } else {
              authorsToUpdate.push({
                id: existingAuthor.id,
                data: authorData
              })
            }
          }

          // 3. 批量创建新作者
          const createdAuthors = []
          if (authorsToCreate.length > 0) {
            await tx.authors.createMany({ data: authorsToCreate as any, skipDuplicates: true })

            // 查询刚创建的作者获取ID
            const newlyCreatedAuthors = await tx.authors.findMany({
              where: {
                rawid: { in: authorsToCreate.map(author => author.rawid) }
              } as any,
              select: { id: true, rawid: true }
            })
            createdAuthors.push(...newlyCreatedAuthors)
          }

          // 4. 批量更新现有作者
          const updatedAuthors = []
          for (const updateItem of authorsToUpdate) {
            await tx.authors.update({
              where: { id: updateItem.id },
              data: updateItem.data as any
            })
            updatedAuthors.push({ id: updateItem.id, rawid: updateItem.data.rawid })
          }

          // 5. 合并所有作者记录（新创建的 + 更新的）
          const allAuthorRecords = [
            ...createdAuthors,
            ...updatedAuthors
          ]

          const authorIdMap = new Map(allAuthorRecords.map(a => [a.rawid as string, a.id]))

          // 6. 批量处理翻译
          for (const author of batch) {
            const authorId = authorIdMap.get(author.AuthorID)
            if (authorId) {
              await tx.author_translations.upsert({
                where: {
                  author_id_language_id: {
                    author_id: authorId,
                    language_id: 'en'
                  }
                },
                update: {
                  name: author.Author,
                  biography: cleanString(author['Author Desc'])
                },
                create: {
                  author_id: authorId,
                  language_id: 'en',
                  name: author.Author,
                  biography: cleanString(author['Author Desc']),
                  is_default: 1
                }
              })
            }
          }

          // 更新成功计数
          result.success += batch.length - result.errors.filter(e =>
            batch.some(author => author.AuthorID === e.id)
          ).length
        }, { timeout: config.timeout })
      }, `作者批次 ${i + 1}/${batches.length}`)
    } catch (error) {
      // 批量失败时回退到逐条处理
      console.warn(`第 ${i + 1} 批作者数据批量处理失败，回退到逐条处理: ${error}`)

      for (const author of batch) {
        try {
          await importSingleAuthor(author)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: author.AuthorID, error: errorMessage })
          console.error(`导入作者失败: ${author.AuthorID} (${author.Author})`, singleError)
        }
      }
    }

    processed += batch.length
    if (i % 5 === 0 || i === batches.length - 1) {
      reportProgress(processed, authorData.length, '作者导入', startTime)
    }
  }

  console.log(`✅ 作者导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条作者导入的回退函数
async function importSingleAuthor(author: AuthorData): Promise<void> {
  await prisma.$transaction(async (tx) => {
    let authorRecord = await tx.authors.findFirst({
      where: { rawid: author.AuthorID } as any
    })

    if (!authorRecord) {
      authorRecord = await tx.authors.create({
        data: {
          rawid: author.AuthorID,
          avatar_url: buildCdnUrl(author['Author Avatar']),
          website: cleanString(author.Website),
          twitter_account: cleanString(author['X Account']),
          born: cleanString(author.Born)
        } as any
      })
    } else {
      authorRecord = await tx.authors.update({
        where: { id: authorRecord.id },
        data: {
          rawid: author.AuthorID,
          avatar_url: buildCdnUrl(author['Author Avatar']),
          website: cleanString(author.Website),
          twitter_account: cleanString(author['X Account']),
          born: cleanString(author.Born)
        } as any
      })
    }

    await tx.author_translations.upsert({
      where: {
        author_id_language_id: {
          author_id: authorRecord.id,
          language_id: 'en'
        }
      },
      update: {
        name: author.Author,
        biography: cleanString(author['Author Desc'])
      },
      create: {
        author_id: authorRecord.id,
        language_id: 'en',
        name: author.Author,
        biography: cleanString(author['Author Desc']),
        is_default: 1
      }
    })
  })
}

// 优化的分类导入 - 添加数据验证和错误处理
async function importCategoriesOptimized(categoryData: CategoryData[]): Promise<ImportResult> {
  console.log(`📂 开始导入分类数据: ${categoryData.length} 条`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const batches = createBatches(categoryData)
  const startTime = Date.now()
  let processed = 0

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    try {
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          // 1. 批量查询现有分类
          const existing = await tx.categories.findMany({
            where: { rawid: { in: batch.map(c => c.GenreID) } } as any,
            select: { id: true, rawid: true }
          })

          const existingCategoriesMap = new Map(existing.map(c => [c.rawid, c]))

          // 2. 数据验证和准备
          const categoriesToCreate = []
          const categoriesToUpdate = []

          for (const category of batch) {
            // 验证必填字段
            const missingFields = validateRequiredFields(category, ['GenreID', 'Genre'])
            if (missingFields.length > 0) {
              result.failed++
              result.errors.push({ id: category.GenreID, error: `缺少必填字段: ${missingFields.join(', ')}` })
              continue
            }

            const existingCategory = existingCategoriesMap.get(category.GenreID)
            const categoryData = { rawid: category.GenreID }

            if (!existingCategory) {
              categoriesToCreate.push(categoryData)
            } else {
              categoriesToUpdate.push({
                id: existingCategory.id,
                data: categoryData
              })
            }
          }

          // 3. 批量创建新分类
          const createdCategories = []
          if (categoriesToCreate.length > 0) {
            await tx.categories.createMany({ data: categoriesToCreate as any, skipDuplicates: true })

            const newlyCreatedCategories = await tx.categories.findMany({
              where: {
                rawid: { in: categoriesToCreate.map(category => category.rawid) }
              } as any,
              select: { id: true, rawid: true }
            })
            createdCategories.push(...newlyCreatedCategories)
          }

          // 4. 批量更新现有分类
          const updatedCategories = []
          for (const updateItem of categoriesToUpdate) {
            await tx.categories.update({
              where: { id: updateItem.id },
              data: updateItem.data as any
            })
            updatedCategories.push({ id: updateItem.id, rawid: updateItem.data.rawid })
          }

          // 5. 合并所有分类记录
          const allCategoryRecords = [
            ...createdCategories,
            ...updatedCategories
          ]

          const categoryIdMap = new Map(allCategoryRecords.map(c => [c.rawid as string, c.id]))

          // 6. 批量处理翻译
          for (const category of batch) {
            const categoryId = categoryIdMap.get(category.GenreID)
            if (categoryId) {
              await tx.category_translations.upsert({
                where: {
                  category_id_language_id: {
                    category_id: categoryId,
                    language_id: 'en'
                  }
                },
                update: {
                  name: category.Genre,
                  description: cleanString(category.GenreDesc)
                },
                create: {
                  category_id: categoryId,
                  language_id: 'en',
                  name: category.Genre,
                  description: cleanString(category.GenreDesc),
                  is_default: 1
                }
              })
            }
          }

          // 更新成功计数
          result.success += batch.length - result.errors.filter(e =>
            batch.some(category => category.GenreID === e.id)
          ).length
        }, { timeout: config.timeout })
      }, `分类批次 ${i + 1}/${batches.length}`)
    } catch (error) {
      // 批量失败时回退到逐条处理
      console.warn(`第 ${i + 1} 批分类数据批量处理失败，回退到逐条处理: ${error}`)

      for (const category of batch) {
        try {
          await importSingleCategory(category)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: category.GenreID, error: errorMessage })
          console.error(`导入分类失败: ${category.GenreID} (${category.Genre})`, singleError)
        }
      }
    }

    processed += batch.length
    if (i % 5 === 0 || i === batches.length - 1) {
      reportProgress(processed, categoryData.length, '分类导入', startTime)
    }
  }

  console.log(`✅ 分类导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条分类导入的回退函数
async function importSingleCategory(category: CategoryData): Promise<void> {
  await prisma.$transaction(async (tx) => {
    let categoryRecord = await tx.categories.findFirst({
      where: { rawid: category.GenreID } as any
    })

    if (!categoryRecord) {
      categoryRecord = await tx.categories.create({
        data: { rawid: category.GenreID } as any
      })
    } else {
      categoryRecord = await tx.categories.update({
        where: { id: categoryRecord.id },
        data: { rawid: category.GenreID } as any
      })
    }

    await tx.category_translations.upsert({
      where: {
        category_id_language_id: {
          category_id: categoryRecord.id,
          language_id: 'en'
        }
      },
      update: {
        name: category.Genre,
        description: cleanString(category.GenreDesc)
      },
      create: {
        category_id: categoryRecord.id,
        language_id: 'en',
        name: category.Genre,
        description: cleanString(category.GenreDesc),
        is_default: 1
      }
    })
  })
}

// 优化的书籍导入 - 修复封面处理逻辑和数据验证
async function importBooksOptimized(bookData: MainContentData[]): Promise<ImportResult> {
  console.log(`📚 开始导入书籍数据: ${bookData.length} 条`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  // 预加载作者和分类映射
  console.log('📋 预加载作者和分类映射...')
  const [authors, categories] = await Promise.all([
    prisma.authors.findMany({ select: { id: true, rawid: true } }),
    prisma.categories.findMany({ select: { id: true, rawid: true } })
  ])

  const authorMap = new Map(authors.filter(a => a.rawid).map(a => [a.rawid as string, a.id]))
  const categoryMap = new Map(categories.filter(c => c.rawid).map(c => [c.rawid as string, c.id]))

  const batches = createBatches(bookData)
  const startTime = Date.now()
  let processed = 0

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    try {
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          // 1. 批量查询现有书籍
          const existingBooks = await tx.books.findMany({
            where: { rawid: { in: batch.map(b => b.BookID) } } as any,
            select: { id: true, rawid: true }
          })

          const existingBookMap = new Map(existingBooks.map(b => [b.rawid, b.id]))

          // 2. 数据验证和准备书籍数据
          const booksToCreate = []
          const booksToUpdate = []

          for (const book of batch) {
            // 验证核心必填字段
            const coreMissingFields = validateRequiredFields(book, ['BookID', 'book title'])
            if (coreMissingFields.length > 0) {
              result.failed++
              result.errors.push({ id: book.BookID, error: `缺少核心必填字段: ${coreMissingFields.join(', ')}` })
              continue
            }

            // 检查可选字段，给出警告但不阻止导入
            const optionalMissingFields = validateRequiredFields(book, ['Author', 'Genre'])
            if (optionalMissingFields.length > 0) {
              console.warn(`书籍 ${book.BookID} (${book['book title']}) 缺少字段: ${optionalMissingFields.join(', ')}，将使用默认值`)
            }

            const bookData = {
              rawid: book.BookID,
              pdf_url: buildCdnUrl(book['PDF file']),
              epub_url: buildCdnUrl(book['EUPB FIle']),
              is_published: 1,
              rate_score: book['Rate Score'] ? Number(book['Rate Score']) : null,
              total_ratings: book.Ratings ? Number(book.Ratings) : 0
            }

            if (existingBookMap.has(book.BookID)) {
              booksToUpdate.push({ id: existingBookMap.get(book.BookID), data: bookData })
            } else {
              booksToCreate.push(bookData)
            }
          }

          // 3. 批量创建新书籍
          if (booksToCreate.length > 0) {
            await tx.books.createMany({ data: booksToCreate as any, skipDuplicates: true })
          }

          // 4. 批量更新现有书籍
          await Promise.all(booksToUpdate.map(({ id, data }) =>
            tx.books.update({ where: { id }, data: data as any })
          ))

          // 5. 获取所有书籍ID
          const allBooks = await tx.books.findMany({
            where: { rawid: { in: batch.map(b => b.BookID) } } as any,
            select: { id: true, rawid: true }
          })

          const bookIdMap = new Map(allBooks.map(b => [b.rawid, b.id]))

          // 6. 处理翻译、封面、关联关系
          for (const book of batch) {
            const bookId = bookIdMap.get(book.BookID)
            if (!bookId) continue

            // 翻译处理
            await tx.book_translations.upsert({
              where: { book_id_language_id: { book_id: bookId, language_id: 'en' } },
              update: {
                title: book['book title'],
                subtitle: cleanString(book['book subtitle']),
                review_summary: cleanString(book['Review Summary']), // 特别注意这里的映射
                best_quote: cleanString(book['Best Quotes'])
              },
              create: {
                book_id: bookId,
                language_id: 'en',
                title: book['book title'],
                subtitle: cleanString(book['book subtitle']),
                review_summary: cleanString(book['Review Summary']), // 特别注意这里的映射
                best_quote: cleanString(book['Best Quotes']),
                is_default: 1
              }
            })

            // 封面处理 - 与原始脚本保持一致
            if (book['Book Cover']) {
              const coverUrl = buildCdnUrl(book['Book Cover'])
              if (coverUrl) {
                // 查找现有封面
                const existingCover = await tx.book_covers.findFirst({
                  where: {
                    book_id: bookId,
                    language_id: 'en',
                    is_primary: 1
                  }
                })

                if (existingCover) {
                  // 更新现有封面
                  await tx.book_covers.update({
                    where: { id: existingCover.id },
                    data: { image_url: coverUrl }
                  })
                } else {
                  // 创建新封面
                  await tx.book_covers.create({
                    data: {
                      book_id: bookId,
                      language_id: 'en',
                      image_url: coverUrl,
                      is_primary: 1
                    }
                  })
                }
              }
            }

            // 作者关联处理 - 与原始脚本保持一致
            if (book.Author) {
              const authorIds = book.Author.split(',').map(id => id.trim())
              for (const authorId of authorIds) {
                const authorDbId = authorMap.get(authorId)

                if (!authorDbId) {
                  console.warn(`找不到作者 ${authorId}，跳过关联`)
                  continue
                }

                // 检查关联是否已存在
                const existingRelation = await tx.book_authors.findFirst({
                  where: {
                    book_id: bookId,
                    author_id: authorDbId
                  }
                })

                if (!existingRelation) {
                  // 创建新关联
                  await tx.book_authors.create({
                    data: {
                      book_id: bookId,
                      author_id: authorDbId,
                      author_order: 1
                    }
                  })
                } else {
                  // 更新现有关联（如果需要）
                  await tx.book_authors.update({
                    where: {
                      book_id_author_id: {
                        book_id: bookId,
                        author_id: authorDbId
                      }
                    },
                    data: {
                      author_order: 1
                    }
                  })
                }
              }
            }

            // 分类关联处理 - 与原始脚本保持一致
            if (book.Genre) {
              const genreIds = book.Genre.split(',').map(id => id.trim())
              for (const genreId of genreIds) {
                const categoryDbId = categoryMap.get(genreId)

                if (!categoryDbId) {
                  console.warn(`找不到分类 ${genreId}，跳过关联`)
                  continue
                }

                try {
                  // 使用 upsert 操作，避免先查询再创建/更新
                  await tx.book_categories.upsert({
                    where: {
                      book_id_category_id: {
                        book_id: bookId,
                        category_id: categoryDbId
                      }
                    },
                    update: {}, // 不需要更新任何字段
                    create: {
                      book_id: bookId,
                      category_id: categoryDbId
                    }
                  })
                } catch (error) {
                  const errorMessage = error instanceof Error ? error.message : String(error)
                  console.warn(`创建分类关联失败: ${genreId} - ${errorMessage}`)
                }
              }
            }
          }

          // 更新成功计数
          result.success += batch.length - result.errors.filter(e =>
            batch.some(book => book.BookID === e.id)
          ).length
        }, { timeout: config.timeout })
      }, `书籍批次 ${i + 1}/${batches.length}`)
    } catch (error) {
      // 批量失败时回退到逐条处理
      console.warn(`第 ${i + 1} 批书籍数据批量处理失败，回退到逐条处理: ${error}`)

      for (const book of batch) {
        try {
          await importSingleBook(book, authorMap, categoryMap)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: book.BookID, error: errorMessage })
          console.error(`导入书籍失败: ${book.BookID} (${book['book title']})`, singleError)
        }
      }
    }

    processed += batch.length
    if (i % 3 === 0 || i === batches.length - 1) {
      reportProgress(processed, bookData.length, '书籍导入', startTime)
    }
  }

  console.log(`✅ 书籍导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条书籍导入的回退函数
async function importSingleBook(book: MainContentData, authorMap: Map<string, number>, categoryMap: Map<string, number>): Promise<void> {
  await prisma.$transaction(async (tx) => {
    // 查找或创建书籍
    let bookRecord = await tx.books.findFirst({
      where: { rawid: book.BookID }
    })

    if (!bookRecord) {
      // 创建新书籍
      const bookData: any = {
        pdf_url: buildCdnUrl(book['PDF file']),
        epub_url: buildCdnUrl(book['EUPB FIle']),
        is_published: 1,
        rate_score: book['Rate Score'] ? Number(book['Rate Score']) : null,
        total_ratings: book.Ratings ? Number(book.Ratings) : 0
      }

      if (book.BookID) {
        bookData.rawid = book.BookID
      }

      bookRecord = await tx.books.create({ data: bookData })
    } else {
      // 更新现有书籍
      const updateData: any = {
        pdf_url: buildCdnUrl(book['PDF file']),
        epub_url: buildCdnUrl(book['EUPB FIle']),
        is_published: 1,
        rate_score: book['Rate Score'] ? Number(book['Rate Score']) : null,
        total_ratings: book.Ratings ? Number(book.Ratings) : 0
      }

      if (book.BookID) {
        updateData.rawid = book.BookID
      }

      bookRecord = await tx.books.update({
        where: { id: bookRecord.id },
        data: updateData
      })
    }

    // 创建书籍翻译记录
    await tx.book_translations.upsert({
      where: {
        book_id_language_id: {
          book_id: bookRecord.id,
          language_id: 'en'
        }
      },
      update: {
        title: book['book title'],
        subtitle: cleanString(book['book subtitle']),
        review_summary: cleanString(book['Review Summary']),
        best_quote: cleanString(book['Best Quotes'])
      },
      create: {
        book_id: bookRecord.id,
        language_id: 'en',
        title: book['book title'],
        subtitle: cleanString(book['book subtitle']),
        review_summary: cleanString(book['Review Summary']),
        best_quote: cleanString(book['Best Quotes']),
        is_default: 1
      }
    })

    // 创建书籍封面记录
    if (book['Book Cover']) {
      const coverUrl = buildCdnUrl(book['Book Cover'])
      if (coverUrl) {
        const existingCover = await tx.book_covers.findFirst({
          where: {
            book_id: bookRecord.id,
            language_id: 'en',
            is_primary: 1
          }
        })

        if (existingCover) {
          await tx.book_covers.update({
            where: { id: existingCover.id },
            data: { image_url: coverUrl }
          })
        } else {
          await tx.book_covers.create({
            data: {
              book_id: bookRecord.id,
              language_id: 'en',
              image_url: coverUrl,
              is_primary: 1
            }
          })
        }
      }
    }

    // 处理作者关系
    if (book.Author) {
      const authorIds = book.Author.split(',').map(id => id.trim())
      for (const authorId of authorIds) {
        const authorDbId = authorMap.get(authorId)
        if (!authorDbId) continue

        const existingRelation = await tx.book_authors.findFirst({
          where: {
            book_id: bookRecord.id,
            author_id: authorDbId
          }
        })

        if (!existingRelation) {
          await tx.book_authors.create({
            data: {
              book_id: bookRecord.id,
              author_id: authorDbId,
              author_order: 1
            }
          })
        }
      }
    }

    // 处理分类关系
    if (book.Genre) {
      const genreIds = book.Genre.split(',').map(id => id.trim())
      for (const genreId of genreIds) {
        const categoryDbId = categoryMap.get(genreId)
        if (!categoryDbId) continue

        try {
          await tx.book_categories.upsert({
            where: {
              book_id_category_id: {
                book_id: bookRecord.id,
                category_id: categoryDbId
              }
            },
            update: {},
            create: {
              book_id: bookRecord.id,
              category_id: categoryDbId
            }
          })
        } catch (error) {
          // 忽略重复创建错误
        }
      }
    }
  })
}

// 优化的书籍详情导入 - 补充缺失的功能
async function importBookDetailsOptimized(bookDetailData: BookDetailData[]): Promise<ImportResult> {
  console.log(`📖 开始导入书籍详情数据: ${bookDetailData.length} 条`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const batches = createBatches(bookDetailData)
  const startTime = Date.now()
  let processed = 0

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    try {
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          for (const detail of batch) {
            try {
              // 验证必填字段
              const missingFields = validateRequiredFields(detail, ['BookID'])
              if (missingFields.length > 0) {
                result.failed++
                result.errors.push({ id: detail.BookID, error: `缺少必填字段: ${missingFields.join(', ')}` })
                continue
              }

              // 查找书籍 - 只使用 rawid 进行匹配
              const book = await tx.books.findFirst({
                where: { rawid: detail.BookID }
              })

              if (!book) {
                result.failed++
                result.errors.push({ id: detail.BookID, error: `找不到书籍 ${detail.BookID}` })
                continue
              }

              // 提取出版年份
              const publicationYear = extractYearFromDate(detail['Publish Date'])

              // 更新书籍记录
              await tx.books.update({
                where: { id: book.id },
                data: {
                  isbn: cleanString(detail.ISBN),
                  isbn13: cleanString(detail.ISBN13),
                  asin: cleanString(detail.ASIN),
                  publication_year: publicationYear,
                  content_type: cleanString(detail.Content),
                  binding: cleanString(detail.Binding)
                } as any
              })

              // 更新书籍翻译记录 - 特别注意 plot_summary 字段
              await tx.book_translations.updateMany({
                where: {
                  book_id: book.id,
                  language_id: 'en'
                },
                data: {
                  plot_summary: cleanString(detail['brief analysis']) // 特别注意这里的映射
                }
              })

              // 如果有出版商信息，处理出版商关系
              if (detail.Publisher) {
                // 查找出版商翻译
                const publisherTranslation = await tx.publisher_translations.findFirst({
                  where: { name: detail.Publisher }
                })

                let publisherId: number | null = null

                if (publisherTranslation && publisherTranslation.publisher_id) {
                  publisherId = publisherTranslation.publisher_id
                } else {
                  // 创建新出版商
                  const newPublisher = await tx.publishers.create({
                    data: {}
                  })

                  // 创建出版商翻译
                  await tx.publisher_translations.create({
                    data: {
                      publisher_id: newPublisher.id,
                      language_id: 'en',
                      name: detail.Publisher,
                      is_default: 1
                    }
                  })

                  publisherId = newPublisher.id
                }

                // 更新书籍的出版商ID
                if (publisherId) {
                  await tx.books.update({
                    where: { id: book.id },
                    data: { publisher_id: publisherId }
                  })
                }
              }

              result.success++
            } catch (error) {
              result.failed++
              const errorMessage = error instanceof Error ? error.message : String(error)
              result.errors.push({ id: detail.BookID, error: errorMessage })
              console.error(`导入书籍详情失败: ${detail.BookID}`, error)
            }
          }
        }, { timeout: config.timeout })
      }, `书籍详情批次 ${i + 1}/${batches.length}`)
    } catch (error) {
      // 批量失败时回退到逐条处理
      console.warn(`第 ${i + 1} 批书籍详情数据批量处理失败，回退到逐条处理: ${error}`)

      for (const detail of batch) {
        try {
          await importSingleBookDetail(detail)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: detail.BookID, error: errorMessage })
          console.error(`导入书籍详情失败: ${detail.BookID}`, singleError)
        }
      }
    }

    processed += batch.length
    if (i % 5 === 0 || i === batches.length - 1) {
      reportProgress(processed, bookDetailData.length, '书籍详情导入', startTime)
    }
  }

  console.log(`✅ 书籍详情导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条书籍详情导入的回退函数
async function importSingleBookDetail(detail: BookDetailData): Promise<void> {
  await prisma.$transaction(async (tx) => {
    // 查找书籍
    const book = await tx.books.findFirst({
      where: { rawid: detail.BookID }
    })

    if (!book) {
      throw new Error(`找不到书籍 ${detail.BookID}`)
    }

    // 提取出版年份
    const publicationYear = extractYearFromDate(detail['Publish Date'])

    // 更新书籍记录
    await tx.books.update({
      where: { id: book.id },
      data: {
        isbn: cleanString(detail.ISBN),
        isbn13: cleanString(detail.ISBN13),
        asin: cleanString(detail.ASIN),
        publication_year: publicationYear,
        content_type: cleanString(detail.Content),
        binding: cleanString(detail.Binding)
      } as any
    })

    // 更新书籍翻译记录
    await tx.book_translations.updateMany({
      where: {
        book_id: book.id,
        language_id: 'en'
      },
      data: {
        plot_summary: cleanString(detail['brief analysis'])
      }
    })

    // 处理出版商关系
    if (detail.Publisher) {
      const publisherTranslation = await tx.publisher_translations.findFirst({
        where: { name: detail.Publisher }
      })

      let publisherId: number | null = null

      if (publisherTranslation && publisherTranslation.publisher_id) {
        publisherId = publisherTranslation.publisher_id
      } else {
        const newPublisher = await tx.publishers.create({ data: {} })
        await tx.publisher_translations.create({
          data: {
            publisher_id: newPublisher.id,
            language_id: 'en',
            name: detail.Publisher,
            is_default: 1
          }
        })
        publisherId = newPublisher.id
      }

      if (publisherId) {
        await tx.books.update({
          where: { id: book.id },
          data: { publisher_id: publisherId }
        })
      }
    }
  })
}

// 优化的章节导入 - 与原始脚本逻辑完全一致
async function importChaptersOptimized(bookData: MainContentData[]): Promise<ImportResult> {
  console.log(`📑 开始导入书籍章节内容，共 ${bookData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const dataBasePath = path.join(__dirname, 'book-data')
  const batches = createBatches(bookData)
  const startTime = Date.now()
  let processed = 0

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]

    try {
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          for (const book of batch) {
            try {
              // 检查是否有章节内容文件路径
              if (!book.BookSummary) {
                console.warn(`书籍 ${book.BookID} (${book['book title']}) 没有章节内容文件路径，跳过`)
                result.skipped++
                continue
              }

              // 构建章节内容文件的完整路径
              // 处理反斜杠路径问题：将反斜杠转换为正斜杠，并去除前导反斜杠
              const cleanBookSummaryPath = book.BookSummary
                .replace(/^[\\\/]+/, '') // 去除前导的反斜杠或正斜杠
                .replace(/\\/g, '/') // 将所有反斜杠转换为正斜杠

              const chapterFilePath = path.join(dataBasePath, cleanBookSummaryPath)

              // 检查文件是否存在
              if (!fs.existsSync(chapterFilePath)) {
                result.failed++
                result.errors.push({ id: book.BookID, error: `章节内容文件不存在: ${chapterFilePath}` })
                continue
              }

              // 读取章节内容文件
              const fileContent = fs.readFileSync(chapterFilePath, 'utf8')
              const chapterData = JSON.parse(fileContent)

              // 查找书籍 - 只使用 rawid 进行匹配
              const bookRecord = await tx.books.findFirst({
                where: {
                  rawid: book.BookID
                }
              })

              if (!bookRecord) {
                result.failed++
                result.errors.push({ id: book.BookID, error: `找不到书籍 ${book.BookID}` })
                continue
              }

              // 确保数据是有效的 JSON
              const validJsonContent = JSON.parse(JSON.stringify(chapterData))

              // 查找现有章节记录
              const existingChapter = await tx.book_chapters.findFirst({
                where: {
                  book_id: bookRecord.id,
                  language_id: 'en'
                }
              })

              if (existingChapter) {
                // 更新现有章节记录
                await tx.book_chapters.update({
                  where: { id: existingChapter.id },
                  data: {
                    content: validJsonContent
                  }
                })
              } else {
                // 创建新章节记录
                await tx.book_chapters.create({
                  data: {
                    book_id: bookRecord.id,
                    language_id: 'en',
                    content: validJsonContent
                  }
                })
              }

              result.success++
              console.log(`✅ 成功导入书籍章节: ${book.BookID} (${book['book title']})`)
            } catch (error) {
              result.failed++
              const errorMessage = error instanceof Error ? error.message : String(error)
              result.errors.push({ id: book.BookID, error: errorMessage })
              console.error(`导入书籍章节失败: ${book.BookID} (${book['book title']})`, error)
            }
          }
        }, { timeout: config.timeout })
      }, `章节批次 ${i + 1}/${batches.length}`)
    } catch (error) {
      // 批量失败时回退到逐条处理
      console.warn(`第 ${i + 1} 批章节数据批量处理失败，回退到逐条处理: ${error}`)

      for (const book of batch) {
        try {
          await importSingleChapter(book, dataBasePath)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: book.BookID, error: errorMessage })
          console.error(`导入书籍章节失败: ${book.BookID} (${book['book title']})`, singleError)
        }
      }
    }

    processed += batch.length
    if (i % 3 === 0 || i === batches.length - 1) {
      reportProgress(processed, bookData.length, '章节导入', startTime)
    }
  }

  console.log(`✅ 书籍章节内容导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条章节导入的回退函数
async function importSingleChapter(book: MainContentData, dataBasePath: string): Promise<void> {
  if (!book.BookSummary) {
    throw new Error(`书籍 ${book.BookID} 没有章节内容文件路径`)
  }

  const cleanBookSummaryPath = book.BookSummary
    .replace(/^[\\\/]+/, '')
    .replace(/\\/g, '/')

  const chapterFilePath = path.join(dataBasePath, cleanBookSummaryPath)

  if (!fs.existsSync(chapterFilePath)) {
    throw new Error(`章节内容文件不存在: ${chapterFilePath}`)
  }

  const fileContent = fs.readFileSync(chapterFilePath, 'utf8')
  const chapterData = JSON.parse(fileContent)

  await prisma.$transaction(async (tx) => {
    const bookRecord = await tx.books.findFirst({
      where: { rawid: book.BookID }
    })

    if (!bookRecord) {
      throw new Error(`找不到书籍 ${book.BookID}`)
    }

    const validJsonContent = JSON.parse(JSON.stringify(chapterData))

    const existingChapter = await tx.book_chapters.findFirst({
      where: {
        book_id: bookRecord.id,
        language_id: 'en'
      }
    })

    if (existingChapter) {
      await tx.book_chapters.update({
        where: { id: existingChapter.id },
        data: { content: validJsonContent }
      })
    } else {
      await tx.book_chapters.create({
        data: {
          book_id: bookRecord.id,
          language_id: 'en',
          content: validJsonContent
        }
      })
    }
  })
}

// 主函数 - 完整的导入流程
async function main() {
  const dataBasePath = path.join(__dirname, 'book-data')
  const totalStartTime = Date.now()

  try {
    console.log('🚀 开始优化导入流程...')
    console.log(`📊 环境配置: ${config.isLocal ? '本地' : '远程'}数据库，批次大小=${config.batchSize}`)

    const results = {
      authors: { success: 0, failed: 0, skipped: 0, errors: [] as Array<{ id: string, error: string }> },
      categories: { success: 0, failed: 0, skipped: 0, errors: [] as Array<{ id: string, error: string }> },
      books: { success: 0, failed: 0, skipped: 0, errors: [] as Array<{ id: string, error: string }> },
      details: { success: 0, failed: 0, skipped: 0, errors: [] as Array<{ id: string, error: string }> },
      chapters: { success: 0, failed: 0, skipped: 0, errors: [] as Array<{ id: string, error: string }> }
    }

    // 1. 导入作者
    console.log('\n📍 步骤 1/5: 导入作者数据')
    const authorFile = path.join(dataBasePath, 'Auhtor Databse Management.json')
    if (fs.existsSync(authorFile)) {
      const authorData = JSON.parse(fs.readFileSync(authorFile, 'utf8'))
      results.authors = await importAuthorsOptimized(authorData)
    } else {
      console.log('⚠️  作者数据文件不存在')
    }

    // 2. 导入分类
    console.log('\n📍 步骤 2/5: 导入分类数据')
    const categoryFile = path.join(dataBasePath, 'Genere Databse Management.json')
    if (fs.existsSync(categoryFile)) {
      const categoryData = JSON.parse(fs.readFileSync(categoryFile, 'utf8'))
      results.categories = await importCategoriesOptimized(categoryData)
    } else {
      console.log('⚠️  分类数据文件不存在')
    }

    // 3. 导入书籍基本信息
    console.log('\n📍 步骤 3/5: 导入书籍基本信息')
    const bookFile = path.join(dataBasePath, 'Main Content and Summary.json')
    let bookData: MainContentData[] = []
    if (fs.existsSync(bookFile)) {
      bookData = JSON.parse(fs.readFileSync(bookFile, 'utf8'))
      results.books = await importBooksOptimized(bookData)
    } else {
      console.log('⚠️  书籍数据文件不存在')
    }

    // 4. 导入书籍详情
    console.log('\n📍 步骤 4/5: 导入书籍详情')
    const detailFile = path.join(dataBasePath, 'Brief Analysis and Book editions.json')
    if (fs.existsSync(detailFile)) {
      const detailData = JSON.parse(fs.readFileSync(detailFile, 'utf8'))
      results.details = await importBookDetailsOptimized(detailData)
    } else {
      console.log('⚠️  书籍详情数据文件不存在')
    }

    // 5. 导入章节内容 (基于书籍数据)
    console.log('\n📍 步骤 5/5: 导入章节内容')
    if (bookData.length > 0) {
      results.chapters = await importChaptersOptimized(bookData)
    } else {
      console.log('⚠️  没有书籍数据，跳过章节导入')
    }

    // 汇总结果
    const totalTime = ((Date.now() - totalStartTime) / 1000).toFixed(1)
    const totalSuccess = results.authors.success + results.categories.success + results.books.success + results.details.success + results.chapters.success
    const totalFailed = results.authors.failed + results.categories.failed + results.books.failed + results.details.failed + results.chapters.failed
    const totalErrors = [
      ...results.authors.errors,
      ...results.categories.errors,
      ...results.books.errors,
      ...results.details.errors,
      ...results.chapters.errors
    ]

    console.log('\n🎉 所有数据导入完成!')
    console.log(`📊 总体统计:`)
    console.log(`   ✅ 成功: ${totalSuccess} 条`)
    console.log(`   ❌ 失败: ${totalFailed} 条`)
    console.log(`   ⏱️  总耗时: ${totalTime}秒`)

    console.log(`\n📋 详细统计:`)
    console.log(`   👥 作者: 成功 ${results.authors.success}, 失败 ${results.authors.failed}`)
    console.log(`   📂 分类: 成功 ${results.categories.success}, 失败 ${results.categories.failed}`)
    console.log(`   📚 书籍: 成功 ${results.books.success}, 失败 ${results.books.failed}`)
    console.log(`   📖 详情: 成功 ${results.details.success}, 失败 ${results.details.failed}`)
    console.log(`   📑 章节: 成功 ${results.chapters.success}, 失败 ${results.chapters.failed}`)

    // 如果有错误，显示前10个错误
    if (totalErrors.length > 0) {
      console.log(`\n⚠️  错误详情 (显示前10个):`)
      totalErrors.slice(0, 10).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.id}: ${error.error}`)
      })
      if (totalErrors.length > 10) {
        console.log(`   ... 还有 ${totalErrors.length - 10} 个错误`)
      }
    }

  } catch (error) {
    console.error('💥 导入过程出错:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}

export {
  main,
  importAuthorsOptimized,
  importCategoriesOptimized,
  importBooksOptimized,
  importBookDetailsOptimized,
  importChaptersOptimized
}
