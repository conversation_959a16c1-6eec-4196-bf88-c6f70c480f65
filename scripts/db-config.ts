/**
 * 数据库连接优化配置
 * 针对不同环境提供优化的连接参数
 */

import { PrismaClient } from '@prisma/client'

// 数据库连接配置接口
interface DatabaseConfig {
  connectionLimit: number
  timeout: number
  batchSize: number
  transactionTimeout: number
  retryAttempts: number
  retryDelay: number
}

// 根据环境检测网络类型
function detectNetworkType(): 'local' | 'remote' {
  const dbUrl = process.env.DATABASE_URL || ''

  // 检查是否为局域网地址
  const localPatterns = [
    /192\.168\./,
    /10\./,
    /172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /localhost/,
    /127\.0\.0\.1/
  ]

  const isLocal = localPatterns.some(pattern => pattern.test(dbUrl))
  return isLocal ? 'local' : 'remote'
}

// 获取优化的数据库配置
export function getDatabaseConfig(): DatabaseConfig {
  const networkType = detectNetworkType()

  if (networkType === 'local') {
    // 局域网环境 - 高性能配置
    return {
      connectionLimit: 20,
      timeout: 30000,        // 30秒
      batchSize: 100,        // 大批量处理
      transactionTimeout: 120000, // 2分钟
      retryAttempts: 3,
      retryDelay: 1000
    }
  } else {
    // 远程环境 - 保守配置
    return {
      connectionLimit: 5,
      timeout: 60000,        // 60秒
      batchSize: 20,         // 小批量处理
      transactionTimeout: 300000, // 5分钟
      retryAttempts: 5,
      retryDelay: 2000
    }
  }
}

// 创建优化的 Prisma 客户端
export function createOptimizedPrismaClient(): PrismaClient {
  const config = getDatabaseConfig()
  const networkType = detectNetworkType()

  console.log(`检测到网络类型: ${networkType}`)
  console.log(`使用配置:`, config)

  return new PrismaClient({
    log: networkType === 'local' ? ['error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // 连接池配置
    __internal: {
      engine: {
        connectionLimit: config.connectionLimit,
      }
    } as unknown as never
  })
}

// 重试机制装饰器
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = getDatabaseConfig().retryAttempts,
  delay: number = getDatabaseConfig().retryDelay
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxAttempts) {
        throw lastError
      }

      console.log(`操作失败，第 ${attempt}/${maxAttempts} 次重试，${delay}ms 后重试...`)
      console.log(`错误信息: ${lastError.message}`)

      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= 1.5 // 指数退避
    }
  }

  throw lastError!
}

// 批量操作辅助函数
export function createBatches<T>(items: T[], batchSize?: number): T[][] {
  const config = getDatabaseConfig()
  const size = batchSize || config.batchSize
  const batches: T[][] = []

  for (let i = 0; i < items.length; i += size) {
    batches.push(items.slice(i, i + size))
  }

  return batches
}

// 进度报告函数
export function reportProgress(
  current: number,
  total: number,
  operation: string,
  startTime: number = Date.now()
): void {
  const percentage = ((current / total) * 100).toFixed(1)
  const elapsed = Date.now() - startTime
  const rate = current / (elapsed / 1000)
  const eta = total > current ? (total - current) / rate : 0

  console.log(
    `${operation}: ${current}/${total} (${percentage}%) | ` +
    `速度: ${rate.toFixed(1)} 条/秒 | ` +
    `预计剩余: ${Math.round(eta)} 秒`
  )
}

export { detectNetworkType }
