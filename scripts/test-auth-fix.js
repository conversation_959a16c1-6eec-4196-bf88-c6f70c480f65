#!/usr/bin/env node

/**
 * 测试认证修复的脚本
 * 用于验证Cookie名称和Redis连接是否正确
 */

const https = require('https');
const http = require('http');

// 配置
const PRODUCTION_URL = 'https://www.15minutes.ai';
const DEV_URL = 'https://15minutes.tangshu.com';

/**
 * 发送HTTP请求
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.end();
  });
}

/**
 * 测试调试接口
 */
async function testDebugAuth(baseUrl) {
  console.log(`\n🔍 测试 ${baseUrl} 的认证调试接口...`);
  
  try {
    const response = await makeRequest(`${baseUrl}/api/debug/auth`);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.data);
      console.log('✅ 调试接口响应成功');
      console.log('📊 环境信息:', data.debug?.environment);
      console.log('🍪 Cookie信息:', data.debug?.cookies);
      console.log('🔑 Token信息:', Object.keys(data.debug?.tokens || {}));
      
      // 检查关键配置
      const env = data.debug?.environment;
      if (env) {
        console.log('\n📋 配置检查:');
        console.log(`   NODE_ENV: ${env.NODE_ENV}`);
        console.log(`   TOKEN_KEY: ${env.TOKEN_KEY}`);
        console.log(`   COMPUTED_TOKEN_KEY: ${env.COMPUTED_TOKEN_KEY}`);
        console.log(`   APP_NAME: ${env.APP_NAME}`);
        
        // 验证配置是否正确
        if (env.NODE_ENV === 'production' && env.COMPUTED_TOKEN_KEY === 'MINUTES_ACCESS_TOKEN') {
          console.log('✅ 生产环境Token Key配置正确');
        } else if (env.NODE_ENV === 'development' && env.COMPUTED_TOKEN_KEY === 'MINUTES_ACCESS_TOKEN-dev') {
          console.log('✅ 开发环境Token Key配置正确');
        } else {
          console.log('⚠️  Token Key配置可能有问题');
        }
      }
      
    } else {
      console.log(`❌ 调试接口返回错误: ${response.statusCode}`);
      console.log(response.data);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 测试用户接口
 */
async function testUserApi(baseUrl) {
  console.log(`\n👤 测试 ${baseUrl} 的用户接口...`);
  
  try {
    const response = await makeRequest(`${baseUrl}/common-api/v1/user`);
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.data);
      if (data.data && data.data.is_logged_in) {
        console.log('✅ 用户已登录，API正常');
        console.log(`   用户ID: ${data.data.id}`);
        console.log(`   用户邮箱: ${data.data.email}`);
      } else {
        console.log('ℹ️  用户未登录');
      }
    } else {
      console.log(`❌ 用户接口返回错误: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始测试认证修复...\n');
  
  // 测试生产环境
  console.log('='.repeat(50));
  console.log('🌐 生产环境测试');
  console.log('='.repeat(50));
  await testDebugAuth(PRODUCTION_URL);
  await testUserApi(PRODUCTION_URL);
  
  // 测试开发环境
  console.log('\n' + '='.repeat(50));
  console.log('🛠️  开发环境测试');
  console.log('='.repeat(50));
  await testDebugAuth(DEV_URL);
  await testUserApi(DEV_URL);
  
  console.log('\n✨ 测试完成！');
  console.log('\n📝 下一步:');
  console.log('1. 检查上述输出中的配置是否正确');
  console.log('2. 如果Token Key配置正确，尝试重新登录测试');
  console.log('3. 检查Redis连接和数据库配置');
  console.log('4. 如果问题仍然存在，查看服务器日志');
}

// 运行测试
main().catch(console.error);
