-- 临时禁用安全更新模式
SET SQL_SAFE_UPDATES = 0;

-- 清空所有书籍和相关数据
-- 按照外键依赖关系的正确顺序执行

-- 1. 首先清空用户相关的书籍数据（这些表引用了books表）
DELETE FROM user_audio_progress;
DELETE FROM user_favorites;
DELETE FROM user_reading_history;
DELETE FROM ratings;

-- 2. 清空书籍的统计和音频文件数据
DELETE FROM view_statistics;
DELETE FROM audio_files;

-- 3. 清空书籍的关联表数据
DELETE FROM book_authors;
DELETE FROM book_categories;
DELETE FROM book_covers;
DELETE FROM book_translations;
DELETE FROM book_chapters;

-- 4. 清空书籍主表
DELETE FROM books;

-- 5. 清空作者相关数据
DELETE FROM author_view_statistics;
DELETE FROM author_translations;
DELETE FROM authors;

-- 6. 清空分类相关数据
DELETE FROM category_translations;
DELETE FROM categories;

-- 7. 清空出版商相关数据
DELETE FROM publisher_translations;
DELETE FROM publishers;

-- 重新启用安全更新模式
SET SQL_SAFE_UPDATES = 1;

-- 8. 重置自增ID（可选，如果需要重新开始计数）
ALTER TABLE books AUTO_INCREMENT = 1;
ALTER TABLE book_translations AUTO_INCREMENT = 1;
ALTER TABLE book_covers AUTO_INCREMENT = 1;
ALTER TABLE book_chapters AUTO_INCREMENT = 1;
ALTER TABLE view_statistics AUTO_INCREMENT = 1;
ALTER TABLE audio_files AUTO_INCREMENT = 1;
ALTER TABLE ratings AUTO_INCREMENT = 1;
ALTER TABLE user_audio_progress AUTO_INCREMENT = 1;
ALTER TABLE user_reading_history AUTO_INCREMENT = 1;
ALTER TABLE authors AUTO_INCREMENT = 1;
ALTER TABLE author_translations AUTO_INCREMENT = 1;
ALTER TABLE author_view_statistics AUTO_INCREMENT = 1;
ALTER TABLE categories AUTO_INCREMENT = 1;
ALTER TABLE category_translations AUTO_INCREMENT = 1;
ALTER TABLE publishers AUTO_INCREMENT = 1;
ALTER TABLE publisher_translations AUTO_INCREMENT = 1;

-- 9. 验证清空结果
SELECT 'books' as table_name, COUNT(*) as count FROM books
UNION ALL
SELECT 'book_translations', COUNT(*) FROM book_translations
UNION ALL
SELECT 'book_covers', COUNT(*) FROM book_covers
UNION ALL
SELECT 'book_chapters', COUNT(*) FROM book_chapters
UNION ALL
SELECT 'book_authors', COUNT(*) FROM book_authors
UNION ALL
SELECT 'book_categories', COUNT(*) FROM book_categories
UNION ALL
SELECT 'view_statistics', COUNT(*) FROM view_statistics
UNION ALL
SELECT 'audio_files', COUNT(*) FROM audio_files
UNION ALL
SELECT 'ratings', COUNT(*) FROM ratings
UNION ALL
SELECT 'user_audio_progress', COUNT(*) FROM user_audio_progress
UNION ALL
SELECT 'user_favorites', COUNT(*) FROM user_favorites
UNION ALL
SELECT 'user_reading_history', COUNT(*) FROM user_reading_history
UNION ALL
SELECT 'authors', COUNT(*) FROM authors
UNION ALL
SELECT 'author_translations', COUNT(*) FROM author_translations
UNION ALL
SELECT 'author_view_statistics', COUNT(*) FROM author_view_statistics
UNION ALL
SELECT 'categories', COUNT(*) FROM categories
UNION ALL
SELECT 'category_translations', COUNT(*) FROM category_translations
UNION ALL
SELECT 'publishers', COUNT(*) FROM publishers
UNION ALL
SELECT 'publisher_translations', COUNT(*) FROM publisher_translations;