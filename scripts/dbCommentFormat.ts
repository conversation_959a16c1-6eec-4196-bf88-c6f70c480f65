import * as fs from 'fs'
import * as path from 'path'

// 直接指定文件路径
const schemaPath = path.resolve(process.cwd(), 'prisma/schema.prisma')

// 转换JSDoc注释为三斜杠注释的函数
function transformComments(content: string): string {
  const lines = content.split('\n')
  const result: string[] = []

  let inJSDocComment = false
  let commentText = ''
  let commentIndentation = ''

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const trimmedLine = line.trim()

    // 检测JSDoc注释的开始
    if (trimmedLine === '/**') {
      inJSDocComment = true
      commentText = ''
      // 保留缩进
      commentIndentation = line.substring(0, line.indexOf('/**'))
      continue
    }

    // 检测JSDoc注释的结束
    if (inJSDocComment && trimmedLine === '*/') {
      inJSDocComment = false

      // 添加三斜杠注释（注意两个空格）
      result.push(`${commentIndentation}///  ${commentText.trim()}`)
      continue
    }

    // 处理注释内容
    if (inJSDocComment) {
      // 提取星号行中的内容
      if (trimmedLine.startsWith('*')) {
        const extractedText = trimmedLine.substring(1).trim()
        if (extractedText) {
          // 如果已经有注释文本，添加空格
          if (commentText) {
            commentText += ' '
          }
          commentText += extractedText
        }
      }
      continue
    }

    // 不在JSDoc注释中，保持行不变
    result.push(line)
  }

  return result.join('\n')
}

// 主函数
async function main() {
  try {
    // 读取schema文件
    console.log(`正在读取文件: ${schemaPath}`)
    const content = fs.readFileSync(schemaPath, 'utf-8')

    // 转换注释
    const transformedContent = transformComments(content)

    // 将转换后的内容写回文件
    fs.writeFileSync(schemaPath, transformedContent)

    console.log('Schema文件中的JSDoc注释已成功转换为三斜杠注释')
  } catch (error) {
    console.error('转换schema文件时出错:', error)
    process.exit(1)
  }
}

// 执行主函数
main()
