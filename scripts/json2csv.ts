import fs from 'node:fs'
import path from 'node:path'
import { Parser, Options } from 'json2csv'
import { locales, SupportedLanguage } from '../src/i18n/routing'

// 定义类型
type FlattenedObject = Record<string, string | number | boolean | null | string[]>
type TranslationRecord = Record<string, FlattenedObject>
type CSVRow = Record<string, string | number | boolean | null | string[]>

// 将嵌套的JSON对象扁平化，使用.连接key
function flattenObject(obj: Record<string, unknown>, prefix = ''): FlattenedObject {
  return Object.keys(obj).reduce<FlattenedObject>((acc, key) => {
    const pre = prefix.length > 0 ? `${prefix}.` : ''

    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(acc, flattenObject(obj[key] as Record<string, unknown>, `${pre}${key}`))
    } else {
      acc[`${pre}${key}`] = obj[key] as string | number | boolean | null | string[]
    }

    return acc
  }, {})
}

// 主处理函数
async function processTranslations(dirPath: string): Promise<void> {
  const languages: SupportedLanguage[] = [...locales]
  const translations: TranslationRecord = {}

  // 读取所有语言文件
  for (const lang of languages) {
    try {
      const filePath = path.join(dirPath, `${lang}.json`)
      const content = fs.readFileSync(filePath, 'utf8')
      translations[lang] = flattenObject(JSON.parse(content))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error(`Error reading ${lang}.json:`, errorMessage)
      throw new Error(`Error reading ${lang}.json`)
    }
  }

  // 收集所有唯一的keys
  const allKeys = new Set<string>()
  Object.values(translations).forEach((trans) => {
    Object.keys(trans).forEach((key) => allKeys.add(key))
  })

  // 准备CSV数据
  const csvData: CSVRow[] = [...allKeys].map((key) => {
    const row: CSVRow = { key }
    languages.forEach((lang) => {
      row[lang] = translations[lang][key] || ''
    })
    return row
  })

  // 配置CSV Parser
  const fields: string[] = ['key', ...languages]
  const opts: Options<CSVRow> = { fields }
  const parser = new Parser<CSVRow>(opts)

  // 生成CSV
  try {
    const csv = parser.parse(csvData)
    const outputPath = path.join(dirPath, 'translations.csv')
    fs.writeFileSync(outputPath, csv)
    console.log(`CSV文件已生成: ${outputPath}`)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error('生成CSV时出错:', errorMessage)
    throw new Error('生成CSV时出错')
  }
}

const dirPath = './messages'
processTranslations(dirPath).catch((error) => {
  console.error('处理翻译文件时发生错误:', error)
  process.exit(1)
})
