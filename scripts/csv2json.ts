import fs from 'node:fs'
import path from 'node:path'
import csvParser from 'csv-parser'
import { fileURLToPath } from 'node:url'

// 定义类型
type TranslationDict = Record<string, string>
type TranslationsMap = Record<string, TranslationDict>
type CsvRow = Record<string, string> & { __key__: string }

const __dirname = path.dirname(fileURLToPath(import.meta.url))

const inputPath = path.join(__dirname, '..', 'messages/translations.csv')
const outputDir = path.join(__dirname, '..', 'messages')

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

const translations: TranslationsMap = {}
let languages: string[] = []
let rowCount = 0

// 添加一个辅助函数，用于将扁平化的对象转换为嵌套结构
function convertToNestedObject(flatObj: TranslationDict): Record<string, unknown> {
  const nestedObj: Record<string, unknown> = {}

  for (const flatKey in flatObj) {
    const value = flatObj[flatKey]
    const keyParts = flatKey.split('.')
    let current: Record<string, unknown> = nestedObj

    for (let i = 0; i < keyParts.length - 1; i++) {
      const key = keyParts[i]
      current[key] = (current[key] as Record<string, unknown>) || {}
      current = current[key] as Record<string, unknown>
    }

    current[keyParts[keyParts.length - 1]] = value
  }

  return nestedObj
}

fs.createReadStream(inputPath)
  .pipe(
    csvParser({
      // 自定义 CSV 解析器选项，确保第一行不被当作数据处理
      mapHeaders: ({ header, index }: { header: string; index: number }) =>
        index === 0 ? '__key__' : header, // 将第一列的空header命名为 __key__
      mapValues: ({ value }: { value: string }) => value
    })
  )
  .on('data', (row: CsvRow) => {
    if (languages.length === 0) {
      // 获取语言代码，跳过第一个空单元格，并过滤掉空的语言代码
      languages = Object.keys(row).filter((key) => key !== '__key__' && key.trim() !== '')
      // 初始化每个语言的翻译对象
      languages.forEach((lang) => {
        translations[lang] = {}
      })
    }

    const key = row.__key__
    if (key) {
      languages.forEach((lang) => {
        // 添加值的处理逻辑：如果值仅为 "\n"，则替换为空字符串
        const value = row[lang]
        translations[lang][key] = value === '\n' ? '' : value
      })
      rowCount++
    }
  })
  .on('end', () => {
    // 将翻译对象写入到对应的 JSON 文件
    languages.forEach((lang) => {
      const outputPath = path.join(outputDir, `${lang}.json`)
      // 在写入之前转换成嵌套结构
      const nestedTranslations = convertToNestedObject(translations[lang])
      const jsonData = JSON.stringify(nestedTranslations, null, 2)
      fs.writeFileSync(outputPath, jsonData)
      console.log(`生成 ${lang}.json`)
    })
    console.log('所有 JSON 文件生成完毕！')
    console.log(`共处理 ${rowCount} 行有效数据`)
  })
  .on('error', (error: Error) => {
    console.error('读取 CSV 文件出错:', error)
  })
