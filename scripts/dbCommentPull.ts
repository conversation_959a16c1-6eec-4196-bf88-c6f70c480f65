/**
 * 这个脚本会将MySQL中的comment抓取下来补充到 schema 中
 * 修复了重复注释和未完成模型定义的问题
 * 将注释改为三斜杠注释格式，并放在相应定义的上一行
 */

import * as mysql from 'mysql2/promise'
import * as fs from 'fs'
import dotenv from 'dotenv'
import path from 'path'

dotenv.config()

async function syncMySQLComments() {
  // 1. 解析当前 Prisma Schema
  const schemaPath = path.resolve(__dirname, '../prisma/schema.prisma')
  const originalSchema = fs.readFileSync(schemaPath, 'utf8')

  // 先清理schema中已有的注释，避免重复
  // 清理行注释
  let cleanSchema = originalSchema.replace(/\s*\/\/\/\s*@comment\s*.*$/gm, '')
  // 清理块注释
  cleanSchema = cleanSchema.replace(/\s*\/\*\s*@comment\s*.*\*\/\s*$/gm, '')

  try {
    // 2. 连接 MySQL 获取注释信息
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    })

    // 获取表注释
    const [tableCommentsResult] = await connection.query(`
      SELECT TABLE_NAME, TABLE_COMMENT
      FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_COMMENT != ''
    `)
    const tableComments = tableCommentsResult as any[]

    // 获取列注释
    const [columnCommentsResult] = await connection.query(`
      SELECT TABLE_NAME, COLUMN_NAME, COLUMN_COMMENT
      FROM information_schema.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
      AND COLUMN_COMMENT != ''
    `)
    const columnComments = columnCommentsResult as any[]

    // 创建映射以更快地查找
    const tableCommentsMap = new Map()
    tableComments.forEach((t) => tableCommentsMap.set(t.TABLE_NAME, t.TABLE_COMMENT))

    const columnCommentsMap = new Map()
    columnComments.forEach((c) => {
      const key = `${c.TABLE_NAME}.${c.COLUMN_NAME}`
      columnCommentsMap.set(key, c.COLUMN_COMMENT)
    })

    // 3. 处理模型和字段注释
    const lines = cleanSchema.split('\n')
    const resultLines = []

    let currentModel = null
    let currentTable = null
    let inModelBlock = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trimEnd() // 只移除末尾空白，保留缩进
      const trimmedLine = line.trim()
      const indentation = line.match(/^\s*/)?.[0] || '' // 获取当前行的缩进，使用可选链避免空值

      // 检测模型定义开始
      const modelMatch = trimmedLine.match(/^model\s+(\w+)\s+{/)
      if (modelMatch) {
        currentModel = modelMatch[1]
        inModelBlock = true
        currentTable = currentModel // 默认使用模型名作为表名

        // 找到该模型的结束位置
        let modelEndLine = i
        for (let j = i + 1; j < lines.length; j++) {
          if (lines[j].trim() === '}') {
            modelEndLine = j
            break
          }
        }

        // 在模型定义块中查找 @@map 属性
        for (let j = i + 1; j <= modelEndLine; j++) {
          const mapMatch = lines[j].trim().match(/^@@map\s*\(\s*["']([^"']+)["']\s*\)/)
          if (mapMatch) {
            currentTable = mapMatch[1]
            break
          }
        }

        // 如果有表注释，添加到模型定义前
        const tableComment = tableCommentsMap.get(currentTable)
        if (tableComment) {
          resultLines.push(`${indentation}/// @comment ${tableComment}`)
        }

        resultLines.push(line)
        continue
      }

      // 检测模型定义结束
      if (inModelBlock && trimmedLine === '}') {
        inModelBlock = false
        currentModel = null
        currentTable = null
        resultLines.push(line)
        continue
      }

      // 处理字段
      if (inModelBlock && currentModel && currentTable) {
        // 匹配字段定义（排除注释行和空行）
        const fieldMatch = trimmedLine.match(/^(\w+)\s+.+/)
        if (
          fieldMatch &&
          !trimmedLine.startsWith('//') &&
          !trimmedLine.startsWith('/*') &&
          trimmedLine !== ''
        ) {
          const fieldName = fieldMatch[1]

          // 查找字段对应的数据库列名 - 查找行内的 @map 属性
          const dbNameMatch = trimmedLine.match(/@map\s*\(\s*["']([^"']+)["']\s*\)/)
          const dbColumn = dbNameMatch ? dbNameMatch[1] : fieldName

          // 查找列注释
          const columnKey = `${currentTable}.${dbColumn}`
          const columnComment = columnCommentsMap.get(columnKey)

          if (columnComment) {
            // 在字段定义前添加注释，保持适当缩进
            resultLines.push(`${indentation}/// @comment ${columnComment}`)
          }
        }

        resultLines.push(line)
      } else {
        // 非字段行，直接添加
        resultLines.push(line)
      }
    }

    // 4. 保存增强后的 schema
    const enhancedSchema = resultLines.join('\n')
    fs.writeFileSync(path.resolve(__dirname, '../prisma/enhanced-schema.prisma'), enhancedSchema)
    console.log('MySQL 注释同步后的版本已经保存到 prisma/enhanced-schema.prisma')

    await connection.end()
  } catch (error) {
    console.error('处理 schema 时出错:', error)
    throw error
  }
}

// 执行同步
syncMySQLComments().catch(console.error)
