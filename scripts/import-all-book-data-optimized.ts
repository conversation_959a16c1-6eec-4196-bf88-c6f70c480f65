/**
 * 优化版本的书籍数据导入脚本
 * 针对远程数据库连接进行优化，减少网络往返次数
 */

import { PrismaClient } from '@prisma/client'

// 检测网络环境
function isRemoteDatabase(): boolean {
  const dbUrl = process.env.DATABASE_URL || ''
  const localPatterns = [
    /192\.168\./,
    /10\./,
    /172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /localhost/,
    /127\.0\.0\.1/
  ]
  return !localPatterns.some(pattern => pattern.test(dbUrl))
}

// 创建优化的 Prisma 客户端
const isRemote = isRemoteDatabase()
const prisma = new PrismaClient({
  log: isRemote ? ['error'] : ['error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  }
})

// 优化配置
const BATCH_SIZE = isRemote ? 10 : 50  // 远程环境使用更小的批次
const TRANSACTION_TIMEOUT = isRemote ? 300000 : 60000  // 远程环境使用更长的超时时间
const MAX_RETRIES = isRemote ? 5 : 3

console.log(`检测到${isRemote ? '远程' : '本地'}数据库环境`)
console.log(`批次大小: ${BATCH_SIZE}, 事务超时: ${TRANSACTION_TIMEOUT}ms`)

// 重试机制
async function withRetry<T>(operation: () => Promise<T>, maxRetries = MAX_RETRIES): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxRetries) {
        throw lastError
      }

      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // 指数退避，最大10秒
      console.log(`操作失败，第 ${attempt}/${maxRetries} 次重试，${delay}ms 后重试...`)
      console.log(`错误: ${lastError.message}`)

      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError!
}

// 批量处理函数
function createBatches<T>(items: T[], batchSize = BATCH_SIZE): T[][] {
  const batches: T[][] = []
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize))
  }
  return batches
}

// 进度报告
function reportProgress(current: number, total: number, operation: string, startTime: number) {
  const percentage = ((current / total) * 100).toFixed(1)
  const elapsed = (Date.now() - startTime) / 1000
  const rate = current / elapsed
  const eta = total > current ? (total - current) / rate : 0

  console.log(
    `${operation}: ${current}/${total} (${percentage}%) | ` +
    `速度: ${rate.toFixed(1)} 条/秒 | ` +
    `预计剩余: ${Math.round(eta)} 秒`
  )
}

// 数据类型定义（简化版）
interface AuthorData {
  AuthorID: string
  Author: string
  'Author Avatar'?: string
  Website?: string
  'X Account'?: string
  Born?: string
  'Author Desc'?: string
}

interface CategoryData {
  GenreID: string
  Genre: string
  GenreDesc?: string
}



// 工具函数
function cleanString(str: string | undefined | null): string | null {
  if (!str || typeof str !== 'string') return null
  const cleaned = str.trim()
  return cleaned === '' ? null : cleaned
}

function buildCdnUrl(filePath: string | undefined | null): string | null {
  if (!filePath || typeof filePath !== 'string') return null
  const cleaned = filePath.trim()
  if (cleaned === '' || cleaned === 'N/A') return null

  if (cleaned.startsWith('http://') || cleaned.startsWith('https://')) {
    return cleaned
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://15minutes.tangshu.com'
  return `${baseUrl}/${cleaned.replace(/^\/+/, '')}`
}



// 优化的批量导入作者
async function importAuthorsBatch(authorData: AuthorData[]): Promise<void> {
  console.log(`开始批量导入作者数据，共 ${authorData.length} 条记录`)

  const batches = createBatches(authorData)
  const startTime = Date.now()

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理第 ${i + 1}/${batches.length} 批作者数据 (${batch.length} 条)`)

    await withRetry(async () => {
      await prisma.$transaction(async (tx) => {
        // 批量查询现有作者
        const existingAuthors = await tx.authors.findMany({
          where: {
            rawid: { in: batch.map(a => a.AuthorID) }
          } as any,
          select: { id: true, rawid: true }
        })

        const existingMap = new Map(existingAuthors.map(a => [a.rawid, a.id]))

        // 准备批量创建数据
        const toCreate = batch
          .filter(author => !existingMap.has(author.AuthorID))
          .map(author => ({
            rawid: author.AuthorID,
            avatar_url: buildCdnUrl(author['Author Avatar']),
            website: cleanString(author.Website),
            twitter_account: cleanString(author['X Account']),
            born: cleanString(author.Born)
          }))

        // 批量创建作者
        if (toCreate.length > 0) {
          await tx.authors.createMany({
            data: toCreate as any,
            skipDuplicates: true
          })
        }

        // 获取所有作者ID（包括新创建的）
        const allAuthors = await tx.authors.findMany({
          where: {
            rawid: { in: batch.map(a => a.AuthorID) }
          } as any,
          select: { id: true, rawid: true }
        })

        const authorIdMap = new Map(allAuthors.map(a => [a.rawid, a.id]))

        // 批量处理翻译
        for (const author of batch) {
          const authorId = authorIdMap.get(author.AuthorID)
          if (authorId) {
            await tx.author_translations.upsert({
              where: {
                author_id_language_id: {
                  author_id: authorId,
                  language_id: 'en'
                }
              },
              update: {
                name: author.Author,
                biography: cleanString(author['Author Desc'])
              },
              create: {
                author_id: authorId,
                language_id: 'en',
                name: author.Author,
                biography: cleanString(author['Author Desc']),
                is_default: 1
              }
            })
          }
        }
      }, { timeout: TRANSACTION_TIMEOUT })
    })

    reportProgress((i + 1) * BATCH_SIZE, authorData.length, '作者导入', startTime)
  }

  console.log('作者数据导入完成')
}

// 优化的批量导入分类
async function importCategoriesBatch(categoryData: CategoryData[]): Promise<void> {
  console.log(`开始批量导入分类数据，共 ${categoryData.length} 条记录`)

  const batches = createBatches(categoryData)
  const startTime = Date.now()

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理第 ${i + 1}/${batches.length} 批分类数据 (${batch.length} 条)`)

    await withRetry(async () => {
      await prisma.$transaction(async (tx) => {
        const existingCategories = await tx.categories.findMany({
          where: {
            rawid: { in: batch.map(c => c.GenreID) }
          } as any,
          select: { id: true, rawid: true }
        })

        const existingMap = new Map(existingCategories.map(c => [c.rawid, c.id]))

        const toCreate = batch
          .filter(category => !existingMap.has(category.GenreID))
          .map(category => ({ rawid: category.GenreID }))

        if (toCreate.length > 0) {
          await tx.categories.createMany({
            data: toCreate as any,
            skipDuplicates: true
          })
        }

        const allCategories = await tx.categories.findMany({
          where: {
            rawid: { in: batch.map(c => c.GenreID) }
          } as any,
          select: { id: true, rawid: true }
        })

        const categoryIdMap = new Map(allCategories.map(c => [c.rawid, c.id]))

        for (const category of batch) {
          const categoryId = categoryIdMap.get(category.GenreID)
          if (categoryId) {
            await tx.category_translations.upsert({
              where: {
                category_id_language_id: {
                  category_id: categoryId,
                  language_id: 'en'
                }
              },
              update: {
                name: category.Genre,
                description: cleanString(category.GenreDesc)
              },
              create: {
                category_id: categoryId,
                language_id: 'en',
                name: category.Genre,
                description: cleanString(category.GenreDesc),
                is_default: 1
              }
            })
          }
        }
      }, { timeout: TRANSACTION_TIMEOUT })
    })

    reportProgress((i + 1) * BATCH_SIZE, categoryData.length, '分类导入', startTime)
  }

  console.log('分类数据导入完成')
}

export { importAuthorsBatch, importCategoriesBatch, isRemoteDatabase }
