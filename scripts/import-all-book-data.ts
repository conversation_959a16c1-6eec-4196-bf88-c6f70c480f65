/**
 * 完整的书籍数据导入脚本
 * 处理所有JSON数据文件并导入到数据库
 * 特别处理review_summary和plot_summary字段的映射
 */

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import {
  createOptimizedPrismaClient,
  getDatabaseConfig,
  withRetry,
  createBatches,
  reportProgress,
  detectNetworkType
} from './db-config'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const prisma = createOptimizedPrismaClient()
const dbConfig = getDatabaseConfig()

// 日志目录
const logDir = path.join(__dirname, '..', 'logs')
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

console.log('process.env', process.env)
// 日志文件
const logFile = path.join(logDir, `import-${new Date().toISOString().replace(/[:.]/g, '-')}.log`)
const errorFile = path.join(logDir, `import-error-${new Date().toISOString().replace(/[:.]/g, '-')}.log`)
const logStream = fs.createWriteStream(logFile, { flags: 'a' })
const errorStream = fs.createWriteStream(errorFile, { flags: 'a' })

// 进度文件
const progressFilePath = path.join(__dirname, 'import-progress.json')

// 数据文件路径
const dataBasePath = path.join(__dirname, 'book-data')
const authorFilePath = path.join(dataBasePath, 'Auhtor Databse Management.json')
const categoryFilePath = path.join(dataBasePath, 'Genere Databse Management.json')
const bookFilePath = path.join(dataBasePath, 'Main Content and Summary.json')
const bookDetailFilePath = path.join(dataBasePath, 'Brief Analysis and Book editions.json')

// 数据类型定义
interface ImportProgress {
  authors: string[]
  categories: string[]
  books: string[]
  bookDetails: string[]
  chapters: string[]
}

interface MainContentData {
  BookID: string
  'book title': string
  'book subtitle'?: string
  "seo description": string
  Author: string
  Genre: string
  'Best Quotes'?: string
  'Review Summary'?: string
  'PDF file'?: string
  'EUPB FIle'?: string
  'Rate Score'?: number
  Ratings?: number
  'Book Cover'?: string
  BookSummary?: string
}

interface AuthorData {
  AuthorID: string
  Author: string
  BookID: string
  'Author Avatar'?: string
  Website?: string
  'X Account'?: string
  'Author Desc'?: string
  Born?: string
}

interface CategoryData {
  GenreID: string
  Genre: string
  BookID: string
  GenreDesc?: string
}

interface BookDetailData {
  BookID: string
  'brief analysis'?: string
  Author?: string
  Content?: string
  Genre?: string
  Binding?: string
  Publisher?: string
  ASIN?: string
  ISBN?: string
  ISBN13?: string
  'Publish Date'?: string
  Language?: string
  File?: string
}

interface ChapterData {
  id: string
  number: number
  title: string
  summary: string
}

interface ImportResult {
  success: number
  failed: number
  skipped: number
  errors: Array<{ id: string, error: string }>
}

// 日志工具
const logger = {
  info: (message: string) => {
    const logMessage = `[INFO] ${new Date().toISOString()} - ${message}\n`
    console.log(logMessage.trim())
    logStream.write(logMessage)
  },

  warn: (message: string) => {
    const logMessage = `[WARN] ${new Date().toISOString()} - ${message}\n`
    console.warn(logMessage.trim())
    logStream.write(logMessage)
  },

  error: (message: string, error?: any) => {
    const errorDetails = error ? `\n${error.stack || error}` : ''
    const logMessage = `[ERROR] ${new Date().toISOString()} - ${message}${errorDetails}\n`
    console.error(logMessage.trim())
    logStream.write(logMessage)
    errorStream.write(logMessage)
  },

  success: (message: string) => {
    const logMessage = `[SUCCESS] ${new Date().toISOString()} - ${message}\n`
    console.log(logMessage.trim())
    logStream.write(logMessage)
  },

  close: () => {
    logStream.end()
    errorStream.end()
  }
}

// 工具函数
function loadProgress(): ImportProgress {
  try {
    if (fs.existsSync(progressFilePath)) {
      const data = fs.readFileSync(progressFilePath, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    logger.warn(`无法加载进度文件: ${error instanceof Error ? error.message : String(error)}`)
  }

  return {
    authors: [],
    categories: [],
    books: [],
    bookDetails: [],
    chapters: []
  }
}

function saveProgress(progress: ImportProgress): void {
  try {
    fs.writeFileSync(progressFilePath, JSON.stringify(progress, null, 2), 'utf8')
  } catch (error) {
    logger.error(`保存进度文件失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

function readJsonFile<T>(filePath: string): T[] {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }

    const data = fs.readFileSync(filePath, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    logger.error(`读取JSON文件失败: ${filePath}`, error)
    throw error
  }
}

function extractYearFromDate(dateStr?: string): number | null {
  if (!dateStr) return null

  const match = dateStr.match(/(\d{4})/)
  if (match && match[1]) {
    return parseInt(match[1], 10)
  }

  return null
}

function validateRequiredFields(data: any, fields: string[]): string[] {
  const missingFields: string[] = []

  for (const field of fields) {
    if (!data[field]) {
      missingFields.push(field)
    }
  }

  return missingFields
}

function cleanString(str?: string | null | undefined): string | null {
  if (!str || typeof str !== 'string') return null
  return str.trim()
}

// CDN基础URL
const CDN_BASE_URL = 'https://cdn.15minutes.ai'

// 处理文件URL，添加CDN前缀
function buildCdnUrl(filePath?: string): string | null {
  if (!filePath) return null

  // 清理路径：处理正斜杠和反斜杠，确保路径格式一致
  let cleanPath = filePath
    .replace(/^[\/\\]+/, '') // 去除前导的正斜杠或反斜杠
    .replace(/\\/g, '/') // 将所有反斜杠转换为正斜杠
    .trim()

  // 如果路径为空，返回null
  if (!cleanPath) return null

  // 构建完整的CDN URL
  return `${CDN_BASE_URL}/${cleanPath}`
}

// 导入作者数据 - 批量优化版本
async function importAuthors(authorData: AuthorData[], progress: ImportProgress): Promise<ImportResult> {
  logger.info(`开始导入作者数据，共 ${authorData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  // 使用优化的批量处理配置
  const batches = createBatches(authorData, dbConfig.batchSize)
  const startTime = Date.now()

  logger.info(`检测到网络类型: ${detectNetworkType()}`)
  logger.info(`数据已分为 ${batches.length} 批，每批最多 ${dbConfig.batchSize} 条记录`)

  // 批量处理每一批数据
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex]
    logger.info(`正在处理第 ${batchIndex + 1}/${batches.length} 批数据，共 ${batch.length} 条记录`)

    try {
      // 使用重试机制和优化的事务处理整批数据
      await withRetry(async () => {
        await prisma.$transaction(async (tx) => {
          // 预查询现有作者
          const existingAuthors = await tx.authors.findMany({
            where: {
              rawid: {
                in: batch.map(author => author.AuthorID)
              }
            } as any,
            select: { id: true, rawid: true }
          })

          const existingAuthorsMap = new Map(
            existingAuthors.map(author => [author.rawid, author])
          )

          // 准备批量创建和更新的数据
          const authorsToCreate = []
          const authorsToUpdate = []
          const translationsToUpsert = []

          for (const author of batch) {
            // 验证必填字段
            const missingFields = validateRequiredFields(author, ['AuthorID', 'Author'])
            if (missingFields.length > 0) {
              result.failed++
              result.errors.push({ id: author.AuthorID, error: `缺少必填字段: ${missingFields.join(', ')}` })
              continue
            }

            const existingAuthor = existingAuthorsMap.get(author.AuthorID)
            const authorData = {
              rawid: author.AuthorID,
              avatar_url: buildCdnUrl(author['Author Avatar']),
              website: cleanString(author.Website),
              twitter_account: cleanString(author['X Account']),
              born: cleanString(author.Born)
            }

            if (!existingAuthor) {
              authorsToCreate.push(authorData)
            } else {
              authorsToUpdate.push({
                id: existingAuthor.id,
                data: authorData
              })
            }
          }

          // 批量创建新作者
          const createdAuthors = []
          if (authorsToCreate.length > 0) {
            // 使用 createMany 然后查询获取ID
            await tx.authors.createMany({
              data: authorsToCreate as any,
              skipDuplicates: true
            })

            // 查询刚创建的作者获取ID
            const newlyCreatedAuthors = await tx.authors.findMany({
              where: {
                rawid: {
                  in: authorsToCreate.map(author => author.rawid)
                }
              } as any,
              select: { id: true, rawid: true }
            })
            createdAuthors.push(...newlyCreatedAuthors)
          }

          // 批量更新现有作者
          const updatedAuthors = []
          for (const updateItem of authorsToUpdate) {
            await tx.authors.update({
              where: { id: updateItem.id },
              data: updateItem.data as any
            })
            updatedAuthors.push({ id: updateItem.id, rawid: updateItem.data.rawid })
          }

          // 合并所有作者记录（新创建的 + 更新的）
          const allAuthorRecords = [
            ...createdAuthors,
            ...updatedAuthors
          ]

          // 创建作者ID映射
          const authorIdMap = new Map(
            allAuthorRecords.map(author => [author.rawid, author.id])
          )

          // 准备翻译数据
          for (const author of batch) {
            const authorId = authorIdMap.get(author.AuthorID)
            if (authorId) {
              translationsToUpsert.push({
                author_id: authorId,
                language_id: 'en',
                name: author.Author,
                biography: cleanString(author['Author Desc']),
                is_default: 1
              })
            }
          }

          // 批量处理翻译
          for (const translation of translationsToUpsert) {
            await tx.author_translations.upsert({
              where: {
                author_id_language_id: {
                  author_id: translation.author_id,
                  language_id: translation.language_id
                }
              },
              update: {
                name: translation.name,
                biography: translation.biography
              },
              create: translation
            })
          }

          // 更新成功计数
          result.success += batch.length - result.errors.filter(e =>
            batch.some(author => author.AuthorID === e.id)
          ).length
        }, {
          timeout: dbConfig.transactionTimeout
        })
      })

      // 批量更新进度
      const batchIds = batch.map(author => author.AuthorID)
      progress.authors.push(...batchIds.filter(id => !progress.authors.includes(id)))
      saveProgress(progress)

      logger.success(`第 ${batchIndex + 1} 批数据处理完成`)

      // 报告进度
      reportProgress((batchIndex + 1) * dbConfig.batchSize, authorData.length, '作者导入', startTime)
    } catch (error) {
      // 如果批量处理失败，回退到逐条处理
      logger.warn(`第 ${batchIndex + 1} 批数据批量处理失败，回退到逐条处理: ${error}`)

      for (const author of batch) {
        try {
          await importSingleAuthor(author, progress)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: author.AuthorID, error: errorMessage })
          logger.error(`导入作者失败: ${author.AuthorID} (${author.Author})`, singleError)
        }
      }
    }
  }

  logger.info(`作者数据导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条作者导入的回退函数
async function importSingleAuthor(author: AuthorData, progress: ImportProgress): Promise<void> {
  await prisma.$transaction(async (tx) => {
    let authorRecord = await tx.authors.findFirst({
      where: { rawid: author.AuthorID } as any
    })

    if (!authorRecord) {
      authorRecord = await tx.authors.create({
        data: {
          rawid: author.AuthorID,
          avatar_url: buildCdnUrl(author['Author Avatar']),
          website: cleanString(author.Website),
          twitter_account: cleanString(author['X Account']),
          born: cleanString(author.Born)
        } as any
      })
    } else {
      authorRecord = await tx.authors.update({
        where: { id: authorRecord.id },
        data: {
          rawid: author.AuthorID,
          avatar_url: buildCdnUrl(author['Author Avatar']),
          website: cleanString(author.Website),
          twitter_account: cleanString(author['X Account']),
          born: cleanString(author.Born)
        } as any
      })
    }

    await tx.author_translations.upsert({
      where: {
        author_id_language_id: {
          author_id: authorRecord.id,
          language_id: 'en'
        }
      },
      update: {
        name: author.Author,
        biography: cleanString(author['Author Desc'])
      },
      create: {
        author_id: authorRecord.id,
        language_id: 'en',
        name: author.Author,
        biography: cleanString(author['Author Desc']),
        is_default: 1
      }
    })
  })

  if (!progress.authors.includes(author.AuthorID)) {
    progress.authors.push(author.AuthorID)
    saveProgress(progress)
  }
}

// 导入分类数据 - 批量优化版本
async function importCategories(categoryData: CategoryData[], progress: ImportProgress): Promise<ImportResult> {
  logger.info(`开始导入分类数据，共 ${categoryData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  // 使用优化的批量处理配置
  const batches = createBatches(categoryData, dbConfig.batchSize)
  const startTime = Date.now()

  logger.info(`数据已分为 ${batches.length} 批，每批最多 ${dbConfig.batchSize} 条记录`)

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex]
    logger.info(`正在处理第 ${batchIndex + 1}/${batches.length} 批分类数据，共 ${batch.length} 条记录`)

    try {
      await prisma.$transaction(async (tx) => {
        // 预查询现有分类
        const existingCategories = await tx.categories.findMany({
          where: {
            rawid: {
              in: batch.map(category => category.GenreID)
            }
          } as any,
          select: { id: true, rawid: true }
        })

        const existingCategoriesMap = new Map(
          existingCategories.map(category => [category.rawid, category])
        )

        const categoriesToCreate = []
        const categoriesToUpdate = []

        for (const category of batch) {
          const missingFields = validateRequiredFields(category, ['GenreID', 'Genre'])
          if (missingFields.length > 0) {
            result.failed++
            result.errors.push({ id: category.GenreID, error: `缺少必填字段: ${missingFields.join(', ')}` })
            continue
          }

          const existingCategory = existingCategoriesMap.get(category.GenreID)
          const categoryData = {
            rawid: category.GenreID
          }

          if (!existingCategory) {
            categoriesToCreate.push(categoryData)
          } else {
            categoriesToUpdate.push({
              id: existingCategory.id,
              data: categoryData
            })
          }
        }

        // 批量创建新分类
        const createdCategories = []
        if (categoriesToCreate.length > 0) {
          await tx.categories.createMany({
            data: categoriesToCreate as any,
            skipDuplicates: true
          })

          const newlyCreatedCategories = await tx.categories.findMany({
            where: {
              rawid: {
                in: categoriesToCreate.map(category => category.rawid)
              }
            } as any,
            select: { id: true, rawid: true }
          })
          createdCategories.push(...newlyCreatedCategories)
        }

        // 批量更新现有分类
        const updatedCategories = []
        for (const updateItem of categoriesToUpdate) {
          await tx.categories.update({
            where: { id: updateItem.id },
            data: updateItem.data as any
          })
          updatedCategories.push({ id: updateItem.id, rawid: updateItem.data.rawid })
        }

        // 合并所有分类记录
        const allCategoryRecords = [
          ...createdCategories,
          ...updatedCategories
        ]

        const categoryIdMap = new Map(
          allCategoryRecords.map(category => [category.rawid, category.id])
        )

        // 批量处理翻译
        for (const category of batch) {
          const categoryId = categoryIdMap.get(category.GenreID)
          if (categoryId) {
            await tx.category_translations.upsert({
              where: {
                category_id_language_id: {
                  category_id: categoryId,
                  language_id: 'en'
                }
              },
              update: {
                name: category.Genre,
                description: cleanString(category.GenreDesc)
              },
              create: {
                category_id: categoryId,
                language_id: 'en',
                name: category.Genre,
                description: cleanString(category.GenreDesc),
                is_default: 1
              }
            })
          }
        }

        result.success += batch.length - result.errors.filter(e =>
          batch.some(category => category.GenreID === e.id)
        ).length
      }, {
        timeout: 60000
      })

      // 批量更新进度
      const batchIds = batch.map(category => category.GenreID)
      progress.categories.push(...batchIds.filter(id => !progress.categories.includes(id)))
      saveProgress(progress)

      logger.success(`第 ${batchIndex + 1} 批分类数据处理完成`)

      // 报告进度
      reportProgress((batchIndex + 1) * dbConfig.batchSize, categoryData.length, '分类导入', startTime)
    } catch (error) {
      logger.warn(`第 ${batchIndex + 1} 批分类数据批量处理失败，回退到逐条处理: ${error}`)

      for (const category of batch) {
        try {
          await importSingleCategory(category, progress)
          result.success++
        } catch (singleError) {
          result.failed++
          const errorMessage = singleError instanceof Error ? singleError.message : String(singleError)
          result.errors.push({ id: category.GenreID, error: errorMessage })
          logger.error(`导入分类失败: ${category.GenreID} (${category.Genre})`, singleError)
        }
      }
    }
  }

  logger.info(`分类数据导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 单条分类导入的回退函数
async function importSingleCategory(category: CategoryData, progress: ImportProgress): Promise<void> {
  await prisma.$transaction(async (tx) => {
    let categoryRecord = await tx.categories.findFirst({
      where: { rawid: category.GenreID } as any
    })

    if (!categoryRecord) {
      categoryRecord = await tx.categories.create({
        data: { rawid: category.GenreID } as any
      })
    } else {
      categoryRecord = await tx.categories.update({
        where: { id: categoryRecord.id },
        data: { rawid: category.GenreID } as any
      })
    }

    await tx.category_translations.upsert({
      where: {
        category_id_language_id: {
          category_id: categoryRecord.id,
          language_id: 'en'
        }
      },
      update: {
        name: category.Genre,
        description: cleanString(category.GenreDesc)
      },
      create: {
        category_id: categoryRecord.id,
        language_id: 'en',
        name: category.Genre,
        description: cleanString(category.GenreDesc),
        is_default: 1
      }
    })
  })

  if (!progress.categories.includes(category.GenreID)) {
    progress.categories.push(category.GenreID)
    saveProgress(progress)
  }
}

// 导入书籍基本信息
async function importBooks(bookData: MainContentData[], progress: ImportProgress): Promise<ImportResult> {
  logger.info(`开始导入书籍基本信息，共 ${bookData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  // 预加载所有作者的映射关系
  const authorsMap = new Map<string, number>()
  const allAuthors = await prisma.authors.findMany({
    select: { id: true, rawid: true }
  })
  allAuthors.forEach(author => {
    if (author.rawid) {
      authorsMap.set(author.rawid, author.id)
    }
  })

  // 预加载所有分类的映射关系
  const categoriesMap = new Map<string, number>()
  const allCategories = await prisma.categories.findMany({
    select: { id: true, rawid: true }
  })
  allCategories.forEach(category => {
    if (category.rawid) {
      categoriesMap.set(category.rawid, category.id)
    }
  })

  for (const book of bookData) {
    try {
      // 检查是否已导入 - 仅记录日志，不跳过处理
      if (progress.books.includes(book.BookID)) {
        logger.info(`书籍 ${book.BookID} (${book['book title']}) 已存在，将更新数据`)
      }

      // 验证核心必填字段
      const coreMissingFields = validateRequiredFields(book, ['BookID', 'book title'])
      if (coreMissingFields.length > 0) {
        throw new Error(`缺少核心必填字段: ${coreMissingFields.join(', ')}`)
      }

      // 检查可选字段，给出警告但不阻止导入
      const optionalMissingFields = validateRequiredFields(book, ['Author', 'Genre'])
      if (optionalMissingFields.length > 0) {
        logger.warn(`书籍 ${book.BookID} (${book['book title']}) 缺少字段: ${optionalMissingFields.join(', ')}，将使用默认值`)
      }

      // 使用事务处理
      await prisma.$transaction(async (tx) => {
        // 查找或创建书籍 - 只使用 rawid 进行匹配
        let bookRecord = await tx.books.findFirst({
          where: {
            rawid: book.BookID
          }
        })

        if (!bookRecord) {
          // 创建新书籍 - 使用 Prisma 的 create 方法
          const bookData: any = {
            pdf_url: buildCdnUrl(book['PDF file']),
            epub_url: buildCdnUrl(book['EUPB FIle']),
            is_published: 1,
            rate_score: book['Rate Score'] ? Number(book['Rate Score']) : null,
            total_ratings: book.Ratings ? Number(book.Ratings) : 0
          }

          // 添加 rawid 字段（如果存在）
          if (book.BookID) {
            bookData.rawid = book.BookID
          }

          bookRecord = await tx.books.create({
            data: bookData
          })
        } else {
          // 更新现有书籍 - 使用 Prisma 的 update 方法
          const updateData: any = {
            pdf_url: buildCdnUrl(book['PDF file']),
            epub_url: buildCdnUrl(book['EUPB FIle']),
            is_published: 1,
            rate_score: book['Rate Score'] ? Number(book['Rate Score']) : null,
            total_ratings: book.Ratings ? Number(book.Ratings) : 0
          }

          // 添加 rawid 字段（如果存在）
          if (book.BookID) {
            updateData.rawid = book.BookID
          }

          bookRecord = await tx.books.update({
            where: { id: bookRecord.id },
            data: updateData
          })
        }

        // 创建书籍翻译记录
        await tx.book_translations.upsert({
          where: {
            book_id_language_id: {
              book_id: bookRecord.id,
              language_id: 'en'
            }
          },
          update: {
            title: book['book title'],
            subtitle: cleanString(book['book subtitle']),
            description: cleanString(book['seo description']),
            review_summary: cleanString(book['Review Summary']), // 特别注意这里的映射
            best_quote: cleanString(book['Best Quotes'])
          },
          create: {
            book_id: bookRecord.id,
            language_id: 'en',
            title: book['book title'],
            subtitle: cleanString(book['book subtitle']),
            description: cleanString(book['seo description']),
            review_summary: cleanString(book['Review Summary']), // 特别注意这里的映射
            best_quote: cleanString(book['Best Quotes']),
            is_default: 1
          }
        })

        // 创建书籍封面记录
        if (book['Book Cover']) {
          const coverUrl = buildCdnUrl(book['Book Cover'])
          if (coverUrl) {
            // 查找现有封面
            const existingCover = await tx.book_covers.findFirst({
              where: {
                book_id: bookRecord.id,
                language_id: 'en',
                is_primary: 1
              }
            })

            if (existingCover) {
              // 更新现有封面
              await tx.book_covers.update({
                where: { id: existingCover.id },
                data: {
                  image_url: coverUrl
                }
              })
            } else {
              // 创建新封面
              await tx.book_covers.create({
                data: {
                  book_id: bookRecord.id,
                  language_id: 'en',
                  image_url: coverUrl,
                  is_primary: 1
                }
              })
            }
          }
        }

        // 处理作者关系
        if (book.Author) {
          const authorIds = book.Author.split(',').map(id => id.trim())
          for (const authorId of authorIds) {
            // 使用预加载的作者映射关系查找作者ID
            const authorDbId = authorsMap.get(authorId)

            if (!authorDbId) {
              logger.warn(`找不到作者 ${authorId}，跳过关联`)
              continue
            }

            // 检查关联是否已存在
            const existingRelation = await tx.book_authors.findFirst({
              where: {
                book_id: bookRecord.id,
                author_id: authorDbId
              }
            })

            if (!existingRelation) {
              // 创建新关联
              await tx.book_authors.create({
                data: {
                  book_id: bookRecord.id,
                  author_id: authorDbId,
                  author_order: 1
                }
              })
            } else {
              // 更新现有关联（如果需要）
              await tx.book_authors.update({
                where: {
                  book_id_author_id: {
                    book_id: bookRecord.id,
                    author_id: authorDbId
                  }
                },
                data: {
                  author_order: 1
                }
              })
            }
          }
        }

        // 处理分类关系
        if (book.Genre) {
          const genreIds = book.Genre.split(',').map(id => id.trim())
          for (const genreId of genreIds) {
            // 使用预加载的分类映射关系查找分类ID
            const categoryDbId = categoriesMap.get(genreId)

            if (!categoryDbId) {
              logger.warn(`找不到分类 ${genreId}，跳过关联`)
              continue
            }

            try {
              // 使用 upsert 操作，避免先查询再创建/更新
              await tx.book_categories.upsert({
                where: {
                  book_id_category_id: {
                    book_id: bookRecord.id,
                    category_id: categoryDbId
                  }
                },
                update: {}, // 不需要更新任何字段
                create: {
                  book_id: bookRecord.id,
                  category_id: categoryDbId
                }
              })
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error)
              logger.warn(`创建分类关联失败: ${genreId} - ${errorMessage}`)
            }
          }
        }
      })

      // 更新进度 - 确保不重复添加
      if (!progress.books.includes(book.BookID)) {
        progress.books.push(book.BookID)
        saveProgress(progress)
      }

      result.success++
      logger.success(`成功导入书籍: ${book.BookID} (${book['book title']})`)
    } catch (error) {
      result.failed++
      const errorMessage = error instanceof Error ? error.message : String(error)
      result.errors.push({ id: book.BookID, error: errorMessage })
      logger.error(`导入书籍失败: ${book.BookID} (${book['book title']})`, error)
    }
  }

  logger.info(`书籍基本信息导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 导入书籍详细信息
async function importBookDetails(bookDetailData: BookDetailData[], progress: ImportProgress): Promise<ImportResult> {
  logger.info(`开始导入书籍详细信息，共 ${bookDetailData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  for (const detail of bookDetailData) {
    try {
      // 检查是否已导入 - 仅记录日志，不跳过处理
      if (progress.bookDetails.includes(detail.BookID)) {
        logger.info(`书籍详情 ${detail.BookID} 已存在，将更新数据`)
      }

      // 验证必填字段
      const missingFields = validateRequiredFields(detail, ['BookID'])
      if (missingFields.length > 0) {
        throw new Error(`缺少必填字段: ${missingFields.join(', ')}`)
      }

      // 使用事务处理
      await prisma.$transaction(async (tx) => {
        // 查找书籍 - 只使用 rawid 进行匹配
        const book = await tx.books.findFirst({
          where: {
            rawid: detail.BookID
          }
        })

        if (!book) {
          throw new Error(`找不到书籍 ${detail.BookID}`)
        }

        // 提取出版年份
        const publicationYear = extractYearFromDate(detail['Publish Date'])

        // 更新书籍记录
        await tx.books.update({
          where: { id: book.id },
          data: {
            isbn: cleanString(detail.ISBN),
            isbn13: cleanString(detail.ISBN13),
            asin: cleanString(detail.ASIN),
            publication_year: publicationYear,
            content_type: cleanString(detail.Content),
            binding: cleanString(detail.Binding)
          } as any
        })

        // 更新书籍翻译记录 - 特别注意 plot_summary 字段
        await tx.book_translations.updateMany({
          where: {
            book_id: book.id,
            language_id: 'en'
          },
          data: {
            plot_summary: cleanString(detail['brief analysis']) // 特别注意这里的映射
          }
        })

        // 如果有出版商信息，处理出版商关系
        if (detail.Publisher) {
          // 查找出版商翻译
          const publisherTranslation = await tx.publisher_translations.findFirst({
            where: {
              name: detail.Publisher
            }
          })

          let publisherId: number | null = null

          if (publisherTranslation && publisherTranslation.publisher_id) {
            publisherId = publisherTranslation.publisher_id
          } else {
            // 创建新出版商
            const newPublisher = await tx.publishers.create({
              data: {}
            })

            // 创建出版商翻译
            await tx.publisher_translations.create({
              data: {
                publisher_id: newPublisher.id,
                language_id: 'en',
                name: detail.Publisher,
                is_default: 1
              }
            })

            publisherId = newPublisher.id
          }

          // 更新书籍的出版商ID
          if (publisherId) {
            await tx.books.update({
              where: { id: book.id },
              data: {
                publisher_id: publisherId
              }
            })
          }
        }
      })

      // 更新进度 - 确保不重复添加
      if (!progress.bookDetails.includes(detail.BookID)) {
        progress.bookDetails.push(detail.BookID)
        saveProgress(progress)
      }

      result.success++
      logger.success(`成功导入书籍详情: ${detail.BookID}`)
    } catch (error) {
      result.failed++
      const errorMessage = error instanceof Error ? error.message : String(error)
      result.errors.push({ id: detail.BookID, error: errorMessage })
      logger.error(`导入书籍详情失败: ${detail.BookID}`, error)
    }
  }

  logger.info(`书籍详细信息导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 导入章节内容
async function importChapters(bookData: MainContentData[], progress: ImportProgress): Promise<ImportResult> {
  logger.info(`开始导入书籍章节内容，共 ${bookData.length} 条记录`)

  const result: ImportResult = {
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  for (const book of bookData) {
    try {
      // 检查是否已导入 - 仅记录日志，不跳过处理
      if (progress.chapters.includes(book.BookID)) {
        logger.info(`书籍章节 ${book.BookID} (${book['book title']}) 已存在，将更新数据`)
      }

      // 检查是否有章节内容文件路径
      if (!book.BookSummary) {
        logger.warn(`书籍 ${book.BookID} (${book['book title']}) 没有章节内容文件路径，跳过`)
        result.skipped++
        continue
      }

      // 构建章节内容文件的完整路径
      // 处理反斜杠路径问题：将反斜杠转换为正斜杠，并去除前导反斜杠
      const cleanBookSummaryPath = book.BookSummary
        .replace(/^[\\\/]+/, '') // 去除前导的反斜杠或正斜杠
        .replace(/\\/g, '/') // 将所有反斜杠转换为正斜杠

      const chapterFilePath = path.join(dataBasePath, cleanBookSummaryPath)

      // 检查文件是否存在
      if (!fs.existsSync(chapterFilePath)) {
        throw new Error(`章节内容文件不存在: ${chapterFilePath}`)
      }

      // 读取章节内容文件
      const chapterData = readJsonFile<ChapterData>(chapterFilePath)

      // 使用事务处理
      await prisma.$transaction(async (tx) => {
        // 查找书籍 - 只使用 rawid 进行匹配
        const bookRecord = await tx.books.findFirst({
          where: {
            rawid: book.BookID
          }
        })

        if (!bookRecord) {
          throw new Error(`找不到书籍 ${book.BookID}`)
        }

        // 确保数据是有效的 JSON
        const validJsonContent = JSON.parse(JSON.stringify(chapterData))

        // 查找现有章节记录
        const existingChapter = await tx.book_chapters.findFirst({
          where: {
            book_id: bookRecord.id,
            language_id: 'en'
          }
        })

        if (existingChapter) {
          // 更新现有章节记录
          await tx.book_chapters.update({
            where: { id: existingChapter.id },
            data: {
              content: validJsonContent
            }
          })
        } else {
          // 创建新章节记录
          await tx.book_chapters.create({
            data: {
              book_id: bookRecord.id,
              language_id: 'en',
              content: validJsonContent
            }
          })
        }
      })

      // 更新进度 - 确保不重复添加
      if (!progress.chapters.includes(book.BookID)) {
        progress.chapters.push(book.BookID)
        saveProgress(progress)
      }

      result.success++
      logger.success(`成功导入书籍章节: ${book.BookID} (${book['book title']})`)
    } catch (error) {
      result.failed++
      const errorMessage = error instanceof Error ? error.message : String(error)
      result.errors.push({ id: book.BookID, error: errorMessage })
      logger.error(`导入书籍章节失败: ${book.BookID} (${book['book title']})`, error)
    }
  }

  logger.info(`书籍章节内容导入完成: 成功 ${result.success} 条, 失败 ${result.failed} 条, 跳过 ${result.skipped} 条`)
  return result
}

// 主函数
async function main() {
  logger.info('开始数据导入流程')

  try {
    // 加载进度
    const progress = loadProgress()
    logger.info(`已加载导入进度: 作者 ${progress.authors.length} 条, 分类 ${progress.categories.length} 条, 书籍 ${progress.books.length} 条, 书籍详情 ${progress.bookDetails.length} 条, 章节 ${progress.chapters.length} 条`)

    // 1. 导入作者数据
    if (fs.existsSync(authorFilePath)) {
      const authorData = readJsonFile<AuthorData>(authorFilePath)
      await importAuthors(authorData, progress)
    } else {
      logger.warn(`作者数据文件不存在: ${authorFilePath}`)
    }

    // 2. 导入分类数据
    if (fs.existsSync(categoryFilePath)) {
      const categoryData = readJsonFile<CategoryData>(categoryFilePath)
      await importCategories(categoryData, progress)
    } else {
      logger.warn(`分类数据文件不存在: ${categoryFilePath}`)
    }

    // 3. 导入书籍基本信息
    if (fs.existsSync(bookFilePath)) {
      const bookData = readJsonFile<MainContentData>(bookFilePath)
      await importBooks(bookData, progress)

      // 5. 导入书籍章节内容 (在书籍基本信息导入后进行)
      await importChapters(bookData, progress)
    } else {
      logger.warn(`书籍数据文件不存在: ${bookFilePath}`)
    }

    // 4. 导入书籍详细信息
    if (fs.existsSync(bookDetailFilePath)) {
      const bookDetailData = readJsonFile<BookDetailData>(bookDetailFilePath)
      await importBookDetails(bookDetailData, progress)
    } else {
      logger.warn(`书籍详情数据文件不存在: ${bookDetailFilePath}`)
    }

    logger.success('数据导入流程完成')
  } catch (error) {
    logger.error('数据导入过程中出错', error)
  } finally {
    // 关闭Prisma客户端
    await prisma.$disconnect()
    // 关闭日志
    logger.close()
  }
}

// 命令行参数处理
async function run() {
  const args = process.argv.slice(2)
  const command = args[0]

  if (command === 'authors') {
    // 仅导入作者数据
    const progress = loadProgress()
    const authorData = readJsonFile<AuthorData>(authorFilePath)
    await importAuthors(authorData, progress)
  } else if (command === 'categories') {
    // 仅导入分类数据
    const progress = loadProgress()
    const categoryData = readJsonFile<CategoryData>(categoryFilePath)
    await importCategories(categoryData, progress)
  } else if (command === 'books') {
    // 仅导入书籍基本信息
    const progress = loadProgress()
    const bookData = readJsonFile<MainContentData>(bookFilePath)
    await importBooks(bookData, progress)
  } else if (command === 'details') {
    // 仅导入书籍详细信息
    const progress = loadProgress()
    const bookDetailData = readJsonFile<BookDetailData>(bookDetailFilePath)
    await importBookDetails(bookDetailData, progress)
  } else if (command === 'chapters') {
    // 仅导入章节内容
    const progress = loadProgress()
    const bookData = readJsonFile<MainContentData>(bookFilePath)
    await importChapters(bookData, progress)
  } else if (command === 'reset') {
    // 重置进度
    fs.writeFileSync(progressFilePath, JSON.stringify({
      authors: [],
      categories: [],
      books: [],
      bookDetails: [],
      chapters: []
    }, null, 2), 'utf8')
    logger.info('已重置导入进度')
  } else {
    // 执行完整导入流程
    await main()
  }

  // 关闭Prisma客户端
  await prisma.$disconnect()
  // 关闭日志
  logger.close()
}

// 如果直接运行此脚本（而不是作为模块导入）
if (import.meta.url === `file://${process.argv[1]}`) {
  run().catch(error => {
    console.error('导入脚本执行失败:', error)
    process.exit(1)
  })
}

// 导出函数供其他模块使用
export {
  importAuthors,
  importCategories,
  importBooks,
  importBookDetails,
  importChapters,
  main
}
