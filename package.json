{"name": "book-summary", "version": "0.0.0", "private": true, "scripts": {"ts": "tsc", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "setup": "bun scripts/setup.ts", "dev": "next dev --turbopack --port=4000", "worker": "bun worker.ts", "dev:all": "prisma generate && concurrently \"bun worker\" \"bun dev\"", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "csv": "bun scripts/csv2json.ts", "i18n": "bun scripts/json2csv.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:format": "prisma format", "db:push": "prisma db push", "db:pull": "prisma db pull", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio --port 5556", "db:comment:pull": "bun scripts/dbCommentPull.ts", "db:comment:push": "bun scripts/dbCommentPush.ts", "db:comment:format": "bun scripts/dbCommentFormat.ts", "db:map": "bun scripts/dbMap.ts", "db:import:book": "bun scripts/import-book-data.ts", "db:import:all": "bun scripts/import-all-book-data.ts", "db:import:all:fast": "bun scripts/import-optimized.ts", "db:import:details:only": "bun scripts/import-book-details-only.ts", "db:import:authors": "bun scripts/import-all-book-data.ts authors", "db:import:categories": "bun scripts/import-all-book-data.ts categories", "db:import:books": "bun scripts/import-all-book-data.ts books", "db:import:details": "bun scripts/import-all-book-data.ts details", "db:import:chapters": "bun scripts/import-all-book-data.ts chapters", "db:import:reset": "bun scripts/import-all-book-data.ts reset", "image:export": "bun scripts/image-process/scripts/export-book-metadata.ts", "image:create": "bun scripts/image-process/scripts/create-tasks.ts", "image:poll": "bun scripts/image-process/scripts/poll-tasks.ts", "image:download": "bun scripts/image-process/scripts/download-images.ts", "image:status": "bun scripts/image-process/scripts/show-status.ts", "image:reset": "bun scripts/image-process/scripts/reset-tasks.ts", "image:clean": "bun scripts/image-process/scripts/clean-completed.ts", "image:smart-reset": "bun scripts/image-process/scripts/reset-failed-and-create.ts", "image:process": "bun scripts/image-process/scripts/full-process.ts", "image:legacy": "bun scripts/image-process/scripts/batch-image-processing.ts", "image:test": "bun scripts/test-image-processing.ts", "test:audio": "node tests/test-audio-api.js", "test:audio:progress": "node scripts/test-audio-progress.js", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/themes": "^3.2.1", "@react-oauth/google": "^0.12.1", "@tailwindcss/forms": "^0.5.9", "@types/js-cookie": "^3.0.6", "ai": "^4.3.5", "amqplib": "^0.10.7", "aws-sdk": "^2.1692.0", "canvas-confetti": "^1.9.3", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "crypto-js": "^4.2.0", "csv-parser": "^3.1.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.11.17", "franc-min": "^6.2.0", "fs-extra": "^11.3.0", "input-otp": "^1.4.1", "ioredis": "^5.6.0", "js-cookie": "^3.0.5", "json2csv": "6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "ky": "^1.7.2", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "mongoose": "^8.13.2", "next": "15.0.2", "next-intl": "^3.25.1", "next-themes": "^0.4.4", "next-view-transitions": "^0.3.4", "p-limit": "^6.2.0", "pluralize": "^8.0.0", "prompts": "^2.4.2", "react": "19.0.0", "react-compare-slider": "^3.1.0", "react-cookie": "^7.2.2", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-h5-audio-player": "^3.10.0", "react-hook-form": "^7.53.2", "react-markdown": "^10.1.0", "react-parallax-tilt": "^1.7.269", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "remark-gfm": "^4.0.1", "remeda": "2", "server-only": "^0.0.1", "shiki": "^1.29.2", "sonner": "^1.7.1", "storybook-addon-module-mock": "^1.3.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tailwindcss-rem2px-preset": "^1.0.3", "ts-morph": "^25.0.1", "usehooks-ts": "^3.1.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1", "zustand": "^5.0.1"}, "devDependencies": {"@ai-sdk/react": "^1.2.8", "@chromatic-com/storybook": "^3", "@google/genai": "^0.8.0", "@prisma/sdk": "^4.0.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/experimental-nextjs-vite": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@streamparser/json": "^0.0.22", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/typography": "^0.5.16", "@types/amqplib": "^0.10.7", "@types/canvas-confetti": "^1.6.4", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/pluralize": "^0.0.33", "@types/prompts": "^2.4.9", "@types/react": "^18", "@types/react-dom": "^18", "@vitest/browser": "^3.1.1", "@vitest/coverage-v8": "^3.1.1", "concurrently": "^9.1.2", "dotenv-cli": "^8.0.0", "esbuild": "^0.24.0", "eslint": "^9.24.0", "eslint-config-next": "15.0.2", "eslint-plugin-storybook": "^0.12.0", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "mysql2": "^3.14.0", "playwright": "^1.51.1", "postcss": "^8", "prettier": "3.3.3", "prisma": "^6.5.0", "storybook": "^8.6.12", "tailwindcss": "^4.1.3", "typescript": "^5", "vitest": "^3.1.1"}, "packageManager": "pnpm@9.14.2+sha512.6e2baf77d06b9362294152c851c4f278ede37ab1eba3a55fda317a4a17b209f4dbb973fb250a77abc463a341fcb1f17f17cfa24091c4eb319cda0d9b84278387", "msw": {"workerDirectory": ["public"]}}