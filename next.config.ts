import { destinationUrl } from '@/utils/constants'
import type { NextConfig } from 'next'
import createNextIntlPlugin from 'next-intl/plugin'
import fs from 'fs'
import path from 'path'

const withNextIntl = createNextIntlPlugin()

const envPath = path.resolve(process.cwd(), '.env')
if (process.env.NODE_ENV !== 'production' && !fs.existsSync(envPath)) {
  console.error('\x1b[31m%s\x1b[0m', '错误: 缺少.env文件!')
  console.error('\x1b[33m%s\x1b[0m', '请根据.env.example创建.env文件')
  process.exit(1)
}

console.log('process env vars: ======', process.env)

const nextConfig: NextConfig = {
  reactStrictMode: false,
  async rewrites() {
    return [
      {
        source: '/common-api/:path*',
        destination: `${destinationUrl}/common-api/:path*`
      },
      {
        source: '/self-api/:path*',
        destination: `${destinationUrl}/self-api/:path*`
      }
    ]
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '**'
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
        pathname: '**'
      },
      {
        protocol: 'https',
        hostname: 'cdn.15minutes.ai',
        pathname: '**'
      }
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: process.env.NODE_ENV !== 'production' // 在开发环境中禁用图片优化，以避免路径问题
  },
  env: {
    NEXT_BUILD_TIME: new Date().toISOString()
  },
  experimental: {
    // optimizeCss: true,
    serverActions: {
      allowedOrigins: [
        '15minutes.tangshu.com',
        '15minutes.ai',
        'www.15minutes.ai'
      ]
    },
    optimisticClientCache: true,
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-tooltip',
      'class-variance-authority',
      'clsx',
      'date-fns',
      'framer-motion',
      'recharts',
      'sonner',
      'react-day-picker',
      'react-hook-form',
      'zod'
    ],
    optimizeServerReact: true
  }
}

export default withNextIntl(nextConfig)
