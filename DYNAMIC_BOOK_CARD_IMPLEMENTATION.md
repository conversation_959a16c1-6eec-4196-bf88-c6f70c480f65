# 动态书籍卡片三层结构实现

## 功能概述

已成功实现了基于三层结构的动态书籍卡片功能，具体包括：

### 三层结构设计

1. **背景色层** (z-index: 1)
   - 从后端返回的封面图片URL自动提取主色
   - 设置30%透明度作为整个卡片的背景色
   - 平滑的颜色过渡动画效果

2. **底图模板层** (z-index: 2)
   - 使用 `public/images/book-templates/book-cover.png` 作为统一的卡片模板
   - 提供一致的视觉风格和3D效果

3. **实际封面层** (z-index: 3)
   - 显示后端返回的真实书籍封面图片
   - 精确定位在底图模板的书籍区域内
   - 尺寸：176px × 256px，居中放置

4. **文本内容层** (z-index: 4)
   - 书名、作者、简介、操作按钮
   - 半透明白色背景确保文字清晰可读
   - 毛玻璃效果增强视觉层次

## 实现的文件

### 新增文件
- `src/hooks/useImageDominantColor.ts` - 图片主色提取Hook
- `src/components/common/DynamicBookCard.tsx` - 三层结构卡片组件

### 修改的文件
- `src/components/common/BookCover.tsx` - 增加颜色提取功能
- `src/app/[locale]/globals.css` - 添加三层结构CSS样式
- `src/app/[locale]/(home)/components/PopularSummaries.tsx` - 使用新卡片组件
- `src/app/[locale]/(home)/components/LatestSummaries.tsx` - 使用新卡片组件
- `src/app/[locale]/(home)/components/MonthlyPickedSummaries.tsx` - 使用新卡片组件

### 静态资源
- `public/images/book-templates/book-cover.png` - 底图模板文件

## 技术特性

### 颜色提取算法
- 使用Canvas API分析图片像素
- 智能过滤白色、黑色和透明像素
- 颜色分组算法提高提取准确性
- 性能优化：缩放图片到100x100进行分析

### 性能优化
- 懒加载和优先级加载
- 颜色提取防抖处理
- CSS硬件加速
- 图片错误处理和降级方案

### 响应式设计
- 保持原有卡片尺寸和布局
- 适配不同屏幕尺寸
- 平滑的hover动画效果

## 使用方式

```tsx
import DynamicBookCard from '@/components/common/DynamicBookCard'

<DynamicBookCard
  id={book.id}
  coverUrl={book.coverUrl} // 后端返回的图片URL
  title={book.title}
  authors={book.authors}
  description={book.description}
  priority={true} // 是否优先加载
  fallbackColor="e9e1cc" // 备用颜色
/>
```

## 视觉效果

- ✅ 动态背景色：自动从封面提取主色，30%透明度
- ✅ 统一模板：所有卡片使用相同的底图模板
- ✅ 真实封面：后端图片精确放置在模板区域
- ✅ 清晰文字：半透明背景确保文字可读性
- ✅ 平滑动画：颜色过渡和hover效果
- ✅ 保持尺寸：与原有卡片大小完全一致

## 兼容性

- 保持现有BookCover组件接口不变
- 向后兼容所有现有功能
- 支持所有现代浏览器
- 优雅降级处理

## 注意事项

1. 确保 `book-cover.png` 文件存在于正确位置
2. 后端图片URL需要支持CORS（用于颜色提取）
3. 颜色提取是异步过程，有短暂的加载时间
4. 如果颜色提取失败，会优雅降级到无背景色状态
