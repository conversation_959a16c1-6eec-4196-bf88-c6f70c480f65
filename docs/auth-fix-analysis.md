# bookSummary 项目认证问题分析与修复方案

## 🔍 问题描述

用户登录后，通过 Server Action 请求用户数据时持续返回 401 未授权错误，但直接访问 API 端点能够正常返回登录用户的数据。

## 🎯 根本原因分析

通过深入分析代码，发现了以下关键问题：

### 1. Token Key 不匹配
- **生产环境配置**: `TOKEN_KEY="MINUTES_ACCESS_TOKEN-dev"`
- **测试代码中使用**: `TAROT_ACCESS_TOKEN-dev`
- **默认值不一致**: 不同文件中使用了不同的默认值

### 2. Redis 数据库选择错误 ⭐ **关键问题**
- **PHP 后端**: 使用 `frontend` Redis 池，对应**数据库1**
- **Next.js**: 默认使用**数据库0**
- **配置文件证据**:
  ```php
  // config/autoload/redis.php
  'frontend' => [
      'db' => 1,  // PHP后端使用数据库1
  ]
  ```
  ```typescript
  // src/lib/redis.ts
  export function getRedisClient(db: number = 1) // 默认使用数据库1，但之前调用时没有指定
  ```

### 3. 应用名称配置不一致
- **Next.js**: 使用 `PROJECT_NAME="Minutes"`
- **PHP 后端**: 使用 `APP_NAME=Minutes`
- **Redis Key 格式**: `${APP_NAME}:u:token:{uid}`

### 4. Cookie 传递机制问题
- Server Action 和直接 API 调用的 Cookie 传递方式不同
- 缺少详细的调试日志

## 🛠️ 修复方案

### 1. 统一配置管理
创建了 `src/lib/auth/config.ts` 统一管理所有认证相关配置：
- 统一 Token Key 获取
- 统一应用名称配置
- 统一 Redis 配置

### 2. 修复 Redis 数据库选择
```typescript
// 修复前
const redis = getRedisClient() // 使用默认数据库0

// 修复后
const redis = getRedisClient(1) // 明确使用数据库1，与PHP后端保持一致
```

### 3. 统一 Redis Key 格式
```typescript
// 修复前
const key = `${process.env.PROJECT_NAME}:u:token:${payload.uid}`

// 修复后
const key = getRedisTokenKey(payload.uid) // 使用统一的key生成函数
```

### 4. 增强调试功能
- 添加详细的调试日志
- 创建 `/api/debug/auth` 调试端点
- 增强 API Server 的请求/响应日志

## 📋 修复文件清单

1. **src/lib/auth/config.ts** - 新增统一配置管理
2. **src/lib/auth/server-actions.ts** - 使用统一配置
3. **src/services/actions/authServer.ts** - 修复Redis数据库选择
4. **src/utils/apiServer.ts** - 增强调试日志
5. **src/app/api/debug/auth/route.ts** - 新增调试端点
6. **scripts/test-auth-fix.ts** - 新增测试脚本

## 🧪 验证步骤

### 1. 运行测试脚本
```bash
bun scripts/test-auth-fix.ts
```

### 2. 访问调试端点
访问 `https://www.15minutes.ai/api/debug/auth` 查看详细的认证状态

### 3. 测试 Server Action
在用户登录状态下，测试调用 `getFullUserInfo()` Server Action

### 4. 检查日志
查看服务器日志中的认证相关信息：
- Token Key 使用情况
- Redis 连接和查询结果
- Cookie 传递情况

## 🎯 预期效果

修复后，Server Action 应该能够：
1. 正确获取用户的认证 Token
2. 在正确的 Redis 数据库中验证 Token
3. 成功返回用户数据，不再出现 401 错误

## 🔧 部署建议

1. **备份当前配置**: 在部署前备份现有的环境变量配置
2. **分步部署**: 建议先在测试环境验证修复效果
3. **监控日志**: 部署后密切关注认证相关的日志输出
4. **回滚准备**: 准备快速回滚方案以防出现问题

## 📞 后续支持

如果修复后仍有问题，可以：
1. 查看 `/api/debug/auth` 的输出结果
2. 检查服务器日志中的详细错误信息
3. 验证环境变量配置是否正确
4. 确认 Redis 服务状态和连接配置
