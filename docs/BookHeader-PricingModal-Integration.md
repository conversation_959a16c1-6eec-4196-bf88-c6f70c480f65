# BookHeader 价格弹窗集成文档

## 功能概述

在 BookHeader 组件的 "Listen" 按钮中集成了付费权限检查和价格弹窗功能。当用户点击听书按钮时，系统会按以下流程处理：

1. **登录检查** - 检查用户是否已登录
2. **付费权限检查** - 检查用户是否有有效的付费套餐
3. **价格弹窗** - 如果用户没有付费权限，显示价格弹窗
4. **支付成功回调** - 支付成功后自动执行听书功能

## 技术实现

### 核心修改

**文件**: `src/app/[locale]/(book)/book-summary/[id]/components/BookHeader.tsx`

### 重要修复说明

在实现过程中发现了数据获取的问题：

1. **第一次尝试**: `getCurrentUserInfo()` 函数只返回 `{ userId: number }` 数据，不包含完整的用户信息
2. **第二次尝试**: 直接使用 `getUserInfo()` 但错误地尝试从响应中提取 `response.data.user`
3. **最终修复**: 发现 `getUserInfo()` 函数已经在内部处理了数据提取，直接返回 `User | null`

现在使用 `getUserInfo()` 函数可以正确获取包含 `rank_name`、`vip_expired_at` 等完整字段的用户数据，确保付费状态判断的准确性。

### 新增功能

#### 1. 付费状态检查函数

```typescript
const hasActivePremium = (user: any): boolean => {
  // 检查用户等级 - 非 Free 用户视为付费用户
  if (user.rank_name && user.rank_name !== 'Free') {
    return true
  }

  // 检查 VIP 过期时间 - 如果有 VIP 且未过期
  if (user.vip_expired_at && user.vip_expired_at > Date.now() / 1000) {
    return true
  }

  return false
}
```

#### 2. 增强的认证和权限检查流程

```typescript
const handleListenClickWithAuth = useCallback(
  async (e: React.MouseEvent) => {
    e.preventDefault()

    // 第一步：检查登录状态
    const isLoggedIn = await checkAuthStatus()
    if (!isLoggedIn) {
      // 显示登录弹窗
      setOnSuccess(() => handleListenClick?.(e))
      openLogin()
      return
    }

    // 第二步：检查付费权限
    try {
      // 获取完整的用户信息（getUserInfo 已经处理了数据提取）
      const currentUser = await getUserInfo()
      console.log('currentUser', currentUser)

      if (!currentUser || !hasActivePremium(currentUser)) {
        // 显示价格弹窗
        await openPricingModalWithData(() => {
          // 支付成功后执行听书功能
          handleListenClick?.(e)
        })
        return
      }

      // 第三步：执行听书功能
      handleListenClick?.(e)
    } catch (error) {
      console.error('Failed to check user premium status:', error)
      // 降级策略：允许用户尝试
      handleListenClick?.(e)
    }
  },
  [handleListenClick, openLogin, setOnSuccess, openPricingModalWithData, hasActivePremium]
)
```

## 用户体验流程

### 场景1: 未登录用户

1. 用户点击 "Listen" 按钮
2. 系统检测到用户未登录
3. 显示登录弹窗
4. 用户登录成功后，自动执行听书功能

### 场景2: 已登录免费用户

1. 用户点击 "Listen" 按钮
2. 系统检测到用户已登录但没有付费权限
3. 显示价格弹窗，展示订阅计划
4. 用户选择计划并完成支付
5. 支付成功后，自动执行听书功能

### 场景3: 已登录付费用户

1. 用户点击 "Listen" 按钮
2. 系统检测到用户已登录且有付费权限
3. 直接执行听书功能

### 场景4: 权限检查失败

1. 用户点击 "Listen" 按钮
2. 系统在检查权限时发生错误
3. 降级策略：允许用户尝试，由服务器端处理权限

## 付费状态判断逻辑

系统通过以下字段判断用户的付费状态：

### 主要判断字段

- `user.rank_name`: 用户等级名称
  - `'Free'`: 免费用户
  - `'Month'`, `'Year'`, `'Premium'` 等: 付费用户
- `user.vip_expired_at`: VIP 过期时间戳
  - 如果存在且大于当前时间，视为有效付费用户

### 判断规则

1. **等级检查**: 如果 `rank_name` 不是 'Free'，视为付费用户
2. **VIP 检查**: 如果 `vip_expired_at` 存在且未过期，视为付费用户
3. **默认**: 其他情况视为免费用户

## 错误处理

### 网络错误

- 如果获取用户信息失败，使用降级策略
- 允许用户尝试访问功能，由服务器端进行最终权限检查

### 数据异常

- 如果用户数据格式异常，默认视为免费用户
- 触发价格弹窗流程

## 集成的组件和服务

### 使用的 Hook

- `usePricingModal`: 价格弹窗控制
- `useAuthModal`: 登录弹窗控制
- `useFavorite`: 收藏功能（原有）

### 使用的服务

- `checkAuthStatus`: 检查登录状态
- `getUserInfo`: 获取完整的用户信息（包含 rank_name, vip_expired_at 等字段）
- `openPricingModalWithData`: 打开价格弹窗并获取数据

### 依赖的组件

- 全局价格弹窗组件
- 全局登录弹窗组件
- 支付流程组件

## 兼容性

### 向后兼容

- 保持原有的 `handleListenClick` 函数签名不变
- 不影响其他组件的使用
- 保持现有的登录检查逻辑

### 与现有系统集成

- 复用现有的认证系统
- 复用现有的支付系统
- 复用现有的弹窗系统

## 测试建议

### 手动测试场景

1. 未登录用户点击听书按钮
2. 免费用户点击听书按钮
3. 付费用户点击听书按钮
4. 网络异常情况下的降级处理
5. 支付成功后的回调执行

### 自动化测试

- 单元测试：付费状态判断逻辑
- 集成测试：完整的用户流程
- E2E 测试：从点击到支付成功的完整流程

## 性能考虑

### 优化点

- 用户信息获取使用缓存
- 避免重复的权限检查
- 异步操作的错误处理

### 监控指标

- 权限检查成功率
- 价格弹窗转化率
- 支付成功率

## 未来扩展

### 可能的增强功能

1. 添加加载状态指示器
2. 优化错误提示信息
3. 添加用户行为分析
4. 支持更复杂的权限规则

### 代码优化

1. 提取权限检查逻辑到独立的 Hook
2. 添加更详细的类型定义
3. 优化性能和缓存策略
