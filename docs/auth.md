# withAuth 认证装饰器使用指南

## 概述

`withAuth` 是一个简单的认证装饰器，用于在 Server Actions 中自动处理用户认证。它只做认证校验，不修改函数签名，让您可以在不改动现有代码的情况下添加认证功能。

## 特点

- ✅ **不修改函数签名**：现有函数不需要添加 userId 参数
- ✅ **自动认证校验**：装饰器自动处理 token 获取和验证
- ✅ **类型安全**：完整的 TypeScript 支持
- ✅ **零依赖**：基于现有的 `verifyJwtToken` 函数
- ✅ **灵活使用**：支持强制认证和可选认证
- ✅ **易于迁移**：可以逐步替换现有认证代码

## 基本使用

### 1. 强制认证

```typescript
import { withAuth, getCurrentAuthUser } from '@/lib/auth/withAuth'

// 需要登录才能访问的功能
export const addToFavorites = withAuth(async (bookId: number) => {
  // 获取当前认证用户
  const { userId } = getCurrentAuthUser()

  // 执行业务逻辑
  return await favoriteService.add(userId, bookId)
})
```

### 2. 可选认证

```typescript
import { withOptionalAuth, getCurrentUserId } from '@/lib/auth/withAuth'

// 登录和未登录用户都可以访问，但功能不同
export const getBookDetail = withOptionalAuth(async (bookId: number) => {
  const userId = getCurrentUserId() // 可能为 null

  if (userId) {
    // 已登录用户：返回个性化内容
    return await bookService.getPersonalizedBook(userId, bookId)
  } else {
    // 未登录用户：返回公共内容
    return await bookService.getPublicBook(bookId)
  }
})
```

### 3. 手动认证检查

```typescript
import { getAuthUser, checkAuth } from '@/lib/auth/withAuth'

// 不使用装饰器，手动检查认证
export async function manualAuthFunction(bookId: number) {
  const authUser = await getAuthUser()

  if (!authUser) {
    return { success: false, error: 'Authentication required' }
  }

  // 执行业务逻辑
  return await bookService.getBook(authUser.userId, bookId)
}
```

## API 参考

### 装饰器函数

#### `withAuth<T, R>(handler: (...args: T) => Promise<R>)`

- **用途**：强制要求用户认证
- **返回**：如果未认证返回错误，否则执行原函数
- **示例**：用于需要登录的操作

#### `withOptionalAuth<T, R>(handler: (...args: T) => Promise<R>)`

- **用途**：可选认证，不强制要求登录
- **返回**：总是执行原函数，但可以通过辅助函数获取认证状态
- **示例**：用于公共内容但可以个性化的场景

### 辅助函数（在装饰器内部使用）

#### `getCurrentAuthUser(): { userId: number }`

- **用途**：获取当前认证用户信息
- **注意**：只能在被 `withAuth` 装饰的函数内部使用
- **抛出错误**：如果未认证会抛出错误

#### `getCurrentUserId(): number | null`

- **用途**：获取当前用户ID，如果未认证返回 null
- **注意**：可以在被 `withAuth` 或 `withOptionalAuth` 装饰的函数内部使用

#### `isAuthenticated(): boolean`

- **用途**：检查当前是否已认证
- **返回**：布尔值表示认证状态

#### `requireAuth(): { userId: number }`

- **用途**：要求必须认证，如果未认证抛出错误
- **使用场景**：在可选认证的函数中强制要求认证

### 独立函数（可在任何地方使用）

#### `getAuthUser(): Promise<{ userId: number } | null>`

- **用途**：获取认证用户信息
- **返回**：Promise，成功时返回用户信息，失败时返回 null

#### `checkAuth(): Promise<boolean>`

- **用途**：检查是否已登录
- **返回**：Promise<boolean>

## 迁移现有代码

### 迁移前（原始代码）

```typescript
export async function getLibraryData(tab: string, page: number) {
  // 手动获取和验证 token
  const cookieStore = await cookies()
  const token = cookieStore.get('TEST_ACCESS_TOKEN-dev')?.value

  if (!token) {
    return { data: [], meta: { total: 0, page, limit: 12, totalPages: 0 } }
  }

  const { isValid, uid } = await verifyJwtToken(token)
  if (!isValid || !uid) {
    return { data: [], meta: { total: 0, page, limit: 12, totalPages: 0 } }
  }

  const userId = Number(uid)

  // 业务逻辑...
  switch (tab) {
    case 'favorites':
      return await myLibraryService.getUserFavoriteBooks(userId, page)
    // ...
  }
}
```

### 迁移后（使用装饰器）

```typescript
export const getLibraryData = withAuth(async (tab: string, page: number) => {
  // 自动获取认证用户
  const { userId } = getCurrentAuthUser()

  // 业务逻辑保持不变
  switch (tab) {
    case 'favorites':
      return await myLibraryService.getUserFavoriteBooks(userId, page)
    // ...
  }
})
```

### 迁移步骤

1. **导入装饰器**：`import { withAuth, getCurrentAuthUser } from '@/lib/auth/withAuth'`
2. **包装函数**：用 `withAuth()` 包装原函数
3. **删除认证代码**：移除手动的 token 获取和验证逻辑
4. **替换用户ID获取**：用 `getCurrentAuthUser()` 替换手动获取的 userId
5. **保持业务逻辑**：其他代码保持不变

## 错误处理

### 认证失败的返回格式

```typescript
{
  success: false,
  error: string // 错误信息
}
```

### 常见错误

- `"No token found"` - 用户未登录
- `"Invalid token"` - token 无效或已过期
- `"Authentication failed"` - 认证过程中发生错误

## 最佳实践

1. **优先使用装饰器**：对于简单的认证需求，使用 `withAuth` 或 `withOptionalAuth`
2. **复杂逻辑使用手动检查**：对于需要复杂权限逻辑的场景，使用 `getAuthUser`
3. **统一错误处理**：保持错误返回格式的一致性
4. **缓存清理**：在数据变更后记得清理相关缓存
5. **类型安全**：充分利用 TypeScript 的类型检查

## 配置

### Token Key 配置

装饰器会自动从以下位置获取 token key：

1. 环境变量 `TOKEN_KEY`
2. 默认值 `'TEST_ACCESS_TOKEN-dev'`

建议在 `.env` 文件中设置：

```
TOKEN_KEY=YOUR_TOKEN_KEY
```

## 注意事项

1. **函数内部使用**：`getCurrentAuthUser` 等函数只能在被装饰的函数内部使用
2. **异步函数**：装饰器只支持异步函数
3. **错误处理**：认证失败时会返回错误对象，而不是抛出异常
4. **性能考虑**：每次调用都会进行认证检查，对于高频调用可以考虑缓存优化
