# 📚 批量图片处理脚本使用指南

## 🎯 功能概述

这个脚本可以批量将现有的书籍封面图片通过 GPT-4o Image API 转换为新的书籍封面设计。脚本会自动从数据库获取书籍标题和作者信息，生成专业的书籍封面。

## 🚀 快速开始

### 第一步：环境配置

1. **配置 API Key**
   ```bash
   # 在项目根目录的 .env 文件中添加
   GPT4O_API_KEY=your_actual_api_key_here
   ```

2. **验证环境**
   ```bash
   bun run image:test
   ```

### 第二步：导出书籍元数据

```bash
# 从数据库导出书籍信息到 JSON 文件
bun run image:export
```

这个命令会：
- 查询数据库中所有有封面的书籍
- 提取书籍标题、作者信息
- 生成 `scripts/book-metadata.json` 文件
- 显示导出统计信息

**示例输出：**
```
[INFO] 开始导出书籍元数据...
[INFO] 找到 3 条封面记录
[SUCCESS] 导出完成！
[INFO] 📊 统计信息:
[INFO]   - 处理成功: 3 条
[INFO]   - 跳过记录: 0 条
[INFO] 📋 示例数据:
[INFO]   40121378.jpg -> "Atomic Habits" by James Clear
[INFO]   37502596.jpg -> "The 5 AM Club" by Robin Sharma
[INFO]   58563697.jpg -> "The Gap and the Gain" by <PERSON>, <PERSON> P. Hardy
```

### 第三步：准备图片文件

1. **获取原始图片**
   - 从数据库的 `book_covers` 表中找到 `image_url`
   - 下载对应的图片文件
   - 例如：`https://cdn.15minutes.ai/book-cover/1000/40121378.jpg`

2. **放置图片文件**
   ```bash
   # 将图片文件放入输入目录
   scripts/input/
   ├── 40121378.jpg    # 对应 "Atomic Habits"
   ├── 37502596.jpg    # 对应 "The 5 AM Club"
   └── 58563697.jpg    # 对应 "The Gap and the Gain"
   ```

3. **文件命名要求**
   - 文件名必须与数据库中 `image_url` 的文件名部分完全匹配
   - 支持格式：`.jpg`, `.jpeg`, `.png`, `.webp`

### 第四步：运行批量处理

```bash
# 开始批量处理
bun run image:process
```

**处理过程：**
1. 扫描输入目录中的图片文件
2. 从元数据文件中获取对应的书籍信息
3. 为每张图片调用 GPT-4o API 生成新封面
4. 下载生成的图片到输出目录
5. 生成处理报告

### 第五步：查看结果

处理完成后，生成的新封面图片保存在：
```bash
scripts/output/
├── 40121378.jpg    # 新生成的封面
├── 37502596.jpg    # 新生成的封面
└── 58563697.jpg    # 新生成的封面
```

## 📋 命令参考

| 命令 | 功能 | 说明 |
|------|------|------|
| `bun run image:export` | 导出书籍元数据 | 从数据库生成 JSON 映射文件 |
| `bun run image:test` | 环境测试 | 验证配置和目录结构 |
| `bun run image:process` | 批量处理 | 执行完整的图片生成流程 |
| `bun run image:retry` | 重试失败 | 重新处理失败的文件 |
| `bun run image:reset` | 重置进度 | 清除所有处理进度 |

## 🔍 处理流程详解

### 1. 元数据匹配逻辑

脚本会按以下优先级获取书籍信息：

1. **精确匹配**：文件名与元数据中的 `imageFilename` 完全匹配
2. **模糊匹配**：去除扩展名后匹配
3. **文件名解析**：如果数据库中没有对应记录，使用文件名解析

### 2. 提示词生成

脚本使用以下模板生成 API 提示词：

```
Generate a book cover image with the following specifications:

Book Title: {从数据库获取的书名}
Author: {从数据库获取的作者}
Cover Style: Modern、Symbolic/Conceptual Graphics、Limited and High-Contrast Color Palettes、Clear Visual Hierarchy、Flat Design Style

**Color Palette Instruction:**
* **Crucially, derive the color palette for this new cover *directly and exclusively* from the colors present in the provided reference image.**
* Strive for a high-contrast application of these extracted colors.
```

### 3. 并发控制

- **最大并发数**：3个任务同时进行
- **轮询间隔**：每10秒检查一次任务状态
- **超时设置**：最多等待10分钟
- **重试机制**：失败后自动重试3次

## 📊 日志和报告

### 处理日志

脚本会生成详细的日志文件：

```bash
scripts/image-processing/logs/
├── processing-2024-01-15T10-30-00-000Z.log  # 处理日志
├── error-2024-01-15T10-30-00-000Z.log       # 错误日志
└── report-2024-01-15T10-30-00-000Z.json     # 处理报告
```

### 进度文件

```bash
scripts/image-processing/progress.json  # 处理进度，支持断点续传
```

### 处理报告示例

```json
{
  "summary": {
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-01-15T10:45:30.000Z",
    "totalFiles": 3,
    "successfulFiles": 2,
    "failedFiles": 1,
    "successRate": "66.67%",
    "processingDuration": "930s"
  },
  "errors": [
    {
      "file": "invalid_file.jpg",
      "error": "API request failed: 500 Internal Server Error"
    }
  ]
}
```

## ⚠️ 注意事项

### 1. API 限制
- 确保 GPT4O_API_KEY 有效且有足够配额
- 遵守 API 调用频率限制
- 单个图片处理时间约 20-60 秒

### 2. 文件要求
- 图片文件大小建议不超过 10MB
- 确保网络连接稳定
- 输出目录需要足够的存储空间

### 3. 数据库依赖
- 确保数据库连接正常
- 书籍必须有对应的封面记录
- 需要英文的书籍和作者翻译信息

## 🔧 故障排除

### 常见问题

**Q: 提示 "GPT4O_API_KEY environment variable is not set"**
```bash
# 解决方案：检查 .env 文件配置
echo $GPT4O_API_KEY  # 验证环境变量
```

**Q: 提示 "书籍元数据文件不存在"**
```bash
# 解决方案：先导出元数据
bun run image:export
```

**Q: 某些文件处理失败**
```bash
# 解决方案：查看错误日志
cat scripts/image-processing/logs/error-*.log

# 重试失败的文件
bun run image:retry
```

**Q: 处理时间过长**
```bash
# 解决方案：检查网络连接和 API 状态
# 可以分批处理大量图片
```

### 调试模式

```bash
# 查看实时日志
tail -f scripts/image-processing/logs/processing-*.log

# 查看进度文件
cat scripts/image-processing/progress.json
```

## 📈 性能预期

- **单个图片**：20-60秒（包含 API 生成时间）
- **并发处理**：最多 3 个同时进行
- **批量 10 张**：约 5-15 分钟
- **批量 100 张**：约 30-60 分钟

## 💡 最佳实践

1. **分批处理**：建议每次处理 10-20 张图片
2. **网络稳定**：确保处理期间网络连接稳定
3. **定期备份**：及时备份生成的图片文件
4. **监控日志**：关注错误日志，及时处理问题
5. **测试先行**：大批量处理前先测试少量文件

## 📞 技术支持

如遇到问题，请按以下步骤排查：

1. 运行环境测试：`bun run image:test`
2. 查看错误日志：`scripts/image-processing/logs/error-*.log`
3. 检查网络连接和 API Key 有效性
4. 参考本文档的故障排除部分
