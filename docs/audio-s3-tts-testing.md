# S3 + TTS音频API测试指南

## 📋 阶段一完成内容

### ✅ 已实现功能

1. **API路由**: `/api/audio/[bookId]` - 完整的S3 + TTS实现
2. **ElevenLabs TTS集成**: Text-to-Speech API调用，生成MP3音频文件
3. **S3存储**: 音频文件上传到S3，支持自定义endpoint
4. **并发控制**: Redis分布式锁 + 等待队列机制
5. **双重缓存**: S3文件存在性检查 + Redis元数据缓存
6. **错误处理**: 完整的错误处理、重试机制和结构化日志
7. **多语言支持**: 英文(en)和中文(zh)，不同声音ID
8. **参数验证**: 严格的输入验证和环境配置检查

### 📁 文件结构

```
src/
├── app/api/audio/[bookId]/route.ts          # 主API路由
├── lib/audio/
│   ├── elevenlabs.ts                        # ElevenLabs TTS API封装
│   ├── s3.ts                                # S3文件操作
│   ├── cache.ts                             # Redis缓存管理
│   ├── concurrency.ts                       # 并发控制管理
│   ├── file-utils.ts                        # 文件工具函数
│   └── logger.ts                            # 结构化日志记录
├── types/audio.types.ts                     # 音频类型定义
└── scripts/test-audio-api.js                # 测试脚本
```

## 🧪 测试方法

### 方法1: 使用自动化测试脚本

```bash
# 确保服务器运行在localhost:4000
npm run dev

# 在另一个终端运行测试
npm run test:audio
```

### 方法2: 手动API测试

#### 1. 基础请求测试
```bash
curl -X GET "http://localhost:4000/api/audio/1?locale=en"
```

**预期响应**:
```json
{
  "success": true,
  "data": {
    "audioUrl": "https://your-bucket.s3.region.amazonaws.com/audio/book_1_en_summary.mp3",
    "duration": 180,
    "fileSize": 2880000,
    "cached": false,
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### 2. 缓存测试（重复请求）
```bash
curl -X GET "http://localhost:4000/api/audio/1?locale=en"
```

**预期响应**:
```json
{
  "success": true,
  "data": {
    "audioUrl": "https://your-bucket.s3.region.amazonaws.com/audio/book_1_en_summary.mp3",
    "duration": 180,
    "fileSize": 2880000,
    "cached": true,
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### 3. 并发测试
```bash
# 同时发送5个相同请求测试并发控制
for i in {1..5}; do
  curl -X GET "http://localhost:4000/api/audio/2?locale=en" &
done
```

#### 4. 中文语言测试
```bash
curl -X GET "http://localhost:4000/api/audio/1?locale=zh"
```

#### 5. 错误情况测试

**无效书籍ID**:
```bash
curl -X GET "http://localhost:4000/api/audio/invalid?locale=en"
```

**不存在的书籍**:
```bash
curl -X GET "http://localhost:4000/api/audio/99999?locale=en"
```

**不支持的HTTP方法**:
```bash
curl -X POST "http://localhost:4000/api/audio/1"
```

## 📊 验收标准检查

### ✅ API功能验证

- [ ] API能成功调用ElevenLabs TTS生成MP3音频
- [ ] 音频文件能正确上传到S3存储
- [ ] S3文件存在性检查机制工作正常
- [ ] Redis分布式锁机制正常工作
- [ ] 并发请求处理正确（多用户同时请求同一音频）
- [ ] 支持英文和中文两种语言
- [ ] 错误情况处理正确（书籍不存在、API调用失败等）
- [ ] 返回正确的S3音频文件URL

### 🔍 日志检查

运行测试时，控制台应显示类似日志：

```
[AUDIO] Request started - BookID: 1, Locale: en
[AUDIO] S3 file check - Key: audio/book_1_en_summary.mp3, Status: NOT_EXISTS, ResponseTime: 150ms
[AUDIO] Concurrency - LOCK_ACQUIRED: audio_lock:1:en for BookID: 1, Locale: en
[AUDIO] TTS generation START - Locale: en, Characters: 1234, VoiceID: 21m00Tcm4TlvDq8ikWAM
[AUDIO] TTS generation SUCCESS - Locale: en, ResponseTime: 3456ms, FileSize: 2880000
[AUDIO] S3 upload START - Key: audio/book_1_en_summary.mp3, FileSize: 2880000 bytes
[AUDIO] S3 upload SUCCESS - Key: audio/book_1_en_summary.mp3, ResponseTime: 1200ms
[AUDIO] Cache SET SUCCESS - Key: audio_metadata:1:en, FileSize: 2880000, TTL: 86400
[AUDIO] Generation COMPLETE - BookID: 1, Locale: en, Status: GENERATED, ResponseTime: 5678ms
```

### 🚨 常见问题排查

#### 1. ElevenLabs API Key错误
```
Error: ELEVENLABS_API_KEY environment variable is not set
```
**解决**: 检查.env文件中的ELEVENLABS_API_KEY配置

#### 2. AWS S3配置错误
```
Error: Missing required environment variables: AWS_S3_BUCKET, AWS_ACCESS_KEY_ID
```
**解决**: 检查AWS S3相关环境变量配置

#### 3. Redis连接失败
```
Error: Redis connection failed
```
**解决**: 检查Redis服务是否运行，检查连接配置

#### 4. 并发锁超时
```
{
  "success": false,
  "error": "Audio generation timeout. Please try again later."
}
```
**解决**: 检查是否有死锁，重启Redis清理锁

#### 5. S3上传失败
```
Error: S3 upload failed: Access Denied
```
**解决**: 检查AWS凭证和S3桶权限配置

## 🔧 环境配置检查

确保以下环境变量已正确配置：

```bash
# 必需配置
ELEVENLABS_API_KEY=sk_your_api_key_here
AWS_S3_BUCKET=your-audio-bucket
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_REGION=us-east-1

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 可选配置（有默认值）
AUDIO_CACHE_TTL=86400
AUDIO_DEFAULT_VOICE_EN=21m00Tcm4TlvDq8ikWAM
AUDIO_DEFAULT_VOICE_ZH=pNInz6obpgDQGcFmaJgB
AUDIO_MAX_FILE_SIZE=10485760
AUDIO_GENERATION_TIMEOUT=30000
AUDIO_LOCK_TTL=60000
AUDIO_MAX_WAIT_TIME=30000
```

## 📈 性能指标

### 预期性能表现

- **首次生成**: 5-8秒（包含TTS生成和S3上传）
- **缓存命中**: <500ms（S3文件存在性检查）
- **并发等待**: 最多30秒（等待其他进程完成）
- **错误响应**: <100ms

### 监控指标

- 响应时间分布
- 缓存命中率
- 并发锁冲突率
- TTS生成成功率
- S3上传成功率
- 错误率分布

## 🎯 下一步

阶段一完成后，请确认：

1. ✅ 所有测试用例通过
2. ✅ 日志输出正常且详细
3. ✅ S3文件正确生成和存储
4. ✅ 并发控制机制工作正常
5. ✅ 缓存机制有效
6. ✅ 错误处理符合预期

确认无误后，我们将进入**阶段二：前端播放器组件开发**。
