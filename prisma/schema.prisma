generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model apple_notifications {
  id                      BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  user_id                 BigInt?
  original_transaction_id String? @db.VarChar(128)
  transaction_id          String? @db.VarChar(128)
  notification_type       String? @db.VarChar(255)
  subtype                 String? @db.VarChar(255)
  web_order_line_item_id  String? @db.VarChar(128)
  product_id              String? @db.VarChar(128)
  purchase_date           Int?    @db.UnsignedInt
  original_purchase_date  Int?    @db.UnsignedInt
  storefront              String? @db.VarChar(16)
  price                   Int?    @db.UnsignedInt
  auto_renew_status       Int?    @db.UnsignedTinyInt
  environment             String? @db.VarChar(16)
  signed_transaction_info Json?
  signed_renewal_info     Json?
  signed_payload          Json?
  raw                     String? @db.Text
  created_at              Int?    @db.UnsignedInt
  updated_at              Int?    @db.UnsignedInt

  @@index([original_transaction_id], map: "idx_ original_transaction_id")
  @@index([transaction_id], map: "idx_ transaction_id")
  @@index([user_id], map: "idx_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model audio_files {
  id               Int       @id @default(autoincrement())
  book_id          Int?
  language_id      String?   @db.VarChar(5)
  file_url         String    @db.VarChar(255)
  duration_seconds Int?
  file_size_mb     Float?    @db.Float
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  book books? @relation(fields: [book_id], references: [id])

  @@unique([book_id, language_id], map: "book_language")
  @@index([book_id], map: "book_id")
  @@index([language_id], map: "language_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model author_translations {
  id          Int       @id @default(autoincrement())
  author_id   Int?
  language_id String?   @db.VarChar(5)
  name        String    @db.VarChar(255)
  biography   String?   @db.Text
  is_default  Int?      @default(0) @db.TinyInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  author authors? @relation(fields: [author_id], references: [id])

  @@unique([author_id, language_id], map: "author_language")
  @@index([author_id], map: "author_id")
  @@index([language_id], map: "language_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model author_view_statistics {
  id             Int       @id @default(autoincrement())
  author_id      Int?      @unique(map: "author_id")
  view_count     BigInt?   @default(0)
  last_viewed_at DateTime? @default(now()) @db.Timestamp(0)
  created_at     DateTime? @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  author authors? @relation(fields: [author_id], references: [id])
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model authors {
  id              Int       @id @default(autoincrement())
  avatar_url      String?   @db.VarChar(255) // 作者头像URL
  website         String?   @db.VarChar(500) // 个人网站
  twitter_account String?   @db.VarChar(100) // X(Twitter)账号
  born            String?   @db.VarChar(255) // 出生地
  created_at      DateTime? @default(now()) @db.Timestamp(0)
  updated_at      DateTime? @default(now()) @db.Timestamp(0)
  rawid           String?   @db.VarChar(50) // 新增字段，存储原始BookID

  // 关联关系
  author_translations    author_translations[]
  book_authors           book_authors[]
  author_view_statistics author_view_statistics?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model book_authors {
  book_id      Int
  author_id    Int
  author_order Int? @default(1)

  // 关联关系
  book   books   @relation(fields: [book_id], references: [id])
  author authors @relation(fields: [author_id], references: [id])

  @@id([book_id, author_id])
  @@index([author_id], map: "author_id")
  @@index([book_id], map: "book_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model book_categories {
  book_id     Int
  category_id Int

  // 关联关系
  book     books      @relation(fields: [book_id], references: [id])
  category categories @relation(fields: [category_id], references: [id])

  @@id([book_id, category_id])
  @@index([book_id], map: "book_id")
  @@index([category_id], map: "category_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model book_covers {
  id          Int       @id @default(autoincrement())
  book_id     Int?
  language_id String?   @db.VarChar(5)
  image_url   String    @db.VarChar(255)
  is_primary  Int?      @default(0) @db.TinyInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  book books? @relation(fields: [book_id], references: [id])

  @@index([book_id], map: "book_id")
  @@index([language_id], map: "language_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model book_translations {
  id             Int       @id @default(autoincrement())
  book_id        Int?
  language_id    String?   @db.VarChar(5)
  title          String    @db.VarChar(255)
  subtitle       String?   @db.VarChar(255)
  description    String?   @db.Text
  plot_summary   String?   @db.Text
  review_summary String?   @db.Text
  best_quote     String?   @db.Text
  synopsis       String?   @db.Text
  is_default     Int?      @default(0) @db.TinyInt
  created_at     DateTime? @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  book books? @relation(fields: [book_id], references: [id])

  @@unique([book_id, language_id], map: "book_language")
  @@index([book_id], map: "book_id")
  @@index([language_id], map: "language_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model books {
  id                   Int       @id @default(autoincrement())
  isbn                 String?   @db.VarChar(100)
  isbn13               String?   @db.VarChar(100)
  asin                 String?   @db.VarChar(100)
  issn                 String?   @db.VarChar(100)
  content_type         String?   @db.VarChar(50)
  binding              String?   @db.VarChar(50)
  publication_year     Int?
  publisher_id         Int?
  original_language_id String?   @db.VarChar(5)
  file_size_pdf        Float?    @db.Float
  file_size_epub       Float?    @db.Float
  pdf_url              String?   @db.VarChar(255)
  epub_url             String?   @db.VarChar(255)
  ipfs_cid             String?   @db.VarChar(100)
  ipfs_cid_blake2b     String?   @db.VarChar(100)
  rate_score           Decimal?  @db.Decimal(3, 2) // 平均评分
  total_ratings        Int?      @default(0) @db.UnsignedInt // 评分总数
  is_published         Int?      @default(0) @db.TinyInt
  published_at         DateTime? @db.Timestamp(0)
  reading_time_minutes Int?
  created_at           DateTime? @default(now()) @db.Timestamp(0)
  updated_at           DateTime? @default(now()) @db.Timestamp(0)
  rawid                String?   @db.VarChar(50) // 新增字段，存储原始BookID

  // 关联关系
  book_translations book_translations[]
  book_covers       book_covers[]
  book_authors      book_authors[]
  book_categories   book_categories[]
  view_statistics   view_statistics?
  audio_files       audio_files[]
  book_chapters     book_chapters[]

  user_audio_progress  user_audio_progress[]
  user_favorites       user_favorites[]
  user_reading_history user_reading_history[]
  ratings              ratings[]

  @@index([original_language_id], map: "original_language_id")
  @@index([publisher_id], map: "publisher_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model categories {
  id         Int       @id @default(autoincrement())
  rawid      String?   @db.VarChar(50)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  category_translations category_translations[]
  book_categories       book_categories[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model category_translations {
  id          Int       @id @default(autoincrement())
  category_id Int?
  language_id String?   @db.VarChar(5)
  name        String    @db.VarChar(100)
  description String?   @db.Text
  is_default  Int?      @default(0) @db.TinyInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  category categories? @relation(fields: [category_id], references: [id])

  @@unique([category_id, language_id], map: "category_language")
  @@index([category_id], map: "category_id")
  @@index([language_id], map: "language_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model daily_users {
  id                  BigInt    @id @default(autoincrement())
  date                DateTime? @unique(map: "user_daily.date") @db.Date
  total               BigInt?
  new_users           BigInt?
  active_users        BigInt?
  active_user_ids     Json?
  inactive_users      BigInt?
  active_seniors      BigInt?
  active_seniors_rate Decimal?  @db.Decimal(10, 2)
  created_at          Int?
  updated_at          Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribution_code {
  id                Int     @id @default(autoincrement()) @db.UnsignedInt
  user_id           BigInt?
  distribution_code String? @unique(map: "distribution_code") @db.VarChar(128)
  rank_data         Json?
  remark            String? @db.VarChar(255)
  commission_rate   Int?
  created_at        Int?
  updated_at        Int?

  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribution_commission {
  id             Int      @id @default(autoincrement()) @db.UnsignedInt
  user_id        BigInt?
  type           Int?
  money          Decimal? @db.Decimal(10, 2)
  current_assets Decimal? @db.Decimal(10, 2)
  created_at     Int?
  updated_at     Int?

  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribution_info {
  id                       Int      @id @default(autoincrement()) @db.UnsignedInt
  account                  String?  @db.VarChar(255)
  user_id                  BigInt?  @unique(map: "user_id")
  distribution_code        String?  @unique(map: "distribution_code") @db.VarChar(128)
  customize_code           String?  @db.VarChar(128)
  status                   Int?
  application_reason       String?  @db.VarChar(1024)
  application_time         Int?
  review_time              Int?
  paypal                   String?  @db.VarChar(255)
  remark                   String?  @db.Text
  order_effect_num         Int?     @default(0)
  order_invalid_num        Int?     @default(0) @db.UnsignedInt
  confirmed_commission     Decimal? @default(0.00) @db.Decimal(10, 2)
  not_confirmed_commission Decimal? @default(0.00) @db.Decimal(10, 2)
  assets                   Decimal? @default(0.00) @db.Decimal(10, 2)
  cancel_commission        Decimal? @default(0.00) @db.Decimal(10, 2)
  settlement_commission    Decimal? @default(0.00) @db.Decimal(10, 2)
  pay_customer             Int?     @default(0)
  click_num                Int?     @default(0)
  register_num             Int?     @default(0)
  income_num               Decimal? @default(0.00) @db.Decimal(10, 2)
  created_at               Int?
  updated_at               Int?
  commission_rate          Int?

  @@index([application_time], map: "application_time")
  @@index([customize_code], map: "customize_code")
  @@index([review_time], map: "review_time")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribution_record {
  id                Int      @id @default(autoincrement()) @db.UnsignedInt
  order_id          BigInt?  @unique(map: "order_id")
  pay_status        Int?
  sure_time         Int?
  commission_rate   Int?
  commission        Decimal? @db.Decimal(10, 2)
  created_at        Int?
  updated_at        Int?
  distribution_code String?  @db.VarChar(128)
  sure_status       Int?

  @@index([distribution_code, sure_time], map: "distribution_code")
  @@index([sure_time], map: "sure_time")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model distribution_settlement {
  id          Int      @id @default(autoincrement()) @db.UnsignedInt
  user_id     BigInt?
  pay_money   Decimal? @db.Decimal(10, 2)
  pay_time    Int?
  status      Int?     @db.TinyInt
  remark      String?  @db.VarChar(1024)
  pay_account String?  @db.VarChar(255)
  created_at  Int?
  updated_at  Int?
  void_time   Int?

  @@index([created_at], map: "created_at")
  @@index([pay_time], map: "pay_time")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model feedbacks {
  id         BigInt  @id @default(autoincrement())
  user_id    BigInt?
  content    String? @db.Text
  created_at Int?
  updated_at Int?

  @@index([created_at], map: "created_at")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model frontend_api_secret_keys {
  id         BigInt  @id @default(autoincrement())
  secret_key String? @unique(map: "secret_key") @db.VarChar(32)
  user_id    BigInt?
  created_at Int?
  updated_at Int?

  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model languages {
  language_code String @id @db.VarChar(5)
  name          String @db.VarChar(50)
  native_name   String @db.VarChar(50)
  is_active     Int?   @default(1) @db.TinyInt

  // 关联关系
  book_chapters book_chapters[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model open_api_secret_keys {
  id         BigInt  @id @default(autoincrement())
  secret_key String? @unique(map: "secret_key") @db.VarChar(32)
  user_id    String? @db.VarChar(20)
  status     Int?    @db.TinyInt
  created_at Int?
  updated_at Int?
  deleted_at Int?

  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model open_apis {
  id             BigInt  @id @default(autoincrement())
  name           String? @db.VarChar(255)
  api            String? @db.VarChar(255)
  method         String? @db.VarChar(10)
  status         Int?    @db.TinyInt
  description    String? @db.VarChar(500)
  rpm            Int?
  billing_method Int?    @db.TinyInt
  cpr            Int?
  created_at     Int?
  updated_at     Int?

  @@unique([api, method], map: "unique")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model open_user_logs {
  id          BigInt  @id @default(autoincrement())
  user_id     String? @db.VarChar(20)
  source_type String? @db.VarChar(50)
  source_id   String? @db.VarChar(20)
  quota       BigInt? @default(0)
  created_at  Int?
  updated_at  Int?

  @@unique([user_id, source_type, source_id], map: "unique")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model open_users {
  id              BigInt  @id @default(autoincrement())
  user_id         String? @unique(map: "user_id") @db.VarChar(20)
  account         String? @db.VarChar(100)
  status          Int?    @db.TinyInt
  quota           BigInt?
  used_quota      BigInt?
  remaining_quota BigInt?
  created_at      Int?
  updated_at      Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model order_refunds {
  id                  BigInt   @id @default(autoincrement())
  order_id            BigInt?
  amount              Decimal? @db.Decimal(10, 2)
  method              Int?     @db.TinyInt
  cancel_vip          Int?     @db.TinyInt
  cancel_subscription Int?     @db.TinyInt
  admin_id            BigInt?
  created_at          Int?

  @@index([admin_id], map: "order_refunds.admin_id")
  @@index([order_id], map: "order_refunds.order_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model orders {
  id                    BigInt   @id @default(autoincrement())
  order_sn              String?  @unique(map: "orders.order_sn") @db.VarChar(100)
  rank_id               Int?     @db.TinyInt
  rank_duration         String?  @db.VarChar(100)
  user_id               BigInt?
  first_time            Int?     @db.TinyInt
  order_status          Int?     @db.TinyInt
  payment_platform      Int?     @db.TinyInt
  subscription_sn       String?  @db.VarChar(100)
  order_amount          Decimal? @db.Decimal(10, 2)
  paid_amount           Decimal? @db.Decimal(10, 2)
  refunded_amount       Decimal? @db.Decimal(10, 2)
  profit_amount         Decimal? @db.Decimal(10, 2)
  remark                String?  @db.Text
  created_at            Int?
  paid_at               Int?
  refunded_at           Int?
  stripe_invoice        String?  @db.VarChar(150)
  hosted_invoice_url    String?  @db.Text
  invoice_pdf           String?  @db.Text
  transaction_number    String?  @db.VarChar(50)
  distribution_code     String?  @db.VarChar(128)
  prev_plan_reamin_days Int?
  trial_days            Int?
  type                  Int?     @db.TinyInt

  @@index([distribution_code], map: "distribution_code")
  @@index([user_id, payment_platform, transaction_number], map: "order.user_platform_transaction_number")
  @@index([created_at], map: "orders.created_at")
  @@index([first_time], map: "orders.first_time")
  @@index([order_status], map: "orders.order_status")
  @@index([paid_at], map: "orders.paid_at")
  @@index([profit_amount], map: "orders.profit_amount")
  @@index([rank_duration], map: "orders.rank_duration")
  @@index([rank_id], map: "orders.rank_id")
  @@index([refunded_amount], map: "orders.refunded_amount")
  @@index([refunded_at], map: "orders.refunded_at")
  @@index([subscription_sn], map: "orders.subscription_sn")
  @@index([transaction_number], map: "orders.transaction_number")
  @@index([user_id], map: "orders.user_id")
  @@index([user_id, stripe_invoice], map: "orders.user_stripe_invoice")
  @@index([type], map: "type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model publisher_translations {
  id           Int       @id @default(autoincrement())
  publisher_id Int?
  language_id  String?   @db.VarChar(5)
  name         String    @db.VarChar(255)
  is_default   Int?      @default(0) @db.TinyInt
  created_at   DateTime? @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @default(now()) @db.Timestamp(0)

  @@unique([publisher_id, language_id], map: "publisher_language")
  @@index([language_id], map: "language_id")
  @@index([publisher_id], map: "publisher_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model publishers {
  id         Int       @id @default(autoincrement())
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @default(now()) @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ranks {
  id                Int      @id @default(autoincrement()) @db.TinyInt
  rank_name         String?  @db.VarChar(100)
  duration          String?  @db.VarChar(100)
  price             Decimal? @db.Decimal(10, 2)
  original_price    Decimal? @db.Decimal(10, 2)
  first_price       Decimal? @db.Decimal(10, 2)
  trial_days        Int?
  allowed_buy       Int?     @db.TinyInt
  is_visibled       Int?     @db.TinyInt
  max_online_users  Int?
  max_team_members  Int?
  permission        Json?
  stripe_product_id String?  @db.VarChar(100)
  stripe_price_id   String?  @db.VarChar(100)
  stripe_coupon_id  String?  @db.VarChar(100)
  product_name      String?  @db.VarChar(200)
  created_at        Int?
  updated_at        Int?
  remark            String?  @db.Text
  apple_product_id  String?  @db.VarChar(100)

  @@index([rank_name], map: "ranks.rank_name")
  @@index([stripe_product_id], map: "ranks.stripe_product_id")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ratings {
  id          Int       @id @default(autoincrement())
  book_id     Int?
  user_id     BigInt?
  score       Decimal   @db.Decimal(2, 1)
  review_text String?   @db.Text
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @default(now()) @db.Timestamp(0)

  // 添加关联关系
  books books? @relation(fields: [book_id], references: [id])

  @@unique([book_id, user_id], map: "book_user")
  @@index([book_id], map: "book_id")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_customer {
  id            BigInt    @id @default(autoincrement())
  user_id       BigInt    @unique(map: "stripe_customer.user_id")
  stripe_id     String?   @db.VarChar(150)
  pm_type       String?   @db.VarChar(150)
  pm_last_four  String?   @db.VarChar(20)
  trial_ends_at DateTime? @db.Timestamp(0)
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)

  @@index([stripe_id], map: "stripe_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_payment_intents {
  id                     BigInt  @id @default(autoincrement())
  payment_intent         String? @unique(map: "stripe_payment_intents.pi") @db.VarChar(150)
  customer               String? @db.VarChar(150)
  invoice                String? @db.VarChar(150)
  payment_method         String? @db.VarChar(150)
  payment_method_details Json?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_subscription_items {
  id              BigInt    @id @default(autoincrement())
  subscription_id BigInt
  stripe_id       String    @db.VarChar(150)
  stripe_product  String    @db.VarChar(255)
  stripe_price    String    @db.VarChar(50)
  quantity        Int?
  created_at      DateTime? @db.Timestamp(0)
  updated_at      DateTime? @db.Timestamp(0)

  @@index([subscription_id, stripe_price], map: "subscription_id_stripe_price")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_subscriptions {
  id            BigInt    @id @default(autoincrement())
  user_id       BigInt
  name          String    @db.VarChar(150)
  stripe_id     String    @unique(map: "stripe_subscriptions.stripe_id") @db.VarChar(150)
  stripe_status String    @db.VarChar(50)
  stripe_price  String?   @db.VarChar(50)
  quantity      Int?
  trial_ends_at DateTime? @db.Timestamp(0)
  ends_at       DateTime? @db.Timestamp(0)
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)

  @@index([user_id, stripe_status], map: "user_id_stripe_status")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model subscriptions {
  id                  BigInt   @id @default(autoincrement())
  user_id             BigInt?
  payment_platform    Int?     @db.TinyInt
  subscription_sn     String?  @db.VarChar(100)
  subscription_status Int?     @db.TinyInt
  platform_status     String?  @db.VarChar(50)
  product_name        String?  @db.VarChar(200)
  start_date          Int?
  next_period_start   Int?
  next_period_amount  Decimal? @db.Decimal(10, 2)
  canceled_handler    String?  @db.VarChar(100)
  canceled_at         Int?
  created_at          Int?
  updated_at          Int?

  @@unique([payment_platform, subscription_sn], map: "subscriptions.subscription")
  @@index([user_id], map: "subscriptions.user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model team_invites {
  id         BigInt  @id @default(autoincrement())
  team_id    BigInt?
  role_id    BigInt?
  code       String? @unique(map: "code") @db.VarChar(32)
  email      String? @db.VarChar(255)
  created_at Int?
  expired_at Int?

  @@index([team_id], map: "team_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model team_members {
  id         BigInt  @id @default(autoincrement())
  team_id    BigInt?
  user_id    BigInt?
  role_id    BigInt?
  status     Int?    @db.TinyInt
  invite_id  BigInt?
  created_at Int?
  updated_at Int?

  @@unique([team_id, user_id], map: "team_member")
  @@index([invite_id], map: "invite_id")
  @@index([team_id], map: "team_id")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model team_roles {
  id         BigInt  @id @default(autoincrement())
  team_id    BigInt?
  name       String? @db.VarChar(255)
  permission Json?
  is_default Int?    @db.TinyInt
  is_admin   Int?    @db.TinyInt
  created_at Int?
  updated_at Int?

  @@index([team_id, is_default], map: "default_role")
  @@index([team_id], map: "team_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model teams {
  id           BigInt  @id @default(autoincrement())
  user_id      BigInt? @unique(map: "user_id")
  name         String? @db.VarChar(255)
  member_count Int?
  created_at   Int?
  updated_at   Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_audio_progress {
  id                  Int      @id @default(autoincrement())
  user_id             BigInt   @map("user_id") // 改为必填
  book_id             Int      @map("book_id") // 改为必填
  position_seconds    Decimal  @default(0) @db.Decimal(10, 3) // 改为Decimal提高精度
  duration_seconds    Decimal? @map("duration_seconds") @db.Decimal(10, 3) // 新增：音频总时长
  progress_percentage Decimal? @map("progress_percentage") @db.Decimal(5, 2) // 新增：进度百分比
  playback_rate       Decimal  @default(1.0) @map("playback_rate") @db.Decimal(3, 2) // 新增：播放倍速
  volume              Decimal  @default(1.0) @db.Decimal(3, 2) // 新增：音量
  is_completed        Boolean  @default(false) @map("is_completed") // 改为Boolean
  last_listened_at    DateTime @default(now()) @map("last_listened_at") @db.Timestamp(0)
  created_at          DateTime @default(now()) @map("created_at") @db.Timestamp(0)
  updated_at          DateTime @updatedAt @map("updated_at") @db.Timestamp(0)

  // 关联关系
  user users? @relation(fields: [user_id], references: [id], onDelete: Cascade)
  book books? @relation(fields: [book_id], references: [id], onDelete: Cascade)

  @@unique([user_id, book_id], map: "user_book")
  @@index([book_id], map: "book_id")
  @@index([user_id], map: "user_id")
  @@index([last_listened_at], map: "last_listened_at")
  @@map("user_audio_progress")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_favorites {
  user_id    BigInt
  book_id    Int
  created_at DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  books books? @relation(fields: [book_id], references: [id])

  @@id([user_id, book_id])
  @@index([book_id], map: "book_id")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_quota {
  id              BigInt  @id @default(autoincrement())
  user_id         BigInt?
  permission_name String? @db.VarChar(100)
  limit           BigInt?
  remaining       BigInt?
  used            BigInt?
  reset_at        Int?
  created_at      Int?
  updated_at      Int?

  @@unique([user_id, permission_name], map: "user_permission")
  @@index([limit], map: "limit")
  @@index([remaining], map: "remaining")
  @@index([reset_at], map: "reset_at")
  @@index([used], map: "used")
  @@index([user_id], map: "user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_reading_history {
  id           Int       @id @default(autoincrement())
  user_id      BigInt // 改为非空，确保数据完整性
  book_id      Int // 改为非空，确保数据完整性
  last_read_at DateTime? @default(now()) @db.Timestamp(0)
  created_at   DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  books books? @relation(fields: [book_id], references: [id])

  // 复合唯一索引 - 支持 upsert 操作
  @@unique([user_id, book_id], map: "user_book_reading_unique")
  @@index([book_id], map: "book_id")
  @@index([user_id], map: "user_id")
  @@index([last_read_at], map: "idx_last_read_at")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_socialites {
  id                 BigInt  @id @default(autoincrement())
  type               Int?    @db.TinyInt
  socialite_id       String? @db.VarChar(100)
  socialite_raw_data Json?
  user_id            BigInt?
  created_at         Int?
  updated_at         Int?

  @@unique([type, socialite_id], map: "user_socialites.unique")
  @@index([user_id], map: "user_socialites.user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model users {
  id                           BigInt                @id @default(autoincrement())
  account                      String?               @unique(map: "users.account") @db.VarChar(100)
  username                     String?               @db.VarChar(255)
  email                        String?               @db.VarChar(100)
  email_verified_at            Int?
  phone_number                 String?               @db.VarChar(20)
  phone_region                 String?               @db.VarChar(100)
  password                     String?               @db.VarChar(32)
  salt                         String?               @db.VarChar(10)
  rank_id                      Int?                  @db.UnsignedTinyInt
  last_ip                      String?               @db.VarChar(100)
  last_location                String?               @db.VarChar(200)
  last_at                      Int?
  vip_started_at               Int?
  vip_expired_at               BigInt?
  subscription_status          Int?                  @db.TinyInt
  subscription_platform        Int?                  @db.TinyInt
  subscription_started_at      Int?
  subscription_next_deduct_at  Int?
  card_last4                   String?               @db.VarChar(10)
  source                       String?               @db.VarChar(1000)
  language                     String?               @db.VarChar(50)
  timezone                     String?               @db.VarChar(50)
  device_language              String?               @db.VarChar(50)
  device_timezone              String?               @db.VarChar(50)
  company                      String?               @db.VarChar(255)
  country                      String?               @db.VarChar(255)
  province                     String?               @db.VarChar(255)
  city                         String?               @db.VarChar(255)
  postal                       String?               @db.VarChar(255)
  address                      String?               @db.VarChar(255)
  phone                        String?               @db.VarChar(50)
  vat                          String?               @db.VarChar(255)
  distribution_code            String?               @db.VarChar(128)
  user_value                   Decimal?              @db.Decimal(10, 2)
  account_status               Int?                  @db.TinyInt
  remark                       String?               @db.Text
  created_at                   Int?
  updated_at                   Int?
  delete_task_created_at       Int?
  delete_task_plan_executed_at Int?
  delete_task_executed_at      Int?
  avatar                       String?               @db.VarChar(500)
  user_audio_progress          user_audio_progress[]

  @@index([card_last4], map: "card_last4")
  @@index([delete_task_plan_executed_at(sort: Desc)], map: "delete_task_plan_executed_at")
  @@index([user_value], map: "user_value")
  @@index([account_status], map: "users.account_status")
  @@index([created_at], map: "users.created_at")
  @@index([device_language], map: "users.device_language")
  @@index([device_timezone], map: "users.device_timezone")
  @@index([email], map: "users.email")
  @@index([language], map: "users.language")
  @@index([last_at], map: "users.last_at")
  @@index([last_ip], map: "users.last_ip")
  @@index([last_location], map: "users.last_location")
  @@index([phone_number], map: "users.phone_number")
  @@index([phone_region], map: "users.phone_region")
  @@index([rank_id], map: "users.rank_id")
  @@index([subscription_next_deduct_at], map: "users.subscription_next_deduct_at")
  @@index([subscription_started_at], map: "users.subscription_started_at")
  @@index([subscription_status], map: "users.subscription_status")
  @@index([timezone], map: "users.timezone")
  @@index([vip_expired_at], map: "users.vip_expired_at")
  @@index([vip_started_at], map: "users.vip_started_at")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model view_statistics {
  id             Int       @id @default(autoincrement())
  book_id        Int?      @unique(map: "book_id")
  view_count     BigInt?   @default(0)
  last_viewed_at DateTime? @default(now()) @db.Timestamp(0)
  created_at     DateTime? @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  book books? @relation(fields: [book_id], references: [id])

  // 添加view_count索引以优化排序查询
  @@index([view_count], map: "idx_view_count")
}

/// 书籍章节内容表
model book_chapters {
  id          Int       @id @default(autoincrement())
  book_id     Int
  language_id String    @db.VarChar(5)
  content     Json?
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @default(now()) @db.Timestamp(0)

  // 关联关系
  book     books     @relation(fields: [book_id], references: [id])
  language languages @relation(fields: [language_id], references: [language_code])

  @@unique([book_id, language_id], map: "book_language")
  @@index([book_id], map: "book_id")
  @@index([language_id], map: "language_id")
}
