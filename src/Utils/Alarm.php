<?php

declare(strict_types=1);
namespace Website\Common\Utils;

use Carbon\Carbon;
use Hyperf\Amqp\Producer;
use Hyperf\Utils\ApplicationContext;
use Website\Common\Amqp\Producer\AlarmProducer;
use Website\Common\Service\FeiShuBotService;

class Alarm
{
    /**
     * 发送告警
     * @param string $type 类型
     * @param string $message 内容
     * @throws \Throwable
     */
    public static function create($type, $message)
    {
        $data = [
            // 发送方式：feishu或其它
            'type' => $type,
            // 告警消息
            'message' => $message,
            // 告警时间
            'created_at' => Carbon::now()->toDateTimeString(),
            // 告警服务
            'service_name' => env('SERVICE_NAME', env('APP_NAME')),
            // 告警环境
            'service_env' => env('APP_ENV'),
            // 告警IP
            'service_ip' => env('SERVICE_IP', array_values(swoole_get_local_ip())[0] ?? 'unknow'),
        ];
        try {
            self::exec($data);
        } catch (\Throwable $e) {
            $Producer = ApplicationContext::getContainer()->get(Producer::class);
            $Producer->produce(new AlarmProducer($data));
        }
    }

    /**
     * 发送告警-飞书
     * @param string $messag 内容
     * @throws \Throwable
     */
    public static function feishu($message)
    {
        self::create('feishu', $message);
    }

    /**
     * 执行发送消息
     * @param array $data
     * @throws \Throwable
     */
    public static function exec($data)
    {
        $data['message'] = mb_substr($data['message'], 0, 10000, 'UTF-8');
        switch ($data['type']) {
            case 'feishu':
                $FeiShuBotService = new FeiShuBotService();
                $card = [
                    "elements" => [
                        [
                            "tag" => "column_set",
                            "flex_mode" => "none",
                            "horizontal_spacing" => "8px",
                            "horizontal_align" => "left",
                            "columns" => [
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "vertical_spacing" => "8px",
                                    "elements" => [
                                        [
                                            "tag" => "markdown",
                                            "content" => "** 📅告警时间：**",
                                            "text_align" => "left",
                                            "text_size" => "normal"
                                        ]
                                    ],
                                    "weight" => 1
                                ],
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "vertical_spacing" => "8px",
                                    "elements" => [
                                        [
                                            "tag" => "markdown",
                                            "content" => "**💻告警服务：**",
                                            "text_align" => "left",
                                            "text_size" => "normal"
                                        ]
                                    ],
                                    "weight" => 1
                                ]
                            ],
                            "margin" => "16px 0px 0px 0px"
                        ],
                        [
                            "tag" => "column_set",
                            "flex_mode" => "none",
                            "columns" => [
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "elements" => [
                                        [
                                            "tag" => "column_set",
                                            "flex_mode" => "none",
                                            "horizontal_spacing" => "default",
                                            "background_style" => "default",
                                            "columns" => [
                                                [
                                                    "tag" => "column",
                                                    "elements" => [
                                                        [
                                                            "tag" => "div",
                                                            "text" => [
                                                                "tag" => "plain_text",
                                                                "content" => $data['created_at'] ?? 'unknow',
                                                                "text_size" => "normal",
                                                                "text_align" => "left",
                                                                "text_color" => "default"
                                                            ]
                                                        ]
                                                    ],
                                                    "width" => "weighted",
                                                    "weight" => 1
                                                ]
                                            ]
                                        ]
                                    ],
                                    "weight" => 1
                                ],
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "elements" => [
                                        [
                                            "tag" => "column_set",
                                            "flex_mode" => "none",
                                            "horizontal_spacing" => "default",
                                            "background_style" => "default",
                                            "columns" => [
                                                [
                                                    "tag" => "column",
                                                    "elements" => [
                                                        [
                                                            "tag" => "div",
                                                            "text" => [
                                                                "tag" => "plain_text",
                                                                "content" => $data['service_name'] ?? 'unknow',
                                                                "text_size" => "normal",
                                                                "text_align" => "left",
                                                                "text_color" => "default"
                                                            ]
                                                        ]
                                                    ],
                                                    "width" => "weighted",
                                                    "weight" => 1
                                                ]
                                            ]
                                        ]
                                    ],
                                    "weight" => 1
                                ]
                            ],
                            "margin" => "16px 0px 0px 0px"
                        ],
                        [
                            "tag" => "column_set",
                            "flex_mode" => "none",
                            "horizontal_spacing" => "8px",
                            "horizontal_align" => "left",
                            "columns" => [
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "vertical_spacing" => "8px",
                                    "elements" => [
                                        [
                                            "tag" => "markdown",
                                            "content" => "**🗳告警环境：**",
                                            "text_align" => "left",
                                            "text_size" => "normal"
                                        ]
                                    ],
                                    "weight" => 1
                                ],
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "vertical_spacing" => "8px",
                                    "elements" => [
                                        [
                                            "tag" => "markdown",
                                            "content" => "**📝告警IP：**",
                                            "text_align" => "left",
                                            "text_size" => "normal"
                                        ]
                                    ],
                                    "weight" => 1
                                ]
                            ],
                            "margin" => "16px 0px 0px 0px"
                        ],
                        [
                            "tag" => "column_set",
                            "flex_mode" => "none",
                            "columns" => [
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "elements" => [
                                        [
                                            "tag" => "column_set",
                                            "flex_mode" => "none",
                                            "horizontal_spacing" => "default",
                                            "background_style" => "default",
                                            "columns" => [
                                                [
                                                    "tag" => "column",
                                                    "elements" => [
                                                        [
                                                            "tag" => "div",
                                                            "text" => [
                                                                "tag" => "plain_text",
                                                                "content" => $data['service_env'] ?? 'unknow',
                                                                "text_size" => "normal",
                                                                "text_align" => "left",
                                                                "text_color" => "default"
                                                            ]
                                                        ]
                                                    ],
                                                    "width" => "weighted",
                                                    "weight" => 1
                                                ]
                                            ]
                                        ]
                                    ],
                                    "weight" => 1
                                ],
                                [
                                    "tag" => "column",
                                    "width" => "weighted",
                                    "vertical_align" => "top",
                                    "elements" => [
                                        [
                                            "tag" => "column_set",
                                            "flex_mode" => "none",
                                            "horizontal_spacing" => "default",
                                            "background_style" => "default",
                                            "columns" => [
                                                [
                                                    "tag" => "column",
                                                    "elements" => [
                                                        [
                                                            "tag" => "div",
                                                            "text" => [
                                                                "tag" => "plain_text",
                                                                "content" => $data['service_ip'] ?? 'unknow',
                                                                "text_size" => "normal",
                                                                "text_align" => "left",
                                                                "text_color" => "default"
                                                            ]
                                                        ]
                                                    ],
                                                    "width" => "weighted",
                                                    "weight" => 1
                                                ]
                                            ]
                                        ]
                                    ],
                                    "weight" => 1
                                ]
                            ],
                            "margin" => "16px 0px 0px 0px"
                        ],
                        [
                            "tag" => "hr"
                        ],
                        [
                            "tag" => "column_set",
                            "flex_mode" => "none",
                            "horizontal_spacing" => "default",
                            "background_style" => "default",
                            "columns" => [
                                [
                                    "tag" => "column",
                                    "elements" => [
                                        [
                                            "tag" => "div",
                                            "text" => [
                                                "tag" => "plain_text",
                                                "content" => $data['message'],
                                                "text_size" => "normal",
                                                "text_align" => "left",
                                                "text_color" => "default",
                                            ]
                                        ]
                                    ],
                                    "width" => "weighted",
                                    "weight" => 1
                                ]
                            ]
                        ]
                    ],
                    "header" => [
                        "title" => [
                            "tag" => "plain_text",
                            "content" => "监控告警"
                        ],
                        "subtitle" => [
                            "tag" => "plain_text",
                            "content" => ""
                        ],
                        "template" => "red"
                    ]
                ];
                $FeiShuBotService->sendInteractiveMessage($card);
                break;
            default:
                break;
        }
    }

    /**
     * 发送报表-飞书
     * @param string $title 报表名称
     * @param string $datetime 报表日期
     * @param array $columns 表头
     * @param array $rows 表数据
     * @param int $page_size 分页大小
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function sendFeishuReportForm($title, $datetime, $columns, $rows, $page_size = 0)
    {
        $FeiShuBotService = new FeiShuBotService(env('FEI_SHU_REPORT_BOT_WEBHOOK'), env('FEI_SHU_REPORT_BOT_SECRET'));
        $card = [
            "elements" => [
                [
                    "tag" => "column_set",
                    "flex_mode" => "none",
                    "columns" => [
                        [
                            "tag" => "column",
                            "width" => "weighted",
                            "vertical_align" => "top",
                            "elements" => [
                                [
                                    "tag" => "markdown",
                                    "content" => "**📅报表日期：**",
                                    "text_align" => "left",
                                    "text_size" => "normal"
                                ],
                                [
                                    "tag" => "column_set",
                                    "flex_mode" => "none",
                                    "horizontal_spacing" => "default",
                                    "background_style" => "default",
                                    "columns" => [
                                        [
                                            "tag" => "column",
                                            "elements" => [
                                                [
                                                    "tag" => "div",
                                                    "text" => [
                                                        "tag" => "plain_text",
                                                        "content" => "{$datetime}",
                                                        "text_size" => "normal",
                                                        "text_align" => "left",
                                                        "text_color" => "default"
                                                    ]
                                                ]
                                            ],
                                            "width" => "weighted",
                                            "weight" => 1
                                        ]
                                    ]
                                ]
                            ],
                            "weight" => 1
                        ]
                    ],
                    "margin" => "16px 0px 0px 0px"
                ],
                [
                    "tag" => "hr"
                ],
                [
                    "tag" => "table",
                    "columns" => array_map(function ($column) {
                        return [
                            "data_type" => "text",
                            "name" => $column['name'],
                            "display_name" => $column['display_name'],
                            "horizontal_align" => "left",
                            "width" => "auto"
                        ];
                    }, $columns),
                    "rows" => $rows,
                    "row_height" => "low",
                    "header_style" => [
                        "background_style" => "none",
                        "bold" => true,
                        "lines" => 1
                    ],
                    "page_size" => $page_size ? $page_size : count($rows)
                ]
            ],
            "header" => [
                "title" => [
                    "tag" => "plain_text",
                    "content" => "{$title}"
                ],
                "subtitle" => [
                    "tag" => "plain_text",
                    "content" => ""
                ],
                "template" => "blue"
            ]
        ];
        $FeiShuBotService->sendInteractiveMessage($card);
    }
}
