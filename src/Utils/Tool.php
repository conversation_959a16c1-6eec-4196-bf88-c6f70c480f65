<?php

namespace Website\Common\Utils;

use Hyperf\Redis\Redis;
use Hyperf\Utils\ApplicationContext;

class Tool
{
    /**
     * 提取出域名中间的部分
     * @param $url
     * @return string
     */
    public static function extractUrlPart($url): string
    {
        if (empty($url)) {
            return '';
        }
        $url = strtolower($url);
        $url = str_replace("https://", '', $url);
        $url = str_replace("http://", '', $url);

        // 使用 explode() 函数将 host 按 . 分割成数组
        $hostParts = explode('.', $url);
        $count = count($hostParts);
        if ($count == 1 || $count == 2) {
            return current($hostParts);
        } elseif ($count == 3) {
            if ($hostParts[0] === 'www') {
                return $hostParts[1];
            } elseif (in_array($hostParts[1], ['com', 'co'])) {
                return $hostParts[0];
            } else {
                return $hostParts[0] . ' ' . $hostParts[1];
            }
        } elseif ($count > 3) {
            $midPart = array_slice($hostParts, 0, -1);
            return implode(' ', $midPart);
        } else {
            return $url;
        }
    }

    /**
     * 生成一个随机A-Z 0-9的字符串
     * @param int $min_length
     * @param int $max_length
     * @return string
     */
    public static function getRandomDistributionCode(int $min_length = 6, int $max_length = 10): string
    {
        // 定义一个包含所有字母和数字的数组
        $chars = array_merge(range('A', 'Z'), range('0', '9'));
        // 随机选择一个字符串长度在6-10之间
        $length = rand($min_length, $max_length);
        // 初始化一个空字符串
        $string = '';
        // 循环生成随机字符并拼接到字符串中
        for ($i = 0; $i < $length; $i++) {
            // 随机选择一个数组索引
            $index = array_rand($chars);
            // 获取对应的字符
            $char = $chars[$index];
            // 拼接到字符串中
            $string .= $char;
        }
        // 返回生成的字符串
        return $string;
    }

    /**
     * redis抢锁
     * @param $lock_name
     * @param int $time
     * @param int $value
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function lock($lock_name, int $time = 5, $value = "1"): bool
    {
        $Redis = ApplicationContext::getContainer()->get(Redis::class);
        return $Redis->set(env('APP_NAME') . ':lock:' . $lock_name, $value, ['nx', 'ex' => $time]);
    }

    /**
     * reids解锁
     * @param $lock_name
     * @param $value
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function unlock($lock_name, $value = null)
    {
        $redis_name = env('APP_NAME') . ':lock:' . $lock_name;
        $Redis = ApplicationContext::getContainer()->get(Redis::class);
        if (!empty($value)) {
            $redis_return = $Redis->get($redis_name);
            if ($redis_return === $value) {
                $Redis->del($redis_name);
            }
        } else {
            $Redis->del($redis_name);
        }
    }

    /**
     * 限制在X秒内最大T次请求
     * @param $key
     * @param int $second
     * @param int $max
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function isAllowed($key, int $second = 10, int $max = 200)
    {
        $redis = ApplicationContext::getContainer()->get(Redis::class);
        // 设置令牌桶的键名和最大令牌数
        $bucketKey = env('APP_NAME') . ':token_bucket:' . $key;

        // 检查令牌桶是否存在，如果不存在则初始化
        if (!$redis->exists($bucketKey)) {
            $redis->setex($bucketKey, $second, $max);
        }

        // 获取当前令牌数
        $data = $redis->decrBy($bucketKey, 1);
        if ($data >= 0) {
            return true;
        } else {
            return false;
        }
    }
}
