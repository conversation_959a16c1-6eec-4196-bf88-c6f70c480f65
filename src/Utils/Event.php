<?php

declare(strict_types=1);
namespace Website\Common\Utils;

use Website\Common\Amqp\Producer\EventProducer;

class Event
{
    /**
     * 发送事件通知
     * @param string $event 事件名称
     * @param array $data 事件数据
     */
    public static function produce(string $event, array $data)
    {
        if (Website::config()->get("website.events.{$event}", false)) {
            Website::producer()
                ->produce(new EventProducer([
                    'event' => $event,
                    'data' => $data,
                    'created_at' => time(),
                ]));
        }

        $callback = config("website.events.{$event}_callback", null);
        if (is_callable($callback)) {
            $callback($data);
        }
    }
}
