<?php

declare(strict_types=1);

namespace Website\Common\Utils;

use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\RedisFactory;
use Hyperf\Redis\RedisProxy;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Amqp\Producer;

class Website
{
    /**
     * @return RedisProxy
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function FrontendRedis()
    {
        return ApplicationContext::getContainer()
            ->get(RedisFactory::class)
            ->get('frontend');
    }

    /**
     * @return ConfigInterface
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function config()
    {
        return ApplicationContext::getContainer()
            ->get(ConfigInterface::class);
    }

    /**
     * @return Producer
     */
    public static function producer()
    {
        return ApplicationContext::getContainer()
            ->get(Producer::class);
    }

    /**
     * @param string $name
     * @param string $group
     * @return mixed
     */
    public static function logger(string $name = 'hyperf', string $group = 'default')
    {
        return ApplicationContext::getContainer()
            ->get(LoggerFactory::class)
            ->get($name, $group);
    }

    /**
     * 获取语言的cookie名称
     * @return string
     */
    public static function getLocaleCookieName()
    {
        return 'locale' . (env('APP_ENV') === 'prod' ? '' : ('-' . env('APP_ENV')));
    }
}
