<?php
declare(strict_types=1);

namespace Website\Common\Utils;

use Carbon\Carbon;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Collection;
use Hyperf\Utils\Parallel;
use League\Flysystem\Filesystem;

class Sitemap
{
    /**
     * 语言在url重的分隔标记
     * @var string
     * @deprecated
     */
    const LANG_DELIMITER = '/{{$lang}}';

    /**
     * 链接支持多语言时的处理方式
     * 1：单独一条
     * 2：使用sitemap的规范
     * @var int
     */
    const LANG_TYPE = 1;

    /**
     * sitemap文件保存的路径
     * @var string
     */
    const SITEMAP_PATH = BASE_PATH . '/public/sitemap';

    /**
     * 是否上传到oss
     * @var bool
     */
    const UPLOAD = false;

    /**
     * 语言简码
     * @var array
     */
    public static $langs = ['en', 'zh', 'tw', 'ko', 'ja', 'pt', 'es', 'de', 'fr', 'vi'];

    /**
     * 生成sitemap目录文件
     * @throws \Throwable
     */
    public static function generateSiteMapIndex()
    {
        if (!is_dir(static::SITEMAP_PATH)) {
            mkdir(static::SITEMAP_PATH, 0777, true);
        }

        $dom = new \DOMDocument('1.0','utf-8');
        $dom->formatOutput = true;
        $sitemapindex = $dom->createElement('sitemapindex');
        $sitemapindex->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $dom->appendChild($sitemapindex);

        $Parallel = new Parallel(4);
        $ReflectionClass = new \ReflectionClass(static::class);
        foreach ($ReflectionClass->getMethods(\ReflectionMethod::IS_STATIC) as $method) {
            if (preg_match('/^generate.+SiteMap$/', $method->name)) {
                $Parallel->add(function () use ($method) {
                    return static::{$method->name}();
                });
            }
        }

        try {
            foreach ($Parallel->wait() as $filenames) {
                foreach ($filenames as $filename) {
                    $sitemap = $dom->createElement('sitemap');
                    $loc = $dom->createElement('loc');
                    $loc->appendChild($dom->createTextNode(env('WEBSITE_FRONTEND_BASE_URI') . '/' . urlencode((string) $filename)));
                    $sitemap->appendChild($loc);
                    $sitemapindex->appendChild($sitemap);
                }
            }
        } catch (\Throwable $e) {
            throw $e;
        }
//        echo $dom->saveXML();
        $dom->save(static::SITEMAP_PATH . '/sitemap.xml');

        if (static::UPLOAD) {
            static::uploadFileToOss('sitemap.xml');
        }
    }

    /**
     * 限制5w个链接生成一个sitemap文件
     * @param $base_filename
     * @param $items
     * @return array
     */
    public static function generateSiteMaps($base_filename, $items)
    {
        $files = [];
        $index = 1;
        Collection::make($items)
            ->chunk(isset($items[0]['image']) ? 2000 : 4000)
            ->each(function (Collection $group) use (&$files, &$index, $base_filename) {
                $filename = "{$base_filename}_{$index}.xml";
                static::generateSiteMap($filename, $group->values()->toArray());
                $files[] = $filename;
                $index++;
            });
        return $files;
    }

    /**
     * 生成sitemap文件
     * @param $filename
     * @param $items
     */
    public static function generateSiteMap($filename, $items)
    {
        $dom = new \DOMDocument('1.0','utf-8');
        $dom->formatOutput = true;
        $urlset = $dom->createElement('urlset');
        $urlset->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $urlset->setAttribute('xmlns:image', 'http://www.google.com/schemas/sitemap-image/1.1');
//        $urlset->setAttribute('xmlns:xhtml', 'http://www.w3.org/1999/xhtml');
        $dom->appendChild($urlset);
        foreach ($items as $item) {
            if (static::LANG_TYPE === 2) {
                $url = $dom->createElement('url');
                $loc = $dom->createElement('loc');
                $loc->appendChild($dom->createTextNode(static::generateLocValue($item['loc'], 'en')));
                $url->appendChild($loc);

                foreach (static::$langs as $lang) {
                    if ($lang === 'en') {
                        continue;
                    }
                    $xhtml_link = $dom->createElement('xhtml:link');
                    $xhtml_link->setAttribute('rel', 'alternate');
                    $xhtml_link->setAttribute('hreflang', $lang);
                    $xhtml_link->setAttribute('href', static::generateLocValue($item['loc'], $lang));
                    $url->appendChild($xhtml_link);
                }

                $lastmod = $dom->createElement('lastmod');
                $lastmod->appendChild($dom->createTextNode($item['lastmod']));
                $url->appendChild($lastmod);

                $changefreq = $dom->createElement('changefreq');
                $changefreq->appendChild($dom->createTextNode($item['changefreq']));
                $url->appendChild($changefreq);

                if (isset($item['image'])) {
                    $image_image = $dom->createElement('image:image');
                    $image_loc = $dom->createElement('image:loc');
                    $image_loc->appendChild($dom->createTextNode($item['image']['loc']));
                    $image_image->appendChild($image_loc);
                    $image_title = $dom->createElement('image:title');
                    $image_title->appendChild($dom->createTextNode(urlencode($item['image']['title'])));
                    $image_image->appendChild($image_title);
                    $url->appendChild($image_image);
                }
            } else {
                foreach (static::$langs as $lang) {
                    $url = $dom->createElement('url');
                    $loc = $dom->createElement('loc');
                    $loc->appendChild($dom->createTextNode(static::generateLocValue($item['loc'], $lang)));
                    $url->appendChild($loc);

                    $lastmod = $dom->createElement('lastmod');
                    $lastmod->appendChild($dom->createTextNode($item['lastmod']));
                    $url->appendChild($lastmod);

                    $changefreq = $dom->createElement('changefreq');
                    $changefreq->appendChild($dom->createTextNode($item['changefreq']));
                    $url->appendChild($changefreq);

                    if (isset($item['image'])) {
                        $image_image = $dom->createElement('image:image');
                        $image_loc = $dom->createElement('image:loc');
                        $image_loc->appendChild($dom->createTextNode($item['image']['loc']));
                        $image_image->appendChild($image_loc);
                        $image_title = $dom->createElement('image:title');
                        $image_title->appendChild($dom->createTextNode(urlencode($item['image']['title'])));
                        $image_image->appendChild($image_title);
                        $url->appendChild($image_image);
                    }

                    $urlset->appendChild($url);
                }
            }
        }

        if (!is_dir(static::SITEMAP_PATH)) {
            mkdir(static::SITEMAP_PATH, 0777);
        }
        //        echo $dom->saveXML();
        $dom->save(static::SITEMAP_PATH . "/{$filename}");

        if (static::UPLOAD) {
            static::uploadFileToOss($filename);
        }
    }

    /**
     * 生成包含语言的链接
     * @param string $loc 原始链接
     * @param string $lang 语言
     * @return string
     */
    public static function generateLocValue($loc, $lang)
    {
        // 兼容处理：之前是通过这个标记，做分隔替换语言的
        $loc = str_replace(static::LANG_DELIMITER,  '', $loc);

        $loc_parser = parse_url($loc);
        $url = (!empty($loc_parser['scheme']) && !empty($loc_parser['host']) ? "{$loc_parser['scheme']}://{$loc_parser['host']}" : env('WEBSITE_FRONTEND_BASE_URI'))
            . ($lang == 'en' ? '' : "/{$lang}")
            . ($loc_parser['path'] ?? '');
        return $url;
    }

    /**
     * 上传文件数据到oss
     * @param string $filename
     * @throws \League\Flysystem\FilesystemException
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function uploadFileToOss($filename)
    {
        $Filesystem = ApplicationContext::getContainer()->get(Filesystem::class);
        $oss_location = (env('APP_ENV') === 'prod' ? '/sitemap' : ('/sitemap-' . env('APP_ENV'))) . "/{$filename}";
        $Filesystem->write($oss_location, file_get_contents(static::SITEMAP_PATH . "/{$filename}"));
    }
}
