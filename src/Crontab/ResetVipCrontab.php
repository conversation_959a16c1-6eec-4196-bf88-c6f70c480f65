<?php
declare(strict_types=1);

namespace Website\Common\Crontab;

use Website\Common\Model\User;
use Website\Common\Service\RankService;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Lete\Base\Abstraction\Crontab as CrontabAbstract;

/**
 * @Command()
 * @Crontab(name="ResetVipCrontab", callback="callback", rule="* * * * *", memo="会员到期重置", enable="isEnable", singleton=true, onOneServer=true)
 */
class ResetVipCrontab extends CrontabAbstract
{
    public function callback()
    {
        User::query()
            ->where('rank_id', '<>', RankService::FREE)
            ->where('vip_expired_at', '<', time())
            ->get()
            ->each(function (User $User) {
                $User->update([
                    'rank_id' => RankService::FREE,
                    'vip_started_at' => null,
                    'vip_expired_at' => null,
                ]);
            });
    }
}
