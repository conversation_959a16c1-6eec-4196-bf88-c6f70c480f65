<?php
declare(strict_types=1);

namespace Website\Common\Crontab;

use Website\Common\Utils\Alarm;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Hyperf\DbConnection\Db;
use Lete\Base\Abstraction\Crontab as CrontabAbstract;
use Website\Common\Model\UserQuota;

/**
 * @Command
 * @Crontab(name="ResetUserQuotaCrontab", callback="callback", rule="*\/5 * * * *", memo="重置用户配额", enable="isEnable", singleton=true, onOneServer=true)
 */
class ResetUserQuotaCrontab extends CrontabAbstract
{
    public function callback()
    {
        try {
            retry(3, function () {
                UserQuota::query()
                    ->where('reset_at', '>', 0)
                    ->where('reset_at', '<=', time())
                    ->update([
                        'remaining' => Db::raw('`limit`'),
                        'used' => 0,
                        'reset_at' => 0,
                    ]);
            }, 1000);
        } catch (\Throwable $e) {
            $message = '重置用户配额，异常';
//            Alarm::feishu($message);
            $this->logger->error("{$message}：{$e->getMessage()}");
        }
    }
}
