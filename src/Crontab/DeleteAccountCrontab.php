<?php
declare(strict_types=1);

namespace Website\Common\Crontab;

use Website\Common\Model\User;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Lete\Base\Abstraction\Crontab as CrontabAbstract;
use Website\Common\Service\UserService;

/**
 * @Command()
 * @Crontab(name="DeleteAccountCrontab", callback="callback", rule="* * * * *", memo="删除账号", enable="isEnable", singleton=true, onOneServer=true)
 */
class DeleteAccountCrontab extends CrontabAbstract
{
    public function callback()
    {
        try {
            User::query()
                ->where('account_status', User::ACCOUNT_STATUS_IN_DELETION_QUEUE)
                ->whereNotNull('delete_task_plan_executed_at')
                ->where('delete_task_plan_executed_at', '<=', time())
                ->get(['id'])
                ->each(function (User $User) {
                    UserService::deleteAccount($User->id);
                });
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
