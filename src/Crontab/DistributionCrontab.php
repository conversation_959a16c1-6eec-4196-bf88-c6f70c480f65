<?php
declare(strict_types=1);
namespace Website\Common\Crontab;

use Website\Common\Model\DistributionRecord;
use Website\Common\Service\DistributionService;
use Website\Common\Utils\Alarm;
use Carbon\Carbon;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Lete\Base\Abstraction\Crontab as CrontabAbstract;

/**
 * @Command
 * @Crontab(name="DistributionCrontab", callback="callback", rule="10 0 * * *", memo="更新每日分销佣金数据", enable="isEnable", singleton=true, onOneServer=true)
 */
class DistributionCrontab extends CrontabAbstract
{
    /**
     * 更新每日分销数据
     */
    public function callback()
    {
        $StartTime = Carbon::now();
        try {
            $distribution_info_id = DistributionRecord::query()
                ->join('distribution_code', 'distribution_code.distribution_code', '=', 'distribution_record.distribution_code')
                ->where('distribution_record.sure_status', '=', 1)
                ->where('distribution_record.sure_time', '<=', time())
                ->where('distribution_record.pay_status', '=', 1)
                ->groupBy('distribution_code.user_id')
                ->pluck('distribution_code.user_id');

            foreach ($distribution_info_id as $user_id) {
                $return = DistributionService::confirmCommission(0, $user_id);
                if ($return['type'] === false) {
                    $this->logger->error($return['msg']);
                }
            }

        } catch (\Throwable $e) {
            $message = '更新每日分销数据，异常：' . $e->getMessage();
            Alarm::feishu($message);
            $this->logger->error($message);
        } finally {
            $cost_time = Carbon::now()->longAbsoluteDiffForHumans($StartTime);
            $this->logger->info("更新每日分销佣金数据，耗时：{$cost_time}");
        }

    }


}
