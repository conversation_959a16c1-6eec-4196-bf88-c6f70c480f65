<?php
declare(strict_types=1);

namespace Website\Common\Crontab;

use Website\Common\Model\DailyUser;
use Website\Common\Service\UserService;
use Carbon\Carbon;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Hyperf\Utils\Str;
use Lete\MongoDB\MongoClient\MongoDb;
use Lete\Base\Abstraction\Crontab as CrontabAbstract;

/**
 * @Command
 * @Crontab(name="DailyUsersCrontab", callback="callback", rule="0 0 * * *", memo="每日用户数统计", enable="isEnable", singleton=true, onOneServer=true)
 */
class DailyUsersCrontab extends CrontabAbstract
{
    public function callback()
    {
        $date = Carbon::yesterday()->format('Y-m-d');

        $this->statistics($date);
    }

    /**
     * 统计
     * @param $date
     */
    protected function statistics($date)
    {
        try {
            $values = UserService::dailyStatistics($date);
            DailyUser::updateOrCreate(['date' => $date], $values);
        } catch (\Throwable $e) {
            MongoDb::collection(Str::snake(class_basename($this)) . '_errors')
                ->insertOne([
                    'data' => array_merge(['date' => $date], $values ?? []),
                    'message' => $e->getMessage(),
                    'created_at' => time(),
                ]);
        }
    }
}
