/**
 * 认证配置统一管理
 * 解决环境变量不一致的问题
 */

/**
 * 获取统一的 Token Key
 * 确保 Server Action 和其他地方使用相同的 token key
 *
 * 重要：必须与PHP后端生成的Cookie名称保持一致
 * PHP后端逻辑：strtoupper(APP_NAME . '_ACCESS_TOKEN') . (APP_ENV === 'prod' ? '' : ('-' . APP_ENV))
 */
export function getTokenKey(): string {
  // 优先使用环境变量配置
  if (process.env.TOKEN_KEY) {
    const tokenKey = process.env.TOKEN_KEY
    console.log('getTokenKey from env:', tokenKey)
    return tokenKey
  }

  // 根据环境自动生成，与PHP后端逻辑保持一致
  const appName = getAppName()
  const nodeEnv = process.env.NODE_ENV

  // 生产环境不添加后缀，开发环境添加-dev后缀
  const tokenKey = nodeEnv === 'production'
    ? `${appName.toUpperCase()}_ACCESS_TOKEN`
    : `${appName.toUpperCase()}_ACCESS_TOKEN-dev`

  console.log('getTokenKey generated:', {
    appName,
    nodeEnv,
    tokenKey
  })

  return tokenKey
}

/**
 * 获取统一的应用名称
 * 确保与 PHP 后端保持一致
 */
export function getAppName(): string {
  // 使用 PROJECT_NAME 环境变量，与 PHP 后端的 APP_NAME 保持一致
  const appName = process.env.PROJECT_NAME || 'Minutes'

  if (process.env.NODE_ENV === 'development') {
    console.log('getAppName:', appName)
  }

  return appName
}

/**
 * 获取 Redis 配置
 * 确保使用正确的数据库
 */
export function getRedisConfig() {
  return {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : 6379,
    password: process.env.REDIS_PASSWORD,
    // PHP 后端使用 'frontend' 池，对应数据库1
    db: 1
  }
}

/**
 * 生成 Redis Token Key
 * 与 PHP 后端保持一致的格式
 */
export function getRedisTokenKey(uid: string | number): string {
  const appName = getAppName()
  return `${appName}:u:token:${uid}`
}

/**
 * 验证配置是否正确
 */
export function validateAuthConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!process.env.TOKEN_KEY) {
    errors.push('TOKEN_KEY environment variable is not set')
  }

  if (!process.env.PROJECT_NAME) {
    errors.push('PROJECT_NAME environment variable is not set')
  }

  if (!process.env.REDIS_HOST) {
    errors.push('REDIS_HOST environment variable is not set')
  }

  if (!process.env.NEXT_PUBLIC_BASE_URL) {
    errors.push('NEXT_PUBLIC_BASE_URL environment variable is not set')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 打印配置信息（仅在开发环境）
 */
export function debugAuthConfig() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Auth Config Debug:', {
      tokenKey: getTokenKey(),
      appName: getAppName(),
      redisConfig: getRedisConfig(),
      validation: validateAuthConfig()
    })
  }
}
