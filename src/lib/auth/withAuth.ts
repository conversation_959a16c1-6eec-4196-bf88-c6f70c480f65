/**
 * 认证装饰器
 */

import { authenticateUser } from './server-actions'

// 当前认证用户信息（用于在函数内部获取）
let currentAuthUser: { userId: number } | null = null



/**
 * 在函数内部获取当前认证用户信息
 * 只能在被 withAuth 装饰的函数内部使用
 */
export function getCurrentAuthUser(): { userId: number } {
  if (!currentAuthUser) {
    throw new Error('No authenticated user found. Make sure this function is called within a withAuth decorated function.')
  }
  return currentAuthUser
}

/**
 * 认证装饰器 - 只做校验，不修改函数签名
 *
 * @param handler 需要认证的处理函数
 * @returns 包装后的函数
 */
export function withAuth<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | { success: false; error: string }> => {
    // 执行认证检查
    const authResult = await authenticateUser()

    if (!authResult.success) {
      return {
        success: false,
        error: authResult.error || 'Authentication required'
      }
    }

    // 设置当前认证用户信息（供函数内部使用）
    currentAuthUser = { userId: authResult.userId! }

    try {
      // 执行原函数（不修改参数）
      const result = await handler(...args)
      return result
    } finally {
      // 清理认证用户信息
      currentAuthUser = null
    }
  }
}

/**
 * 可选认证装饰器 - 不强制要求登录
 *
 * @param handler 处理函数
 * @returns 包装后的函数
 */
export function withOptionalAuth<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    // 尝试认证，但不强制要求成功
    const authResult = await authenticateUser()

    if (authResult.success) {
      // 如果认证成功，设置当前用户信息
      currentAuthUser = { userId: authResult.userId! }
    } else {
      // 如果认证失败，设置为 null
      currentAuthUser = null
    }

    try {
      // 执行原函数
      return await handler(...args)
    } finally {
      // 清理认证用户信息
      currentAuthUser = null
    }
  }
}

/**
 * 检查当前是否已认证
 * 可以在被装饰的函数内部使用
 */
export function isAuthenticated(): boolean {
  return currentAuthUser !== null
}

/**
 * 获取当前用户ID（如果已认证）
 * 可以在被装饰的函数内部使用
 */
export function getCurrentUserId(): number | null {
  return currentAuthUser?.userId || null
}

/**
 * 要求必须认证的辅助函数
 * 在函数内部使用，如果未认证会抛出错误
 */
export function requireAuth(): { userId: number } {
  if (!currentAuthUser) {
    throw new Error('Authentication required')
  }
  return currentAuthUser
}

/**
 * 直接获取认证信息的函数（不依赖装饰器）
 * 可以在任何地方使用
 */
export async function getAuthUser(): Promise<{ userId: number } | null> {
  const authResult = await authenticateUser()
  return authResult.success ? { userId: authResult.userId! } : null
}

/**
 * 检查是否已登录的函数（不依赖装饰器）
 * 可以在任何地方使用
 */
export async function checkAuth(): Promise<boolean> {
  const authResult = await authenticateUser()
  return authResult.success
}
