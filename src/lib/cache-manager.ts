/**
 * 缓存管理器
 * 统一管理项目中的缓存失效操作
 */

import { revalidateTag, revalidatePath } from 'next/cache';

/**
 * 缓存标签常量
 * 确保与项目中已有的标签保持一致
 */
export const CacheTags = {
  // 书籍相关
  BOOK_DETAIL: 'book-detail',
  POPULAR_BOOKS: 'popular-books',
  NEWEST_BOOKS: 'newest-books',
  BOOK_BY_ID: (id: number) => `book-${id}`,
  BOOK_BY_LANGUAGE: (language: string) => `book-${language}`,

  // 作者相关
  AUTHOR_DETAIL: 'author-detail',
  POPULAR_AUTHORS: 'popular-authors',
  AUTHOR_BY_ID: (id: number) => `author-${id}`,
  AUTHOR_BY_LANGUAGE: (language: string) => `author-${language}`,

  // 分类相关
  CATEGORY: 'category',
  CATEGORY_BY_SLUG: (slug: string) => `category-${slug}`,

  // 用户图书馆相关
  USER_LIBRARY: 'user-library',
  USER_BY_ID: (userId: number) => `user-${userId}`,

  // 首页相关
  HOMEPAGE: 'homepage',
  HOMEPAGE_BY_LANGUAGE: (language: string) => `homepage-${language}`,

  // 评分相关
  USER_RATING: 'user-rating',
  BOOK_RATING_STATS: 'book-rating-stats',
  RATING_STATS: 'rating-stats',
  USER_RATINGS: 'user-ratings',
  RATING_BY_USER_BOOK: (userId: number, bookId: number) => `rating-${userId}-${bookId}`,

  // 测试相关
  TEST_CACHE: 'test-cache'
};

/**
 * 缓存路径常量
 * 用于 revalidatePath 函数
 */
export const CachePaths = {
  HOME: '/',
  BOOK_DETAIL: (id: number) => `/book-summary/${id}`,
  AUTHOR_DETAIL: (id: number) => `/author/${id}`,
  AUTHOR_DETAIL_PATTERN: '/author/[slug]', // 用于失效所有作者页面
  CATEGORY: (slug: string) => `/category/${slug}`,
  SEARCH: '/search',
  MY_LIBRARY: '/my-library'
};

/**
 * 缓存管理器
 * 提供针对不同数据类型的缓存失效方法
 */
export class CacheManager {
  /**
   * 书籍相关缓存管理
   */
  static book = {
    /**
     * 重新验证特定书籍的缓存
     * @param bookId 书籍ID
     * @param language 语言（可选）
     */
    revalidate(bookId: number, language: string = 'en'): void {
      // 重新验证书籍详情缓存
      revalidateTag(CacheTags.BOOK_BY_ID(bookId));
      revalidateTag(CacheTags.BOOK_DETAIL);

      // 如果提供了语言，重新验证特定语言的缓存
      if (language) {
        revalidateTag(CacheTags.BOOK_BY_LANGUAGE(language));
      }

      // 重新验证书籍详情页路径
      revalidatePath(CachePaths.BOOK_DETAIL(bookId));

      console.log(`已重新验证书籍(ID: ${bookId})的缓存`);
    },

    /**
     * 重新验证热门书籍缓存
     * @param language 语言（可选）
     */
    revalidatePopular(language: string = 'en'): void {
      revalidateTag(CacheTags.POPULAR_BOOKS);

      if (language) {
        revalidateTag(`${CacheTags.POPULAR_BOOKS}-${language}`);
      }

      // 重新验证首页缓存，因为首页通常会显示热门书籍
      CacheManager.homepage.revalidate(language);

      console.log('已重新验证热门书籍缓存');
    },

    /**
     * 重新验证最新书籍缓存
     * @param language 语言（可选）
     */
    revalidateNewest(language: string = 'en'): void {
      revalidateTag(CacheTags.NEWEST_BOOKS);

      if (language) {
        revalidateTag(`${CacheTags.NEWEST_BOOKS}-${language}`);
      }

      console.log('已重新验证最新书籍缓存');
    }
  };

  /**
   * 作者相关缓存管理
   */
  static author = {
    /**
     * 重新验证特定作者的缓存
     * @param authorId 作者ID
     * @param language 语言（可选）
     */
    revalidate(authorId: number, language: string = 'en'): void {
      // 重新验证作者详情缓存
      revalidateTag(CacheTags.AUTHOR_BY_ID(authorId));
      revalidateTag(CacheTags.AUTHOR_DETAIL);

      // 如果提供了语言，重新验证特定语言的缓存
      if (language) {
        revalidateTag(CacheTags.AUTHOR_BY_LANGUAGE(language));
      }

      // 重新验证作者详情页路径
      // 由于新的URL格式包含作者名称，我们需要失效整个作者路由模式
      // 这样可以确保所有相关的作者页面都被重新验证
      revalidatePath(CachePaths.AUTHOR_DETAIL_PATTERN);

      // 同时也失效旧格式的路径（向后兼容）
      revalidatePath(CachePaths.AUTHOR_DETAIL(authorId));

      console.log(`已重新验证作者(ID: ${authorId})的缓存`);
    },

    /**
     * 重新验证热门作者缓存
     * @param language 语言（可选）
     */
    revalidatePopular(language: string = 'en'): void {
      revalidateTag(CacheTags.POPULAR_AUTHORS);

      if (language) {
        revalidateTag(`${CacheTags.POPULAR_AUTHORS}-${language}`);
      }

      console.log('已重新验证热门作者缓存');
    }
  };

  /**
   * 分类相关缓存管理
   */
  static category = {
    /**
     * 重新验证特定分类的缓存
     * @param slug 分类slug
     * @param language 语言（可选）
     */
    revalidate(slug: string, language: string = 'en'): void {
      revalidateTag(CacheTags.CATEGORY_BY_SLUG(slug));
      revalidateTag(CacheTags.CATEGORY);

      if (language) {
        revalidateTag(`${CacheTags.CATEGORY}-${language}`);
      }

      revalidatePath(CachePaths.CATEGORY(slug));

      console.log(`已重新验证分类(Slug: ${slug})的缓存`);
    },

    /**
     * 重新验证分类列表缓存
     * @param language 语言（可选）
     */
    revalidateList(language: string = 'en'): void {
      revalidateTag('categories');

      if (language) {
        revalidateTag(`categories-${language}`);
      }

      console.log(`已重新验证分类列表缓存(语言: ${language})`);
    }
  };

  /**
   * 用户图书馆相关缓存管理
   */
  static userLibrary = {
    /**
     * 重新验证用户图书馆缓存
     * @param userId 用户ID
     */
    revalidate(userId: number): void {
      revalidateTag(CacheTags.USER_BY_ID(userId));
      revalidateTag(CacheTags.USER_LIBRARY);
      revalidatePath(CachePaths.MY_LIBRARY);

      console.log(`已重新验证用户(ID: ${userId})的图书馆缓存`);
    }
  };

  /**
   * 首页相关缓存管理
   */
  static homepage = {
    /**
     * 重新验证首页缓存
     * @param language 语言（可选）
     */
    revalidate(language: string = 'en'): void {
      revalidateTag(CacheTags.HOMEPAGE);

      if (language) {
        revalidateTag(CacheTags.HOMEPAGE_BY_LANGUAGE(language));
      }

      revalidatePath(CachePaths.HOME);

      console.log('已重新验证首页缓存');
    }
  };

  /**
   * 评分相关缓存管理
   */
  static rating = {
    /**
     * 重新验证用户评分缓存
     * @param userId 用户ID
     * @param bookId 书籍ID
     */
    revalidateUserRating(userId: number, bookId: number): void {
      // 重新验证用户特定评分缓存
      revalidateTag(CacheTags.RATING_BY_USER_BOOK(userId, bookId));
      revalidateTag(CacheTags.USER_RATING);
      revalidateTag(CacheTags.USER_BY_ID(userId));
      revalidateTag(CacheTags.BOOK_BY_ID(bookId));

      console.log(`已重新验证用户(ID: ${userId})对书籍(ID: ${bookId})的评分缓存`);
    },

    /**
     * 重新验证书籍评分统计缓存
     * @param bookId 书籍ID
     */
    revalidateBookStats(bookId: number): void {
      // 重新验证书籍评分统计缓存
      revalidateTag(CacheTags.BOOK_RATING_STATS);
      revalidateTag(CacheTags.RATING_STATS);
      revalidateTag(CacheTags.BOOK_BY_ID(bookId));

      // 重新验证书籍详情页（因为评分统计会影响书籍详情显示）
      revalidatePath(CachePaths.BOOK_DETAIL(bookId));

      console.log(`已重新验证书籍(ID: ${bookId})的评分统计缓存`);
    },

    /**
     * 重新验证用户所有评分缓存
     * @param userId 用户ID
     */
    revalidateUserRatings(userId: number): void {
      // 重新验证用户评分列表缓存
      revalidateTag(CacheTags.USER_RATINGS);
      revalidateTag(CacheTags.USER_BY_ID(userId));

      // 重新验证用户图书馆缓存（评分页面）
      CacheManager.userLibrary.revalidate(userId);

      console.log(`已重新验证用户(ID: ${userId})的所有评分缓存`);
    },

    /**
     * 评分操作后的完整缓存失效
     * 在用户提交评分后调用，确保所有相关缓存都被正确失效
     * @param userId 用户ID
     * @param bookId 书籍ID
     */
    revalidateAfterRating(userId: number, bookId: number): void {
      // 1. 重新验证用户评分相关缓存
      this.revalidateUserRating(userId, bookId);

      // 2. 重新验证书籍统计相关缓存
      this.revalidateBookStats(bookId);

      // 3. 重新验证用户评分列表缓存
      this.revalidateUserRatings(userId);

      // 4. 重新验证书籍详情缓存（因为评分会影响书籍的平均评分显示）
      CacheManager.book.revalidate(bookId);

      // 5. 重新验证热门书籍缓存（因为评分可能影响热门排行榜）
      CacheManager.book.revalidatePopular();

      // 6. 重新验证用户图书馆缓存
      CacheManager.userLibrary.revalidate(userId);

      console.log(`已完成评分操作后的完整缓存失效 - 用户(ID: ${userId}), 书籍(ID: ${bookId})`);
    },

    /**
     * 重新验证全局评分统计缓存
     * 用于系统级别的评分数据更新
     */
    revalidateGlobalStats(): void {
      revalidateTag(CacheTags.RATING_STATS);
      revalidateTag(CacheTags.BOOK_RATING_STATS);

      // 重新验证热门书籍缓存（评分变化可能影响排行榜）
      CacheManager.book.revalidatePopular();

      console.log('已重新验证全局评分统计缓存');
    }
  };
}
