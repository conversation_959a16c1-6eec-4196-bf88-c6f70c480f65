/**
 * Redis连接测试
 * 验证修复后的Redis连接管理是否正常工作
 */

import { getRedisClient, closeAllRedisConnections } from '../redis'

// 模拟环境变量
const originalEnv = process.env

beforeEach(() => {
  process.env = {
    ...originalEnv,
    REDIS_HOST: 'localhost',
    REDIS_PORT: '6379',
    REDIS_PASSWORD: undefined
  }
})

afterEach(async () => {
  process.env = originalEnv
  await closeAllRedisConnections()
})

describe('Redis连接管理', () => {
  test('应该能够创建默认数据库连接', () => {
    const redis = getRedisClient()
    expect(redis).toBeDefined()
    expect(redis.options.db).toBe(1) // 默认数据库1
  })

  test('应该能够创建指定数据库连接', () => {
    const redis0 = getRedisClient(0)
    const redis1 = getRedisClient(1)
    
    expect(redis0).toBeDefined()
    expect(redis1).toBeDefined()
    expect(redis0.options.db).toBe(0)
    expect(redis1.options.db).toBe(1)
  })

  test('应该复用相同数据库的连接', () => {
    const redis1a = getRedisClient(1)
    const redis1b = getRedisClient(1)
    
    // 应该返回同一个实例
    expect(redis1a).toBe(redis1b)
  })

  test('应该为不同数据库创建不同连接', () => {
    const redis0 = getRedisClient(0)
    const redis1 = getRedisClient(1)
    
    // 应该是不同的实例
    expect(redis0).not.toBe(redis1)
  })

  test('应该正确处理环境变量', () => {
    process.env.REDIS_HOST = 'test-host'
    process.env.REDIS_PORT = '1234'
    process.env.REDIS_PASSWORD = 'test-password'
    
    const redis = getRedisClient()
    
    expect(redis.options.host).toBe('test-host')
    expect(redis.options.port).toBe(1234)
    expect(redis.options.password).toBe('test-password')
  })

  test('应该使用默认值当环境变量未设置时', () => {
    delete process.env.REDIS_HOST
    delete process.env.REDIS_PORT
    delete process.env.REDIS_PASSWORD
    
    const redis = getRedisClient()
    
    expect(redis.options.host).toBe('localhost')
    expect(redis.options.port).toBe(6379)
    expect(redis.options.password).toBeUndefined()
  })
})

describe('连接池管理', () => {
  test('应该能够关闭所有连接', async () => {
    // 创建多个连接
    getRedisClient(0)
    getRedisClient(1)
    getRedisClient(2)
    
    // 关闭所有连接应该不抛出错误
    await expect(closeAllRedisConnections()).resolves.toBeUndefined()
  })
})
