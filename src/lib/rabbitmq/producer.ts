'server-only'
import { getChannel } from './client'

// 定义消息发布选项接口
export interface PublishOptions {
  persistent?: boolean
  expiration?: string | number
  contentType?: string
  correlationId?: string
  replyTo?: string
  messageId?: string
  timestamp?: number
  appId?: string
}

// 使用泛型的消息发布函数
export async function publishMessage<T>(
  queue: string,
  message: T,
  options: PublishOptions = { persistent: true }
): Promise<boolean> {
  const channel = await getChannel()
  await channel.assertQueue(queue, { durable: true })

  const buffer = Buffer.from(JSON.stringify(message))
  return channel.sendToQueue(queue, buffer, options)
}

// 发布到交换机的函数
export async function publishToExchange<T>(
  exchange: string,
  routingKey: string,
  message: T,
  options: PublishOptions = { persistent: true }
): Promise<boolean> {
  const channel = await getChannel()
  await channel.assertExchange(exchange, 'topic', { durable: true })

  const buffer = Buffer.from(JSON.stringify(message))
  return channel.publish(exchange, routingKey, buffer, options)
}
