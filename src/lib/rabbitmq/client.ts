'server-only'
/** RabbitMQ 连接客户端 */
import { connect, Channel, ConsumeMessage } from 'amqplib'

// 使用泛型重新定义接口
export interface TypedMessage<T = Record<string, unknown>> {
  [key: string]: unknown
  payload: T
}

// 定义连接和通道的类型，以符合实际使用
type AmqpConnection = Awaited<ReturnType<typeof connect>>
type AmqpChannel = Awaited<ReturnType<AmqpConnection['createChannel']>>

// 使用正确的类型定义避免类型混淆
let connectionInstance: AmqpConnection | undefined
let channelInstance: AmqpChannel | undefined
let connectionPromise: Promise<{
  rabbitConnection: AmqpConnection
  rabbitChannel: AmqpChannel
}> | null = null

// 添加重连相关配置
const RECONNECT_INTERVAL = 5000 // 5秒重试间隔
const MAX_RECONNECT_ATTEMPTS = 10 // 最大重试次数
let reconnectAttempts = 0
let reconnectTimer: NodeJS.Timeout | null = null

// 连接单例模式实现 - 防止多次创建连接
export async function connectRabbitMQ(): Promise<{
  rabbitConnection: AmqpConnection
  rabbitChannel: AmqpChannel
}> {
  // 如果已经有一个连接过程在进行中，返回该promise
  if (connectionPromise) {
    return connectionPromise
  }

  // 如果已经连接，直接返回现有实例
  if (connectionInstance && channelInstance) {
    return {
      rabbitConnection: connectionInstance,
      rabbitChannel: channelInstance
    }
  }

  // 创建新连接
  connectionPromise = (async () => {
    try {
      // 使用更明确的导入方式和变量命名
      const connection = await connect(process.env.RABBITMQ_URL || 'amqp://localhost')
      const channel = await connection.createChannel()

      // 保存到全局变量
      connectionInstance = connection
      channelInstance = channel
      reconnectAttempts = 0 // 重置重连次数

      console.log('Connected to RabbitMQ')

      // 设置连接关闭事件处理
      connection.on('close', () => {
        console.log('RabbitMQ connection closed')
        connectionInstance = undefined
        channelInstance = undefined
        connectionPromise = null

        // 尝试重连
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          console.log(
            `Attempting to reconnect (${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})...`
          )
          reconnectTimer = setTimeout(() => {
            reconnectAttempts++
            connectRabbitMQ().catch((err) => {
              console.error('Reconnection failed:', err)
            })
          }, RECONNECT_INTERVAL)
        } else {
          console.error('Max reconnection attempts reached. Please check your RabbitMQ server.')
        }
      })

      connection.on('error', (err) => {
        console.error('RabbitMQ connection error:', err)
        connectionPromise = null

        // 在错误时也尝试重连
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          console.log(
            `Attempting to reconnect after error (${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})...`
          )
          reconnectTimer = setTimeout(() => {
            reconnectAttempts++
            connectRabbitMQ().catch((err) => {
              console.error('Reconnection failed:', err)
            })
          }, RECONNECT_INTERVAL)
        }
      })

      // 处理进程退出时的连接关闭
      process.once('SIGINT', () => {
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
        }
        closeConnection()
      })
      process.once('SIGTERM', () => {
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
        }
        closeConnection()
      })

      // 返回明确的类型和名称
      return {
        rabbitConnection: connection,
        rabbitChannel: channel
      }
    } catch (error) {
      connectionPromise = null
      console.error('RabbitMQ连接错误:', error)

      // 在初始连接失败时也尝试重连
      if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        console.log(
          `Attempting to reconnect after initial failure (${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})...`
        )
        reconnectTimer = setTimeout(() => {
          reconnectAttempts++
          connectRabbitMQ().catch((err) => {
            console.error('Reconnection failed:', err)
          })
        }, RECONNECT_INTERVAL)
      }

      throw error
    }
  })()

  return connectionPromise
}

// 获取channel的快捷方法 - 兼容之前架构的getChannel
export async function getChannel(): Promise<Channel> {
  const { rabbitChannel } = await connectRabbitMQ()
  return rabbitChannel
}

// 发布消息函数
export async function publishMessage<T>(
  queue: string,
  message: TypedMessage<T>,
  options: { durable?: boolean; persistent?: boolean } = { durable: true, persistent: true }
): Promise<boolean> {
  // 处理通道不存在的情况
  if (!channelInstance) {
    const result = await connectRabbitMQ()
    connectionInstance = result.rabbitConnection
    channelInstance = result.rabbitChannel
  }

  // 确保通道存在
  if (!channelInstance) {
    throw new Error('Failed to establish RabbitMQ channel')
  }

  await channelInstance.assertQueue(queue, { durable: options.durable ?? true })
  const result = channelInstance.sendToQueue(queue, Buffer.from(JSON.stringify(message)), {
    persistent: options.persistent ?? true
  })
  console.log(`消息已发送到队列 ${queue}`)
  return result
}

// 消费消息函数
export async function consumeMessages<T>(
  queue: string,
  callback: (message: TypedMessage<T>, msg: ConsumeMessage) => Promise<void>,
  options: { durable?: boolean; noAck?: boolean; prefetch?: number } = {}
): Promise<{ cancel: () => Promise<void> }> {
  // 处理通道不存在的情况
  if (!channelInstance) {
    const result = await connectRabbitMQ()
    connectionInstance = result.rabbitConnection
    channelInstance = result.rabbitChannel
  }

  // 确保通道存在
  if (!channelInstance) {
    throw new Error('Failed to establish RabbitMQ channel')
  }

  const currentChannel = channelInstance

  // 设置预取数量
  if (options.prefetch) {
    await currentChannel.prefetch(options.prefetch)
  }

  await currentChannel.assertQueue(queue, { durable: options.durable ?? true })

  // 添加消费标签以便后续取消
  const { consumerTag } = await currentChannel.consume(
    queue,
    (msg: ConsumeMessage | null) => {
      if (msg) {
        try {
          const content = JSON.parse(msg.content.toString()) as TypedMessage<T>

          // 执行回调
          callback(content, msg)
            .then(() => {
              // 只有当noAck为false时才需要确认
              if (!options.noAck) {
                currentChannel.ack(msg)
              }
            })
            .catch((err) => {
              console.error(`处理消息失败: ${err}`)
              // 拒绝消息并不重新排队
              if (!options.noAck) {
                currentChannel.nack(msg, false, false)
              }
            })
        } catch (error) {
          console.error(`解析消息失败: ${error}`)
          if (!options.noAck) {
            currentChannel.nack(msg, false, false)
          }
        }
      }
    },
    { noAck: options.noAck ?? false }
  )

  console.log(`正在监听队列 ${queue} 的消息`)

  // 返回取消消费的函数
  return {
    cancel: async () => {
      if (currentChannel && consumerTag) {
        await currentChannel.cancel(consumerTag)
        console.log(`已停止监听队列 ${queue}`)
      }
    }
  }
}

// 关闭连接函数
export async function closeConnection(): Promise<void> {
  try {
    if (channelInstance) {
      await channelInstance.close()
      channelInstance = undefined
    }
    if (connectionInstance) {
      await connectionInstance.close()
      connectionInstance = undefined
    }
    connectionPromise = null
    console.log('RabbitMQ连接已关闭')
  } catch (error) {
    console.error('Error closing connection:', error)
    throw error
  }
}

// 直接导出获取连接的函数，与之前架构兼容
export async function getConnection(): Promise<AmqpConnection> {
  const { rabbitConnection } = await connectRabbitMQ()
  return rabbitConnection
}
