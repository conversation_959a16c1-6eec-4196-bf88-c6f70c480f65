'server-only'
/** 消费消费者基础类 */
import { Channel, ConsumeMessage } from 'amqplib'
import { getChannel } from './client'

// 消息处理结果枚举
export enum ProcessStatus {
  SUCCESS = 'success',
  RETRY = 'retry',
  FAILURE = 'failure'
}

// 消费选项接口
export interface ConsumeOptions {
  prefetch?: number
  noAck?: boolean
  exclusive?: boolean
}

// 泛型消费者类
export class Consumer<T> {
  private queue: string
  private channel: Channel | null = null

  constructor(queue: string) {
    this.queue = queue
  }

  // 初始化通道和队列
  private async initialize(): Promise<Channel> {
    if (!this.channel) {
      this.channel = await getChannel()
      await this.channel.assertQueue(this.queue, { durable: true })
    }
    return this.channel
  }

  // 主要消费方法
  async consume(
    callback: (message: T, originalMessage: ConsumeMessage) => Promise<ProcessStatus>,
    options: ConsumeOptions = { prefetch: 1 }
  ): Promise<void> {
    const channel = await this.initialize()

    if (options.prefetch) {
      await channel.prefetch(options.prefetch)
    }

    console.log(`等待来自队列的消息: ${this.queue}`)

    channel.consume(
      this.queue,
      async (msg) => {
        if (!msg) return // null 消息表示消费者被取消

        try {
          const content = JSON.parse(msg.content.toString()) as T
          const status = await callback(content, msg)

          switch (status) {
            case ProcessStatus.SUCCESS:
              channel.ack(msg)
              break
            case ProcessStatus.RETRY:
              // 拒绝并重新入队
              channel.nack(msg, false, true)
              break
            case ProcessStatus.FAILURE:
              // 拒绝不重新入队（将转到死信交换机，如果已配置）
              channel.nack(msg, false, false)
              break
          }
        } catch (error) {
          console.error(`处理消息时出错: ${error}`)
          channel.nack(msg, false, false)
        }
      },
      { noAck: options.noAck, exclusive: options.exclusive }
    )
  }

  // 从交换机消费消息
  async consumeFromExchange(
    exchange: string,
    patterns: string[],
    callback: (message: T, originalMessage: ConsumeMessage) => Promise<ProcessStatus>,
    options: ConsumeOptions = { prefetch: 1 }
  ): Promise<void> {
    const channel = await this.initialize()

    await channel.assertExchange(exchange, 'topic', { durable: true })
    const { queue } = await channel.assertQueue('', { exclusive: true })

    for (const pattern of patterns) {
      await channel.bindQueue(queue, exchange, pattern)
    }

    if (options.prefetch) {
      await channel.prefetch(options.prefetch)
    }

    console.log(`等待来自交换机的消息: ${exchange}, 路由模式: ${patterns.join(', ')}`)

    channel.consume(
      queue,
      async (msg) => {
        if (!msg) return

        try {
          const content = JSON.parse(msg.content.toString()) as T
          const status = await callback(content, msg)

          switch (status) {
            case ProcessStatus.SUCCESS:
              channel.ack(msg)
              break
            case ProcessStatus.RETRY:
              channel.nack(msg, false, true)
              break
            case ProcessStatus.FAILURE:
              channel.nack(msg, false, false)
              break
          }
        } catch (error) {
          console.error(`处理消息时出错: ${error}`)
          channel.nack(msg, false, false)
        }
      },
      { noAck: options.noAck, exclusive: options.exclusive }
    )
  }
}
