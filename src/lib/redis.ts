'server-only'
import { Redis } from 'ioredis'

// 支持多数据库的Redis连接池
const redisClients: Map<number, Redis> = new Map()

/**
 * 获取Redis客户端
 * @param options Redis配置选项，可以是数据库编号或配置对象
 * @returns Redis客户端实例
 */
export function getRedisClient(options: number | { db?: number } = 1): Redis {
  // 处理参数，支持数字或配置对象
  const db = typeof options === 'number' ? options : (options.db ?? 1)
  console.log('getRedisClient:', {
    db,
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || '6379',
    hasPassword: !!(process.env.REDIS_PASSWORD)
  })

  // 检查是否已存在该数据库的连接
  if (redisClients.has(db)) {
    return redisClients.get(db)!
  }

  // 创建新的Redis连接
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
    lazyConnect: true
  })

  // 添加连接事件监听
  redis.on('connect', () => {
    console.log(`Redis connected to database ${db}`)
  })

  redis.on('error', (err) => {
    console.error(`Redis connection error for database ${db}:`, err)
  })

  redis.on('close', () => {
    console.log(`Redis connection closed for database ${db}`)
    // 连接关闭时从缓存中移除
    redisClients.delete(db)
  })

  // 缓存连接
  redisClients.set(db, redis)

  return redis
}

/**
 * 关闭所有Redis连接
 */
export async function closeAllRedisConnections(): Promise<void> {
  const promises = Array.from(redisClients.values()).map(redis => redis.quit())
  await Promise.all(promises)
  redisClients.clear()
  console.log('All Redis connections closed')
}
