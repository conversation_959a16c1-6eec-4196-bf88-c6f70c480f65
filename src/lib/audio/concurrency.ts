/**
 * 音频生成并发控制管理
 * 负责分布式锁和等待队列的实现
 */

import { getRedisClient } from '@/lib/redis';
import { LockResult, WaitResult } from '@/types/audio.types';
import { generateLockKey, generateLockValue, delay } from './file-utils';
import { logLockOperation, logConcurrencyEvent, logError } from './logger';
import { createS3Manager } from './s3';

/**
 * 音频并发管理器
 */
export class AudioConcurrencyManager {
  private redis: ReturnType<typeof getRedisClient>;
  private s3Manager: ReturnType<typeof createS3Manager>;

  constructor() {
    this.redis = getRedisClient();
    this.s3Manager = createS3Manager();
  }

  /**
   * 尝试获取分布式锁
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<LockResult> 锁获取结果
   */
  async acquireLock(bookId: number, locale: string): Promise<LockResult> {
    const lockKey = generateLockKey(bookId, locale);
    const lockValue = generateLockValue();
    const lockTTL = parseInt(process.env.AUDIO_LOCK_TTL || '60000'); // 默认60秒

    try {
      // 使用Redis SET命令的NX和PX选项实现分布式锁
      const result = await this.redis.set(
        lockKey,
        lockValue,
        'PX', lockTTL, // 过期时间（毫秒）
        'NX'  // 只在key不存在时设置
      );

      const acquired = result === 'OK';

      logLockOperation('ACQUIRE', lockKey, acquired, {
        lockValue: acquired ? lockValue : undefined,
        ttl: lockTTL
      });

      if (acquired) {
        logConcurrencyEvent('LOCK_ACQUIRED', { bookId, locale, lockKey });
      } else {
        logConcurrencyEvent('LOCK_FAILED', { bookId, locale, lockKey });
      }

      return {
        acquired,
        lockKey,
        lockValue: acquired ? lockValue : undefined
      };

    } catch (error) {
      logError('Failed to acquire lock', {
        lockKey,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        acquired: false,
        lockKey
      };
    }
  }

  /**
   * 释放分布式锁
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @param lockValue 锁值（防止误删）
   * @returns Promise<boolean> 是否释放成功
   */
  async releaseLock(bookId: number, locale: string, lockValue?: string): Promise<boolean> {
    const lockKey = generateLockKey(bookId, locale);

    try {
      if (lockValue) {
        // 使用Lua脚本确保原子性：只有锁值匹配才删除
        const luaScript = `
          if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
          else
            return 0
          end
        `;

        const result = await this.redis.eval(luaScript, 1, lockKey, lockValue) as number;
        const released = result === 1;

        logLockOperation('RELEASE', lockKey, released, { lockValue });
        return released;
      } else {
        // 如果没有锁值，直接删除（不推荐，但作为备用方案）
        const result = await this.redis.del(lockKey);
        const released = result > 0;

        logLockOperation('RELEASE', lockKey, released, { forced: true });
        return released;
      }

    } catch (error) {
      logError('Failed to release lock', {
        lockKey,
        lockValue,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * 等待其他进程完成音频生成
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<WaitResult> 等待结果
   */
  async waitForCompletion(bookId: number, locale: string): Promise<WaitResult> {
    const maxWaitTime = parseInt(process.env.AUDIO_MAX_WAIT_TIME || '30000'); // 默认30秒
    const checkInterval = parseInt(process.env.AUDIO_CHECK_INTERVAL || '1000'); // 默认1秒
    const startTime = Date.now();

    logConcurrencyEvent('WAITING_START', { bookId, locale });

    try {
      while (Date.now() - startTime < maxWaitTime) {
        // 检查S3文件是否已生成
        const fileResult = await this.s3Manager.checkFileExists(bookId, locale);
        
        if (fileResult.exists && fileResult.url) {
          const waitTime = Date.now() - startTime;
          
          logConcurrencyEvent('WAITING_SUCCESS', { 
            bookId, 
            locale, 
            waitTime 
          });

          return {
            success: true,
            audioUrl: fileResult.url,
            timeout: false,
            waitTime
          };
        }

        // 等待指定间隔后重试
        await delay(checkInterval);
      }

      // 等待超时
      const waitTime = Date.now() - startTime;
      
      logConcurrencyEvent('WAITING_TIMEOUT', { 
        bookId, 
        locale, 
        waitTime 
      });

      return {
        success: false,
        timeout: true,
        waitTime
      };

    } catch (error) {
      const waitTime = Date.now() - startTime;
      
      logError('Wait for completion failed', {
        bookId,
        locale,
        waitTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        timeout: false,
        waitTime
      };
    }
  }

  /**
   * 检查锁是否存在
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<boolean> 锁是否存在
   */
  async isLocked(bookId: number, locale: string): Promise<boolean> {
    const lockKey = generateLockKey(bookId, locale);

    try {
      const exists = await this.redis.exists(lockKey);
      return exists === 1;
    } catch (error) {
      logError('Failed to check lock existence', {
        lockKey,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * 获取锁的剩余TTL
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<number> 剩余TTL（毫秒），-1表示锁不存在
   */
  async getLockTTL(bookId: number, locale: string): Promise<number> {
    const lockKey = generateLockKey(bookId, locale);

    try {
      const ttl = await this.redis.pttl(lockKey);
      return ttl;
    } catch (error) {
      logError('Failed to get lock TTL', {
        lockKey,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return -1;
    }
  }

  /**
   * 清理过期的锁（维护功能）
   * @returns Promise<number> 清理的锁数量
   */
  async cleanupExpiredLocks(): Promise<number> {
    try {
      const pattern = 'audio_lock:*';
      const keys = await this.redis.keys(pattern);
      
      let cleanedCount = 0;
      
      for (const key of keys) {
        const ttl = await this.redis.pttl(key);
        
        // TTL为-1表示没有过期时间，TTL为-2表示key不存在
        if (ttl === -2) {
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logLockOperation('CLEANUP', `${cleanedCount} expired locks`, true);
      }

      return cleanedCount;
    } catch (error) {
      logError('Failed to cleanup expired locks', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * 获取当前所有锁的状态（调试用）
   * @returns Promise<Array<{key: string, ttl: number}>>
   */
  async getAllLockStatus(): Promise<Array<{ key: string; ttl: number; bookId?: number; locale?: string }>> {
    try {
      const pattern = 'audio_lock:*';
      const keys = await this.redis.keys(pattern);
      
      const lockStatus = [];
      
      for (const key of keys) {
        const ttl = await this.redis.pttl(key);
        
        // 尝试从key中解析bookId和locale
        const match = key.match(/audio_lock:(\d+):(\w+)/);
        const bookId = match ? parseInt(match[1]) : undefined;
        const locale = match ? match[2] : undefined;
        
        lockStatus.push({
          key,
          ttl,
          bookId,
          locale
        });
      }

      return lockStatus;
    } catch (error) {
      logError('Failed to get lock status', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }
}

/**
 * 创建并发管理器实例
 * @returns AudioConcurrencyManager实例
 */
export function createConcurrencyManager(): AudioConcurrencyManager {
  return new AudioConcurrencyManager();
}
