/**
 * 音频分段处理模块
 * 负责智能分割文本内容，决定是否需要分段生成音频
 */

import { Chapter } from './chapter-utils';
import { calculateCharacterCount } from './file-utils';
import { estimateAudioFileSize, estimateAudioDuration } from './elevenlabs';

// 分段配置
export interface SegmentationConfig {
  maxCharsPerSegment: number;     // 单段最大字符数
  minCharsPerSegment: number;     // 单段最小字符数
  maxFileSize: number;            // 最大文件大小（字节）
  targetSegmentCount: number;     // 目标分段数量
  maxSegmentCount: number;        // 最大分段数量
  overlapChars: number;          // 段落间重叠字符
}

// 文本分段接口
export interface TextSegment {
  index: number;
  text: string;
  startChar: number;
  endChar: number;
  estimatedDuration: number;
  estimatedSize: number;
  chapters: Chapter[];           // 包含的章节
}

// 分段分析结果
export interface SegmentationAnalysis {
  needsSegmentation: boolean;
  totalCharacters: number;
  estimatedTotalSize: number;
  estimatedTotalDuration: number;
  recommendedSegmentCount: number;
  segments?: TextSegment[];
  reason: string;
}

// 默认分段配置
const DEFAULT_CONFIG: SegmentationConfig = {
  maxCharsPerSegment: 8000,
  minCharsPerSegment: 2000,
  maxFileSize: 25 * 1024 * 1024, // 25MB
  targetSegmentCount: 5,
  maxSegmentCount: 10,
  overlapChars: 50
};

/**
 * 分析是否需要分段处理
 * @param chapters 章节数组
 * @param locale 语言代码
 * @param config 分段配置
 * @returns 分段分析结果
 */
export function analyzeSegmentationNeeds(
  chapters: Chapter[],
  locale: string,
  config: SegmentationConfig = DEFAULT_CONFIG
): SegmentationAnalysis {
  // 1. 计算总文本长度
  const fullText = assembleFullText(chapters);
  const totalCharacters = calculateCharacterCount(fullText);
  
  // 2. 估算总文件大小和时长
  const estimatedTotalSize = estimateAudioFileSize(fullText, locale);
  const estimatedTotalDuration = estimateAudioDuration(fullText, locale);
  
  // 3. 判断是否需要分段
  const needsSegmentation = shouldSegment(totalCharacters, estimatedTotalSize, config);
  
  if (!needsSegmentation) {
    return {
      needsSegmentation: false,
      totalCharacters,
      estimatedTotalSize,
      estimatedTotalDuration,
      recommendedSegmentCount: 1,
      reason: `Text size (${Math.round(estimatedTotalSize / 1024 / 1024 * 100) / 100}MB) is within limits`
    };
  }
  
  // 4. 计算推荐分段数量
  const recommendedSegmentCount = calculateRecommendedSegmentCount(
    totalCharacters,
    estimatedTotalSize,
    config
  );
  
  // 5. 生成分段
  const segments = createSegments(chapters, locale, recommendedSegmentCount, config);
  
  return {
    needsSegmentation: true,
    totalCharacters,
    estimatedTotalSize,
    estimatedTotalDuration,
    recommendedSegmentCount,
    segments,
    reason: `Text size (${Math.round(estimatedTotalSize / 1024 / 1024 * 100) / 100}MB) exceeds limit, splitting into ${recommendedSegmentCount} segments`
  };
}

/**
 * 判断是否需要分段
 */
function shouldSegment(
  totalCharacters: number,
  estimatedSize: number,
  config: SegmentationConfig
): boolean {
  // 文件大小超限
  if (estimatedSize > config.maxFileSize) {
    return true;
  }
  
  // 字符数过多
  if (totalCharacters > config.maxCharsPerSegment) {
    return true;
  }
  
  return false;
}

/**
 * 计算推荐分段数量
 */
function calculateRecommendedSegmentCount(
  totalCharacters: number,
  estimatedSize: number,
  config: SegmentationConfig
): number {
  // 基于文件大小计算
  const sizeBasedCount = Math.ceil(estimatedSize / (config.maxFileSize * 0.8)); // 80%安全边际
  
  // 基于字符数计算
  const charBasedCount = Math.ceil(totalCharacters / config.maxCharsPerSegment);
  
  // 取较大值，但不超过最大限制
  const recommendedCount = Math.max(sizeBasedCount, charBasedCount);
  
  return Math.min(recommendedCount, config.maxSegmentCount);
}

/**
 * 创建文本分段
 */
function createSegments(
  chapters: Chapter[],
  locale: string,
  segmentCount: number,
  config: SegmentationConfig
): TextSegment[] {
  // 策略1: 按章节分组（优先）
  if (chapters.length >= segmentCount) {
    return createChapterBasedSegments(chapters, locale, segmentCount);
  }
  
  // 策略2: 按字符数分割
  return createCharacterBasedSegments(chapters, locale, segmentCount, config);
}

/**
 * 按章节创建分段
 */
function createChapterBasedSegments(
  chapters: Chapter[],
  locale: string,
  segmentCount: number
): TextSegment[] {
  const segments: TextSegment[] = [];
  const chaptersPerSegment = Math.ceil(chapters.length / segmentCount);
  
  for (let i = 0; i < segmentCount; i++) {
    const startIndex = i * chaptersPerSegment;
    const endIndex = Math.min(startIndex + chaptersPerSegment, chapters.length);
    const segmentChapters = chapters.slice(startIndex, endIndex);
    
    if (segmentChapters.length === 0) break;
    
    const text = assembleFullText(segmentChapters);
    const startChar = i === 0 ? 0 : segments[i - 1].endChar;
    const endChar = startChar + text.length;
    
    segments.push({
      index: i,
      text,
      startChar,
      endChar,
      estimatedDuration: estimateAudioDuration(text, locale),
      estimatedSize: estimateAudioFileSize(text, locale),
      chapters: segmentChapters
    });
  }
  
  return segments;
}

/**
 * 按字符数创建分段
 */
function createCharacterBasedSegments(
  chapters: Chapter[],
  locale: string,
  segmentCount: number,
  config: SegmentationConfig
): TextSegment[] {
  const fullText = assembleFullText(chapters);
  const totalLength = fullText.length;
  const segmentLength = Math.ceil(totalLength / segmentCount);
  
  const segments: TextSegment[] = [];
  
  for (let i = 0; i < segmentCount; i++) {
    const startChar = i * segmentLength;
    const endChar = Math.min(startChar + segmentLength, totalLength);
    
    if (startChar >= totalLength) break;
    
    // 在句子边界调整切分点
    const adjustedEndChar = adjustSegmentBoundary(fullText, endChar, totalLength);
    const text = fullText.substring(startChar, adjustedEndChar);
    
    segments.push({
      index: i,
      text,
      startChar,
      endChar: adjustedEndChar,
      estimatedDuration: estimateAudioDuration(text, locale),
      estimatedSize: estimateAudioFileSize(text, locale),
      chapters: [] // 字符分割时无法精确对应章节
    });
  }
  
  return segments;
}

/**
 * 调整分段边界到句子结尾
 */
function adjustSegmentBoundary(text: string, endChar: number, totalLength: number): number {
  if (endChar >= totalLength) return totalLength;
  
  // 向后查找句子结束符
  const sentenceEnders = ['.', '!', '?', '\n'];
  for (let i = endChar; i < Math.min(endChar + 200, totalLength); i++) {
    if (sentenceEnders.includes(text[i])) {
      return i + 1;
    }
  }
  
  // 如果找不到句子结束符，返回原位置
  return endChar;
}

/**
 * 组装完整文本
 */
function assembleFullText(chapters: Chapter[]): string {
  return chapters
    .filter(chapter => chapter.title && chapter.summary)
    .map(chapter => `${chapter.title.trim()}\n${chapter.summary.trim()}`)
    .join('\n\n');
}

/**
 * 获取默认分段配置
 */
export function getDefaultSegmentationConfig(): SegmentationConfig {
  return { ...DEFAULT_CONFIG };
}

/**
 * 验证分段配置
 */
export function validateSegmentationConfig(config: SegmentationConfig): boolean {
  return (
    config.maxCharsPerSegment > config.minCharsPerSegment &&
    config.maxSegmentCount > 0 &&
    config.targetSegmentCount > 0 &&
    config.maxFileSize > 0
  );
}
