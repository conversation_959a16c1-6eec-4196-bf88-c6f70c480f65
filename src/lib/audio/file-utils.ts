/**
 * 音频文件相关工具函数
 */

import { AudioGenerationConfig } from '@/types/audio.types';

/**
 * 生成S3文件键
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns S3文件键
 */
export function generateS3Key(bookId: number, locale: string): string {
  const prefix = process.env.AWS_S3_AUDIO_PREFIX || 'audio/';
  return `${prefix}book_${bookId}_${locale}_summary.mp3`;
}

/**
 * 生成音频文件名
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns 文件名
 */
export function generateAudioFileName(bookId: number, locale: string): string {
  return `book_${bookId}_${locale}_summary.mp3`;
}

/**
 * 生成缓存键
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns 缓存键
 */
export function generateCacheKey(bookId: number, locale: string): string {
  return `audio_metadata:${bookId}:${locale}`;
}

/**
 * 生成分布式锁键
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns 锁键
 */
export function generateLockKey(bookId: number, locale: string): string {
  return `audio_lock:${bookId}:${locale}`;
}

/**
 * 生成锁值（防止误删锁）
 * @returns 唯一锁值
 */
export function generateLockValue(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 验证书籍ID
 * @param bookId 书籍ID字符串
 * @returns 数字形式的bookId或null
 */
export function validateBookId(bookId: string): number | null {
  const bookIdNum = parseInt(bookId, 10);
  if (isNaN(bookIdNum) || bookIdNum <= 0) {
    return null;
  }
  return bookIdNum;
}

/**
 * 验证语言代码
 * @param locale 语言代码
 * @returns 有效的locale或默认值
 */
export function validateLocale(locale: string | null): string {
  const supportedLocales = ['en', 'zh'];
  if (!locale || !supportedLocales.includes(locale)) {
    return 'en'; // 默认英文
  }
  return locale;
}

/**
 * 验证文本长度
 * @param text 要验证的文本
 * @returns 是否有效
 */
export function validateTextLength(text: string): boolean {
  const trimmedText = text.trim();
  const minLength = parseInt(process.env.AUDIO_MIN_TEXT_LENGTH || '10');
  const maxLength = parseInt(process.env.AUDIO_MAX_TEXT_LENGTH || '50000');
  
  return trimmedText.length >= minLength && trimmedText.length <= maxLength;
}

/**
 * 计算文本字符数
 * @param text 文本内容
 * @returns 字符数
 */
export function calculateCharacterCount(text: string): number {
  return text.trim().length;
}

/**
 * 验证文件大小
 * @param fileSize 文件大小（字节）
 * @returns 是否在允许范围内
 */
export function validateFileSize(fileSize: number): boolean {
  const maxSize = parseInt(process.env.AUDIO_MAX_FILE_SIZE || '10485760'); // 10MB
  return fileSize > 0 && fileSize <= maxSize;
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化持续时间
 * @param seconds 秒数
 * @returns 格式化的时间字符串
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 获取音频生成配置
 * @returns 音频生成配置对象
 */
export function getAudioConfig(): AudioGenerationConfig {
  return {
    maxFileSize: parseInt(process.env.AUDIO_MAX_FILE_SIZE || '10485760'),
    timeout: parseInt(process.env.AUDIO_GENERATION_TIMEOUT || '30000'),
    retryAttempts: 3,
    cacheTTL: parseInt(process.env.AUDIO_CACHE_TTL || '86400'),
    maxTextLength: parseInt(process.env.AUDIO_MAX_TEXT_LENGTH || '50000'),
    minTextLength: parseInt(process.env.AUDIO_MIN_TEXT_LENGTH || '10')
  };
}

/**
 * 检查环境变量配置
 * @returns 配置检查结果
 */
export function checkEnvironmentConfig(): { valid: boolean; missing: string[] } {
  const requiredEnvVars = [
    'ELEVENLABS_API_KEY',
    'AWS_S3_BUCKET',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY'
  ];
  
  const missing: string[] = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missing.push(envVar);
    }
  }
  
  return {
    valid: missing.length === 0,
    missing
  };
}

/**
 * 生成唯一请求ID
 * @returns 请求ID
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 安全地解析JSON
 * @param jsonString JSON字符串
 * @param defaultValue 默认值
 * @returns 解析结果或默认值
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
}

/**
 * 延迟函数
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
