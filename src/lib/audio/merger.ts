/**
 * 音频合并模块
 * 负责将多个音频文件合并成一个完整的MP3文件
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

// 音频分段接口
export interface AudioSegment {
  index: number;
  buffer: Buffer;
  duration: number;
  fileSize: number;
  tempPath?: string;
}

// 合并结果接口
export interface MergeResult {
  success: boolean;
  finalBuffer?: Buffer;
  totalDuration: number;
  totalSize: number;
  segmentCount: number;
  error?: string;
  tempFiles?: string[];
}

// 合并配置
export interface MergeConfig {
  outputFormat: string;
  quality: string;
  addSilence: boolean;
  silenceDuration: number; // 段落间静音时长（毫秒）
  tempDir: string;
}

// 默认合并配置
const DEFAULT_MERGE_CONFIG: MergeConfig = {
  outputFormat: 'mp3',
  quality: '128k',
  addSilence: true,
  silenceDuration: 500, // 0.5秒静音
  tempDir: join(tmpdir(), 'audio-merge')
};

/**
 * 合并多个音频分段
 * @param segments 音频分段数组
 * @param config 合并配置
 * @returns 合并结果
 */
export async function mergeAudioSegments(
  segments: AudioSegment[],
  config: MergeConfig = DEFAULT_MERGE_CONFIG
): Promise<MergeResult> {
  const tempFiles: string[] = [];
  
  try {
    // 1. 验证输入
    if (!segments || segments.length === 0) {
      throw new Error('No audio segments provided for merging');
    }
    
    if (segments.length === 1) {
      // 只有一个分段，直接返回
      return {
        success: true,
        finalBuffer: segments[0].buffer,
        totalDuration: segments[0].duration,
        totalSize: segments[0].fileSize,
        segmentCount: 1
      };
    }
    
    // 2. 创建临时目录
    await ensureTempDirectory(config.tempDir);
    
    // 3. 保存分段到临时文件
    const tempFilePaths = await saveSegmentsToTempFiles(segments, config.tempDir);
    tempFiles.push(...tempFilePaths);
    
    // 4. 使用简单的Buffer拼接方法合并音频
    const mergedBuffer = await mergeAudioBuffers(segments, config);
    
    // 5. 计算总时长和大小
    const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0);
    const totalSize = mergedBuffer.length;
    
    return {
      success: true,
      finalBuffer: mergedBuffer,
      totalDuration,
      totalSize,
      segmentCount: segments.length,
      tempFiles
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown merge error';
    
    return {
      success: false,
      totalDuration: 0,
      totalSize: 0,
      segmentCount: segments.length,
      error: errorMessage,
      tempFiles
    };
  }
}

/**
 * 使用Buffer拼接方法合并音频（简单实现）
 * 注意：这是一个简化的实现，可能不会产生完美的音频质量
 * 在生产环境中建议使用FFmpeg等专业工具
 */
async function mergeAudioBuffers(
  segments: AudioSegment[],
  config: MergeConfig
): Promise<Buffer> {
  const buffers: Buffer[] = [];
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    
    // 添加音频数据
    buffers.push(segment.buffer);
    
    // 在分段之间添加静音（除了最后一个分段）
    if (config.addSilence && i < segments.length - 1) {
      const silenceBuffer = createSilenceBuffer(config.silenceDuration);
      buffers.push(silenceBuffer);
    }
  }
  
  // 合并所有Buffer
  return Buffer.concat(buffers);
}

/**
 * 创建静音Buffer（简化实现）
 * @param durationMs 静音时长（毫秒）
 * @returns 静音Buffer
 */
function createSilenceBuffer(durationMs: number): Buffer {
  // 简化实现：创建一个小的静音MP3片段
  // 在实际应用中，这里应该生成正确的MP3静音数据
  const silenceSize = Math.ceil(durationMs * 16); // 粗略估算
  return Buffer.alloc(silenceSize, 0);
}

/**
 * 保存音频分段到临时文件
 */
async function saveSegmentsToTempFiles(
  segments: AudioSegment[],
  tempDir: string
): Promise<string[]> {
  const tempFilePaths: string[] = [];
  
  for (const segment of segments) {
    const tempFilePath = join(tempDir, `segment_${segment.index}.mp3`);
    await fs.writeFile(tempFilePath, segment.buffer);
    tempFilePaths.push(tempFilePath);
  }
  
  return tempFilePaths;
}

/**
 * 确保临时目录存在
 */
async function ensureTempDirectory(tempDir: string): Promise<void> {
  try {
    await fs.access(tempDir);
  } catch {
    await fs.mkdir(tempDir, { recursive: true });
  }
}

/**
 * 清理临时文件
 * @param tempFiles 临时文件路径数组
 */
export async function cleanupTempFiles(tempFiles: string[]): Promise<void> {
  const cleanupPromises = tempFiles.map(async (filePath) => {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.warn(`Failed to cleanup temp file ${filePath}:`, error);
    }
  });
  
  await Promise.allSettled(cleanupPromises);
}

/**
 * 清理临时目录
 * @param tempDir 临时目录路径
 */
export async function cleanupTempDirectory(tempDir: string): Promise<void> {
  try {
    const files = await fs.readdir(tempDir);
    const cleanupPromises = files.map(file => 
      fs.unlink(join(tempDir, file)).catch(() => {})
    );
    await Promise.allSettled(cleanupPromises);
    
    // 尝试删除目录（如果为空）
    await fs.rmdir(tempDir).catch(() => {});
  } catch (error) {
    console.warn(`Failed to cleanup temp directory ${tempDir}:`, error);
  }
}

/**
 * 验证音频分段
 */
export function validateAudioSegments(segments: AudioSegment[]): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!segments || segments.length === 0) {
    errors.push('No segments provided');
    return { valid: false, errors };
  }
  
  segments.forEach((segment, index) => {
    if (!segment.buffer || segment.buffer.length === 0) {
      errors.push(`Segment ${index} has empty buffer`);
    }
    
    if (segment.duration <= 0) {
      errors.push(`Segment ${index} has invalid duration: ${segment.duration}`);
    }
    
    if (segment.fileSize <= 0) {
      errors.push(`Segment ${index} has invalid file size: ${segment.fileSize}`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 获取默认合并配置
 */
export function getDefaultMergeConfig(): MergeConfig {
  return { ...DEFAULT_MERGE_CONFIG };
}

/**
 * 估算合并后的文件大小
 */
export function estimateMergedSize(segments: AudioSegment[], config: MergeConfig): number {
  const audioSize = segments.reduce((sum, segment) => sum + segment.fileSize, 0);
  
  // 添加静音部分的大小估算
  if (config.addSilence && segments.length > 1) {
    const silenceCount = segments.length - 1;
    const silenceSizePerGap = Math.ceil(config.silenceDuration * 16); // 粗略估算
    const totalSilenceSize = silenceCount * silenceSizePerGap;
    return audioSize + totalSilenceSize;
  }
  
  return audioSize;
}
