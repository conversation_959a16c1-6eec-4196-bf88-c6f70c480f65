/**
 * S3音频文件操作封装
 * 负责音频文件的上传、检查和管理
 */

import AWS from 'aws-sdk';
import { FileExistsResult, S3UploadResponse } from '@/types/audio.types';
import { generateS3Key, validateFileSize, getAudioConfig } from './file-utils';
import { logS3FileCheck, logS3Upload, logError } from './logger';

/**
 * S3音频文件管理器
 */
export class AudioS3Manager {
  private s3: AWS.S3;
  private bucket: string;
  private cdnUrl: string;

  constructor() {
    // 检查必要的环境变量
    this.validateEnvironment();

    // 初始化S3客户端
    const s3Config: AWS.S3.ClientConfiguration = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    };

    // 如果有自定义endpoint（如S3兼容的CDN）
    if (process.env.S3_ENDPOINT) {
      s3Config.endpoint = process.env.S3_ENDPOINT;
      s3Config.s3ForcePathStyle = true; // 强制使用路径样式
    }

    this.s3 = new AWS.S3(s3Config);
    this.cdnUrl = process.env.S3_CDN_URL || process.env.S3_ENDPOINT || '';
    this.bucket = process.env.AWS_S3_BUCKET!;
  }

  /**
   * 验证环境变量配置
   */
  private validateEnvironment(): void {
    const requiredEnvVars = [
      'AWS_S3_BUCKET',
      'AWS_ACCESS_KEY_ID',
      'AWS_SECRET_ACCESS_KEY'
    ];

    const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);

    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // 检查CDN URL或S3 Endpoint
    if (!process.env.S3_CDN_URL && !process.env.S3_ENDPOINT) {
      throw new Error('Either S3_CDN_URL or S3_ENDPOINT must be configured');
    }
  }

  /**
   * 检查S3文件是否存在
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<FileExistsResult> 文件存在性检查结果
   */
  async checkFileExists(bookId: number, locale: string): Promise<FileExistsResult> {
    const s3Key = generateS3Key(bookId, locale);
    const startTime = Date.now();

    try {
      const result = await this.s3.headObject({
        Bucket: this.bucket,
        Key: s3Key
      }).promise();

      const responseTime = Date.now() - startTime;
      const fileUrl = this.generatePublicUrl(s3Key);

      logS3FileCheck(s3Key, true, responseTime);

      return {
        exists: true,
        url: fileUrl,
        fileSize: result.ContentLength,
        lastModified: result.LastModified?.toISOString()
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      if (error.statusCode === 404 || error.code === 'NotFound') {
        logS3FileCheck(s3Key, false, responseTime);
        return { exists: false };
      }

      // 其他错误
      logError(`S3 file check failed for ${s3Key}`, {
        error: error.message,
        statusCode: error.statusCode,
        code: error.code
      });

      throw new Error(`Failed to check S3 file existence: ${error.message}`);
    }
  }

  /**
   * 上传音频文件到S3
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @param audioBuffer 音频数据Buffer
   * @param metadata 额外的元数据
   * @returns Promise<S3UploadResponse> 上传结果
   */
  async uploadAudioFile(
    bookId: number,
    locale: string,
    audioBuffer: Buffer,
    metadata?: Record<string, string>
  ): Promise<S3UploadResponse> {
    const s3Key = generateS3Key(bookId, locale);
    const startTime = Date.now();

    // 验证文件大小
    if (!validateFileSize(audioBuffer.length)) {
      const config = getAudioConfig();
      throw new Error(`Audio file too large: ${audioBuffer.length} bytes (max: ${config.maxFileSize})`);
    }

    logS3Upload('START', {
      s3Key,
      fileSize: audioBuffer.length
    });

    try {
      // 准备上传参数
      const uploadParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucket,
        Key: s3Key,
        Body: audioBuffer,
        ContentType: 'audio/mpeg',
        CacheControl: 'public, max-age=31536000', // 1年缓存
        Metadata: {
          bookId: bookId.toString(),
          locale,
          generatedAt: new Date().toISOString(),
          fileSize: audioBuffer.length.toString(),
          ...metadata
        }
      };

      // 执行上传
      await this.s3.upload(uploadParams).promise();

      const responseTime = Date.now() - startTime;
      const publicUrl = this.generatePublicUrl(s3Key);

      logS3Upload('SUCCESS', {
        s3Key,
        fileSize: audioBuffer.length,
        responseTime
      });

      return {
        success: true,
        url: publicUrl,
        fileSize: audioBuffer.length,
        key: s3Key
      };

    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      logS3Upload('FAILED', {
        s3Key,
        fileSize: audioBuffer.length,
        responseTime,
        error: error.message
      });

      return {
        success: false,
        error: `S3 upload failed: ${error.message}`
      };
    }
  }

  /**
   * 生成公共访问URL
   * @param s3Key S3文件键
   * @returns 公共URL
   */
  generatePublicUrl(key: string): string {
    if (!this.cdnUrl) {
      throw new Error('CDN URL not configured. Please set S3_CDN_URL environment variable.');
    }

    // 确保CDN URL不以斜杠结尾，key不以斜杠开头
    const cleanCdnUrl = this.cdnUrl.replace(/\/$/, '');
    const cleanKey = key.replace(/^\//, '');

    const finalUrl = `${cleanCdnUrl}/${cleanKey}`;

    // 确保没有双斜杠（除了协议部分）
    return finalUrl.replace(/([^:]\/)\/+/g, '$1');
  }

  /**
   * 删除音频文件（可选功能）
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<boolean> 是否删除成功
   */
  async deleteAudioFile(bookId: number, locale: string): Promise<boolean> {
    const s3Key = generateS3Key(bookId, locale);

    try {
      await this.s3.deleteObject({
        Bucket: this.bucket,
        Key: s3Key
      }).promise();

      return true;
    } catch (error: any) {
      logError(`Failed to delete S3 file ${s3Key}`, { error: error.message });
      return false;
    }
  }

  /**
   * 获取文件元数据
   * @param bookId 书籍ID
   * @param locale 语言代码
   * @returns Promise<AWS.S3.HeadObjectOutput | null> 文件元数据
   */
  async getFileMetadata(bookId: number, locale: string): Promise<AWS.S3.HeadObjectOutput | null> {
    const s3Key = generateS3Key(bookId, locale);

    try {
      const result = await this.s3.headObject({
        Bucket: this.bucket,
        Key: s3Key
      }).promise();

      return result;
    } catch (error: any) {
      if (error.statusCode === 404 || error.code === 'NotFound') {
        return null;
      }
      throw error;
    }
  }

  /**
   * 检查S3连接性
   * @returns Promise<boolean> 是否连接正常
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.s3.headBucket({ Bucket: this.bucket }).promise();
      return true;
    } catch (error) {
      logError('S3 connection check failed', {
        bucket: this.bucket,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * 获取存储桶信息
   * @returns 存储桶配置信息
   */
  getBucketInfo(): { bucket: string; region: string; endpoint?: string } {
    return {
      bucket: this.bucket,
      region: process.env.AWS_S3_REGION || 'us-east-1',
      endpoint: process.env.S3_ENDPOINT
    };
  }
}

/**
 * 创建S3管理器实例
 * @returns AudioS3Manager实例
 */
export function createS3Manager(): AudioS3Manager {
  return new AudioS3Manager();
}
