/**
 * 音频缓存管理
 * 负责音频元数据的Redis缓存操作
 */

import { getRedisClient } from '@/lib/redis';
import { CachedAudioData, AudioFileMetadata } from '@/types/audio.types';
import { generateCacheKey, getAudioConfig, safeJsonParse } from './file-utils';
import { logCacheOperation, logError } from './logger';

// 缓存配置
const CACHE_PREFIX = 'audio_metadata:';

/**
 * 获取缓存的音频元数据
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<CachedAudioData | null>
 */
export async function getCachedAudioMetadata(bookId: number, locale: string): Promise<CachedAudioData | null> {
  try {
    const redis = getRedisClient();
    const cacheKey = generateCacheKey(bookId, locale);
    const cached = await redis.get(cacheKey);

    if (!cached) {
      logCacheOperation('GET', cacheKey, false);
      return null;
    }

    const parsedData: CachedAudioData | null = safeJsonParse<CachedAudioData | null>(cached, null);

    if (!parsedData) {
      logCacheOperation('GET', cacheKey, false, { reason: 'Invalid JSON' });
      // 删除无效缓存
      await redis.del(cacheKey);
      return null;
    }

    // 验证缓存数据结构
    if (!parsedData.audioUrl || !parsedData.generatedAt || !parsedData.fileSize) {
      logCacheOperation('GET', cacheKey, false, { reason: 'Missing required fields' });
      // 删除无效缓存
      await redis.del(cacheKey);
      return null;
    }

    // 检查缓存是否过期（双重保险）
    const config = getAudioConfig();
    const now = Date.now();
    const cacheAge = now - parsedData.lastChecked;
    const maxAge = config.cacheTTL * 1000; // 转换为毫秒

    if (cacheAge > maxAge) {
      logCacheOperation('GET', cacheKey, false, { reason: 'Cache expired', age: cacheAge, maxAge });
      await redis.del(cacheKey);
      return null;
    }

    logCacheOperation('GET', cacheKey, true, { fileSize: parsedData.fileSize });
    return parsedData;

  } catch (error) {
    const cacheKey = generateCacheKey(bookId, locale);
    logError('Failed to get cached audio metadata', {
      cacheKey,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

/**
 * 设置音频元数据缓存
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @param metadata 音频元数据
 * @returns Promise<boolean> 是否缓存成功
 */
export async function setCachedAudioMetadata(
  bookId: number,
  locale: string,
  metadata: AudioFileMetadata
): Promise<boolean> {
  try {
    // 验证输入数据
    if (!metadata.audioUrl || !metadata.fileSize || !metadata.generatedAt) {
      throw new Error('Invalid metadata: audioUrl, fileSize and generatedAt are required');
    }

    const redis = getRedisClient();
    const cacheKey = generateCacheKey(bookId, locale);
    const config = getAudioConfig();

    const cacheData: CachedAudioData = {
      ...metadata,
      bookId,
      locale,
      fileName: `book_${bookId}_${locale}_summary.mp3`,
      lastChecked: Date.now()
    };

    await redis.setex(cacheKey, config.cacheTTL, JSON.stringify(cacheData));

    logCacheOperation('SET', cacheKey, true, {
      fileSize: metadata.fileSize,
      ttl: config.cacheTTL
    });

    return true;

  } catch (error) {
    const cacheKey = generateCacheKey(bookId, locale);
    logError('Failed to cache audio metadata', {
      cacheKey,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

/**
 * 删除音频元数据缓存
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<boolean> 是否删除成功
 */
export async function deleteCachedAudioMetadata(bookId: number, locale: string): Promise<boolean> {
  try {
    const redis = getRedisClient();
    const cacheKey = generateCacheKey(bookId, locale);
    const result = await redis.del(cacheKey);

    const success = result > 0;
    logCacheOperation('DELETE', cacheKey, success);

    return success;

  } catch (error) {
    const cacheKey = generateCacheKey(bookId, locale);
    logError('Failed to delete cached audio metadata', {
      cacheKey,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

/**
 * 更新缓存的最后检查时间
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<boolean> 是否更新成功
 */
export async function updateCacheLastChecked(bookId: number, locale: string): Promise<boolean> {
  try {
    const cachedData = await getCachedAudioMetadata(bookId, locale);
    if (!cachedData) {
      return false;
    }

    cachedData.lastChecked = Date.now();
    return await setCachedAudioMetadata(bookId, locale, cachedData);

  } catch (error) {
    logError('Failed to update cache last checked time', {
      bookId,
      locale,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

/**
 * 清除所有音频缓存
 * @returns Promise<number> 删除的缓存数量
 */
export async function clearAllAudioCache(): Promise<number> {
  try {
    const redis = getRedisClient();
    const pattern = `${CACHE_PREFIX}*`;
    const keys = await redis.keys(pattern);

    if (keys.length === 0) {
      return 0;
    }

    const result = await redis.del(...keys);
    logCacheOperation('DELETE', `${keys.length} keys`, true, { pattern });

    return result;

  } catch (error) {
    logError('Failed to clear audio cache', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return 0;
  }
}

/**
 * 获取缓存统计信息
 * @returns Promise<{totalKeys: number, totalMemory: string}>
 */
export async function getCacheStats(): Promise<{
  totalKeys: number;
  totalMemory: string;
  sampleEntries: Array<{ key: string; size: number; age: number }>;
}> {
  try {
    const redis = getRedisClient();
    const pattern = `${CACHE_PREFIX}*`;
    const keys = await redis.keys(pattern);

    // 获取内存使用情况（近似值）
    let totalMemory = 0;
    const sampleEntries: Array<{ key: string; size: number; age: number }> = [];

    // 只采样前10个键来避免性能问题
    const sampleKeys = keys.slice(0, 10);

    for (const key of sampleKeys) {
      const value = await redis.get(key);
      if (value) {
        const size = Buffer.byteLength(value, 'utf8');
        totalMemory += size;

        // 尝试解析数据获取年龄
        const data = safeJsonParse<CachedAudioData | null>(value, null);
        const age = data ? Date.now() - data.lastChecked : 0;

        sampleEntries.push({
          key: key.replace(CACHE_PREFIX, ''),
          size,
          age
        });
      }
    }

    // 估算总内存使用（基于采样）
    const estimatedTotalMemory = keys.length > 0 ?
      (totalMemory / sampleKeys.length) * keys.length : 0;

    return {
      totalKeys: keys.length,
      totalMemory: `${(estimatedTotalMemory / 1024 / 1024).toFixed(2)} MB`,
      sampleEntries
    };

  } catch (error) {
    logError('Failed to get cache stats', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      totalKeys: 0,
      totalMemory: '0 MB',
      sampleEntries: []
    };
  }
}

/**
 * 检查Redis连接性
 * @returns Promise<boolean> 是否连接正常
 */
export async function checkCacheConnection(): Promise<boolean> {
  try {
    const redis = getRedisClient();
    const testKey = 'audio_cache_test';
    const testValue = 'test';

    await redis.set(testKey, testValue, 'EX', 10); // 10秒过期
    const result = await redis.get(testKey);
    await redis.del(testKey);

    return result === testValue;
  } catch (error) {
    logError('Cache connection check failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}
