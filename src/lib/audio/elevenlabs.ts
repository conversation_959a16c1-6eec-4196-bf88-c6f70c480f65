/**
 * ElevenLabs TTS API 封装
 * 负责与 ElevenLabs Text-to-Speech API 的交互
 */

import {
  TTSGenerationParams,
  ElevenLabsErrorResponse,
  VoiceIdMap
} from '@/types/audio.types';
import { logTTSGeneration, logError } from './logger';
import { calculateCharacterCount, getAudioConfig } from './file-utils';

// 声音ID映射配置
const VOICE_ID_MAP: VoiceIdMap = {
  'en': process.env.AUDIO_DEFAULT_VOICE_EN || '21m00Tcm4TlvDq8ikWAM',
  'zh': process.env.AUDIO_DEFAULT_VOICE_ZH || 'pNInz6obpgDQGcFmaJgB'
};

// ElevenLabs API 基础URL
const ELEVENLABS_API_BASE = 'https://api.elevenlabs.io/v1';

/**
 * 根据语言获取对应的声音ID
 * @param locale 语言代码
 * @returns 声音ID
 */
export function getVoiceIdByLocale(locale: string): string {
  return VOICE_ID_MAP[locale] || VOICE_ID_MAP['en'];
}

/**
 * 验证 ElevenLabs API Key 是否存在
 * @throws Error 如果API Key不存在
 */
function validateApiKey(): void {
  if (!process.env.ELEVENLABS_API_KEY) {
    throw new Error('ELEVENLABS_API_KEY environment variable is not set');
  }
}

/**
 * 创建TTS生成参数
 * @param text 文本内容
 * @param locale 语言代码
 * @returns TTS参数对象
 */
function createTTSParams(text: string, locale: string): TTSGenerationParams {
  return {
    text: text.trim(),
    voice_id: getVoiceIdByLocale(locale),
    model_id: process.env.ELEVENLABS_MODEL_ID || 'eleven_flash_v2_5', // 使用最新的Flash模型
    voice_settings: {
      stability: 0.5,
      similarity_boost: 0.75, // 提高相似度以保持音质
      style: 0.0,
      use_speaker_boost: true
    },
    // 添加音频格式优化参数
    output_format: process.env.ELEVENLABS_OUTPUT_FORMAT || 'mp3_44100_128' // 44.1kHz采样率，128kbps比特率
  };
}

/**
 * 调用ElevenLabs TTS API生成音频
 * @param text 文本内容
 * @param locale 语言代码
 * @returns Promise<Buffer> 音频数据Buffer
 * @throws Error 当API调用失败时
 */
export async function generateTTSAudio(
  text: string,
  locale: string
): Promise<Buffer> {
  validateApiKey();

  // 验证必要参数
  if (!text || text.trim().length === 0) {
    throw new Error('Text content is required and cannot be empty');
  }

  const config = getAudioConfig();
  const characterCount = calculateCharacterCount(text);

  // 验证文本长度
  if (characterCount < config.minTextLength) {
    throw new Error(`Text too short. Minimum ${config.minTextLength} characters required, got ${characterCount}`);
  }

  if (characterCount > config.maxTextLength) {
    throw new Error(`Text too long. Maximum ${config.maxTextLength} characters allowed, got ${characterCount}`);
  }

  const startTime = Date.now();
  const voiceId = getVoiceIdByLocale(locale);

  logTTSGeneration('START', {
    locale,
    characterCount,
    voiceId
  });

  try {
    // 创建TTS参数
    const ttsParams = createTTSParams(text, locale);

    // 调用 ElevenLabs TTS API
    const response = await fetch(`${ELEVENLABS_API_BASE}/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': process.env.ELEVENLABS_API_KEY!
      },
      body: JSON.stringify({
        text: ttsParams.text,
        model_id: ttsParams.model_id,
        voice_settings: ttsParams.voice_settings,
        output_format: ttsParams.output_format // 添加音频格式参数
      })
    });

    // 处理响应
    if (!response.ok) {
      let errorMessage = `ElevenLabs TTS API error: ${response.status}`;

      try {
        const errorData: ElevenLabsErrorResponse = await response.json();
        errorMessage += ` - ${errorData.detail?.message || 'Unknown error'}`;
      } catch {
        // 如果无法解析错误响应，使用默认错误信息
        const errorText = await response.text();
        errorMessage += ` - ${errorText || 'Unknown error'}`;
      }

      throw new Error(errorMessage);
    }

    // 获取音频数据
    const audioBuffer = Buffer.from(await response.arrayBuffer());

    // 验证音频数据
    if (!audioBuffer || audioBuffer.length === 0) {
      throw new Error('Received empty audio data from ElevenLabs API');
    }

    // 验证文件大小
    if (audioBuffer.length > config.maxFileSize) {
      throw new Error(`Generated audio file too large: ${audioBuffer.length} bytes (max: ${config.maxFileSize})`);
    }

    const responseTime = Date.now() - startTime;

    logTTSGeneration('SUCCESS', {
      locale,
      characterCount,
      responseTime,
      fileSize: audioBuffer.length
    });

    return audioBuffer;

  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logTTSGeneration('FAILED', {
      locale,
      characterCount,
      responseTime,
      error: errorMessage
    });

    // 重新抛出错误，保持错误链
    if (error instanceof Error) {
      throw new Error(`Failed to generate TTS audio: ${error.message}`);
    }
    throw new Error('Failed to generate TTS audio: Unknown error');
  }
}

/**
 * 估算音频文件大小（基于文本长度和音频格式）
 * @param text 文本内容
 * @param locale 语言代码
 * @param outputFormat 音频输出格式
 * @returns 估算的文件大小（字节）
 */
export function estimateAudioFileSize(text: string, locale: string, outputFormat: string = 'mp3_44100_128'): number {
  const duration = estimateAudioDuration(text, locale);

  // 根据音频格式估算文件大小
  let bitrate = 128; // 默认128kbps

  // 解析输出格式中的比特率
  const bitrateMatch = outputFormat.match(/_(\d+)$/);
  if (bitrateMatch) {
    bitrate = parseInt(bitrateMatch[1]);
  }

  // 计算文件大小：时长(秒) * 比特率(kbps) * 1000 / 8 = 字节数
  const estimatedBytes = Math.ceil(duration * bitrate * 1000 / 8);

  return estimatedBytes;
}

/**
 * 验证文本是否适合音频生成（基于预估文件大小）
 * @param text 文本内容
 * @param locale 语言代码
 * @param maxFileSize 最大文件大小限制（字节）
 * @returns 验证结果
 */
export function validateTextForAudioGeneration(text: string, locale: string, maxFileSize: number): {
  valid: boolean;
  estimatedSize: number;
  estimatedDuration: number;
  error?: string;
} {
  const estimatedDuration = estimateAudioDuration(text, locale);
  const estimatedSize = estimateAudioFileSize(text, locale);

  if (estimatedSize > maxFileSize) {
    return {
      valid: false,
      estimatedSize,
      estimatedDuration,
      error: `Estimated audio file size (${Math.round(estimatedSize / 1024 / 1024 * 100) / 100}MB) exceeds limit (${Math.round(maxFileSize / 1024 / 1024 * 100) / 100}MB). Consider reducing text length.`
    };
  }

  return {
    valid: true,
    estimatedSize,
    estimatedDuration
  };
}

/**
 * 估算音频时长（基于文本长度的粗略估算）
 * @param text 文本内容
 * @param locale 语言代码
 * @returns 估算的音频时长（秒）
 */
export function estimateAudioDuration(text: string, locale: string): number {
  const characterCount = calculateCharacterCount(text);

  // 基于语言的不同阅读速度估算
  // 英文：约150-200 words per minute，平均5字符/单词
  // 中文：约200-300字符 per minute
  const charsPerMinute = locale === 'zh' ? 250 : 750; // 中文250字符/分钟，英文750字符/分钟

  const estimatedMinutes = characterCount / charsPerMinute;
  const estimatedSeconds = Math.ceil(estimatedMinutes * 60);

  // 最少10秒，最多30分钟
  return Math.max(10, Math.min(estimatedSeconds, 1800));
}

/**
 * 验证声音ID是否有效
 * @param voiceId 声音ID
 * @returns 是否有效
 */
export function isValidVoiceId(voiceId: string): boolean {
  return Object.values(VOICE_ID_MAP).includes(voiceId);
}

/**
 * 获取所有支持的语言和对应的声音ID
 * @returns 语言-声音ID映射
 */
export function getSupportedVoices(): VoiceIdMap {
  return { ...VOICE_ID_MAP };
}

/**
 * 检查ElevenLabs API连接性
 * @returns Promise<boolean> 是否连接正常
 */
export async function checkElevenLabsConnection(): Promise<boolean> {
  try {
    validateApiKey();

    const response = await fetch(`${ELEVENLABS_API_BASE}/voices`, {
      method: 'GET',
      headers: {
        'xi-api-key': process.env.ELEVENLABS_API_KEY!
      }
    });

    return response.ok;
  } catch (error) {
    logError('ElevenLabs connection check failed', { error: error instanceof Error ? error.message : 'Unknown error' });
    return false;
  }
}
