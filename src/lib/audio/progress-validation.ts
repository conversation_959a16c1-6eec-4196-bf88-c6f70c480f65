/**
 * 播放进度数据验证工具
 */

import { z } from 'zod';
import { PlaybackProgressData, PlaybackProgressResponse } from '@/types/audio-progress.types';

// 播放进度数据验证Schema
const PlaybackProgressSchema = z.object({
  bookId: z.number().int().positive('Book ID must be a positive integer'),
  currentTime: z.number().min(0, 'Current time cannot be negative'),
  duration: z.number().min(0, 'Duration cannot be negative'),
  progressPercentage: z.number().min(0).max(100, 'Progress must be between 0 and 100'),
  playbackRate: z.number().min(0.5).max(2.0).optional(),
  volume: z.number().min(0).max(1).optional()
});

/**
 * 验证播放进度数据
 */
export function validateProgressData(data: any): PlaybackProgressData {
  try {
    return PlaybackProgressSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => err.message).join(', ');
      throw new Error(`Validation failed: ${errorMessages}`);
    }
    throw error;
  }
}

/**
 * 验证播放时间的合理性
 */
export function validatePlaybackTime(currentTime: number, duration: number): boolean {
  if (currentTime > duration) return false;
  if (currentTime < 0 || duration < 0) return false;
  return true;
}

/**
 * 计算播放进度百分比
 */
export function calculateProgressPercentage(currentTime: number, duration: number): number {
  if (duration <= 0) return 0;
  return Math.min(100, Math.max(0, (currentTime / duration) * 100));
}

/**
 * 格式化播放时间显示
 */
export function formatPlaybackTime(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 数据库模型适配器
 */
export function adaptProgressFromDatabase(dbProgress: any): PlaybackProgressResponse {
  return {
    id: dbProgress.id,
    userId: dbProgress.user_id.toString(), // BigInt转string
    bookId: dbProgress.book_id,
    currentTime: Number(dbProgress.position_seconds),
    duration: dbProgress.duration_seconds ? Number(dbProgress.duration_seconds) : null,
    progressPercentage: dbProgress.progress_percentage ? Number(dbProgress.progress_percentage) : null,
    playbackRate: Number(dbProgress.playback_rate),
    volume: Number(dbProgress.volume),
    isCompleted: dbProgress.is_completed,
    lastListenedAt: dbProgress.last_listened_at.toISOString(),
    createdAt: dbProgress.created_at.toISOString(),
    updatedAt: dbProgress.updated_at.toISOString(),
    book: dbProgress.book
  };
}
