/**
 * 音频文件检查模块
 * 负责缓存优先的文件存在性检查逻辑
 */

import { FileExistsResult } from '@/types/audio.types';
import { getCachedAudioMetadata, setCachedAudioMetadata } from './cache';
import { AudioS3Manager } from './s3';
import { generateCacheKey } from './file-utils';
import { logCacheOperation } from './logger';

// 扩展FileExistsResult以包含缓存来源信息
export interface EnhancedFileExistsResult extends FileExistsResult {
  fromCache?: boolean;
  cacheAge?: number;
}

/**
 * 缓存优先的文件存在性检查
 * 先检查Redis缓存，缓存未命中时再检查S3
 * 
 * @param s3Manager S3管理器实例
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<EnhancedFileExistsResult> 文件检查结果
 */
export async function checkAudioFileWithCache(
  s3Manager: AudioS3Manager,
  bookId: number,
  locale: string
): Promise<EnhancedFileExistsResult> {
  const cacheKey = generateCacheKey(bookId, locale);
  
  try {
    // 1. 先检查Redis缓存
    const cacheResult = await checkRedisCache(bookId, locale);
    
    if (cacheResult.found) {
      logCacheOperation('HIT', cacheKey, true, {
        fileSize: cacheResult.data!.fileSize,
        cacheAge: cacheResult.cacheAge
      });
      
      return {
        exists: true,
        url: cacheResult.data!.audioUrl,
        fileSize: cacheResult.data!.fileSize,
        lastModified: cacheResult.data!.generatedAt,
        fromCache: true,
        cacheAge: cacheResult.cacheAge
      };
    }
    
    // 2. 缓存未命中，检查S3
    logCacheOperation('MISS', cacheKey, false);
    
    const s3Result = await checkS3AndUpdateCache(s3Manager, bookId, locale);
    
    return {
      ...s3Result,
      fromCache: false
    };
    
  } catch (error) {
    // 缓存检查失败，降级到直接S3检查
    console.warn(`[AUDIO] Cache check failed for ${cacheKey}, falling back to S3:`, error);
    
    const s3Result = await s3Manager.checkFileExists(bookId, locale);
    return {
      ...s3Result,
      fromCache: false
    };
  }
}

/**
 * 检查Redis缓存
 * 
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<CacheCheckResult> 缓存检查结果
 */
async function checkRedisCache(
  bookId: number,
  locale: string
): Promise<CacheCheckResult> {
  try {
    const cachedData = await getCachedAudioMetadata(bookId, locale);
    
    if (!cachedData || !cachedData.audioUrl) {
      return { found: false };
    }
    
    // 验证缓存数据完整性
    if (!isValidCacheData(cachedData)) {
      console.warn(`[AUDIO] Invalid cache data for ${generateCacheKey(bookId, locale)}, ignoring cache`);
      return { found: false };
    }
    
    const cacheAge = Date.now() - cachedData.lastChecked;
    
    return {
      found: true,
      data: cachedData,
      cacheAge
    };
    
  } catch (error) {
    console.warn(`[AUDIO] Redis cache check failed for ${generateCacheKey(bookId, locale)}:`, error);
    return { found: false };
  }
}

/**
 * 检查S3并更新缓存
 * 
 * @param s3Manager S3管理器实例
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<FileExistsResult> S3检查结果
 */
async function checkS3AndUpdateCache(
  s3Manager: AudioS3Manager,
  bookId: number,
  locale: string
): Promise<FileExistsResult> {
  const s3Result = await s3Manager.checkFileExists(bookId, locale);
  
  // 如果S3文件存在，更新Redis缓存
  if (s3Result.exists && s3Result.url && s3Result.fileSize) {
    try {
      await setCachedAudioMetadata(bookId, locale, {
        audioUrl: s3Result.url,
        fileSize: s3Result.fileSize,
        generatedAt: s3Result.lastModified || new Date().toISOString(),
        lastChecked: Date.now()
      });
      
      logCacheOperation('UPDATE', generateCacheKey(bookId, locale), true, {
        fileSize: s3Result.fileSize,
        source: 'S3_CHECK'
      });
      
    } catch (cacheError) {
      // 缓存更新失败不影响主流程
      console.warn(`[AUDIO] Failed to update cache after S3 check:`, cacheError);
    }
  }
  
  return s3Result;
}

/**
 * 验证缓存数据的完整性
 * 
 * @param cachedData 缓存数据
 * @returns boolean 是否有效
 */
function isValidCacheData(cachedData: any): boolean {
  return !!(
    cachedData &&
    cachedData.audioUrl &&
    cachedData.fileSize &&
    cachedData.generatedAt &&
    cachedData.lastChecked &&
    typeof cachedData.fileSize === 'number' &&
    cachedData.fileSize > 0
  );
}

/**
 * 缓存检查结果接口
 */
interface CacheCheckResult {
  found: boolean;
  data?: any;
  cacheAge?: number;
}

/**
 * 获取缓存统计信息（用于监控）
 * 
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<CacheStats> 缓存统计
 */
export async function getCacheStats(
  bookId: number,
  locale: string
): Promise<CacheStats> {
  try {
    const cachedData = await getCachedAudioMetadata(bookId, locale);
    
    if (!cachedData) {
      return {
        exists: false,
        age: 0,
        size: 0
      };
    }
    
    return {
      exists: true,
      age: Date.now() - cachedData.lastChecked,
      size: cachedData.fileSize || 0,
      url: cachedData.audioUrl
    };
    
  } catch (error) {
    return {
      exists: false,
      age: 0,
      size: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 缓存统计信息接口
 */
export interface CacheStats {
  exists: boolean;
  age: number;
  size: number;
  url?: string;
  error?: string;
}

/**
 * 强制刷新缓存
 * 删除现有缓存并重新从S3检查
 * 
 * @param s3Manager S3管理器实例
 * @param bookId 书籍ID
 * @param locale 语言代码
 * @returns Promise<EnhancedFileExistsResult> 刷新后的结果
 */
export async function refreshCache(
  s3Manager: AudioS3Manager,
  bookId: number,
  locale: string
): Promise<EnhancedFileExistsResult> {
  const cacheKey = generateCacheKey(bookId, locale);
  
  try {
    // 删除现有缓存
    const { deleteCachedAudioMetadata } = await import('./cache');
    await deleteCachedAudioMetadata(bookId, locale);
    
    logCacheOperation('REFRESH', cacheKey, true);
    
    // 重新检查S3并更新缓存
    const s3Result = await checkS3AndUpdateCache(s3Manager, bookId, locale);
    
    return {
      ...s3Result,
      fromCache: false
    };
    
  } catch (error) {
    console.error(`[AUDIO] Failed to refresh cache for ${cacheKey}:`, error);
    
    // 降级到直接S3检查
    const s3Result = await s3Manager.checkFileExists(bookId, locale);
    return {
      ...s3Result,
      fromCache: false
    };
  }
}
