/**
 * 批量音频处理模块
 * 负责并发/串行处理多个文本分段的TTS生成
 */

import { generateTTSAudio } from './elevenlabs';
import { TextSegment } from './segmentation';
import { AudioSegment } from './merger';
import { logTTSGeneration } from './logger';

// 批量处理配置
export interface BatchProcessConfig {
  concurrency: number;        // 并发数量
  retryAttempts: number;      // 重试次数
  retryDelay: number;         // 重试延迟（毫秒）
  failureThreshold: number;   // 失败阈值（0-1）
  timeoutMs: number;          // 单个请求超时时间
}

// 处理结果
export interface BatchProcessResult {
  success: boolean;
  audioSegments: AudioSegment[];
  failedSegments: number[];
  totalProcessingTime: number;
  errors: Array<{ index: number; error: string }>;
}

// 单个分段处理结果
interface SegmentProcessResult {
  index: number;
  success: boolean;
  audioSegment?: AudioSegment;
  error?: string;
  processingTime: number;
}

// 默认配置
const DEFAULT_CONFIG: BatchProcessConfig = {
  concurrency: 3,
  retryAttempts: 3,
  retryDelay: 2000,
  failureThreshold: 0.3, // 30%失败率则整体失败
  timeoutMs: 60000 // 60秒超时
};

/**
 * 批量处理文本分段生成音频
 * @param segments 文本分段数组
 * @param locale 语言代码
 * @param config 处理配置
 * @returns 批量处理结果
 */
export async function processBatchSegments(
  segments: TextSegment[],
  locale: string,
  config: BatchProcessConfig = DEFAULT_CONFIG
): Promise<BatchProcessResult> {
  const startTime = Date.now();
  
  try {
    // 1. 验证输入
    if (!segments || segments.length === 0) {
      throw new Error('No segments provided for processing');
    }
    
    // 2. 选择处理策略
    const results = segments.length <= 3 
      ? await processSequentially(segments, locale, config)
      : await processConcurrently(segments, locale, config);
    
    // 3. 分析结果
    const successfulResults = results.filter(r => r.success);
    const failedResults = results.filter(r => !r.success);
    
    // 4. 检查失败率
    const failureRate = failedResults.length / results.length;
    if (failureRate > config.failureThreshold) {
      throw new Error(
        `Batch processing failed: ${failedResults.length}/${results.length} segments failed (${Math.round(failureRate * 100)}% failure rate)`
      );
    }
    
    // 5. 构建音频分段数组
    const audioSegments: AudioSegment[] = successfulResults
      .map(result => result.audioSegment!)
      .sort((a, b) => a.index - b.index);
    
    const totalProcessingTime = Date.now() - startTime;
    
    return {
      success: true,
      audioSegments,
      failedSegments: failedResults.map(r => r.index),
      totalProcessingTime,
      errors: failedResults.map(r => ({ index: r.index, error: r.error || 'Unknown error' }))
    };
    
  } catch (error) {
    const totalProcessingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown batch processing error';
    
    return {
      success: false,
      audioSegments: [],
      failedSegments: segments.map((_, index) => index),
      totalProcessingTime,
      errors: [{ index: -1, error: errorMessage }]
    };
  }
}

/**
 * 串行处理分段
 */
async function processSequentially(
  segments: TextSegment[],
  locale: string,
  config: BatchProcessConfig
): Promise<SegmentProcessResult[]> {
  const results: SegmentProcessResult[] = [];
  
  for (const segment of segments) {
    const result = await processSegmentWithRetry(segment, locale, config);
    results.push(result);
    
    // 添加延迟避免API限流
    if (result.success && segment.index < segments.length - 1) {
      await delay(500); // 0.5秒延迟
    }
  }
  
  return results;
}

/**
 * 并发处理分段
 */
async function processConcurrently(
  segments: TextSegment[],
  locale: string,
  config: BatchProcessConfig
): Promise<SegmentProcessResult[]> {
  // 使用信号量控制并发数量
  const semaphore = new Semaphore(config.concurrency);
  
  const promises = segments.map(async (segment) => {
    await semaphore.acquire();
    try {
      return await processSegmentWithRetry(segment, locale, config);
    } finally {
      semaphore.release();
    }
  });
  
  return Promise.all(promises);
}

/**
 * 处理单个分段（带重试）
 */
async function processSegmentWithRetry(
  segment: TextSegment,
  locale: string,
  config: BatchProcessConfig
): Promise<SegmentProcessResult> {
  const startTime = Date.now();
  
  for (let attempt = 1; attempt <= config.retryAttempts; attempt++) {
    try {
      // 添加超时控制
      const audioBuffer = await Promise.race([
        generateTTSAudio(segment.text, locale),
        timeoutPromise(config.timeoutMs)
      ]);
      
      const processingTime = Date.now() - startTime;
      
      const audioSegment: AudioSegment = {
        index: segment.index,
        buffer: audioBuffer,
        duration: segment.estimatedDuration,
        fileSize: audioBuffer.length
      };
      
      logTTSGeneration('SUCCESS', {
        locale,
        characterCount: segment.text.length,
        responseTime: processingTime,
        fileSize: audioBuffer.length,
        segmentIndex: segment.index
      });
      
      return {
        index: segment.index,
        success: true,
        audioSegment,
        processingTime
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      logTTSGeneration('FAILED', {
        locale,
        characterCount: segment.text.length,
        responseTime: Date.now() - startTime,
        error: errorMessage,
        segmentIndex: segment.index,
        attempt
      });
      
      // 最后一次尝试失败
      if (attempt === config.retryAttempts) {
        return {
          index: segment.index,
          success: false,
          error: `Failed after ${config.retryAttempts} attempts: ${errorMessage}`,
          processingTime: Date.now() - startTime
        };
      }
      
      // 等待后重试
      await delay(config.retryDelay * attempt); // 指数退避
    }
  }
  
  // 不应该到达这里
  return {
    index: segment.index,
    success: false,
    error: 'Unexpected error in retry logic',
    processingTime: Date.now() - startTime
  };
}

/**
 * 简单的信号量实现
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];
  
  constructor(permits: number) {
    this.permits = permits;
  }
  
  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return;
    }
    
    return new Promise<void>((resolve) => {
      this.waitQueue.push(resolve);
    });
  }
  
  release(): void {
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!;
      resolve();
    } else {
      this.permits++;
    }
  }
}

/**
 * 超时Promise
 */
function timeoutPromise(timeoutMs: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Operation timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });
}

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 获取默认批量处理配置
 */
export function getDefaultBatchConfig(): BatchProcessConfig {
  return { ...DEFAULT_CONFIG };
}

/**
 * 验证批量处理配置
 */
export function validateBatchConfig(config: BatchProcessConfig): boolean {
  return (
    config.concurrency > 0 &&
    config.retryAttempts >= 0 &&
    config.retryDelay >= 0 &&
    config.failureThreshold >= 0 && config.failureThreshold <= 1 &&
    config.timeoutMs > 0
  );
}
