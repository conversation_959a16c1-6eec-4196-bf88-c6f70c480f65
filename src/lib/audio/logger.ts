/**
 * 音频功能专用日志记录器
 * 提供结构化的日志记录功能
 */

interface AudioLogData {
  bookId?: number;
  locale?: string;
  cacheKey?: string;
  responseTime?: number;
  characterCount?: number;
  fileSize?: number;
  cached?: boolean;
  error?: string;
  lockKey?: string;
  waitTime?: number;
  s3Key?: string;
  audioUrl?: string;
  [key: string]: any;
}

/**
 * 记录音频请求开始
 */
export function logAudioRequestStart(bookId: number, locale: string): void {
  console.log(`[AUDIO] Request started - BookID: ${bookId}, Locale: ${locale}`);
}

/**
 * 记录S3文件检查
 */
export function logS3FileCheck(s3Key: string, exists: boolean, responseTime?: number): void {
  const status = exists ? 'EXISTS' : 'NOT_EXISTS';
  const timeStr = responseTime ? `, ResponseTime: ${responseTime}ms` : '';
  console.log(`[AUDIO] S3 file check - Key: ${s3Key}, Status: ${status}${timeStr}`);
}

/**
 * 记录缓存操作
 * @param operation 操作类型
 * @param cacheKey 缓存键
 * @param success 操作是否成功
 * @param metadata 额外元数据
 */
export function logCacheOperation(
  operation: 'HIT' | 'MISS' | 'UPDATE' | 'REFRESH' | 'DELETE' | 'GET' | 'SET',
  cacheKey: string,
  success: boolean,
  metadata?: {
    fileSize?: number;
    cacheAge?: number;
    source?: string;
    error?: string;
    data?: any;
    reason?: string;
    age?: number;
    maxAge?: number;
    ttl?: number;
    pattern?: string;
  }
): void {
  const status = success ? 'SUCCESS' : 'FAILED';
  let logMessage = `[AUDIO] Cache ${operation} ${status} - Key: ${cacheKey}`;

  if (metadata) {
    const details: string[] = [];

    if (metadata.fileSize) {
      details.push(`Size: ${Math.round(metadata.fileSize / 1024 / 1024 * 100) / 100}MB`);
    }

    if (metadata.cacheAge !== undefined) {
      const ageMinutes = Math.round(metadata.cacheAge / 1000 / 60);
      details.push(`Age: ${ageMinutes}min`);
    }

    if (metadata.age !== undefined) {
      const ageMinutes = Math.round(metadata.age / 1000 / 60);
      details.push(`Age: ${ageMinutes}min`);
    }

    if (metadata.maxAge !== undefined) {
      const maxAgeMinutes = Math.round(metadata.maxAge / 1000 / 60);
      details.push(`MaxAge: ${maxAgeMinutes}min`);
    }

    if (metadata.ttl !== undefined) {
      details.push(`TTL: ${metadata.ttl}s`);
    }

    if (metadata.source) {
      details.push(`Source: ${metadata.source}`);
    }

    if (metadata.reason) {
      details.push(`Reason: ${metadata.reason}`);
    }

    if (metadata.error) {
      details.push(`Error: ${metadata.error}`);
    }

    if (metadata.pattern) {
      details.push(`Pattern: ${metadata.pattern}`);
    }

    if (metadata.data) {
      details.push(`Data: ${JSON.stringify(metadata.data)}`);
    }

    if (details.length > 0) {
      logMessage += `, Details: {${details.join(', ')}}`;
    }
  }

  console.log(logMessage);
}

/**
 * 记录分布式锁操作
 */
export function logLockOperation(operation: 'ACQUIRE' | 'RELEASE' | 'WAIT' | 'CLEANUP', lockKey: string, success: boolean, details?: any): void {
  const status = success ? 'SUCCESS' : 'FAILED';
  const detailsStr = details ? `, Details: ${JSON.stringify(details)}` : '';
  console.log(`[AUDIO] Lock ${operation} ${status} - Key: ${lockKey}${detailsStr}`);
}

/**
 * 记录TTS生成过程
 */
export function logTTSGeneration(stage: 'START' | 'SUCCESS' | 'FAILED', data: AudioLogData): void {
  const { bookId, locale, characterCount, responseTime, error } = data;

  if (stage === 'START') {
    console.log(`[AUDIO] TTS generation START - BookID: ${bookId}, Locale: ${locale}, Characters: ${characterCount}`);
  } else if (stage === 'SUCCESS') {
    console.log(`[AUDIO] TTS generation SUCCESS - BookID: ${bookId}, Locale: ${locale}, ResponseTime: ${responseTime}ms`);
  } else {
    console.error(`[AUDIO] TTS generation FAILED - BookID: ${bookId}, Locale: ${locale}, Error: ${error}`);
  }
}

/**
 * 记录S3上传过程
 */
export function logS3Upload(stage: 'START' | 'SUCCESS' | 'FAILED', data: AudioLogData): void {
  const { s3Key, fileSize, responseTime, error } = data;

  if (stage === 'START') {
    console.log(`[AUDIO] S3 upload START - Key: ${s3Key}, FileSize: ${fileSize} bytes`);
  } else if (stage === 'SUCCESS') {
    console.log(`[AUDIO] S3 upload SUCCESS - Key: ${s3Key}, ResponseTime: ${responseTime}ms`);
  } else {
    console.error(`[AUDIO] S3 upload FAILED - Key: ${s3Key}, Error: ${error}`);
  }
}

/**
 * 记录音频生成完整流程
 */
export function logAudioGenerationComplete(data: AudioLogData): void {
  const { bookId, locale, responseTime, fileSize, cached, waitedForGeneration } = data;
  const cacheStatus = cached ? 'CACHED' : 'GENERATED';
  const waitStatus = waitedForGeneration ? ', WAITED' : '';

  console.log(`[AUDIO] Generation COMPLETE - BookID: ${bookId}, Locale: ${locale}, ` +
    `Status: ${cacheStatus}${waitStatus}, ResponseTime: ${responseTime}ms, FileSize: ${fileSize} bytes`);
}

/**
 * 记录并发处理
 */
export function logConcurrencyEvent(event: 'LOCK_ACQUIRED' | 'LOCK_FAILED' | 'WAITING_START' | 'WAITING_SUCCESS' | 'WAITING_TIMEOUT', data: AudioLogData): void {
  const { bookId, locale, lockKey, waitTime } = data;

  switch (event) {
    case 'LOCK_ACQUIRED':
      console.log(`[AUDIO] Concurrency - LOCK_ACQUIRED: ${lockKey} for BookID: ${bookId}, Locale: ${locale}`);
      break;
    case 'LOCK_FAILED':
      console.log(`[AUDIO] Concurrency - LOCK_FAILED: ${lockKey} for BookID: ${bookId}, Locale: ${locale}`);
      break;
    case 'WAITING_START':
      console.log(`[AUDIO] Concurrency - WAITING_START for BookID: ${bookId}, Locale: ${locale}`);
      break;
    case 'WAITING_SUCCESS':
      console.log(`[AUDIO] Concurrency - WAITING_SUCCESS for BookID: ${bookId}, Locale: ${locale}, WaitTime: ${waitTime}ms`);
      break;
    case 'WAITING_TIMEOUT':
      console.warn(`[AUDIO] Concurrency - WAITING_TIMEOUT for BookID: ${bookId}, Locale: ${locale}, WaitTime: ${waitTime}ms`);
      break;
  }
}

/**
 * 记录参数验证错误
 */
export function logValidationError(field: string, value: any, reason: string): void {
  console.warn(`[AUDIO] Validation ERROR - Field: ${field}, Value: ${value}, Reason: ${reason}`);
}

/**
 * 记录性能指标
 */
export function logPerformanceMetrics(data: {
  operation: string;
  duration: number;
  success: boolean;
  metadata?: Record<string, any>;
}): void {
  const { operation, duration, success, metadata = {} } = data;
  const status = success ? 'SUCCESS' : 'FAILED';
  const metadataStr = Object.keys(metadata).length > 0 ?
    `, Metadata: ${JSON.stringify(metadata)}` : '';

  console.log(`[AUDIO] Performance - Operation: ${operation}, ` +
    `Duration: ${duration}ms, Status: ${status}${metadataStr}`);
}

/**
 * 记录章节处理过程
 */
export function logChapterProcessing(data: {
  bookId: number;
  locale: string;
  totalChapters: number;
  validChapters: number;
  finalTextLength: number;
  averageChapterLength?: number;
}): void {
  const { bookId, locale, totalChapters, validChapters, finalTextLength, averageChapterLength } = data;

  console.log(`[AUDIO] Chapter processing - BookID: ${bookId}, Locale: ${locale}, ` +
    `Chapters: ${validChapters}/${totalChapters}, FinalLength: ${finalTextLength}` +
    `${averageChapterLength ? `, AvgLength: ${averageChapterLength}` : ''}`);
}

/**
 * 记录错误详情
 */
export function logError(error: Error | string, context?: AudioLogData): void {
  const errorMessage = error instanceof Error ? error.message : error;
  const contextStr = context ? `, Context: ${JSON.stringify(context)}` : '';

  console.error(`[AUDIO] ERROR - ${errorMessage}${contextStr}`);

  if (error instanceof Error && error.stack) {
    console.error(`[AUDIO] ERROR Stack:`, error.stack);
  }
}


