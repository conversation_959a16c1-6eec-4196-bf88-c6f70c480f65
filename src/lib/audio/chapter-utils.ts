/**
 * 章节内容处理工具函数
 * 用于处理book_chapters.content字段的JSON数据
 */

export interface ChapterData {
  id: string
  title: string
  summary: string
}

// 导入Chapter类型定义
export interface Chapter {
  id: string
  number: number
  title: string
  summary: string
}

/**
 * 解析单个章节内容
 * @param chapterContent JSON字符串格式的章节内容
 * @returns 解析后的章节数据或null
 */
export function parseChapterContent(chapterContent: string): ChapterData | null {
  try {
    const parsed = JSON.parse(chapterContent)

    // 验证必需字段
    if (!parsed.title || !parsed.summary) {
      console.warn('Chapter content missing title or summary:', parsed)
      return null
    }

    return {
      id: parsed.id || '',
      title: String(parsed.title).trim(),
      summary: String(parsed.summary).trim()
    }
  } catch (error) {
    console.error('Failed to parse chapter content:', error)
    return null
  }
}

/**
 * 组装章节文本 - 标准格式 (title + \n + summary + \n\n)
 * @param chapters 章节内容数组
 * @returns 组装后的完整文本
 */
export function assembleChapterText(chapters: string[]): string {
  if (!chapters || chapters.length === 0) {
    throw new Error('No chapters available for audio generation')
  }

  let fullText = ""
  let validChapterCount = 0

  for (const chapterContent of chapters) {
    const chapterData = parseChapterContent(chapterContent)

    if (!chapterData) {
      continue // 跳过无效章节
    }

    // 格式: title + \n + summary + \n\n
    fullText += `${chapterData.title}\n${chapterData.summary}\n\n`
    validChapterCount++
  }

  if (validChapterCount === 0) {
    throw new Error('No valid chapters found for audio generation')
  }

  console.log(`[AUDIO] Assembled text from ${validChapterCount} chapters, total length: ${fullText.length} characters`)

  return fullText.trim()
}

/**
 * 组装章节文本 - 从Chapter对象数组 (title + \n + summary + \n\n)
 * @param chapters Chapter对象数组
 * @returns 组装后的完整文本
 */
export function assembleChapterTextFromObjects(chapters: Chapter[]): string {
  if (!chapters || chapters.length === 0) {
    throw new Error('No chapters available for audio generation')
  }

  let fullText = ""
  let validChapterCount = 0

  for (const chapter of chapters) {
    // 验证章节数据
    if (!chapter.title || !chapter.summary) {
      console.warn('Chapter missing title or summary:', chapter)
      continue // 跳过无效章节
    }

    // 格式: title + \n + summary + \n\n
    fullText += `${chapter.title.trim()}\n${chapter.summary.trim()}\n\n`
    validChapterCount++
  }

  if (validChapterCount === 0) {
    throw new Error('No valid chapters found for audio generation')
  }

  console.log(`[AUDIO] Assembled text from ${validChapterCount} chapters, total length: ${fullText.length} characters`)

  return fullText.trim()
}

/**
 * 获取章节统计信息
 * @param chapters 章节内容数组
 * @returns 统计信息
 */
export function getChapterStats(chapters: string[]): {
  totalChapters: number
  validChapters: number
  totalCharacters: number
  averageChapterLength: number
} {
  let validChapters = 0
  let totalCharacters = 0

  for (const chapterContent of chapters) {
    const chapterData = parseChapterContent(chapterContent)
    if (chapterData) {
      validChapters++
      const chapterTextLength = chapterData.title.length + chapterData.summary.length + 3 // +3 for \n + \n\n
      totalCharacters += chapterTextLength
    }
  }

  return {
    totalChapters: chapters.length,
    validChapters,
    totalCharacters,
    averageChapterLength: validChapters > 0 ? Math.round(totalCharacters / validChapters) : 0
  }
}

/**
 * 获取Chapter对象数组的统计信息
 * @param chapters Chapter对象数组
 * @returns 统计信息
 */
export function getChapterStatsFromObjects(chapters: Chapter[]): {
  totalChapters: number
  validChapters: number
  totalCharacters: number
  averageChapterLength: number
} {
  let validChapters = 0
  let totalCharacters = 0

  for (const chapter of chapters) {
    if (chapter.title && chapter.summary) {
      validChapters++
      const chapterTextLength = chapter.title.length + chapter.summary.length + 3 // +3 for \n + \n\n
      totalCharacters += chapterTextLength
    }
  }

  return {
    totalChapters: chapters.length,
    validChapters,
    totalCharacters,
    averageChapterLength: validChapters > 0 ? Math.round(totalCharacters / validChapters) : 0
  }
}

/**
 * 验证章节数据是否有效
 * @param chapters 章节内容数组
 * @returns 是否有有效章节
 */
export function hasValidChapters(chapters: string[]): boolean {
  if (!chapters || chapters.length === 0) {
    return false
  }

  return chapters.some(chapterContent => {
    const chapterData = parseChapterContent(chapterContent)
    return chapterData !== null
  })
}

/**
 * 验证Chapter对象数组是否有效
 * @param chapters Chapter对象数组
 * @returns 是否有有效章节
 */
export function hasValidChapterObjects(chapters: Chapter[]): boolean {
  if (!chapters || chapters.length === 0) {
    return false
  }

  return chapters.some(chapter =>
    chapter.title && chapter.summary
  )
}
