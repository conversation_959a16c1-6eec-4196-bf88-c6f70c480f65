import { ErrorLogSchema } from '@/models/ErrorLog'
import { cookies, headers } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import reportError from '@/services/server/errorService'
import { verifyJwtToken } from '@/services/actions/authServer'
import { ResponseCode } from '@/utils/constants'
import { errorResponse } from '@/services/response'
import { routing } from '@/i18n/routing'
import { getTranslations } from 'next-intl/server'

type ApiHandler = (
  request: NextRequest,
  context: RequestContext
) => Promise<NextResponse | Response>

// Next.js 15兼容的动态路由处理器类型
type DynamicRouteHandler<T = Record<string, string>> = (
  request: NextRequest,
  context: { params: Promise<T> }
) => Promise<NextResponse | Response>

// 适配器函数：将Next.js 15动态路由转换为withRoute兼容格式
export function adaptDynamicRoute<T = Record<string, string>>(
  ...middlewares: Middleware[]
) {
  return function (handler: (request: NextRequest, context: RequestContext, routeParams: T) => Promise<NextResponse | Response>): DynamicRouteHandler<T> {
    return async (request: NextRequest, context: { params: Promise<T> }) => {
      // 解析动态路由参数
      const routeParams = await context.params

      // 创建适配的处理器
      const adaptedHandler: ApiHandler = async (req: NextRequest, ctx: RequestContext) => {
        return handler(req, ctx, routeParams)
      }

      // 应用中间件
      const wrappedHandler = [i18nMiddleware, ...middlewares, errorMiddleware].reduceRight(
        (prev, middleware) => middleware(prev),
        adaptedHandler
      )

      // 创建初始context
      const requestContext: RequestContext = {
        params: {},
        t: (key: string) => key // 会被i18nMiddleware覆盖
      }

      return wrappedHandler(request, requestContext)
    }
  }
}

// 中间件类型
type Middleware = (handler: ApiHandler) => ApiHandler

// 柯里化的withRoute函数
export default function withRoute(...middlewares: Middleware[]) {
  return function (handler: ApiHandler) {
    // 组合所有中间件和错误处理
    // 使用reduceRight确保中间件按照从左到右的顺序执行
    const wrappedHandler = [i18nMiddleware, ...middlewares, errorMiddleware].reduceRight(
      (prev, middleware) => middleware(prev),
      handler
    )

    return wrappedHandler
  }
}

/**
 * 国际化中间件
 * 获取cookie中的locale，并添加到context中
 */
const i18nMiddleware: Middleware = (handler: ApiHandler) => {
  return async (request: NextRequest, context: RequestContext) => {
    const cookieStore = await cookies()
    const locale = cookieStore.get('NEXT_LOCALE')?.value ?? routing.defaultLocale

    // 尝试获取API命名空间的翻译，如果失败则使用默认翻译函数
    let t: (key: string, values?: Record<string, string>) => string
    try {
      t = await getTranslations({ locale, namespace: 'API' })
    } catch (error) {
      // 如果API命名空间不存在，使用默认的翻译函数
      console.warn(`API namespace not found for locale ${locale}, using default translations`)
      t = (key: string, values?: Record<string, string>) => {
        // 提供默认的错误消息
        const defaultMessages: Record<string, string> = {
          'Error.InternalServerError': 'Internal server error',
          'Error.Unauthorized': 'Unauthorized access',
          'Error.userNotLoggedIn': 'User not logged in',
          'Error.invalidBookId': 'Invalid book ID',
          'Error.bookIdRequired': 'Book ID is required',
          'Error.audioServiceConfigError': 'Audio service configuration error',
          'Error.methodNotAllowed': 'Method not allowed',
          'Error.fieldMustBeANumber': 'Field {field} must be a number',
          'Error.invalidJsonPayload': 'Invalid JSON payload',
          'Error.fieldIsRequired': 'Field {field} is required'
        }

        let message = defaultMessages[key] || key

        // 简单的变量替换
        if (values) {
          Object.entries(values).forEach(([placeholder, value]) => {
            message = message.replace(`{${placeholder}}`, value)
          })
        }

        return message
      }
    }

    if (!context.params) {
      context.params = {}
    }
    context.params.locale = locale
    context.t = t
    return await handler(request, context)
  }
}

/**
 * 错误处理中间件
 * 捕获所有错误，并返回错误响应
 */
const errorMiddleware: Middleware = (handler: ApiHandler) => {
  return async (request: NextRequest, context) => {
    try {
      // 执行传入的处理函数
      return await handler(request, context)
    } catch (error) {
      // 获取错误信息
      const errorInstance = error instanceof Error ? error : new Error(String(error))

      // 获取请求信息
      const headersList = await headers()
      const userAgent = headersList.get('user-agent') || undefined

      // 尝试从cookie获取用户ID
      const cookieStore = await cookies()
      const userId = cookieStore.get('userId')?.value
      const locale = cookieStore.get('NEXT_LOCALE')?.value ?? routing.defaultLocale
      const t = await getTranslations({ locale, namespace: 'API' })

      // 获取环境信息
      const environment = process.env.NODE_ENV || 'development'

      // 确定请求路径
      const path = request.nextUrl.pathname

      // 确定运行环境类型
      let type: 'client' | 'server' | 'edge' = 'server'
      if (process.env.NEXT_RUNTIME === 'edge') {
        type = 'edge'
      }

      // 创建错误日志对象
      const errorLog: ErrorLogSchema = {
        timestamp: new Date(),
        environment,
        type,
        path,
        message: errorInstance.message,
        stack: errorInstance.stack || 'No stack trace available',
        userAgent,
        userId
      }

      // 上报错误
      await reportError(errorLog)

      // 返回错误响应
      return NextResponse.json(
        errorResponse(ResponseCode.InternalServerError, t('Error.InternalServerError'))
      )
    }
  }
}

/**
 * 认证中间件
 * 检查用户是否已登录
 * 如果未登录，返回401错误
 * 如果已登录，将uid添加到context上下文中
 * 继续执行处理函数
 */
export const auth: Middleware = (handler: ApiHandler) => {
  return async (request: NextRequest, context: RequestContext) => {
    // 检查用户是否已登录
    const cookieStore = await cookies()
    const token = cookieStore.get(process.env.TOKEN_KEY!)?.value
    const locale = cookieStore.get('NEXT_LOCALE')?.value ?? routing.defaultLocale

    // 获取翻译函数，如果失败则使用默认消息
    let t: (key: string, values?: Record<string, string>) => string
    try {
      t = await getTranslations({ locale, namespace: 'API' })
    } catch (error) {
      t = (key: string) => {
        const defaultMessages: Record<string, string> = {
          'Error.userNotLoggedIn': 'User not logged in',
          'Error.Unauthorized': 'Unauthorized access'
        }
        return defaultMessages[key] || key
      }
    }

    if (!token) {
      return NextResponse.json(errorResponse(ResponseCode.Unauthorized, t('Error.userNotLoggedIn')))
    }

    const { isValid, uid } = await verifyJwtToken(token)
    if (!isValid) {
      return NextResponse.json(errorResponse(ResponseCode.Unauthorized, t('Error.Unauthorized')))
    }

    // 确保 context.params 存在
    if (!context.params) {
      context.params = {}
    }

    // 将uid添加到context上下文中
    context.params.userId = uid?.toString() || ''

    // 用户已登录，继续执行处理函数
    return handler(request, context)
  }
}

/**
 * 数字验证中间件
 * 验证请求体中的指定字段是否为数字
 * 不是数字，返回 400 错误
 */
export const numeric = (...fieldNames: string[]): Middleware => {
  return (handler: ApiHandler) => {
    return async (request: NextRequest, context: RequestContext) => {
      const cookieStore = await cookies()
      const locale = cookieStore.get('NEXT_LOCALE')?.value ?? routing.defaultLocale

      // 获取翻译函数，如果失败则使用默认消息
      let t: (key: string, values?: Record<string, string>) => string
      try {
        t = await getTranslations({ locale, namespace: 'API' })
      } catch (error) {
        t = (key: string, values?: Record<string, string>) => {
          const defaultMessages: Record<string, string> = {
            'Error.fieldMustBeANumber': 'Field {field} must be a number',
            'Error.invalidJsonPayload': 'Invalid JSON payload'
          }
          let message = defaultMessages[key] || key
          if (values) {
            Object.entries(values).forEach(([placeholder, value]) => {
              message = message.replace(`{${placeholder}}`, value)
            })
          }
          return message
        }
      }
      // 克隆请求以避免消费原始请求体
      const clonedRequest = request.clone()

      try {
        // 解析请求体
        const body = await clonedRequest.json()

        // 验证指定字段是否为数字
        for (const fieldName of fieldNames) {
          const value = body[fieldName]

          // 检查字段是否为数字类型
          if (typeof value !== 'number') {
            return NextResponse.json(
              errorResponse(
                ResponseCode.BadRequest,
                t('Error.fieldMustBeANumber', { field: fieldName })
              )
            )
          }
        }

        // 验证通过，继续处理请求
        return handler(request, context)
      } catch (error) {
        // 处理JSON解析错误
        if (error instanceof Error && error.message.includes('JSON')) {
          return NextResponse.json(
            errorResponse(ResponseCode.BadRequest, t('Error.invalidJsonPayload'))
          )
        }
        throw error
      }
    }
  }
}

/**
 * 必填字段验证中间件
 * 验证请求体中的指定字段是否存在且不为空
 * 如果字段缺失或为空，返回400错误
 */
export const required = (...fieldNames: string[]): Middleware => {
  return (handler: ApiHandler) => {
    return async (request: NextRequest, context: RequestContext) => {
      const cookieStore = await cookies()
      const locale = cookieStore.get('NEXT_LOCALE')?.value ?? routing.defaultLocale

      // 获取翻译函数，如果失败则使用默认消息
      let t: (key: string, values?: Record<string, string>) => string
      try {
        t = await getTranslations({ locale, namespace: 'API' })
      } catch (error) {
        t = (key: string, values?: Record<string, string>) => {
          const defaultMessages: Record<string, string> = {
            'Error.fieldIsRequired': 'Field {field} is required',
            'Error.invalidJsonPayload': 'Invalid JSON payload'
          }
          let message = defaultMessages[key] || key
          if (values) {
            Object.entries(values).forEach(([placeholder, value]) => {
              message = message.replace(`{${placeholder}}`, value)
            })
          }
          return message
        }
      }
      // 克隆请求以避免消费原始请求体
      const clonedRequest = request.clone()

      try {
        // 解析请求体
        const body = await clonedRequest.json()

        // 验证指定字段是否存在且不为空
        for (const fieldName of fieldNames) {
          const value = body[fieldName]

          // 检查字段是否存在且不为空
          if (value === undefined || value === null || value === '') {
            return NextResponse.json(
              errorResponse(
                ResponseCode.BadRequest,
                t('Error.fieldIsRequired', { field: fieldName })
              )
            )
          }
        }

        // 验证通过，继续处理请求
        return handler(request, context)
      } catch (error) {
        // 处理JSON解析错误
        if (error instanceof Error && error.message.includes('JSON')) {
          return NextResponse.json(errorResponse(ResponseCode.BadRequest, 'Invalid JSON payload'))
        }

        // 其他错误交给errorMiddleware处理
        throw error
      }
    }
  }
}
