'use server'
import 'server-only'
import reportError from '@/services/server/errorService'
import { ErrorLogSchema } from '@/models/ErrorLog'

type ServerAction<Args extends any[], Return> = (...args: Args) => Promise<Return>

export default async function withServerActionErrorHandling<Args extends any[], Return>(
  action: ServerAction<Args, Return>,
  errorInfo: Omit<ErrorLogSchema, 'timestamp' | 'message' | 'stack' | 'type'>
): Promise<ServerAction<Args, Return>> {
  return async (...args: Args): Promise<Return> => {
    try {
      return await action(...args)
    } catch (error) {
      const errorObject = error instanceof Error ? error : new Error(String(error))

      await reportError({
        ...errorInfo,
        type: 'server',
        message: errorObject.message,
        stack: errorObject.stack || '',
        timestamp: new Date()
      })

      throw errorObject
    }
  }
}
