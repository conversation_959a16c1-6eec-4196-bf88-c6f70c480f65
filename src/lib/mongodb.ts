import 'server-only'
import mongoose from 'mongoose'

const MONGODB_URL = process.env.MONGODB_URL

/**
 * 全局变量用于缓存 MongoDB 连接
 * 在开发模式下，由于热重载会保留状态，此变量将防止连接被多次创建
 */
let cached = global.mongoose

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null }
}

async function dbConnect() {
  // 运行时检查 MONGODB_URL
  if (!MONGODB_URL) {
    throw new Error('请在环境变量中设置 MONGODB_URL')
  }

  // 如果已经连接，直接返回
  if (cached.conn) {
    return cached.conn
  }

  // 如果没有正在进行的连接尝试，则创建一个
  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      // 添加其他有用的配置选项
      serverSelectionTimeoutMS: 5000, // 选择服务器超时
      heartbeatFrequencyMS: 10000 // 心跳频率
    }

    // 设置连接事件处理
    mongoose.connection.on('connected', () => {
      console.log('MongoDB 连接成功')
    })

    mongoose.connection.on('error', (err) => {
      console.error('MongoDB 连接错误:', err)
    })

    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB 连接断开')
    })

    // 保持与原始代码相同的模式，确保类型一致性
    cached.promise = mongoose.connect(MONGODB_URL, opts).then(() => {
      return cached
    })
  }

  try {
    cached.conn = await cached.promise
  } catch (e) {
    cached.promise = null
    console.error('MongoDB 连接失败:', e)
    throw e
  }

  return cached.conn
}

/**
 * 关闭 MongoDB 连接
 * 在需要显式关闭连接的情况下使用
 */
async function dbDisconnect() {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.disconnect()
    cached.conn = null
    cached.promise = null
    console.log('MongoDB 连接已关闭')
  }
}

export { dbConnect as default, dbDisconnect }
