<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Hyperf\Context\Context;
use Lete\Pay\Cashier;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Query;
use Website\Common\Service\OrderService;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="stripe", tag="Stripe", server="website-common-frontend-api")
 */
class StripeController extends AbstractController
{
    /**
     * @GetApi(path="portal", description="管理入口")
     */
    public function portal()
    {
        $user = $this->user();

        $BillingPortalSession = Cashier::stripe()->billingPortal->sessions->create([
            'customer' => UserService::stripeCustomer($user['id'])->stripeId()
        ]);

        $data = [
            'url' => $BillingPortalSession->url,
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @GetApi(path="check-session", description="检查支付会话")
     * @Query(key="session_id|session id", rule="required")
     */
    public function checkSession()
    {
        $validator_data = Context::get('validator.data');
        try {
            retry(10, function () use ($validator_data, &$OrderService) {
                $CheckoutSession = Cashier::stripe()->checkout->sessions->retrieve($validator_data['session_id']);

                $OrderService = OrderService::make($CheckoutSession->client_reference_id);
                if (!$OrderService) {
                    throw new \Exception('Order not found.');
                }

                $object = $CheckoutSession->toArray();
                switch ($object['mode']) {
                    case 'payment':
                        if ($object['payment_status'] === 'paid') {
                            $paid_amount = ($object['currency'] === 'usd' ? $object['amount_total'] : $object['currency_conversion']['amount_total']) / 100;
                            $OrderService->paid($paid_amount, time(), OrderService::PAYMENT_PLATFORM_STRIPE);

                            $OrderService->save([
                                'transaction_number' => $object['payment_intent'],
                            ]);
                        }
                        break;
                    default:
                        break;
                }
            }, 500);

            return $this->response(200, 'success', [
                'order_sn' => $OrderService->order_sn,
                'order_status' => $OrderService->order_status,
                'type' => $OrderService->type,
            ]);
        } catch (\Throwable $e) {
            return $this->response(400, $e->getMessage());
        }
    }
}
