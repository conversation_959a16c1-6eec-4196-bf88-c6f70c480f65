<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Constants\ErrorCode;
use Website\Common\Model\Order;
use Website\Common\Service\OrderService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\HttpMessage\Exception\NotFoundHttpException;
use Hyperf\HttpMessage\Exception\ServerErrorHttpException;
use Hyperf\Utils\Arr;
use Lete\Pay\Cashier;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="invoices", tag="账单", server="website-common-frontend-api")
 */
class InvoicesController extends AbstractController
{
    /**
     * @GetApi(path="", description="账单列表")
     * @Query(key="page|page", rule="integer|min:1")
     * @Query(key="per_page|per page", rule="integer|max:500")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $data = Order::select(['order_sn', 'order_status', 'paid_at', 'paid_amount', 'type'])
            ->where('user_id', $this->user()['id'])
            ->whereRaw("((order_status in(2,3) and transaction_number<>'') or order_status=1)")
            ->orderByDesc('id')
            ->paginate((int) Arr::get($validator_data, 'per_page', 10))
            ->toArray();
        $data['data'] = array_map(function ($item) {
            $item['paid_at'] = $item['paid_at'];
            return $item;
        }, $data['data']);
        return $this->response(200, 'success', $data);
    }

    /**
     * @GetApi(path="{order_sn}", description="查看账单")
     * @Path(key="order_sn|order sn", rule="required")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');
        $Order = Order::query()
            ->where([
                ['user_id', $this->user()['id']],
                ['order_sn', $validator_data['order_sn']],
            ])
            ->first(['id']);
        if (!$Order) {
            return $this->response(404, 'The order not found.');
        }
        $data = OrderService::getInvoiceTemplateData($Order->id);
        if (!$data) {
            return $this->response(500, ErrorCode::getMessage(ErrorCode::SERVER_ERROR));
        }
        return $this->response(200, 'success', $data);
    }
}
