<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Exception\UserException;
use Website\Common\Service\UserService;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Context\Context;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="auth-google", tag="谷歌授权", server="website-common-frontend-api")
 */
class AuthGoogleController extends AbstractController
{
    /**
     * @PostApi(path="login", description="注册登录")
     * @FormData(key="code|code", rule="required_without:credential|string|min:1")
     * @FormData(key="redirect_uri|redirect uri", rule="required_with:code|url")
     * @FormData(key="credential|credential", rule="required_without:code")
     * @FormData(key="source|source", rule="")
     * @FormData(key="distribution_code|distribution_code", rule="")
     */
    public function login()
    {
        try {
            $validator_data = Context::get('validator.data');
            $GoogleClient = $this->getGoogleClient();
            JWT::$leeway = 30;
            if (isset($validator_data['code'])) {
                $GoogleClient->setRedirectUri($validator_data['redirect_uri']);
                $GoogleClient->fetchAccessTokenWithAuthCode(urldecode($validator_data['code']));
                $payload = $GoogleClient->verifyIdToken();
            } else {
                $payload = $GoogleClient->verifyIdToken($validator_data['credential']);
            }
            if (!$payload) {
                return $this->response(400, 'The token (id_token) was verification failed.');
            }
            if (empty($payload['email'])) {
                return $this->response(400, 'The email is empty.');
            }
            $User = UserService::loginByGoogle($payload, [
                'source' => $validator_data['source'] ?? '',
                'language' => $this->request->hasCookie(Website::getLocaleCookieName()) ?
                    $this->request->cookie(Website::getLocaleCookieName()) :
                    ($this->request->hasHeader('accept-language') ? explode(',', $this->request->header('accept-language'))[0] : null),
                'distribution_code' => strtoupper(strval($validator_data['distribution_code'] ?? '')),
            ]);
            $this->authGuard()->login($User);
            return $this->response(200, 'success', [
                'user' => UserController::userInfo($this->user($User)),
                'new_user' => $User->getNewUser(),
            ]);
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage(), [], $e->getCode());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="auth-url", description="生成授权链接")
     * @FormData(key="redirect_uri|redirect uri", rule="required|url")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $GoogleClient = $this->getGoogleClient();
        $GoogleClient->setRedirectUri($validator_data['redirect_uri']);
        $data = [
            'auth_url' => $GoogleClient->createAuthUrl(['email', 'profile']),
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @return \Google_Client
     * @throws \Google\Exception
     */
    protected function getGoogleClient()
    {
        $GoogleClient = new \Google_Client();
        $config = [
            'verify' => false,
            'connect_timeout' => 5,
            'timeout' => 120,
        ];
        if ($proxy = env('GOOGLE_HTTP_PROXY')) {
            $config['proxy'] = $proxy;
        }
        $GoogleClient->setHttpClient((new Client($config)));
        $GoogleClient->setApplicationName(env('APP_NAME'));
        $GoogleClient->setAuthConfig(json_decode(env('GOOGLE_CLIENT_SECRET'), true));
        return $GoogleClient;
    }
}
