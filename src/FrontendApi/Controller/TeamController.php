<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Hyperf\Apidog\Annotation\PostApi;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\TeamException;
use Website\Common\Exception\UserException;
use Website\Common\Model\Team;
use Website\Common\Model\TeamMember;
use Website\Common\Model\TeamRole;
use Website\Common\Model\User;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Context\Context;
use Hyperf\Utils\Arr;
use Website\Common\Service\RankService;
use Website\Common\Service\TeamService;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="team", tag="团队", server="website-common-frontend-api")
 */
class TeamController extends AbstractController
{
    /**
     * 当前团队信息
     * @param boolean $auto_create 当没有团队时，自动创建
     * @param boolean $must_admin 必须是管理员
     * @return \Hyperf\Database\Model\Model|\Website\Common\Model\Team
     * @throws \Throwable
     */
    public function getTeam($auto_create = false, $must_admin = false)
    {
        $user = $this->user();
        if ($team_id = $this->request->header('team-id')) {
            $TeamMember = TeamMember::query()
                ->with('team')
                ->where('team_id', $team_id)
                ->where('user_id', $user['id'])
                ->first();
            if ($TeamMember) {
                if ($must_admin && !$TeamMember->is_admin) {
                    throw new TeamException(ErrorCode::TEAM_MUST_BE_ADMIN);
                }
                return $TeamMember->team;
            }

            $Team = Team::query()
                ->where('id', $team_id)
                ->where('user_id', $user['id'])
                ->first();
            if ($Team) {
                return $Team;
            }

            throw new TeamException(ErrorCode::TEAM_NOT_MEMBER);
        } else {
            return TeamService::myTeam($user['id'], $auto_create);
        }
    }

    /**
     * @GetApi(path="", description="团队信息")
     */
    public function index()
    {
        $user = $this->user();
        try {
            $Team = $this->getTeam(true);
            $team = Arr::only($Team->toArray(), ['id', 'name', 'member_count']);
            $team['max_team_members'] = $user['rank']['max_team_members'];

            // 团队所有者
            $owner = User::query()
                ->with('rank:id,rank_name,permission')
                ->find($Team->user_id, ['id', 'account', 'username', 'rank_id', 'created_at', 'vip_started_at'])
                ->toArray();

            // 成员信息
            $members = TeamService::getMembers($team['id']);

            // 角色信息
            $roles = TeamRole::query()
                ->where('team_id', $team['id'])
                ->get(['id', 'name', 'permission'])
                ->toArray();

            // 我的信息
            $me = array_values(array_filter($members, function ($item) use ($user) {
                return $user['account'] === $item['user']['account'];
            }))[0];

            // 我在团队中的次数权限配额
            $permissions_quota = [];
            $permissions = RankService::flattenPermission($owner['rank']['permission'], '');
            if ($owner['id'] !== $user['id'] && config('website.team.permission_isolation', false)) {
                $permissions_used = TeamService::getAllPermissionsUsed($team['id'], $user['id']);
            } else {
                $permissions_used = UserService::getAllPermissionsUsed($owner['id']);
            }
            foreach (array_keys(RankService::getPermissions()) as $permission_name) {
                $item = [
                    'permission_name' => $permission_name,
                    'limit' => $permissions[$permission_name] ?? 0,
                    'used' => $permissions_used[$permission_name] ?? 0,
                    'remaining' => 0,
                    'reset_at' => UserService::getPermissionResetAt($owner['id'], $permission_name, $user['created_at'], $user['vip_started_at'], $this->request->header('timezone')),
                ];
                $item['remaining'] = intval($item['limit'] - $item['used']);
                $item['remaining'] < 0 && $item['remaining'] = 0;
                $permissions_quota[] = $item;
            }
            $me['permissions_quota'] = $permissions_quota;

            Arr::forget($owner, ['id', 'rank_id', 'rank.id', 'created_at', 'vip_started_at']);
            $data = [
                'team' => $team,
                'owner' => $owner,
                'members' => $members,
                'roles' => $roles,
                'me' => $me,
            ];
            return $this->response(200, 'success', $data);
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="generate-invite-code", description="生成邀请码")
     */
    public function generateInviteCode()
    {
        try {
            $Team = $this->getTeam(false, true);
            $data = [
                'code' => TeamService::generateInviteCode($Team->id),
            ];
            return $this->response(200, 'success', $data);
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="send-invite-email", description="发送邀请邮件")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="role_id|role id", rule="required")
     */
    public function sendInviteEmail()
    {
        $validator_data = Context::get('validator.data');
        $user = $this->user();
        try {
            $Team = $this->getTeam(false, true);
            TeamService::sendInviteEmail($Team->id, $validator_data['role_id'], $validator_data['email'], $user['id']);
            return $this->response(200, 'success');
        } catch (TeamException|UserException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="retrieve", description="通过邀请码，获取团队信息")
     * @FormData(key="code|code", rule="required")
     */
    public function retrieve()
    {
        $validator_data = Context::get('validator.data');
        try {
            $team = Arr::only(TeamService::getTeamByCode($validator_data['code'])->toArray(), ['id', 'name']);
            return $this->response(200, 'success', [
                'team' => $team,
            ]);
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="join", description="加入团队")
     * @FormData(key="code|code", rule="required")
     */
    public function join()
    {
        $validator_data = Context::get('validator.data');
        $user = $this->user();
        try {
            TeamService::joinByCode($validator_data['code'], $user['id']);
            $team = Arr::only(TeamService::getTeamByCode($validator_data['code'])->toArray(), ['id', 'name']);
            return $this->response(200, 'success', [
                'team' => $team,
            ]);
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }


    /**
     * @PostApi(path="add-member", description="添加团队成员")
     * @FormData(key="email|email", rule="required|email")
     */
    public function addMember()
    {
        $validator_data = Context::get('validator.data');
        $user = $this->user();
        try {
            $Team = $this->getTeam(false, true);
            TeamService::addMember($Team->id, $validator_data['email']);
            return $this->response(200, 'success');
        } catch (TeamException|UserException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }


    /**
     * @PostApi(path="remove-member", description="删除成员")
     * @FormData(key="member_id|member id", rule="required")
     */
    public function removeMember()
    {
        $validator_data = Context::get('validator.data');
        try {
            $Team = $this->getTeam(false, true);
            TeamService::removeMember($Team->id, $validator_data['member_id']);
            return $this->response(200, 'success');
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="change-member-role", description="更改成员的角色")
     * @FormData(key="member_id|member id", rule="required")
     * @FormData(key="role_id|role id", rule="required")
     */
    public function changeMemberRole()
    {
        $validator_data = Context::get('validator.data');
        try {
            $Team = $this->getTeam(false, true);
            TeamService::changeMemberRole($Team->id, $validator_data['member_id'], $validator_data['role_id']);
            return $this->response(200, 'success');
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @PostApi(path="exit", description="【成员则】退出团队")
     */
    public function exitTeam()
    {
        $user = $this->user();
        try {
            $Team = $this->getTeam();
            TeamService::removeMember($Team->id, null, $user['id']);
            return $this->response(200, 'success');
        } catch (TeamException $e) {
            return $this->response($e->getCode(), $e->getMessage());
        }
    }
}
