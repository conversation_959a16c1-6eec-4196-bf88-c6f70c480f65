<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Model\StripePaymentIntent;
use Website\Common\Model\User;
use Website\Common\Service\OrderService;
use Website\Common\Service\SubscriptionService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\DbConnection\Db;
use Hyperf\HttpMessage\Exception\ForbiddenHttpException;
use Hyperf\HttpMessage\Exception\ServerErrorHttpException;
use Hyperf\Utils\Str;
use Lete\MongoDB\MongoClient\MongoDb;
use Lete\Pay\Cashier;
use Lete\Pay\Subscription;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Stripe;
use Stripe\Subscription as StripeSubscription;
use Stripe\WebhookSignature;
use Website\Common\Service\WebsiteSettingService;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="stripe-webhook", tag="Stripe Webhook", server="website-common-frontend-api")
 */
class StripeWebhookController extends AbstractController
{
    /**
     * @PostApi(path="", description="响应事件")
     */
    public function index()
    {
        // 校验签名
        if (config('letepay.webhook.secret') && intval(WebsiteSettingService::get('stripe-webhook-signature-verify', 1))) {
            try {
                WebhookSignature::verifyHeader(
                    $this->request->getBody()->getContents(),
                    $this->request->header('Stripe-Signature'),
                    config('letepay.webhook.secret'),
                    config('letepay.webhook.tolerance')
                );
            } catch (SignatureVerificationException $exception) {
                throw new ForbiddenHttpException($exception->getMessage(), $exception->getCode(), $exception);
            }
        }

        $payload = $this->request->all();

        if ($payload && $payload['type']) {
            go(function () use ($payload) {
                $this->saveLogs($payload);
            });

            $method = 'handle'.Str::studly(str_replace('.', '_', $payload['type']));
            if (method_exists($this, $method)) {
                $this->setMaxNetworkRetries();

                try {
                    return $this->{$method}($payload);
                } catch (\Throwable $e) {
                    Website::logger()->error((string) $e);
                    throw new ServerErrorHttpException($e->getMessage(), 0, $e);
                }
            }
        }

        return $this->missingMethod($payload);
    }

    /**
     * 保存日志
     * @param $payload
     */
    protected function saveLogs($payload)
    {
        $object = $payload['data']['object'];
        if (in_array($payload['type'], ['customer.created', 'customer.updated', 'customer.deleted']) || isset($object['customer'])) {
            $Billable = Cashier::findBillable($object['customer'] ?? $object['id']);
            $Billable && $payload['user_id'] = $Billable->user_id;
        }
        MongoDb::collection('stripe_events')
            ->insertOne($payload);
    }

    /**
     * 支付意图成功事件
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Exception
     */
    protected function handlePaymentIntentSucceeded(array $payload)
    {
        $object = $payload['data']['object'];
        // api版本>=2022-11-15，就不会返回charges属性
        if (isset($object['charges'])) {
            $payment_method = $object['charges']['data'][0]['payment_method'];
            $payment_method_details = $object['charges']['data'][0]['payment_method_details'];
        } else {
            $payment_method = $object['payment_method'];
            retry(3, function () use ($payment_method, &$payment_method_details) {
                $StripePaymentMethods = Cashier::stripe()->paymentMethods->retrieve($payment_method);
                $payment_method_details = $StripePaymentMethods->toArray();
            }, 1000);
        }
        StripePaymentIntent::updateOrCreate([
            'payment_intent' => $object['id']
        ], [
            'customer' => $object['customer'],
            'invoice' => $object['invoice'],
            'payment_method' => $payment_method,
            'payment_method_details' => $payment_method_details,
        ]);

        if ($Customer = $this->getCustomerByStripeId($object['customer'])) {
            User::query()
                ->find($Customer->user_id)
                ->update([
                    'card_last4' => $payment_method_details['card']['last4'] ?? null,
                ]);
        }

        return $this->successMethod();
    }

    /**
     * 账单支付失败
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleInvoicePaymentFailed(array $payload)
    {
        $object = $payload['data']['object'];
        if ($object['billing_reason'] === 'subscription_cycle') {
            $SubscriptionService = SubscriptionService::make(OrderService::PAYMENT_PLATFORM_STRIPE, $object['subscription']);
            if ($SubscriptionService && $SubscriptionService->subscription_status === SubscriptionService::STATUS_ACTIVE) {
                $SubscriptionService->cancel(SubscriptionService::CANCELED_HANDLER_APPLICATION);
            }
        }

        return $this->successMethod();
    }

    /**
     * 账单已支付事件
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleInvoicePaid(array $payload)
    {
        $object = $payload['data']['object'];

        // 订单已支付
        $Customer = $this->getCustomerByStripeId($object['customer']);
        $Order = null;
        retry(30, function () use ($object, $Customer, &$Order) {
            $Order = Db::table('orders')
                ->where([
                    ['user_id', $Customer ? $Customer->user_id : null],
                    ['stripe_invoice', $object['id']]
                ])->first(['id']);
            if (!$Order) {
                $Order = Db::table('orders')
                    ->where([
                        ['user_id', $Customer ? $Customer->user_id : null],
                        ['subscription_sn', $object['subscription']],
                        ['stripe_invoice', null],
                    ])->first(['id']);
                $Order && (new OrderService($Order->id))->save([
                    'payment_platform' => OrderService::PAYMENT_PLATFORM_STRIPE,
                    'stripe_invoice' => $object['id'],
                ]);
            }

            if (!$Order) {
                throw new \Exception('Can not find the order.');
            }
        }, 1000);

        if ($Order) {
            $OrderService = new OrderService($Order->id);
            $OrderService->save([
                'hosted_invoice_url' => $object['hosted_invoice_url'],
                'invoice_pdf' => $object['invoice_pdf'],
                'transaction_number' => $object['payment_intent'],
            ]);
            $OrderService->paid(
                $object['total'] / 100,
                $object['status_transitions']['paid_at']
            );
        }

        return $this->successMethod();
    }

    /**
     * 账单已更新事件
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleInvoiceUpdated(array $payload)
    {
        $object = $payload['data']['object'];

        $Customer = $this->getCustomerByStripeId($object['customer']);
        if ($object['billing_reason'] === 'subscription_create') {
            // 更新订单
            $Order = Db::table('orders')
                ->where([
                    ['user_id', $Customer ? $Customer->user_id : null],
                    ['subscription_sn', $object['subscription']],
                    ['stripe_invoice', null],
                ])->first(['id']);
            $Order && (new OrderService($Order->id))->save([
                'payment_platform' => OrderService::PAYMENT_PLATFORM_STRIPE,
                'stripe_invoice' => $object['id'],
                'hosted_invoice_url' => $object['hosted_invoice_url'],
                'invoice_pdf' => $object['invoice_pdf'],
                'transaction_number' => $object['payment_intent'],
            ]);
        }

        return $this->successMethod();
    }

    /**
     * 账单已创建事件
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleInvoiceCreated(array $payload)
    {
        $object = $payload['data']['object'];

        $Customer = $this->getCustomerByStripeId($object['customer']);
        if ($object['billing_reason'] === 'subscription_cycle') {
            // 创建订单
            $data = [
                'order_sn' => OrderService::generateOrderSN($Customer ? $Customer->user_id : 0),
                'subscription_sn' => $object['subscription'],
                'first_time' => false,
                'order_status' => OrderService::STATUS_UNPAID,
                'order_amount' => $object['subtotal'] / 100,
                'payment_platform' => OrderService::PAYMENT_PLATFORM_STRIPE,
                'created_at' => $object['created'],
                'stripe_invoice' => $object['id'],
                'hosted_invoice_url' => $object['hosted_invoice_url'],
                'invoice_pdf' => $object['invoice_pdf'],
                'transaction_number' => $object['payment_intent'],
            ];
            if ($Customer) {
                $data['user_id'] = $Customer->user_id;
                $data['first_time'] = Db::table('orders')
                        ->where('user_id', $data['user_id'])
                        ->whereIn('order_status', [OrderService::STATUS_PAID, OrderService::STATUS_REFUNDED])
                        ->count() === 0;
            }
            $plan = $object['lines']['data'][0]['plan'];
            if ($Rank = Db::table('ranks')->where('stripe_product_id', $plan['product'])->first()) {
                $data['rank_id'] = $Rank->id;
                $data['rank_duration'] = $Rank->duration;
            }
            (new OrderService())->save($data);
        } else {
            // 更新订单
            $Order = Db::table('orders')
                ->where([
                    ['user_id', $Customer ? $Customer->user_id : null],
                    ['subscription_sn', $object['subscription']],
                    ['stripe_invoice', null],
                ])->first(['id']);
            $Order && (new OrderService($Order->id))->save([
                'payment_platform' => OrderService::PAYMENT_PLATFORM_STRIPE,
                'stripe_invoice' => $object['id'],
                'hosted_invoice_url' => $object['hosted_invoice_url'],
                'invoice_pdf' => $object['invoice_pdf'],
                'transaction_number' => $object['payment_intent'],
            ]);
        }

        return $this->successMethod();
    }

    /**
     * 结束会话完成事件
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Exception
     */
    protected function handleCheckoutSessionCompleted(array $payload)
    {
        $object = $payload['data']['object'];

        if ($object['client_reference_id']) {
            $OrderService = OrderService::make($object['client_reference_id']);
            $OrderService->FrontendRedis->del($OrderService->stripeCheckoutSessionKey());

            switch ($object['mode']) {
                case 'subscription':
                    $OrderService->save([
                        'subscription_sn' => $object['subscription'],
                    ]);
                    break;
                case 'payment':
                    if ($object['payment_status'] === 'paid') {
                        $paid_amount = ($object['currency'] === 'usd' ? $object['amount_total'] : $object['currency_conversion']['amount_total']) / 100;
                        $OrderService->paid($paid_amount, time(), OrderService::PAYMENT_PLATFORM_STRIPE);

                        $OrderService->save([
                            'transaction_number' => $object['payment_intent'],
                        ]);
                    }
                    break;
                default:
                    break;
            }
        }

        return $this->successMethod();
    }

    /**
     * 新订阅响应
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleCustomerSubscriptionCreated(array $payload)
    {
        $customer = $this->getCustomerByStripeId($payload['data']['object']['customer']);

        if ($customer) {
            $data = $payload['data']['object'];

            if (! $customer->subscriptions->contains('stripe_id', $data['id'])) {
                if (isset($data['trial_end'])) {
                    $trialEndsAt = Carbon::createFromTimestamp($data['trial_end']);
                } else {
                    $trialEndsAt = null;
                }

                $firstItem = $data['items']['data'][0];
                $isSinglePrice = count($data['items']['data']) === 1;

                $subscription = $customer->subscriptions()->create([
                    'name' => $data['metadata']['name'] ?? $this->newSubscriptionName($payload),
                    'stripe_id' => $data['id'],
                    'stripe_status' => $data['status'],
                    'stripe_price' => $isSinglePrice ? $firstItem['price']['id'] : null,
                    'quantity' => $isSinglePrice && isset($firstItem['quantity']) ? $firstItem['quantity'] : null,
                    'trial_ends_at' => $trialEndsAt,
                    'ends_at' => null,
                ]);

                // 保存订阅记录
                if (in_array($data['status'], [StripeSubscription::STATUS_ACTIVE, StripeSubscription::STATUS_TRIALING])) {
                    SubscriptionService::firstOrCreate(OrderService::PAYMENT_PLATFORM_STRIPE, $data['id'], [
                        'user_id' => $customer->user_id,
                        'platform_status' => $data['status'],
                        'product_name' => $subscription->name,
                        'start_date' => $data['start_date'],
                        'next_period_start' => $data['current_period_end'],
                        'next_period_amount' => $data['plan']['amount'] / 100,
                    ]);
                }

                foreach ($data['items']['data'] as $item) {
                    $subscription->items()->create([
                        'stripe_id' => $item['id'],
                        'stripe_product' => $item['price']['product'],
                        'stripe_price' => $item['price']['id'],
                        'quantity' => $item['quantity'] ?? null,
                    ]);
                }
            }
        }

        return $this->successMethod();
    }

    /**
     * 订阅更新响应
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface|void
     * @throws \Throwable
     */
    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        if ($customer = $this->getCustomerByStripeId($payload['data']['object']['customer'])) {
            $data = $payload['data']['object'];

            $subscription = $customer->subscriptions()->firstOrNew(['stripe_id' => $data['id']]);

            // 保存订阅记录
            $SubscriptionService = SubscriptionService::firstOrCreate(OrderService::PAYMENT_PLATFORM_STRIPE, $data['id'], [
                'user_id' => $customer->user_id,
                'platform_status' => $data['status'],
                'start_date' => $data['start_date'],
                'next_period_start' => $data['current_period_end'],
                'next_period_amount' => $data['plan']['amount'] / 100,
            ]);

            if (
                isset($data['status']) &&
                $data['status'] === StripeSubscription::STATUS_INCOMPLETE_EXPIRED
            ) {
                $subscription->items()->delete();
                $subscription->delete();

                return;
            }

            $subscription->name = $subscription->name ?? $data['metadata']['name'] ?? $this->newSubscriptionName($payload);

            $firstItem = $data['items']['data'][0];
            $isSinglePrice = count($data['items']['data']) === 1;

            // Price...
            $subscription->stripe_price = $isSinglePrice ? $firstItem['price']['id'] : null;

            // Quantity...
            $subscription->quantity = $isSinglePrice && isset($firstItem['quantity']) ? $firstItem['quantity'] : null;

            // Trial ending date...
            if (isset($data['trial_end'])) {
                $trialEnd = Carbon::createFromTimestamp($data['trial_end']);

                if (! $subscription->trial_ends_at || $subscription->trial_ends_at->ne($trialEnd)) {
                    $subscription->trial_ends_at = $trialEnd;
                }
            }

            // Cancellation date...
            if (isset($data['cancel_at_period_end'])) {
                if ($data['cancel_at_period_end']) {
                    $subscription->ends_at = $subscription->onTrial()
                        ? $subscription->trial_ends_at
                        : Carbon::createFromTimestamp($data['current_period_end']);
                } elseif (isset($data['cancel_at'])) {
                    $subscription->ends_at = Carbon::createFromTimestamp($data['cancel_at']);
                } else {
                    $subscription->ends_at = null;
                }
            }

            // Status...
            if (isset($data['status'])) {
                $subscription->stripe_status = $data['status'];
            }

            $subscription->save();

            $SubscriptionService->id && $SubscriptionService->save([
                'product_name' => $subscription->name,
            ]);

            // Update subscription items...
            if (isset($data['items'])) {
                $prices = [];

                foreach ($data['items']['data'] as $item) {
                    $prices[] = $item['price']['id'];

                    $subscription->items()->updateOrCreate([
                        'stripe_id' => $item['id'],
                    ], [
                        'stripe_product' => $item['price']['product'],
                        'stripe_price' => $item['price']['id'],
                        'quantity' => $item['quantity'] ?? null,
                    ]);
                }

                // Delete items that aren't attached to the subscription anymore...
                $subscription->items()->whereNotIn('stripe_price', $prices)->delete();
            }
        }

        return $this->successMethod();
    }

    /**
     * 取消订阅响应
     * @param array $payload
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Throwable
     */
    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        if ($customer = $this->getCustomerByStripeId($payload['data']['object']['customer'])) {
            $customer->subscriptions->filter(function ($subscription) use ($payload) {
                return $subscription->stripe_id === $payload['data']['object']['id'];
            })->each(function ($subscription) {
                $subscription->markAsCanceled();
            });

            // 保存订阅记录
            $subscription_object = $payload['data']['object'];
            SubscriptionService::make(OrderService::PAYMENT_PLATFORM_STRIPE, $subscription_object['id'])
                ->cancel(SubscriptionService::CANCELED_HANDLER_STRIPE, $subscription_object['canceled_at'], [
                    'platform_status' => $subscription_object['status'],
                ]);
        }

        return $this->successMethod();
    }

    /**
     * 更新客户响应
     *
     * @param  array  $payload
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function handleCustomerUpdated(array $payload)
    {
        if ($customer = $this->getCustomerByStripeId($payload['data']['object']['id'])) {
            $customer->updateDefaultPaymentMethodFromStripe();
        }

        return $this->successMethod();
    }

    /**
     * 删除客户响应
     *
     * @param  array  $payload
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function handleCustomerDeleted(array $payload)
    {
        if ($customer = $this->getCustomerByStripeId($payload['data']['object']['id'])) {
            $customer->subscriptions->each(function (Subscription $subscription) {
                $subscription->skipTrial()->markAsCanceled();
            });

            $customer->delete();
        }

        return $this->successMethod();
    }

    /**
     * Determines the name that should be used when new subscriptions are created from the Stripe dashboard.
     *
     * @param  array  $payload
     * @return string
     */
    protected function newSubscriptionName(array $payload)
    {
        if ($plan = $payload['data']['object']['plan']) {
            $rank = Db::table('ranks')->where('stripe_product_id', $plan['product'])->first();
            if ($rank && $rank->product_name) {
                return $rank->product_name;
            }
        }
        return 'default';
    }

    /**
     * Get the customer instance by Stripe ID.
     *
     * @param  string|null  $stripeId
     * @return \Lete\Pay\Billable|null
     */
    protected function getCustomerByStripeId($stripeId)
    {
        return Cashier::findBillable($stripeId);
    }

    /**
     * Handle successful calls on the controller.
     *
     * @param  array  $parameters
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function successMethod($parameters = [])
    {
        return $this->response->raw('Webhook Handled');
    }

    /**
     * Handle calls to missing methods on the controller.
     *
     * @param  array  $parameters
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function missingMethod($parameters = [])
    {
        return $this->response->raw('Received unknown event type.' . (($parameters && $parameters['type']) ? '【' . $parameters['type'] . '】': ''));
    }

    /**
     * Set the number of automatic retries due to an object lock timeout from Stripe.
     *
     * @param  int  $retries
     * @return void
     */
    protected function setMaxNetworkRetries($retries = 3)
    {
        Stripe::setMaxNetworkRetries($retries);
    }
}
