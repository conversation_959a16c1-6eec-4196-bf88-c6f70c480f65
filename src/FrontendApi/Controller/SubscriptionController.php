<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Stripe\Exception\ApiErrorException;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\OrderException;
use Website\Common\Service\OrderService;
use Website\Common\Service\SubscriptionService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\HttpMessage\Exception\NotFoundHttpException;
use Hyperf\HttpMessage\Exception\ServerErrorHttpException;
use Hyperf\Utils\Arr;
use Lete\Pay\Cashier;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="subscription", tag="订阅", server="website-common-frontend-api")
 */
class SubscriptionController extends AbstractController
{
    /**
     * @PostApi(path="", description="创建订阅")
     * @FormData(key="plan_id|plan id", rule="required")
     * @FormData(key="first_time|first time", rule="boolean")
     * @FormData(key="success_url|success url", rule="required|url")
     * @FormData(key="cancel_url|cancel url", rule="url")
     */
    public function store()
    {
        try {
            $validator_data = Context::get('validator.data');

            $OrderService = OrderService::createSubscriptionOrder(
                $this->user()['id'],
                $validator_data['plan_id'],
                (bool) Arr::get($validator_data, 'first_time', false),
            );
            $StripeCheckoutSession = $OrderService->checkout(
                OrderService::PAYMENT_PLATFORM_STRIPE,
                $validator_data['success_url'],
                $validator_data['cancel_url'] ?? null
            );
            $data = [
                'stripe' => [
                    'url' => $StripeCheckoutSession->url,
                ],
            ];
            return $this->response(200, 'success', $data);
        } catch (\Throwable $e) {
            if ($e instanceof OrderException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            if ($e instanceof ApiErrorException) {
                return $this->response(ErrorCode::STRIPE_API_ERROR, $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @GetApi(path="", description="查看订阅信息")
     */
    public function index()
    {
        $last_subcription = UserService::getLastSubcription($this->user()['id']);
        $subcription = $last_subcription && $last_subcription['subscription_status'] === SubscriptionService::STATUS_ACTIVE ? [
            'next_period_amount' => $last_subcription['next_period_amount'],
            'next_period_start' => $last_subcription['next_period_start'],
            'card_last4' => $last_subcription['payment_method_details']['card']['last4'] ?? null,
        ] : null;

        $user = $this->user();

        $data = [
            'user' => [
                'vip_expired_at' => $user['vip_expired_at'],
                'rank_name' => $user['rank']['rank_name']
            ],
            'subcription' => $subcription,
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @DeleteApi(path="", description="取消订阅")
     */
    public function destory()
    {
        $last_subcription = UserService::getLastSubcription($this->user()['id']);
        if (!$last_subcription || $last_subcription['subscription_status'] !== SubscriptionService::STATUS_ACTIVE) {
            return $this->response(404, 'subscription not found');
        }

        try {
            (new SubscriptionService($last_subcription['id']))->cancel(SubscriptionService::CANCELED_HANDLER_MEMBER);
            return $this->response(200, '请求成功', []);
        } catch(\Throwable $e) {
            return $this->response(400, $e->getMessage());
        }
    }

    /**
     * @GetApi(path="check-session", description="检查支付会话")
     * @Query(key="session_id|session id", rule="required")
     */
    public function check()
    {
        $validator_data = Context::get('validator.data');
        try {
            $OrderService = null;
            retry(30, function () use ($validator_data, &$OrderService) {
                $CheckoutSession = Cashier::stripe()->checkout->sessions->retrieve($validator_data['session_id']);

                $Customer = Cashier::findBillable($CheckoutSession->customer);
                if (!$Customer) {
                    throw new \Exception('Customer not found.');
                }

                $OrderService = OrderService::make($CheckoutSession->client_reference_id);
                if (!$OrderService) {
                    throw new \Exception('Order not found.');
                }

                $object = $CheckoutSession->toArray();
                switch ($object['mode']) {
                    case 'subscription':
                    default:
                        if ($CheckoutSession->payment_status === 'paid') {
                            if ($OrderService->order_status === OrderService::STATUS_UNPAID) {
                                $StripeSubscription = Cashier::stripe()->subscriptions->retrieve($CheckoutSession->subscription);
                                $StripeInvoice = Cashier::stripe()->invoices->retrieve($StripeSubscription->latest_invoice);

                                if (!$OrderService->stripe_invoice) {
                                    $OrderService->save([
                                        'stripe_invoice' => $StripeInvoice->id,
                                        'hosted_invoice_url' => $StripeInvoice->hosted_invoice_url,
                                        'invoice_pdf' => $StripeInvoice->invoice_pdf,
                                        'transaction_number' => $StripeInvoice->payment_intent,
                                    ]);
                                }

                                $OrderService->paid($CheckoutSession->amount_total / 100, $StripeInvoice->status_transitions->paid_at, OrderService::PAYMENT_PLATFORM_STRIPE, $CheckoutSession->subscription);
                            }

                            $SubscriptionService = SubscriptionService::make(OrderService::PAYMENT_PLATFORM_STRIPE, $CheckoutSession->subscription);
                            if (!$SubscriptionService) {
                                if (!$StripeSubscription) {
                                    $StripeSubscription = Cashier::stripe()->subscriptions->retrieve($CheckoutSession->subscription);
                                }
                                $StripePlan = Cashier::stripe()->plans->retrieve($StripeSubscription->items->data[0]->price->id);

                                $product_name = 'default';
                                if ($StripePlan) {
                                    $Rank = Db::table('ranks')->where('stripe_product_id', $StripePlan->product)->first();
                                    if ($Rank && $Rank->product_name) {
                                        $product_name = $Rank->product_name;
                                    }
                                }

                                SubscriptionService::firstOrCreate(OrderService::PAYMENT_PLATFORM_STRIPE, $StripeSubscription->id, [
                                    'user_id' => $Customer->user_id,
                                    'platform_status' => $StripeSubscription->status,
                                    'product_name' => $product_name,
                                    'start_date' => $StripeSubscription->start_date,
                                    'next_period_start' => $StripeSubscription->current_period_end,
                                    'next_period_amount' => $StripePlan->amount / 100,
                                ]);
                            }
                        }
                        break;
                    case 'payment':
                        if ($object['payment_status'] === 'paid') {
                            $paid_amount = ($object['currency'] === 'usd' ? $object['amount_total'] : $object['currency_conversion']['amount_total']) / 100;
                            $OrderService->paid($paid_amount, time(), OrderService::PAYMENT_PLATFORM_STRIPE);

                            $OrderService->save([
                                'transaction_number' => $object['payment_intent'],
                            ]);
                        }
                        break;
                }
            }, 2000);
        } catch (\Throwable $e) {
            return $this->response(400, $e->getMessage());
        }

        return $this->response(200, 'success', [
            'order' => [
                'order_sn' => $OrderService->order_sn,
                'paid_amount' => $OrderService->paid_amount,
                'hosted_invoice_url' => $OrderService->hosted_invoice_url,
                'invoice_pdf' => $OrderService->invoice_pdf,
            ],
        ]);
    }
}
