<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Model\User;
use Website\Common\Service\OrderService;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Website\Common\Model\Rank;
use Website\Common\Service\RankService;
use Website\Common\Service\SubscriptionService;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="price", tag="报价", server="website-common-frontend-api")
 */
class PriceController extends AbstractController
{
    /**
     * @GetApi(path="", description="套餐价格")
     * @Query(key="group|group", rule="in:0,1")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $user =  $this->authGuard()->check() ? $this->user() : null;
        $data = [
            'first_time' => $user ? Db::table('orders')
                    ->where('user_id', $user['id'])
                    ->whereIn('order_status', [OrderService::STATUS_PAID, OrderService::STATUS_REFUNDED])
                    ->count() === 0 : true,
            'plans' => [],
        ];
        $ranks = Rank::findManyFromCache(RankService::getPlanIds())
            ->where('is_visibled', 1)
            ->toArray();
        // 按价格排升序
        usort($ranks, function ($rank1, $rank2) {
            if ($rank1['price'] === $rank2['price']) {
                return 0;
            }
            return $rank1['price'] < $rank2['price'] ? -1 : 1;
        });
        $group = (int) Arr::get($validator_data, 'group', 0);
        $last_subcription = $user ? UserService::getLastSubcription($user['id']) : null;
        foreach ($ranks as $rank) {
            $plan = Arr::only($rank, ['id', 'rank_name', 'duration', 'price', 'original_price', 'first_price', 'permission', 'discounts', 'trial_days', 'max_online_users', 'max_team_members']);
            // 是否可以购买该套餐（如果购买的套餐和现在订阅的套餐相同，则不能购买）
            $plan['is_actived'] = true;
            if ($user) {
                $plan['is_actived'] = !(strtolower($user['rank']['rank_name']) === strtolower($plan['rank_name']) && (($plan['duration'] !== 'forever' && $last_subcription && $last_subcription['subscription_status'] === SubscriptionService::STATUS_ACTIVE) || $plan['duration'] === 'forever'));
            }
            $data['plans'][] = $plan;
            if ($group === 1) {
                $data['groups'][$plan['rank_name']][$plan['duration']] = $plan;
            }
        }

        // 返回免费套餐的信息
        $data['free'] =  Arr::only(Rank::findFromCache(RankService::FREE)->toArray(), ['rank_name', 'permission', 'max_online_users', 'max_team_members']);

        return $this->response(200, 'success', $data);
    }
}
