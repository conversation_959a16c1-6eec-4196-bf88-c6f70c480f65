<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Constants\ErrorCode;
use Website\Common\Model\DistributionCode;
use Website\Common\Model\DistributionCommission;
use Website\Common\Model\DistributionInfo;
use Website\Common\Model\DistributionSettlement;
use Website\Common\Model\Order;
use Website\Common\Service\DistributionService;
use Website\Common\Service\OrderService;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\Arr;
use Website\Common\Utils\Tool;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="distribution", tag="分销信息", server="website-common-frontend-api")
 */
class DistributionController extends AbstractController
{

    /**
     * @PostApi(path="", description="新建分销")
     * @FormData(key="customize_code|customize_code", rule="required")
     * @FormData(key="application_reason|application_reason", rule="required")
     * @FormData(key="paypal|paypal", rule="required")
     * @throws \Throwable
     */
    public function store()
    {
        $validator_data = Context::get('validator.data');
        $user = $this->user();
        $user_id = $user['id'];
        $account = $user['account'];
        // 会员是否已经分销校验
        $distribution_info_id = DistributionInfo::query()->where('user_id', '=', $user_id)->value('id');
        if ($distribution_info_id) {
            return $this->response(ErrorCode::DISTRIBUTION_CODE_ALREADY_HAVE, 'error');
        }

        $customize_code = strtoupper($validator_data['customize_code']);
        // 分销码创建
        $pattern = '/^[A-Z0-9]{3,50}$/';
        if (!preg_match($pattern, $customize_code)) {
            return $this->response(ErrorCode::CREATE_DISTRIBUTION_CODE, 'error');
        }

        // 分销码保障唯一
        $distribution_info_id = DistributionCode::query()->where('distribution_code', '=', strtoupper($customize_code))->value('id');
        if ($distribution_info_id) {
            return $this->response(ErrorCode::DISTRIBUTION_CODE_ALREADY_EXISTS, 'error');
        }

        try {
            Db::beginTransaction();
            $insert = [
                'account' => $account,
                'user_id' => $user_id,
                'customize_code' => $customize_code,
                'distribution_code' => $customize_code,
                'application_time' => time(),
                'review_time' => time(),
                'status' => DistributionInfo::STATUS_DISTRIBUTION,
                'commission_rate' => DistributionCode::COMMISSION_RATE,
                'application_reason' => $validator_data['application_reason'] ?? '',
                'paypal' => $validator_data['paypal'] ?? ''
            ];
            DistributionInfo::create($insert);

            $code_insert = [
                'user_id' => $user_id,
                'distribution_code' => $customize_code,
                'commission_rate' => DistributionCode::COMMISSION_RATE,
            ];
            DistributionCode::create($code_insert);
            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            Website::logger()->error('新建分销异常，异常：' . $e->getMessage() . ',line' . $e->getLine() . ',file' . $e->getFile());
            return $this->response(400, 'error');
        }

        return $this->response(200, 'success');
    }

    /**
     * @GetApi(path="check", description="检查分销码")
     * @Query(key="distribution_code|distribution_code", rule="required")
     */
    public function check()
    {
        $validator_data = Context::get('validator.data');
        $distribution_code = Arr::get($validator_data, 'distribution_code', '');
        $distribution_bool = (new DistributionService())->check($distribution_code);
        $return = [];
        if ($distribution_bool) {
            $return['tips'] = 'Congratulations, the coupon code is valid!';
            $return['status'] = 1;
        } else {
            $return['tips'] = 'Sorry, this coupon code is not valid. please check if the coupon code is filled in correctly';
            $return['status'] = 0;
        }

        return $this->response(200, 'success', $return);

    }


    /**
     * @GetApi(path="show", description="查看详情")
     */
    public function show()
    {
        $user_id = $this->user()['id'];

        DistributionService::confirmCommission(0, $user_id);

        $data = DistributionInfo::query()->where('user_id', '=', $user_id)
            ->select('id', 'user_id', 'distribution_code', 'status', 'order_effect_num', 'order_invalid_num', 'confirmed_commission', 'not_confirmed_commission', 'assets', 'cancel_commission', 'settlement_commission', 'pay_customer', 'click_num', 'register_num', 'income_num', 'commission_rate', 'paypal')
            ->limit(1)
            ->get()
            ->toArray();
        if (!empty($data)) {
            $data = current($data);
        }
        return $this->response(200, 'success', $data);
    }

    /**
     * @GetApi(path="record-list", description="分销记录表")
     * @Query(key="page|page", rule="integer|min:1")
     * @Query(key="per_page|per_page", rule="integer|max:10000")
     */
    public function recordList()
    {
        $validator_data = Context::get('validator.data');
        $user_id = $this->user()['id'];

        $distribution_code_arr = DistributionCode::query()->where('user_id', '=', $user_id)->pluck('distribution_code')->toArray();

        $per_page = (int)Arr::get($validator_data, 'per_page', 10);

        $data = Order::query()
            ->with('rank:id,rank_name')
            ->with('user:id,email')
            ->with('distribution:order_id,pay_status,sure_status,commission_rate,commission')
            ->whereIn('distribution_code', $distribution_code_arr)
            ->whereIn('order_status', [OrderService::STATUS_PAID, OrderService::STATUS_REFUNDED])
            ->select('rank_duration', 'user_id', 'paid_amount', 'paid_at', 'distribution_code', 'id', 'rank_id')
            ->orderBy('id', 'desc')
            ->paginate($per_page)
            ->toArray();

        $return = [];
        foreach ($data['data'] as $value) {
            $return[] = [
                'paid_at' => $value['paid_at'],
                'pay_status' => $value['distribution']['pay_status'],
                'sure_status' => $value['distribution']['sure_status'],
                'email' => $value['user']['email'],
                'rank_name' => $value['rank']['rank_name'],
                'rank_duration' => $value['rank_duration'],
                'paid_amount' => $value['paid_amount'],
                'commission_rate' => $value['distribution']['commission_rate'],
                'commission' => $value['distribution']['commission'],
            ];
        }
        $data['data'] = $return;
        return $this->response(200, 'success', $data);
    }

    /**
     * @PostApi(path="update-info", description="编辑数据")
     * @FormData(key="paypal|paypal", rule="")
     */
    public function updateInfo()
    {
        $user_id = $this->user()['id'];
        $validator_data = Context::get('validator.data');
        $update = [
            'paypal' => $validator_data['paypal'],
        ];
        DistributionInfo::query()->where('user_id', '=', $user_id)->update($update);
        return $this->response(200, 'success');
    }


    /**
     * @GetApi(path="settle-list", description="结算记录列表")
     * @Query(key="page|page", rule="integer|min:1")
     * @Query(key="per_page|per_page", rule="integer|max:10000")
     */
    public function settleList()
    {
        $validator_data = Context::get('validator.data');
        $user_id = $this->user()['id'];
        $per_page = (int)Arr::get($validator_data, 'per_page', 10);


        $data = DistributionSettlement::query(false, true)
            ->where('user_id', '=', $user_id)
            ->select('pay_money', 'pay_time', 'status', 'remark', 'pay_account', 'created_at')
            ->orderBy('id', 'desc')
            ->paginate($per_page)
            ->toArray();

        foreach ($data['data'] as &$value) {
            if (!empty($value['created_at'])) {
                $value['created_at'] = strtotime($value['created_at']);
            }
        }
        unset($value);
        return $this->response(200, 'success', $data);

    }

    /**
     * @GetApi(path="commission", description="佣金记录列表")
     * @Query(key="page|page", rule="integer|min:1")
     * @Query(key="per_page|per_page", rule="integer|max:10000")
     */
    public function commission()
    {
        $validator_data = Context::get('validator.data');
        $user_id = $this->user()['id'];
        $per_page = (int)Arr::get($validator_data, 'per_page', 10);

        $data = DistributionCommission::query()
            ->where('user_id', '=', $user_id)
            ->select('type', 'money', 'current_assets', 'created_at')
            ->orderBy('id', 'desc')
            ->paginate($per_page)
            ->toArray();

        foreach ($data['data'] as &$value) {
            $value['created_at'] = strtotime($value['created_at']);
        }
        unset($value);

        return $this->response(200, 'success', $data);
    }


    /**
     * @PostApi(path="click", description="增加分销点击数")
     * @FormData(key="distribution_code|distribution_code", rule="required")
     */
    public function click()
    {
        $validator_data = Context::get('validator.data');
        $distribution_code = $validator_data['distribution_code'];

        // 限制5秒内请求100次
        if (Tool::isAllowed('distribution_click', 5, 100)) {
            $code_user_id = DistributionCode::query()->where('distribution_code', '=', $distribution_code)->value('user_id');
            DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('click_num');
            return $this->response(200, 'success');
        } else {
            return $this->response(400, 'Clicking too often, please try again later');
        }

    }

    /**
     * @GetApi(path="check-code", description="判断用户是否申请分销码")
     */
    public function checkCode()
    {
        $user_id = $this->user()['id'];
        $data = DistributionInfo::query()->where('user_id', '=', $user_id)->select('distribution_code', 'status')->limit(1)->get()->toArray();
        if (!empty($data)) {
            $data = current($data);
        }
        $return = [
            'distribution_code' => $data['distribution_code'] ?? '',
            'status' => $data['status'] ?? '',
        ];
        return $this->response(200, 'success', $return);
    }

    /**
     * @GetApi(path="get-code-list", description="获取分销码列表")
     */
    public function showCodeList()
    {
        $user_id = $this->user()['id'];
        $distribution_code_list = DistributionCode::query()
            ->where('user_id', '=', $user_id)
            ->select("id", "distribution_code", "rank_data")
            ->orderBy('id', 'desc')
            ->get()
            ->toArray();
        /* 默认值 */
        $is_default = false;
        foreach ($distribution_code_list as $value) {
            if (empty($value['rank_data'])) {
                $is_default = true;
                break;
            }
        }
        if ($is_default) {
            // 默认全部为已勾选
            $default_rank_data = DistributionService::defaultRankData('1');
            foreach ($distribution_code_list as &$value) {
                if (empty($value['rank_data'])) {
                    $value['rank_data'] = $default_rank_data;
                }
            }
            unset($value);
        }
        return $this->response(200, '请求成功', ['list' => $distribution_code_list]);

    }

}
