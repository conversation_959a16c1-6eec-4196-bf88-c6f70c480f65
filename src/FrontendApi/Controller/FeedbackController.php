<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Constants\ErrorCode;
use Website\Common\Model\Feedback;
use Website\Common\Utils\Website;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiServer;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Context\Context;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="feedback", tag="用户反馈", server="website-common-frontend-api")
 */
class FeedbackController extends AbstractController
{
    /**
     * @PostApi(path="", description="提交反馈")
     * @FormData(key="content|content", rule="required|string|max:65535")
     */
    public function store()
    {
        $validator_data = Context::get('validator.data');

        $user = $this->user();
        $redis_key = env('APP_NAME') . ":user-feedback:{$user['id']}";
        $FrontendRedis = Website::FrontendRedis();
        $limit = 10;
        if ($FrontendRedis->get($redis_key) >= $limit) {
            return $this->response(ErrorCode::USER_FEEDBACK_SUBMIT_MAXIMUM, ErrorCode::getMessage(ErrorCode::USER_FEEDBACK_SUBMIT_MAXIMUM));
        }
        if ($FrontendRedis->incr($redis_key) > $limit) {
            return $this->response(ErrorCode::USER_FEEDBACK_SUBMIT_MAXIMUM, ErrorCode::getMessage(ErrorCode::USER_FEEDBACK_SUBMIT_MAXIMUM));
        }
        if ($FrontendRedis->ttl($redis_key) === -1) {
            $FrontendRedis->expire($redis_key, 3600 * 24);
        }

        try {
            Feedback::create([
                'user_id' => $user['id'],
                'content' => $validator_data['content'],
            ]);
            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            $FrontendRedis->decr($redis_key);
            throw $e;
        }
    }
}
