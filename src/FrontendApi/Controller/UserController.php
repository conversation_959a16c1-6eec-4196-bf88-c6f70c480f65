<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Carbon\Carbon;
use Hyperf\Apidog\Annotation\PostApi;
use Website\Common\Exception\TeamException;
use Website\Common\Exception\UserException;
use Website\Common\Model\Order;
use Website\Common\Model\TeamMember;
use Website\Common\Model\User;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Context\Context;
use Hyperf\Utils\Arr;
use Website\Common\Model\UserSocialite;
use Website\Common\Service\OrderService;
use Website\Common\Service\RankService;
use Website\Common\Service\TeamService;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="user", tag="用户", server="website-common-frontend-api")
 */
class UserController extends AbstractController
{
    /**
     * @GetApi(path="", description="用户信息")
     */
    public function index()
    {
        $user = $this->user();

        // 套餐
        $rank = Arr::only($user['rank'], ['rank_name', 'permission']);

        // 次数权限配额
        $permissions_quota = [];
        $permissions = RankService::flattenPermission($user['rank']['permission'], '');
        $permissions_used = UserService::getAllPermissionsUsed($user['id']);
        foreach (array_keys(RankService::getPermissions()) as $permission_name) {
            $item = [
                'permission_name' => $permission_name,
                'limit' => $permissions[$permission_name] ?? 0,
                'used' => $permissions_used[$permission_name] ?? 0,
                'remaining' => 0,
                'reset_at' => UserService::getPermissionResetAt($user['id'], $permission_name, $user['created_at'], $user['vip_started_at'], $this->request->header('timezone')),
            ];
            $item['remaining'] = intval($item['limit'] - $item['used']);
            $item['remaining'] < 0 && $item['remaining'] = 0;
            $permissions_quota[] = $item;
        }

        // 团队相关
        $my_team = null;
        $teams = [];
        if (config('website.team')) {
            // 我的团队
            try {
                $my_team = Arr::only(TeamService::myTeam($user['id'])->toArray(), ['id', 'name', 'member_count']);
                $my_team['max_team_members'] = $user['rank']['max_team_members'];
            } catch (TeamException $e) {
            }

            // 我加入的团队
            $teams = TeamService::getTeams($user['id'], TeamMember::STATUS_ACTIVATED);
        }

        $data = [
            'user' => self::userInfo($user),
            'rank' => $rank,
            'permissions_quota' => $permissions_quota,
            'my_team' => $my_team,
            'teams' => $teams,
        ];
        return $this->response(200, 'success', $data);
    }

    /**
     * @PutApi(path="", description="编辑")
     * @FormData(key="language|language", rule="sometimes|required")
     * @FormData(key="username|username", rule="sometimes|required|string|max:255")
     * @FormData(key="company|company", rule="nullable|string|max:255")
     * @FormData(key="country|country", rule="nullable|string|max:255")
     * @FormData(key="province|province", rule="nullable|string|max:255")
     * @FormData(key="city|city", rule="nullable|string|max:255")
     * @FormData(key="postal|postal", rule="nullable|string|max:255")
     * @FormData(key="address|address", rule="nullable|string|max:255")
     * @FormData(key="phone|phone", rule="nullable|string|max:50")
     * @FormData(key="vat|vat", rule="nullable|string|max:255")
     * @FormData(key="password|password", rule="sometimes|required|string|min:32")
     * @FormData(key="comfirm_password|comfirm password", rule="required_with:password|same:password")
     * @FormData(key="email|email", rule="sometimes|required|email")
     */
    public function update()
    {
        $validator_data = Context::get('validator.data');

        $attributes = $validator_data;
        if (isset($attributes['comfirm_password'])) {
            unset($attributes['comfirm_password']);
        }
        if ($attributes) {
            $User = User::query()
                ->find($this->user()['id']);
            $User->update($attributes);

            // 激活邮箱账号
            if (isset($attributes['password'])) {
                UserSocialite::updateOrCreate([
                    'type' => UserSocialite::TYPE_EMAIL,
                    'socialite_id' => $User->email ?: $User->account,
                    'user_id' => $User->id,
                ]);
            }
        }
        return $this->response(200, 'success');
    }

    /**
     * @param array $value
     * @return array
     */
    public static function userInfo(array $value)
    {
        $user_info = array_merge(Arr::only($value, ['account', 'username', 'language', 'company', 'country', 'province', 'city', 'postal', 'address', 'phone', 'vat', 'distribution_code', 'created_at', 'email', 'delete_task_plan_executed_at', 'avatar']), [
            'rank_name' => $value['rank']['rank_name'],
            'last_rank_name' => UserService::getLastRankNameFromCache($value['id']),
        ]);
        foreach (['created_at', 'delete_task_plan_executed_at'] as $column) {
            if (!empty($user_info[$column])) {
                $user_info[$column] = Carbon::createFromTimeString($user_info[$column])->timestamp;
            }
        }
        return $user_info;
    }

    /**
     * @PostApi(path="delete-account-verification-code", description="删除账号-发送验证码邮件")
     */
    public function deleteAccountVerificationCode()
    {
        try {
            UserService::deleteAccountSendEmail($this->user()['id']);

            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="delete-account", description="删除账号")
     * @FormData(key="verification_code|verification code", rule="required|string|size:4")
     */
    public function deleteAccount()
    {
        $validator_data = Context::get('validator.data');
        try {
            UserService::deleteAccountJoinDeletionQueue($this->user()['id'], $validator_data['verification_code'], [
                'handler_type' => 'member',
                'handler' => $this->user()['account'],
            ]);

            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="restore-account", description="恢复账号")
     */
    public function restoreAccount()
    {
        $validator_data = Context::get('validator.data');
        try {
            UserService::deleteAccountRemoveDeletionQueue($this->user()['id'], [
                'handler_type' => 'member',
                'handler' => $this->user()['account'],
            ]);

            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }
}
