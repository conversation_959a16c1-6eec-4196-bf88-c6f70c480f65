<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Utils\Str;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="preview-email", tag="邮件预览", server="website-common-frontend-api")
 */
class PreviewEmailController extends AbstractController
{
    /**
     * @GetApi(path="forgot-password", description="忘记密码")
     */
    public function forgotPassword()
    {
        return (string) \Hyperf\ViewEngine\view('mail/forgot-password', [
            'reset_url' => env('WEBSITE_FRONTEND_BASE_URI') . "/resetPassword?type=forget&email=<EMAIL>&code=" . Str::random(32),
        ]);
    }
}
