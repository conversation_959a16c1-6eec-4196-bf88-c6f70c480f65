<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Hyperf\Utils\ApplicationContext;
use Psr\Http\Message\ServerRequestInterface;
use Website\Common\Model\Rank;
use Website\Common\Model\User;
use Website\Common\Service\RankService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerInterface;
use Qbhy\HyperfAuth\AuthManager;

abstract class AbstractController
{
    /**
     * @Inject
     * @var ContainerInterface
     */
    protected $container;
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;
    /**
     * @Inject
     * @var ResponseInterface
     */
    protected $response;
    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;

    /**
     * @param int $code
     * @param string $message
     * @param array $data
     * @param int $error_code
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function response(int $code, string $message, array $data = [], int $error_code = 0)
    {
        $data = array_merge($data, [
            'is_logged_in' => $this->authGuard()->check(),
        ]);
        return $this->response->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'error_code' => $error_code,
        ]);
    }

    /**
     * @return \Qbhy\HyperfAuth\AuthGuard
     */
    protected function authGuard()
    {
        if ($this->request->hasHeader(config('auth.guards.frontend-api-secret-key.header_name', 'secret-key'))) {
            return $this->auth->guard('frontend-api-secret-key');
        } else {
            return $this->auth->guard('frontend-api-sso');
        }
    }

    /**
     * @param User|null $user
     * @return array
     */
    protected function user($User = null)
    {
        if (is_null($User)) {
            $User = $this->authGuard()->user();
        }
        $Rank = Rank::findFromCache($User->rank_id);
        return array_merge($User->toArray(), [
            'rank' => $Rank ? $Rank->toArray() : null,
        ]);
    }

    /**
     * @return array|null
     */
    protected function rankFree()
    {
        $Rank = Rank::findFromCache(RankService::FREE);
        return $Rank ? $Rank->toArray() : null;
    }

    /**
     * 游客
     * @return array
     */
    protected function rankVistor()
    {
        $rank = [
            'rank_name' => 'Vistor',
            'permission' => RankService::structurePermission(),
        ];
        return $rank;
    }

    /**
     * 国际化语言
     * @return array
     */
    public static function languages()
    {
        return [
            [
                'title' => 'English',
                'value' => 'en',
            ],
            [
                'title' => '简体中文',
                'value' => 'zh',
            ],
            [
                'title' => '繁體中文',
                'value' => 'tw',
            ],
            [
                'title' => '한국어',
                'value' => 'ko',
            ],
            [
                'title' => '日本語',
                'value' => 'ja',
            ],
            [
                'title' => 'Português',
                'value' => 'pt',
            ],
            [
                'title' => 'Español',
                'value' => 'es',
            ],
            [
                'title' => 'Deutsch',
                'value' => 'de',
            ],
            [
                'title' => 'Français',
                'value' => 'fr',
            ],
            [
                'title' => 'Tiếng Việt',
                'value' => 'vi',
            ],
        ];
    }

    /**
     * 国际化语言的keys
     * @return array
     */
    public static function locales()
    {
        return array_map(function ($item) {
            return $item['value'];
        }, self::languages());
    }

    /**
     * 获取语言
     * @return string
     */
    public static function getLocale()
    {
        $locale = null;
        $ServerRequestInterface = ApplicationContext::getContainer()
            ->get(ServerRequestInterface::class);

        // header中的语言
        if ($ServerRequestInterface->hasHeader('locale')) {
            $locale = $ServerRequestInterface->header('locale');
        }

        // cookie保存的语言
        if (!$locale) {
            $locale_cookie_name = 'locale' . (env('APP_ENV') === 'prod' ? '' : ('-' . env('APP_ENV')));
            $locale = $ServerRequestInterface->cookie($locale_cookie_name);
            if (empty($locale)) {
                $locale = $ServerRequestInterface->cookie('locale', 'en');
            }
        }

        return $locale;
    }
}
