<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\UserException;
use Website\Common\Model\User;
use Website\Common\Service\UserService;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Context\Context;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="", tag="授权", server="website-common-frontend-api")
 */
class AuthController extends AbstractController
{
    /**
     * @PostApi(path="reset-password", description="通过验证码重置密码")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="password|password", rule="required|min:32")
     * @FormData(key="comfirm_password|comfirm password", rule="required|same:password")
     * @FormData(key="code|code", rule="required")
     */
    public function resetPassword()
    {
        try {
            $validator_data = Context::get('validator.data');
            UserService::resetPasswordByCode($validator_data['email'], $validator_data['password'], $validator_data['code']);
            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="forgot-password", description="忘记密码")
     * @FormData(key="email|email", rule="required|email")
     */
    public function forgotPassword()
    {
        try {
            $validator_data = Context::get('validator.data');
            UserService::sendForgotPasswordEmail($validator_data['email']);
            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @DeleteApi(path="logout", description="注销登录")
     */
    public function logout()
    {
        $this->authGuard()->logout();
        return $this->response(200, 'success');
    }

    /**
     * @PostApi(path="login", description="登录")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="password|password", rule="required|min:32")
     */
    public function login()
    {
        try {
            $validator_data = Context::get('validator.data');
            $User = UserService::loginByEmail($validator_data['email'], $validator_data['password']);
            $this->authGuard()->login($User);
            return $this->response(200, 'success', [
                'user' => UserController::userInfo($this->user($User)),
            ]);
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                if ($e->getCode() !== ErrorCode::USER_EMAIL_NOT_REGISTERED) {
                    // 手动登录失败逻辑
                    $User = User::query()
                        ->where('account', $validator_data['email'])
                        ->first();
                    $User && $this->authGuard()->markLogin($User->id, UserService::LOGIN_MODE_MANUAL, false, $e->getMessage());
                }

                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="signup", description="通过邮箱注册")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="password|password", rule="required|min:32")
     * @FormData(key="comfirm_password|comfirm password", rule="required|same:password")
     * @FormData(key="username|username", rule="sometimes|required|string|max:255")
     * @FormData(key="company|company", rule="nullable|string|max:255")
     * @FormData(key="source|source", rule="")
     * @FormData(key="distribution_code|distribution_code", rule="")
     */
    public function singup()
    {
        try {
            $validator_data = Context::get('validator.data');
            $others = [
                'source' => $validator_data['source'] ?? '',
                'language' => $this->request->hasCookie(Website::getLocaleCookieName()) ?
                    $this->request->cookie(Website::getLocaleCookieName()) :
                    ($this->request->hasHeader('accept-language') ? explode(',', $this->request->header('accept-language'))[0] : null),
                'distribution_code' => strtoupper(strval($validator_data['distribution_code'] ?? '')),
            ];
            isset($validator_data['username']) && $others['username'] = $validator_data['username'];
            isset($validator_data['company']) && $others['company'] = $validator_data['company'];
            $User = UserService::signupByEmail($validator_data['email'], $validator_data['password'], $others);
            $validate_email = Website::config()->get('website.user_account.register.validate_email', true);
            if ((!$validate_email || ($validate_email && $User->email_verified_at)) && $User->account_status === User::ACCOUNT_STATUS_ACTIVATED) {
                $this->authGuard()->login($User);
                return $this->response(200, 'success', [
                    'user' => UserController::userInfo($this->user($User)),
                ]);
            } else {
                return $this->response(200, 'success');
            }
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="resend-activate-email", description="重新发送激活邮件")
     * @FormData(key="email|email", rule="required|email")
     */
    public function resendActivateEmail()
    {
        try {
            $validator_data = Context::get('validator.data');
            $User = User::query()
                ->where('account', $validator_data['email'])
                ->first(['id', 'account']);
            if (!$User) {
                return $this->response(400, 'The email account not registered.');
            }
            UserService::sendActivateEmail((int) $User->id, (string) $User->account);
            return $this->response(200, 'success');
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * @PostApi(path="complete-signup", description="完成注册")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="activate_code|activate code", rule="required|string|size:4")
     */
    public function completeSingup()
    {
        try {
            $validator_data = Context::get('validator.data');
            $User = UserService::completeSignupByEmail($validator_data['email'], $validator_data['activate_code']);
            $this->authGuard()->login($User);
            return $this->response(200, 'success', [
                'new_user' => $User->getNewUser(),
                'user' => UserController::userInfo($this->user($User)),
            ]);
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage());
            }
            throw $e;
        }
    }
}
