<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Controller;

use Website\Common\Exception\UserException;
use Website\Common\Service\UserService;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Context\Context;
use Website\Common\Utils\Website;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="auth-email", tag="邮箱授权", server="website-common-frontend-api")
 */
class AuthEmailController extends AbstractController
{
    /**
     * @PostApi(path="login", description="注册登录")
     * @FormData(key="email|email", rule="required|email")
     * @FormData(key="source|source", rule="")
     * @FormData(key="distribution_code|distribution_code", rule="")
     */
    public function login()
    {
        try {
            $validator_data = Context::get('validator.data');
            if (!Website::config()->get('website.user_account.register.email_without_password', false)) {
                return $this->response(400, 'The interface is not available.');
            }
            $User = UserService::loginByEmailWithoutPassword($validator_data['email'], [
                'source' => $validator_data['source'] ?? '',
                'language' => $this->request->hasCookie(Website::getLocaleCookieName()) ?
                    $this->request->cookie(Website::getLocaleCookieName()) :
                    ($this->request->hasHeader('accept-language') ? explode(',', $this->request->header('accept-language'))[0] : null),
                'distribution_code' => strtoupper(strval($validator_data['distribution_code'] ?? '')),
            ]);
            $this->authGuard()->login($User);
            return $this->response(200, 'success', [
                'user' => UserController::userInfo($this->user($User)),
                'new_user' => $User->getNewUser(),
            ]);
        } catch (\Throwable $e) {
            if ($e instanceof UserException) {
                return $this->response($e->getCode(), $e->getMessage(), [], $e->getCode());
            }
            throw $e;
        }
    }
}
