<?php
declare(strict_types=1);

namespace Website\Common\FrontendApi\Auth\Guard;

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Context;
use Hyperf\Utils\Str;
use Qbhy\HyperfAuth\Authenticatable;
use Qbhy\HyperfAuth\Exception\AuthException;
use Qbhy\HyperfAuth\Exception\UnauthorizedException;
use Qbhy\HyperfAuth\Guard\AbstractAuthGuard;
use Qbhy\HyperfAuth\UserProvider;

class SecretKeyGuard extends AbstractAuthGuard
{
    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * 请求头
     * @var string
     */
    protected $headerName = 'secret-key';

    /**
     * SecretKeyGuard constructor.
     * @param array $config
     * @param string $name
     * @param UserProvider $userProvider
     * @param RequestInterface $request
     */
    public function __construct(
        array $config,
        string $name,
        UserProvider $userProvider,
        RequestInterface $request
    ) {
        parent::__construct($config, $name, $userProvider);
        if ($config['header_name'] ?? '') {
            $this->headerName = $config['header_name'];
        }
        $this->request = $request;
    }

    /**
     * @param Authenticatable $user
     */
    public function login(Authenticatable $user)
    {
        // TODO: Implement login() method.
    }

    /**
     *
     */
    public function logout()
    {
        // TODO: Implement logout() method.
    }

    /**
     * @return mixed
     */
    public function parseToken()
    {
        return $this->request->header($this->headerName, null);
    }

    /**
     * @param string|null $token
     * @return Authenticatable|null
     * @throws \Throwable
     */
    public function user(?string $token = null): ?Authenticatable
    {
        $token = $token ?? $this->parseToken();

        try {
            if ($token) {
                $user = $this->userProvider->retrieveByCredentials($token);
                return $user;
            }

            throw new UnauthorizedException('The token is required.', $this);
        } catch (\Throwable $exception) {
            $newException = $exception instanceof AuthException ? $exception : new UnauthorizedException(
                $exception->getMessage(),
                $this,
                $exception
            );
            throw $newException;
        }
    }
}
