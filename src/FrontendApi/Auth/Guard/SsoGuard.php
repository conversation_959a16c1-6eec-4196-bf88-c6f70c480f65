<?php

declare(strict_types=1);
namespace Website\Common\FrontendApi\Auth\Guard;

use Hyperf\HttpServer\Contract\RequestInterface;
use Qbhy\HyperfAuth\UserProvider;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\UserException;
use Website\Common\FrontendApi\Auth\SSOManager;
use Website\Common\Model\Rank;
use Website\Common\Service\UserService;
use Hyperf\Context\Context;
use Hyperf\HttpMessage\Cookie\Cookie;
use Hyperf\Utils\Str;
use Psr\Http\Message\ResponseInterface;
use Qbhy\HyperfAuth\Authenticatable;
use Qbhy\HyperfAuth\Events\ForcedOfflineEvent;
use Qbhy\HyperfAuth\Guard\SsoGuard as BaseSsoGuard;
use Website\Common\Utils\Event;

class SsoGuard extends BaseSsoGuard
{
    /**
     * SsoGuard constructor.
     * @param array $config
     * @param string $name
     * @param UserProvider $userProvider
     * @param RequestInterface $request
     */
    public function __construct(array $config, string $name, UserProvider $userProvider, RequestInterface $request)
    {
        parent::__construct($config, $name, $userProvider, $request);

        $this->jwtManager = new SSOManager($config);
    }

    /**
     * 设置请求者信息
     * @param array $requester
     */
    public function setRequester(array $requester)
    {
        Context::set('SsoGuardRequester', $requester);
        if (!empty($requester['session_id'])) {
            $this->setSessionId($requester['session_id']);
        }
    }

    /**
     * 获取请求者信息
     * @return array|null
     */
    public function getRequester()
    {
        return Context::get('SsoGuardRequester', null);
    }

    /**
     * 设置会话id
     * @param string $session_id
     * @return string
     */
    public function setSessionId($session_id)
    {
        Context::set('SsoGuardSessionId', $session_id);
    }

    /**
     * 获取会话id
     * @return string
     */
    public function getSessionId()
    {
        return Context::get('SsoGuardSessionId', null);
    }

    /**
     * @return string
     */
    protected function getAccessTokenCookieName()
    {
        return strtoupper(env('APP_NAME', 'HYPERF') . '_ACCESS_TOKEN') . (env('APP_ENV') === 'prod' ? '' : ('-' . env('APP_ENV')));
    }

    /**
     * @param Authenticatable $user
     * @return string|string[]
     */
    protected function getRedisTokenKey(Authenticatable $user)
    {
        return str_replace('{uid}', (string) $user->getId(), $this->config['redis_key'] ?? 'u:token:{uid}');
    }

    /**
     * @param Authenticatable $user
     * @return string|string[]
     */
    protected function getRedisSessionKey(Authenticatable $user)
    {
        return str_replace('{uid}', (string) $user->getId(), $this->config['redis_session_key'] ?? 'u:session:{uid}');
    }

    /**
     * @param Authenticatable $user
     * @param string|null $client
     * @throws \Qbhy\SimpleJwt\Exceptions\InvalidTokenException
     * @throws \Qbhy\SimpleJwt\Exceptions\SignatureException
     * @throws \Qbhy\SimpleJwt\Exceptions\TokenExpiredException
     */
    protected function forceOffline(Authenticatable $user, string $client = null)
    {
        $client = $client ?: $this->getClients()[0];
        $redis_token_key = $this->getRedisTokenKey($user);
        $redis_session_key = $this->getRedisSessionKey($user);
        $total = $this->redis->zCard($redis_token_key);
        $Rank = Rank::findFromCache($user->rank_id);
        $limit = $Rank && $Rank->max_online_users > 0 ? $Rank->max_online_users : 100;
        if ($total > $limit) {
            // 超出同时在线数的部分，就给他拉黑，也就是强制下线
            foreach ($this->redis->zRevRange($redis_token_key, $limit, $total - 1) as $value) {
                Context::set($this->resultKey($value), null);
//                $this->getJwtManager()->addBlacklist($this->getJwtManager()->justParse($value));
                $this->eventDispatcher->dispatch(new ForcedOfflineEvent($user, $client));
                $this->redis->zRem($redis_token_key, $value);
                $this->redis->hDel($redis_session_key, $value);
            }
        }
    }

    /**
     * @param Authenticatable $user
     * @param array $payload
     * @param string|null $client
     * @return string
     * @throws \Qbhy\SimpleJwt\Exceptions\InvalidTokenException
     * @throws \Qbhy\SimpleJwt\Exceptions\SignatureException
     * @throws \Qbhy\SimpleJwt\Exceptions\TokenExpiredException
     */
    public function login(Authenticatable $user, array $payload = [], string $client = null)
    {
        if (!$user->account_status) {
            throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
        }

        $now = time();
        $redis_token_key = $this->getRedisTokenKey($user);
        $redis_session_key = $this->getRedisSessionKey($user);
        $token = $this->getJwtManager()->make(array_merge($payload, [
            'uid' => $user->getId(),
            's' => str_random(),
        ]))->token();
        Context::set($this->resultKey($token), $user);
        $this->redis->zAdd($redis_token_key, ['NX'], $now, $token);

        $this->forceOffline($user, $client);

        $this->setCookie($this->getAccessTokenCookieName(), $token, $now + $this->getJwtManager()->getTtl());
        $this->redis->hSet($redis_session_key, $token, $this->getSessionId());
        $this->markLogin($user->getId(), UserService::LOGIN_MODE_MANUAL, true, 'success');

        return $token;
    }

    /**
     * @param null $token
     * @return bool
     * @throws \Qbhy\SimpleJwt\Exceptions\InvalidTokenException
     * @throws \Qbhy\SimpleJwt\Exceptions\SignatureException
     * @throws \Qbhy\SimpleJwt\Exceptions\TokenExpiredException
     * @throws \Throwable
     */
    public function logout($token = null)
    {
        if ($token = $token ?? $this->parseToken()) {
            /*$this->getJwtManager()->addBlacklist(
                $this->getJwtManager()->parse($token)
            );*/

            $user = $this->user($token);
            $redis_token_key = $this->getRedisTokenKey($user);
            $redis_session_key = $this->getRedisSessionKey($user);
            $this->redis->zRem($redis_token_key, $token);
            $this->redis->hDel($redis_session_key, $token);

            $this->deletedToken($token);

            // 用户注销登录事件
            Event::produce('auth.logout', [
                'user_id' => $user->getId(),
            ]);

            return true;
        }
        return false;
    }

    /**
     * @param null $token
     * @return bool
     */
    public function deletedToken($token = null)
    {
        if ($token = $token ?? $this->parseToken()) {
            Context::set($this->resultKey($token), null);
            if (!$this->getRequester()) {
                $this->setCookie($this->getAccessTokenCookieName(), $token, time());
            }
            $this->simulateSessionStart(true);
            return true;
        }
        return false;
    }

    /**
     * @return string|null
     */
    public function parseToken()
    {
        if ($requester = $this->getRequester()) {
            return $requester['token'] ?? null;
        } elseif ($this->request->hasCookie($this->getAccessTokenCookieName())) {
            return $this->request->cookie($this->getAccessTokenCookieName());
        }

        return parent::parseToken();
    }

    /**
     * @param string|null $token
     * @return Authenticatable|null
     * @throws \Throwable
     */
    public function user(?string $token = null): ?Authenticatable
    {
        $token = $token ?? $this->parseToken();
        $user = parent::user($token);

        if ($user instanceof Authenticatable && $user->account_status) {
            $redis_session_key = $this->getRedisSessionKey($user);
            if (!$this->redis->hExists($redis_session_key, $token)) {
                // 避免更新规则后，导致原有效key不在当前列表中
                $redis_token_key = $this->getRedisTokenKey($user);
                $this->redis->zAdd($redis_token_key, ['NX'], time(), $token);
                $this->forceOffline($user);
            }
            if ($this->redis->hGet($redis_session_key, $token) !== $this->getSessionId()) {
                $this->redis->hSet($redis_session_key, $token, $this->getSessionId());
                $this->markLogin($user->getId(), UserService::LOGIN_MODE_AUTOMATIC, true, 'success');
            }
        }

        return $user;
    }

    /**
     * 登录日志
     * @param int $user_id
     * @param string $login_mode
     * @param bool $login_status
     * @param string $login_message
     * @throws \Exception
     */
    public function markLogin(int $user_id, string $login_mode, bool $login_status, string $login_message)
    {
        if ($this->redis->set(env('APP_NAME', 'HYPERF') . ":mark:user:login:{$user_id}", 1, ['NX', 'EX' => 2])) {
            $requester = $this->getRequester();
            $accept_language = $requester ? $requester['accept_language'] : $this->request->header('accept-language');
            $others = [
                'session_id' => $this->getSessionId(),
                'device_agent' => $requester ? $requester['user_agent'] : $this->request->header('user-agent'),
                'login_mode' => $login_mode,
                'login_status' => $login_status,
                'login_message' => $login_message,
                'device_language' => $accept_language ? explode(',', $accept_language)[0] : null,
                'device_timezone' => $requester ? $requester['timezone'] : ($this->request->header('timezone') ?: $this->request->cookie('timezone')),
            ];
            $ip = $requester ? $requester['user_ip'] : $this->request->getAttribute('client-ip');
            go(function () use ($user_id, $ip, $others) {
                UserService::markLogin($user_id, $ip, $others);
            });
        }
    }

    /**
     * 模拟一个session_id
     * @param bool $force
     */
    public function simulateSessionStart($force = false)
    {
        $requester = $this->getRequester();
        $session_id = $force ? null : ($requester ? ($requester['session_id'] ?? null) : $this->request->cookie($this->getSessionCookieName(), null));
        if (!(is_string($session_id) && ctype_alnum($session_id) && strlen($session_id) === 40)) {
            $session_id = Str::random(40);
        }
        $this->setSessionId($session_id);
        if (!$requester) {
            $this->setCookie($this->getSessionCookieName(), $session_id, time() + 1800);
        }
    }

    /**
     * @return string
     */
    protected function getSessionCookieName(): string
    {
        return strtoupper(env('APP_NAME', 'HYPERF') . '_SESSION_ID') . (env('APP_ENV') === 'prod' ? '' : ('-' . env('APP_ENV')));
    }

    /**
     * @param $cookie_name
     * @param $value
     * @param $expire
     */
    protected function setCookie($cookie_name, $value, $expire)
    {
        // 根据优先级，从请求头中获取域名：x-forwarded-host、origin
        $request_domain = null;
        if (!empty($this->request->getHeaders()['x-forwarded-host'][0])) {
            $request_domain = explode(':', $this->request->getHeaders()['x-forwarded-host'][0])[0];
        } elseif (!empty($this->request->getHeaders()['origin'][0])) {
            $request_domain = explode(':', explode('://', $this->request->getHeaders()['origin'][0])[1])[0];
        }

        // dev环境，针对localhost的特殊处理，方便前端开发
        $uri = $this->request->getUri();
        if (env('APP_ENV') === 'dev' && $request_domain === 'localhost') {
            $domain = $request_domain;
        } else {
            $domain = env('WEBSITE_FRONTEND_COOKIE_DOMAIN');
            if (!$domain) {
                $domain = $request_domain;

                // 当上面规则获取不到cookie域名，或者获取到的cookie域名，不是一个有效的IP地址或者域名时，使用host作为cookie域名
                if (!$domain || ($domain !== 'localhost' && !filter_var($domain, FILTER_VALIDATE_IP) && !preg_match('/^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z]{2,})+$/', $domain))) {
                    $domain = $uri->getHost();
                }
            }
        }

        $cookie = new Cookie(
            $cookie_name,
            $value,
            $expire,
            '/',
            $domain,
            strtolower($uri->getScheme()) === 'https',
            false
        );
        $response = Context::get(ResponseInterface::class);
        if (!method_exists($response, 'withCookie')) {
            $response = $response->withHeader('Set-Cookie', (string) $cookie);
        } else {
            /* @var \Hyperf\HttpMessage\Server\Response $response */
            $response = $response->withCookie($cookie);
        }
        Context::set(ResponseInterface::class, $response);
    }
}
