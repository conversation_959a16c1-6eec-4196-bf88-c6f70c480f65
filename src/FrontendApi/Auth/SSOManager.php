<?php
declare(strict_types=1);
namespace Website\Common\FrontendApi\Auth;

use Hyperf\Redis\Redis;
use Qbhy\SimpleJwt\Exceptions\InvalidTokenException;
use Qbhy\SimpleJwt\Exceptions\SignatureException;
use Qbhy\SimpleJwt\JWT;

class SSOManager extends \Qbhy\SimpleJwt\JWTManager
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var Redis
     */
    protected $redis;

    /**
     * SSOManager constructor.
     * @param array $config
     */
    public function __construct(array $config)
    {
        parent::__construct($config);

        $this->config = $config;

        // 初始化redis实例
        $this->redis = is_callable($config['redis']) ? call_user_func_array($config['redis'], []) : make(Redis::class);
    }

    /**
     * @param string $token
     * @return JWT
     * @throws InvalidTokenException
     * @throws SignatureException
     */
    public function justParse(string $token): \Qbhy\SimpleJwt\JWT
    {
        $encoder = $this->getEncoder();
        $encrypter = $this->getEncrypter();
        $arr = explode('.', $token);

        if (count($arr) !== 3) {
            throw new InvalidTokenException('Invalid token');
        }

        $headers = @json_decode($encoder->decode($arr[0]), true);
        $payload = @json_decode($encoder->decode($arr[1]), true);

        $signatureString = "{$arr[0]}.{$arr[1]}";

        if (! is_array($headers) || ! is_array($payload)) {
            throw new InvalidTokenException('Invalid token');
        }

        if (!empty($payload['uid'])) {
            $redis_key = str_replace('{uid}', (string) $payload['uid'], $this->config['redis_key'] ?? 'u:token:{uid}');
            if ($this->redis->zScore($redis_key, $token) !== false) {
                return new JWT($this, $headers, $payload);
            }
        }

        throw new SignatureException('Invalid signature');
    }
}
