<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Middleware;

use Website\Common\Constants\ErrorCode;
use Website\Common\Utils\Website;
use Website\Common\FrontendApi\Auth\Guard\SsoGuard;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Str;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Qbhy\HyperfAuth\AuthManager;

/**
 * 防重复提交
 * @package App\FrontendApi\Middleware
 */
class ReSubmitMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var \Hyperf\HttpServer\Contract\ResponseInterface
     */
    protected $response;

    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if ($this->isExcepted()) {
            return $handler->handle($request);
        }

        if (in_array($this->request->getMethod(), ['POST', 'PUT', 'DELETE']) &&
            !$request->hasHeader(config('auth.guards.frontend-api-secret-key.header_name', 'secret-key'))) {
            /** @var SsoGuard $guard */
            $guard = $this->auth->guard('frontend-api-sso');
            $key =  env('APP_NAME') . ':resubmit:' . md5($this->request->getPathInfo() . ':' . $guard->getSessionId() . ($guard->check() ? $guard->id() : 0));
            if (!Website::FrontendRedis()->set($key, 1, ['NX', 'EX' => 1])) {
                return $this->response->json([
                    'code' => ErrorCode::REQUEST_TOO_FAST,
                    'message' => ErrorCode::getMessage(ErrorCode::REQUEST_TOO_FAST),
                    'data' => [],
                ]);
            }
        }

        return $handler->handle($request);
    }

    /**
     * @return bool
     */
    protected function isExcepted()
    {
        $arr = array_merge([
            'POST:/v1/stripe/webhook',
        ], Website::config()->get('website.frontend.middlewares.resubmit.except', []));
        $uri_path = $this->request->getUri()->getPath();
        foreach ($arr as $except) {
            $methods = [];
            if (Str::contains($except, ':')) {
                list($methods, $except) = explode(':', $except);
                $methods = explode(',', $methods);
            }
            $methods = array_map('strtoupper', $methods);

            if (($uri_path === $except || @preg_match("/^{$except}$/", $uri_path)) &&
                (empty($methods) || in_array($this->request->getMethod(), $methods))) {
                return true;
            }
        }

        return false;
    }
}
