<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Middleware;

use Hyperf\Contract\TranslatorInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Context\Context;
use Lete\Base\Utils\IP;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class CoreMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var TranslatorInterface
     */
    protected $translator;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        /*$mapping = [
            'ZN' => 'zh_CN',
            'EN' => 'en',
        ];
        $key = $this->request->cookie('localeKey', 'EN');
        $this->translator->setLocale($mapping[$key] ?: 'en');*/
        $this->translator->setLocale('en');

        //用户ip
        $request = Context::set(ServerRequestInterface::class, $request->withAttribute('client-ip', IP::get($this->request)));

        return $handler->handle($request);
    }
}
