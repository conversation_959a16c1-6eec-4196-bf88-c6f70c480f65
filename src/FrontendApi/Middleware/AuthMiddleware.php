<?php

declare(strict_types=1);

namespace Website\Common\FrontendApi\Middleware;

use Website\Common\FrontendApi\Controller\AbstractController;
use Website\Common\Exception\UserException;
use Website\Common\Model\User;
use Website\Common\Service\UserService;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Str;
use Qbhy\HyperfAuth\Authenticatable;
use Qbhy\HyperfAuth\AuthMiddleware as BaseAuthMiddleware;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Qbhy\HyperfAuth\Exception\UnauthorizedException;
use Qbhy\SimpleJwt\Exceptions\TokenBlacklistException;
use Qbhy\SimpleJwt\Exceptions\TokenExpiredException;
use Qbhy\SimpleJwt\Exceptions\TokenNotActiveException;
use Qbhy\SimpleJwt\JWT;
use Website\Common\Utils\Website;

/**
 * 身份认证中间件
 * Class AuthMiddleware
 * @package App\FrontendApi\Middleware
 */
class AuthMiddleware extends BaseAuthMiddleware
{
    protected $guards = ['frontend-api-secret-key', 'frontend-api-sso'];

    /**
     * @Inject
     * @var ConfigInterface
     */
    protected $config;

    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var \Hyperf\HttpServer\Contract\ResponseInterface
     */
    protected $response;

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if ($request->hasHeader(config('auth.guards.frontend-api-secret-key.header_name', 'secret-key'))) {
            return $this->handleFrontendApiSecretKeyProcess($request, $handler);
        } else {
            return $this->handleFrontendApiSsoProcess($request, $handler);
        }
    }

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    protected function handleFrontendApiSecretKeyProcess(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $AuthGuard = $this->auth->guard('frontend-api-secret-key');

        if ($this->isExcepted()) {
            return $handler->handle($request);
        }

        try {
            $user = $AuthGuard->user();

            if (! $user instanceof Authenticatable) {
                throw new UnauthorizedException("Without authorization from {$AuthGuard->getName()} guard.", $AuthGuard);
            }

            if (in_array($user->account_status, [User::ACCOUNT_STATUS_DISABLED, User::ACCOUNT_STATUS_DELETED])) {
                throw new UserException(401, 'Your account is temporarily unavailable, please contact us.');
            }
        } catch (\Throwable $e) {
            return $this->response->json([
                'code' => $e->getCode(),
                'message' => $e->getPrevious() instanceof TokenBlacklistException ? 'Token expired' : $e->getMessage(),
                'data' => [],
                'error_code' => 0,
            ]);
        }

        return $handler->handle($request);
    }

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    protected function handleFrontendApiSsoProcess(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $AuthGuard = $this->auth->guard('frontend-api-sso');
        $AuthGuard->simulateSessionStart();

        if ($this->isExcepted()) {
            return $handler->handle($request);
        }

        try {
            $user = $AuthGuard->user();

            if (! $user instanceof Authenticatable) {
                throw new UnauthorizedException("Without authorization from {$AuthGuard->getName()} guard.", $AuthGuard);
            }

            if (in_array($user->account_status, [User::ACCOUNT_STATUS_DISABLED, User::ACCOUNT_STATUS_DELETED])) {
                throw new UserException(401, 'Your account is temporarily unavailable, please contact us.');
            }

            // 更改用户语言
            if ($this->request->hasCookie(Website::getLocaleCookieName()) && $user->language !== $this->request->cookie(Website::getLocaleCookieName())) {
                $user->language = $this->request->cookie(Website::getLocaleCookieName());
                $user->save();
            }
        } catch (\Throwable $e) {
            // 自动登录失败逻辑
            if ($e->getPrevious() instanceof TokenBlacklistException ||
                $e->getPrevious() instanceof TokenExpiredException ||
                $e->getPrevious() instanceof TokenNotActiveException ||
                $e instanceof UserException
            ) {
                /* @var JWT $JWT */
                $JWT = $AuthGuard->getJwtManager()->justParse($AuthGuard->parseToken());
                $AuthGuard->markLogin($JWT->getPayload()['uid'], UserService::LOGIN_MODE_AUTOMATIC, false, $e->getMessage());
                $AuthGuard->deletedToken();
            }

            return $this->response->json([
                'code' => $e->getCode(),
                'message' => $e->getPrevious() instanceof TokenBlacklistException ? 'Token expired' : $e->getMessage(),
                'data' => [],
                'error_code' => 0,
            ]);
        }

        return $handler->handle($request);
    }

    /**
     * 判断当前uri是否不需认证
     * @return bool
     */
    protected function isExcepted()
    {
        $locales_str = implode('|', AbstractController::locales());
        $arr = array_merge([
            "\/({$locales_str})*",
            "(\/({$locales_str})*)*\/terms-of-service",
            "(\/({$locales_str})*)*\/privacy-policy",
            "(\/({$locales_str})*)*\/subscription-refund-policy",
            "(\/({$locales_str})*)*\/rank",
            "(\/({$locales_str})*)*\/affiliate",
            '\/preview-email\/.*',
            'POST:/v1/stripe-webhook',
            '\/v1\/signup',
            '\/v1\/login',
            '\/v1\/forgot-password',
            '\/v1\/reset-password',
            '\/v1\/resend-activate-email',
            '\/v1\/complete-signup',
            '\/v1\/auth-google\/.*',
            '\/v1\/auth-email\/.*',
            '\/v1\/distribution\/click',
            '\/v1\/price',
        ], Website::config()->get('website.auth.except', []));
        $uri_path = $this->request->getUri()->getPath();
        foreach ($arr as $except) {
            $methods = [];
            if (Str::contains($except, ':')) {
                list($methods, $except) = explode(':', $except);
                $methods = explode(',', $methods);
            }
            $methods = array_map('strtoupper', $methods);

            if (($uri_path === $except || @preg_match("/^{$except}$/", $uri_path)) &&
                (empty($methods) || in_array($this->request->getMethod(), $methods))) {
                return true;
            }
        }

        return false;
    }
}
