<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $type
 * @property float $money
 * @property float $current_assets
 * @property int $user_id
 * @property string $created_at
 * @property string $updated_at
 */
class DistributionCommission extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'distribution_commission';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * 类型-结算
     * @var int
     */
    const DEL_TYPE = 1;

    /**
     * 类型-收入
     * @var int
     */
    const ADD_TYPE = 2;


    /**
     * 类型-结算撤销
     * @var int
     */
    const ADD_SETTLEMENT_TYPE = 3;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'money' => 'float',
        'current_assets' => 'float',
        'user_id' => 'integer',
    ];

}
