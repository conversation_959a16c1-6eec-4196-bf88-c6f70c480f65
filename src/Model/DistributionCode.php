<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $distribution_code
 * @property int $status
 * @property array $rank_data
 * @property string $created_at
 * @property string $updated_at
 */
class DistributionCode extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'distribution_code';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * @var array
     */
    protected $guarded = ['id'];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'status' => 'integer',
        'commission_rate' => 'integer',
        'user_id' => 'integer',
        'rank_data' => 'json',
    ];
    /**
     * 默认分销比例
     * @var string|null
     */
    const COMMISSION_RATE = 30;

}
