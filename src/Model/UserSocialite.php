<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $type
 * @property string $socialite_id
 * @property array $socialite_raw_data
 * @property int $user_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserSocialite extends Model
{
    /**
     * 登录类型-邮箱
     * @var int
     */
    const TYPE_EMAIL = 1;

    /**
     * 登录类型-手机号
     * @var int
     */
    const TYPE_PHONE_NUMBER = 2;

    /**
     * 登录类型-谷歌账号
     * @var int
     */
    const TYPE_GOOGLE = 3;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_socialites';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['socialite_raw_data' => 'json', 'id' => 'integer', 'type' => 'integer', 'user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * 获取登录类型的名称
     * @param int $type 登录类型
     * @param $type
     * @return string|null
     */
    public static function getTitle($type)
    {
        return [
            self::TYPE_EMAIL => '邮箱',
            self::TYPE_PHONE_NUMBER => '手机号',
            self::TYPE_GOOGLE => '谷歌',
        ][$type] ?? null;
    }
}
