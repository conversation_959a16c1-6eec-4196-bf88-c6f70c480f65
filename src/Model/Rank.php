<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Website\Common\Service\RankService;
use Hyperf\Database\Model\Events\Saved;
use Hyperf\Database\Model\Events\Updated;
use Hyperf\Utils\Arr;
use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property string $rank_name
 * @property string $duration
 * @property float $price
 * @property float $original_price
 * @property float $first_price
 * @property int $trial_days
 * @property bool $allowed_buy
 * @property bool $is_visibled
 * @property int $max_online_users
 * @property int $max_team_members
 * @property array $permission
 * @property string $stripe_product_id
 * @property string $stripe_price_id
 * @property string $stripe_coupon_id
 * @property string $product_name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $remark
 */
class Rank extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ranks';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['max_team_members' => 'integer', 'max_online_users' => 'integer', 'is_visibled' => 'bool', 'allowed_buy' => 'bool', 'trial_days' => 'integer', 'permission' => 'json', 'first_price' => 'float', 'original_price' => 'float', 'price' => 'float', 'id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @var array
     */
    protected $attributes = [
        'trial_days' => 0,
        'allowed_buy' => 0,
        'is_visibled' => 0,
        'max_online_users' => 0,
        'max_team_members' => 0,
    ];

    /**
     * Get the expire time for cache.
     */
    public function getCacheTTL(): ?int
    {
        return 300;
    }

    /**
     * @param $value
     */
    public function setPermissionAttribute($value)
    {
        if (is_array($value)) {
            // 剔除不合法的key
            $value = Arr::only($value, array_keys(RankService::structurePermission()));

            // 根据权限结构的键值类型，强制转换类型
            foreach (Arr::dot(RankService::structurePermission()) as $key => $key_value) {
                $type = gettype($key_value);
                $permission_key_value = Arr::get($value, $key);
                if (!is_null($permission_key_value) && $type !== gettype($permission_key_value)) {
                    switch ($type) {
                        case 'integer':
                            Arr::set($value, $key, intval($permission_key_value));
                            break;
                        case 'boolean':
                            Arr::set($value, $key, boolval($permission_key_value));
                            break;
                        default:
                            break;
                    }
                }
            }

            $this->attributes['permission'] = json_encode($value);;
        } else {
            $this->attributes['permission'] = null;
        }
    }

    /**
     * @param $value
     */
    public function setFirstPriceAttribute($value)
    {
        $this->attributes['first_price'] = empty($value) ? null : $value;
    }

    /**
     * @param $value
     */
    public function setRankNameAttribute($value)
    {
        $this->attributes['rank_name'] = $value;
        if ($value && $this->duration) {
            $this->product_name = RankService::generateProductName($value, $this->duration);
        }
    }

    /**
     * @param $value
     */
    public function setDurationAttribute($value)
    {
        $this->attributes['duration'] = $value;
        if ($this->rank_name && $value) {
            $this->product_name = RankService::generateProductName($this->rank_name, $value);
        }
    }

    /**
     * @param $value
     * @return array
     */
    public function getPermissionAttribute($value)
    {
        $permission = $value ? json_decode($value, true) : [];
        $permission = Arr::merge(RankService::structurePermission(), $permission);
        return $permission;
    }

    /**
     * @param Saved $event
     */
    public function saved(Saved $event)
    {
        // 相同等级，自动同步权限、允许购买（前台）、前台展示、最大同时在线人数、团队最大成员数
        if ($this->wasChanged(['permission', 'allowed_buy', 'is_visibled', 'max_online_users', 'max_team_members'])) {
            Rank::query(true)
                ->where('rank_name', $this->rank_name)
                ->where('id', '<>', $this->id)
                ->update([
                    'permission' => $this->permission ? json_encode($this->permission) : null,
                    'allowed_buy' => $this->allowed_buy,
                    'is_visibled' => $this->is_visibled,
                    'max_online_users' => $this->max_online_users,
                    'max_team_members' => $this->max_team_members,
                ]);
        }

        RankService::flushPlanIds();
    }

    /**
     * @param Updated $event
     */
    public function updated(Updated $event)
    {
        RankService::flushPlanIds();
    }
}
