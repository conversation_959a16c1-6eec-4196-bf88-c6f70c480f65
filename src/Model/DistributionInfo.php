<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property string $account
 * @property int $user_id
 * @property string $distribution_code
 * @property string $customize_code
 * @property int $status
 * @property string $application_reason
 * @property int $application_time
 * @property int $review_time
 * @property string $paypal
 * @property string $remark
 * @property int $order_effect_num
 * @property int $order_invalid_num
 * @property double $confirmed_commission
 * @property double $not_confirmed_commission
 * @property double $assets
 * @property double $cancel_commission
 * @property double $settlement_commission
 * @property int $pay_customer
 * @property int $click_num
 * @property int $register_num
 * @property int $income_num
 * @property int $commission_rate
 * @property string $created_at
 * @property string $updated_at
 */
class DistributionInfo extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'distribution_info';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * @var array
     */
    protected $guarded = ['id'];


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'status' => 'integer',
        'application_time' => 'datetime',
        'review_time' => 'datetime',
        'order_effect_num' => 'integer',
        'order_invalid_num' => 'integer',
        'confirmed_commission' => 'float',
        'not_confirmed_commission' => 'float',
        'assets' => 'float',
        'cancel_commission' => 'float',
        'settlement_commission' => 'float',
        'pay_customer' => 'integer',
        'click_num' => 'integer',
        'register_num' => 'integer',
        'income_num' => 'float',
        'commission_rate' => 'integer',
    ];

    /**
     * 分销时间周期
     * @var string|null
     */
    const SURE_TIME = 30 * 86400;
    /**
     * 等待处理
     * @var string|null
     */
    const STATUS_PENDING = 1;
    /**
     * 分销中
     * @var string|null
     */
    const STATUS_DISTRIBUTION = 2;
    /**
     * 不可分销
     * @var string|null
     */
    const STATUS_NOT_DISTRIBUTION = 3;
}
