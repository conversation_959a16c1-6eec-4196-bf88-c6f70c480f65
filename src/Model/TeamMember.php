<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Hyperf\Database\Model\Events\Created;
use Hyperf\Database\Model\Events\Deleted;
use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $team_id
 * @property int $user_id
 * @property int $role_id
 * @property int $status
 * @property int $invite_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class TeamMember extends Model
{
    /**
     * 状态-正常
     * @var int
     */
    const STATUS_ACTIVATED = 1;

    /**
     * 状态-禁用
     * @var int
     */
    const STATUS_DISABLED = 2;

    /**
     * 状态-由于超出套餐成员数，所以禁用
     * @var int
     */
    const STATUS_DISABLED_DUE_REACHED_MAXIMUM_LIMIT = 3;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'team_members';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'team_id' => 'integer', 'user_id' => 'integer', 'role_id' => 'integer', 'status' => 'integer', 'invite_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @var array
     */
    protected $attributes = [
        'status' => self::STATUS_ACTIVATED,
    ];

    /**
     * @param Created $event
     */
    public function created(Created $event)
    {
        Team::query()->where('id', $this->team_id)->increment('member_count', 1);
    }

    /**
     * @param Deleted $event
     */
    public function deleted(Deleted $event)
    {
        Team::query()->where('id', $this->team_id)->decrement('member_count', 1);
    }

    /**
     * 团队
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function team()
    {
        return $this->hasOne(Team::class, 'id', 'team_id');
    }

    /**
     * 角色
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function role()
    {
        return $this->hasOne(TeamRole::class, 'id', 'role_id');
    }

    /**
     * 用户
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
