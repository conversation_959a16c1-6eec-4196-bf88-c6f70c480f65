<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $team_id
 * @property string $name
 * @property array $permission
 * @property boolean $is_default
 * @property boolean $is_admin
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class TeamRole extends \Lete\Base\Abstraction\Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'team_roles';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['is_admin' => 'boolean', 'is_default' => 'boolean', 'permission' => 'json', 'id' => 'integer', 'team_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @var array
     */
    protected $attributes = [
        'is_default' => 0,
        'is_admin' => 0,
    ];

    /**
     * @param $value
     * @return array
     */
    public function getPermissionAttribute($value)
    {
        $permission = !empty($value) ? json_decode($value, true) : [];
        return array_merge(config('website.team.role.permission_structure', []), $permission);
    }
}
