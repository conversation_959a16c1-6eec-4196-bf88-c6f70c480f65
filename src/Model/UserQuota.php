<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $permission_name
 * @property int $limit
 * @property int $remaining
 * @property int $used
 * @property int $reset_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserQuota extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_quota';
    /**
     *
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'limit' => 'integer', 'remaining' => 'integer', 'used' => 'integer', 'reset_at' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
