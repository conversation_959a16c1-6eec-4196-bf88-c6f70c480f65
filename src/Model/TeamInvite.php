<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $team_id
 * @property int $role_id
 * @property string $code
 * @property string $email
 * @property \Carbon\Carbon $created_at
 * @property int $expired_at
 */
class TeamInvite extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'team_invites';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['role_id' => 'integer', 'id' => 'integer', 'team_id' => 'integer', 'created_at' => 'datetime', 'expired_at' => 'integer'];

    const UPDATED_AT = null;

    /**
     * 团队
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function team()
    {
        return $this->hasOne(Team::class, 'id', 'team_id');
    }
}
