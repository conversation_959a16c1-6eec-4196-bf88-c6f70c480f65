<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $order_id
 * @property float $amount
 * @property int $method
 * @property bool $cancel_vip
 * @property bool $cancel_subscription
 * @property int $admin_id
 * @property \Carbon\Carbon $created_at
 */
class OrderRefund extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_refunds';
    /**
     *
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'order_id' => 'integer', 'method' => 'integer', 'cancel_vip' => 'bool', 'cancel_subscription' => 'bool', 'admin_id' => 'integer', 'created_at' => 'datetime'];

    public const UPDATED_AT = null;
}
