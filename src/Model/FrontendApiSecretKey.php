<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Hyperf\Utils\Str;
use Lete\Base\Abstraction\Model;
use Qbhy\HyperfAuth\Authenticatable;

/**
 * @property int $id
 * @property string $secret_key
 * @property int $user_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class FrontendApiSecretKey extends Model implements Authenticatable
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'frontend_api_secret_keys';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @return int
     */
    public function getId()
    {
        return $this->user_id;
    }

    /**
     * @param string $secret_key 密钥
     * @return Authenticatable|null
     */
    public static function retrieveById($secret_key): ?Authenticatable
    {
        $user_id = self::query()
            ->where('secret_key', $secret_key)
            ->value('user_id');
        if (!$user_id) {
            return null;
        }
        return User::findFromCache($user_id);
    }

    /**
     * 生成密钥
     * @param int $user_id 用户id
     * @return string
     */
    public static function generateSecretKey($user_id)
    {
        $secret_key = md5($user_id . '-' . Str::random(rand(16, 32)));
        self::create([
            'secret_key' => $secret_key,
            'user_id' => $user_id,
        ]);
        return $secret_key;
    }
}
