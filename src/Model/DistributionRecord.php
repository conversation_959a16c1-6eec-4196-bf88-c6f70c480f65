<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $order_id
 * @property int $pay_status
 * @property string $sure_time
 * @property int $commission_rate
 * @property double $commission
 * @property string $created_at
 * @property string $updated_at
 */
class DistributionRecord extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'distribution_record';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * 类型-没确认
     * @var int
     */
    CONST NO_SURE_STATUS = 1;
    /**
     * 类型-已确认
     * @var int
     */
    CONST SURE_STATUS = 2;
    /**
     * 类型-已支付
     * @var int
     */
    CONST PAY_STATUS = 1;
    /**
     * 类型-已退款
     * @var int
     */
    CONST REFUND_STATUS = 2;


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'order_id' => 'integer',
        'pay_status' => 'integer',
        'commission_rate' => 'integer',
        'commission' => 'float',
        'sure_time' => 'datetime',
    ];

    protected $other_casts = [
        'sure_time' => 'integer',
    ];
}
