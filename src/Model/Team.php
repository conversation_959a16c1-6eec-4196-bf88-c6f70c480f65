<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Hyperf\Database\Model\Events\Created;
use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property int $member_count
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Team extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'teams';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'member_count' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @var array
     */
    protected $attributes = [
        'member_count' => 1
    ];

    /**
     * @param Created $event
     */
    public function created(Created $event)
    {
        if (config('website.team.role.type') === 'default') {
            foreach (config('website.team.role.default_data', []) as $role) {
                $role['team_id'] = $this->id;
                TeamRole::create($role);
            }
        }
    }

    /**
     * 团队所有人
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function owner()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
