<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Hyperf\Database\Model\Events\Updated;
use Website\Common\Exception\TeamException;
use Website\Common\Service\OrderService;
use Website\Common\Service\RankService;
use Website\Common\Service\SubscriptionService;
use Website\Common\Service\TeamService;
use Website\Common\Service\UserService;
use Carbon\Carbon;
use Hyperf\Database\Model\Events\Created;
use Hyperf\Utils\Str;
use Lete\Base\Abstraction\Model;
use Hyperf\Utils\Parallel;
use Qbhy\HyperfAuth\Authenticatable;
use Website\Common\Utils\Event;
use Website\Common\Utils\Website;

/**
 * @property int $id
 * @property string $account
 * @property string $username
 * @property string $email
 * @property int $email_verified_at
 * @property string $phone_number
 * @property string $phone_region
 * @property string $password
 * @property string $salt
 * @property int $rank_id
 * @property string $last_ip
 * @property string $last_location
 * @property int $last_at
 * @property int $vip_started_at
 * @property int $vip_expired_at
 * @property boolean $subscription_status
 * @property int $subscription_platform
 * @property int $subscription_started_at
 * @property int $subscription_next_deduct_at
 * @property string $card_last4
 * @property string $source
 * @property string $language
 * @property string $timezone
 * @property string $device_language
 * @property string $device_timezone
 * @property int $account_status
 * @property string $remark
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $company
 * @property string $country
 * @property string $province
 * @property string $city
 * @property string $postal
 * @property string $address
 * @property string $phone
 * @property string $vat
 * @property string $distribution_code
 * @property float $user_value
 * @property \Carbon\Carbon $delete_task_created_at
 * @property \Carbon\Carbon $delete_task_plan_executed_at
 * @property \Carbon\Carbon $delete_task_executed_at
 * @property string $avatar
 */
class User extends Model implements Authenticatable
{
    /**
     * 账号状态-已停用
     * @var int
     */
    const ACCOUNT_STATUS_DISABLED = 0;

    /**
     * 账号状态-已激活
     * @var int
     */
    const ACCOUNT_STATUS_ACTIVATED = 1;

    /**
     * 账号状态-未激活
     * @var int
     */
    const ACCOUNT_STATUS_UNACTIVATED = 2;

    /**
     * 账号状态-已加入删除队列
     * @var int
     */
    const ACCOUNT_STATUS_IN_DELETION_QUEUE = 3;

    /**
     * 账号状态-已删除
     * @var int
     */
    const ACCOUNT_STATUS_DELETED = 4;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['delete_task_executed_at' => 'datetime', 'delete_task_plan_executed_at' => 'datetime', 'delete_task_created_at' => 'datetime', 'user_value' => 'float', 'id' => 'integer', 'email_verified_at' => 'integer', 'rank_id' => 'integer', 'last_at' => 'integer', 'vip_started_at' => 'integer', 'vip_expired_at' => 'integer', 'subscription_status' => 'boolean', 'subscription_platform' => 'integer', 'subscription_started_at' => 'integer', 'subscription_next_deduct_at' => 'integer', 'account_status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * @var array
     */
    protected $hidden = ['password', 'salt'];

    /**
     * @var array
     */
    protected $attributes = [
        'rank_id' => 1,
        'account_status' => self::ACCOUNT_STATUS_UNACTIVATED,
    ];

    /**
     * @var array
     */
    protected $other_casts = [
        'vip_started_at' => 'datetime',
        'vip_expired_at' => 'datetime',
        'last_at' => 'datetime',
        'subscription_started_at' => 'datetime',
        'subscription_next_deduct_at' => 'datetime',
        'email_verified_at' => 'datetime',
    ];

    /**
     * @param Created $event
     */
    public function created(Created $event)
    {
        // 新用户送试用套餐
        if ($trial_name = Website::config()->get('website.user_account.register.trial_name')) {
            $RankTrial = Rank::query()
                ->where('rank_name', $trial_name)
                ->orderByDesc('id')
                ->first(['id', 'duration']);
            if ($RankTrial) {
                $this->rank_id = $RankTrial->id;
                $this->vip_started_at = time();
                $arr = explode(' ', $RankTrial->duration);
                if (count($arr) === 1 && $arr[0] === 'forever') {
                    $Carbon = Carbon::createFromTimeString('9999-01-01 00:00:00');
                } else  {
                    $function = 'add' . Str::title($arr[1]);
                    $Carbon = Carbon::createFromTimestamp($this->vip_started_at)->{$function}((int) $arr[0]);
                }
                $this->vip_expired_at = $Carbon->timestamp;
                $this->save();
            }
        }

        // 创建用户权限配额记录
        foreach (RankService::getPermissions() as $permission_name => $permission) {
            UserService::updatePermissionQuota($this->id, $permission_name, 0, Carbon::today()->addDays($permission['reset_days'])->timestamp);
        }

        // 自动创建团队
        if (config('website.team')) {
            try {
                TeamService::create($this->id);
            } catch (TeamException $e) {
            }
        }

        // 用户注册事件
        Event::produce('auth.signup', [
            'user_id' => $this->id,
            'account' => $this->account,
        ]);
    }

    public function updated(Updated $event)
    {
        if (config('website.team')) {
            $Team = Team::query()
                ->where('user_id', $this->id)
                ->first();
            if ($Team) {
                // 用户名变化，同步更改团队名称
                if ($this->wasChanged('username')) {
                    $Team->update([
                        'name' => TeamService::generateTeamName($this->username),
                    ]);
                }

                // 用户等级变化，检查团队人数是否超出上限
                if ($this->wasChanged('rank_id')) {
                    TeamService::ownerRankChangeEvent($Team->id);
                }
            }
        }

        // 会员到期事件
        if ($this->wasChanged('rank_id') && $this->rank_id === RankService::FREE) {
            Event::produce('user.expired', [
                'user_id' => $this->id,
                'original_rank_id' => $this->getOriginal('rank_id'),
                'original_rank_name' => Rank::query()
                    ->where('id', $this->getOriginal('rank_id'))
                    ->value('rank_name'),
            ]);
        }
    }

    /**
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void
     */
    public function getId()
    {
        return $this->getKey();
    }

    /**
     * @param $key
     * @return Authenticatable|null
     */
    public static function retrieveById($key): ?Authenticatable
    {
        return self::findFromCache($key);
    }

    /**
     * 是否新用户
     * @return bool
     */
    public function getNewUser()
    {
        return time() - $this->created_at->timestamp <= 60;
    }

    /**
     * 设置密码
     * @param $value
     */
    public function setPasswordAttribute($value)
    {
        if (!$this->salt) {
            $this->salt = rand(1, 9999);
        }
        $this->attributes['password'] = md5("{$this->salt}-{$value}");
    }

    /**
     * @param $value
     */
    public function setDeviceLanguage($value)
    {
        $this->attributes['device_language'] = !is_null($value) ? Str::substr($value, 0, 50) : $value;
    }

    /**
     * @param $value
     */
    public function setDeviceTimezone($value)
    {
        $this->attributes['device_timezone'] = !is_null($value) ? Str::substr($value, 0, 50) : $value;
    }

    /**
     * 关联等级
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function rank()
    {
        return $this->hasOne(Rank::class, 'id', 'rank_id');
    }

    /**
     * 第三方账号
     * @return \Hyperf\Database\Model\Relations\HasMany
     */
    public function socialites()
    {
        return $this->hasMany(UserSocialite::class, 'user_id', 'id')
            ->whereNotIn('type', [UserSocialite::TYPE_EMAIL, UserSocialite::TYPE_PHONE_NUMBER]);
    }

    /**
     * 用户权限额度
     * @return \Hyperf\Database\Model\Relations\HasMany
     */
    public function quota()
    {
        return $this->hasMany(UserQuota::class, 'user_id', 'id');
    }
}
