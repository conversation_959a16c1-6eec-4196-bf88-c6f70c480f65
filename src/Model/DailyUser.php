<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property string $date
 * @property int $total
 * @property int $new_users
 * @property int $active_users
 * @property array $active_user_ids
 * @property int $inactive_users
 * @property int $active_seniors
 * @property string $active_seniors_rate
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class DailyUser extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'daily_users';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['active_user_ids' => 'array', 'id' => 'integer', 'total' => 'integer', 'new_users' => 'integer', 'active_users' => 'integer', 'inactive_users' => 'integer', 'active_seniors' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
