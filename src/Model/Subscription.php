<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property int $payment_platform
 * @property string $subscription_sn
 * @property int $subscription_status
 * @property string $platform_status
 * @property string $product_name
 * @property int $start_date
 * @property int $next_period_start
 * @property float $next_period_amount
 * @property string $canceled_handler
 * @property \Carbon\Carbon $canceled_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Subscription extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscriptions';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'payment_platform' => 'integer',
        'subscription_status' => 'integer',
        'next_period_amount' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'canceled_at' => 'datetime',
    ];

    /**
     * @var array
     */
    protected $other_casts = [
        'start_date' => 'datetime',
        'next_period_start' => 'datetime',
    ];

    /**
     * 关联用户
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
