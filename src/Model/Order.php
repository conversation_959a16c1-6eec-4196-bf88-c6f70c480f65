<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Hyperf\Database\Model\Events\Creating;
use Lete\Base\Abstraction\Model;
use Website\Common\Service\OrderService;

/**
 * @property int $id
 * @property int $rank_id
 * @property string $rank_duration
 * @property int $user_id
 * @property int $first_time
 * @property int $order_status
 * @property int $payment_platform
 * @property string $subscription_sn
 * @property float $order_amount
 * @property float $paid_amount
 * @property float $refunded_amount
 * @property float $profit_amount
 * @property string $remark
 * @property \Carbon\Carbon $created_at
 * @property int $paid_at
 * @property int $refunded_at
 * @property string $stripe_invoice
 * @property string $hosted_invoice_url
 * @property string $invoice_pdf
 * @property string $transaction_number
 * @property string $distribution_code
 * @property int $prev_plan_reamin_days
 * @property int $trial_days
 * @property int $type
 */
class Order extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';
    /**
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'rank_id' => 'integer',
        'user_id' => 'integer',
        'first_time' => 'boolean',
        'order_status' => 'integer',
        'payment_platform' => 'integer',
        'order_amount' => 'float',
        'paid_amount' => 'float',
        'refunded_amount' => 'float',
        'profit_amount' => 'float',
        'created_at' => 'datetime',
        'paid_at' => 'integer',
        'refunded_at' => 'integer',
        'prev_plan_reamin_days' => 'integer',
        'trial_days' => 'integer',
        'type' => 'integer',
    ];

    /**
     * @var array
     */
    protected $other_casts = [
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    /**
     * @var array
     */
    protected $attributes = [
        'first_time' => false,
        'type' => OrderService::TYPE_SUBSCRIPTION,
        'order_status' => OrderService::STATUS_UNPAID,
    ];

    /**
     * @param $value
     */
    public function setPaidAmountAttribute($value)
    {
        $this->attributes['paid_amount'] = $value;
        $this->profit_amount = round($value - $this->refunded_amount, 2);
    }

    /**
     * @param $value
     */
    public function setRefundedAmountAttribute($value)
    {
        $this->attributes['refunded_amount'] = $value;
        $this->profit_amount = round($this->paid_amount - $value, 2);
    }

    /**
     * 关联用户
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * 关联等级
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function rank()
    {
        return $this->hasOne(Rank::class, 'id', 'rank_id');
    }

    /**
     * 关联分销记录
     * @return \Hyperf\Database\Model\Relations\HasOne
     */
    public function distribution()
    {
        return $this->hasOne(DistributionRecord::class, 'order_id', 'id');
    }

    /**
     * 数据创建时的钩子
     * @param Creating $event
     * @return void
     */
    public function creating(Creating $event)
    {
        /* 记录分销者的分销码 */
        $user_id = $this->getAttribute('user_id');
        $payment_platform = $this->getAttribute('payment_platform');
        if (!empty($user_id) && $payment_platform !== OrderService::PAYMENT_PLATFORM_OFFLINE) {
            $distribution_code = User::query()->where('id', '=', $user_id)->value('distribution_code');
            if (!empty($distribution_code)) {
                $this->setAttribute('distribution_code', $distribution_code);
                $code_user_id = DistributionCode::query()->where('distribution_code', '=', $distribution_code)->value('user_id');
                // 未支付订单数加一
                DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('order_invalid_num');
            }
        }

    }
}
