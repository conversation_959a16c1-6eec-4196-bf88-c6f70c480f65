<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property float $pay_money
 * @property string $pay_time
 * @property int $status
 * @property string $remark
 * @property string $pay_account
 * @property int $user_id
 * @property string $created_at
 * @property string $updated_at
 */
class DistributionSettlement extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'distribution_settlement';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * 类型-已付款
     * @var int
     */
    CONST PAY_STATUS = 1;

    /**
     * 类型-已作废
     * @var int
     */
    CONST VOID_STATUS = 2;
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'pay_money' => 'float',
        'pay_time' => 'datetime',
        'void_time' => 'datetime',
        'status' => 'integer',
    ];

    /**
     * 前台的
     * @var string[]
     */
    protected $other_casts = [
        'pay_time' => 'integer',
        'void_time' => 'integer',
    ];

}
