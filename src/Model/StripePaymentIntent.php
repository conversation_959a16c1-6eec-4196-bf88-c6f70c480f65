<?php

declare (strict_types=1);
namespace Website\Common\Model;

use Lete\Base\Abstraction\Model;

/**
 * @property int $id
 * @property string $payment_intent
 * @property string $customer
 * @property string $invoice
 * @property string $payment_method
 * @property string $payment_method_details
 */
class StripePaymentIntent extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'stripe_payment_intents';
    /**
     *
     * @var array
     */
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'payment_method_details' => 'array',
    ];

    const CREATED_AT = null;

    const UPDATED_AT = null;
}
