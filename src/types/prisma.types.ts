/**
 * 书籍模型相关类型定义
 * 用于替换 book.model.ts 中的 any 类型
 */

import { Prisma } from '@prisma/client';

// ==================== Prisma 查询参数类型 ====================

/**
 * 书籍查询参数类型
 */
export type BookFindManyArgs = Prisma.booksFindManyArgs;
export type BookFindUniqueArgs = Prisma.booksFindUniqueArgs;
export type BookCountArgs = Prisma.booksCountArgs;

/**
 * 书籍关联查询包含参数
 */
export type BookInclude = Prisma.booksInclude;

/**
 * 书籍查询条件
 */
export type BookWhereInput = Prisma.booksWhereInput & {
  book_categories?: {
    some: {
      category_id: number;
    };
  };
  book_authors?: {
    some: {
      author_id: number;
    };
  };
  book_translations?: {
    some: {
      OR: Array<{
        title?: { contains: string };
        subtitle?: { contains: string };
        description?: { contains: string };
      }>;
    };
  };
};
export type BookWhereUniqueInput = Prisma.booksWhereUniqueInput;

/**
 * 书籍排序参数
 */
export type BookOrderByWithRelationInput = Prisma.booksOrderByWithRelationInput & {
  view_statistics?: {
    view_count: 'asc' | 'desc';
  };
};

// ==================== Prisma 查询结果类型 ====================

/**
 * 基础书籍类型（不包含关联）
 */
export type BookModel = Prisma.booksGetPayload<{}>;

/**
 * 包含所有关联的书籍类型（用于详情页）
 */
export interface BookWithAllRelations {
  id: number;
  isbn?: string | null;
  asin?: string | null;
  publication_year?: number | null;
  publisher_id?: number | null;
  original_language_id?: string | null;
  is_published?: number | null;
  published_at?: Date | null;
  reading_time_minutes?: number | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  book_translations: Array<{
    id: number;
    book_id?: number | null;
    language_id?: string | null;
    title: string;
    subtitle?: string | null;
    description?: string | null;
    plot_summary?: string | null;
    best_quote?: string | null;
    synopsis?: string | null;
    is_default?: number | null;
  }>;
  book_covers: Array<{
    id: number;
    book_id?: number | null;
    language_id?: string | null;
    image_url: string;
    is_primary?: number | null;
  }>;
  book_authors: Array<{
    id: number;
    book_id?: number | null;
    author_id?: number | null;
    author_order?: number | null;
    authors: {
      id: number;
    };
  }>;
  book_categories: Array<{
    id: number;
    book_id?: number | null;
    category_id?: number | null;
    categories: {
      id: number;
    };
  }>;
  view_statistics?: {
    id: number;
    book_id?: number | null;
    view_count?: number | null;
    last_viewed_at?: Date | null;
  } | null;
  audio_files?: Array<{
    id: number;
    book_id?: number | null;
    language_id?: string | null;
    file_url: string;
    duration_seconds?: number | null;
    file_size_mb?: number | null;
  }>;
}

/**
 * 包含基本关联的书籍类型（用于列表页）
 */
export interface BookWithBasicRelations {
  id: number;
  isbn?: string | null;
  asin?: string | null;
  publication_year?: number | null;
  publisher_id?: number | null;
  original_language_id?: string | null;
  is_published?: number | null;
  published_at?: Date | null;
  reading_time_minutes?: number | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  book_translations: Array<{
    id: number;
    book_id?: number | null;
    language_id?: string | null;
    title: string;
    subtitle?: string | null;
    description?: string | null;
    plot_summary?: string | null;
    synopsis?: string | null;
    is_default?: number | null;
  }>;
  book_covers: Array<{
    id: number;
    book_id?: number | null;
    language_id?: string | null;
    image_url: string;
    is_primary?: number | null;
  }>;
  book_authors: Array<{
    id: number;
    book_id?: number | null;
    author_id?: number | null;
    author_order?: number | null;
    authors: {
      id: number;
    };
  }>;
  book_categories: Array<{
    id: number;
    book_id?: number | null;
    category_id?: number | null;
    categories: {
      id: number;
    };
  }>;
  view_statistics?: {
    id: number;
    book_id?: number | null;
    view_count?: number | null;
    last_viewed_at?: Date | null;
  } | null;
};

/**
 * 书籍翻译类型
 */
export interface BookTranslation {
  id: number;
  book_id?: number | null;
  language_id?: string | null;
  title: string;
  subtitle?: string | null;
  description?: string | null;
  plot_summary?: string | null;
  synopsis?: string | null;
  is_default?: number | null;
}

/**
 * 书籍封面类型
 */
export interface BookCover {
  id: number;
  book_id?: number | null;
  language_id?: string | null;
  image_url: string;
  is_primary?: number | null;
}

/**
 * 书籍作者关联类型
 */
export interface BookAuthor {
  id: number;
  book_id?: number | null;
  author_id?: number | null;
  author_order?: number | null;
  authors: {
    id: number;
  };
}

/**
 * 书籍分类关联类型
 */
export interface BookCategory {
  id: number;
  book_id?: number | null;
  category_id?: number | null;
  categories: {
    id: number;
  };
}

/**
 * 作者翻译类型
 */
export interface AuthorTranslation {
  id: number;
  author_id?: number | null;
  language_id?: string | null;
  name: string;
  biography?: string | null;
  is_default?: number | null;
}

/**
 * 分类翻译类型
 */
export interface CategoryTranslation {
  id: number;
  category_id?: number | null;
  language_id?: string | null;
  name: string;
  description?: string | null;
  is_default?: number | null;
}

/**
 * 书籍浏览统计类型
 */
export interface ViewStatistics {
  id: number;
  book_id?: number | null;
  view_count?: number | null;
  last_viewed_at?: Date | null;
}

/**
 * 书籍评分类型
 */
export interface Rating {
  id: number;
  book_id?: number | null;
  user_id?: bigint | null;
  score: number;
  review_text?: string | null;
}

/**
 * 书籍音频文件类型
 */
export interface AudioFile {
  id: number;
  book_id?: number | null;
  language_id?: string | null;
  file_url: string;
  duration_seconds?: number | null;
  file_size_mb?: number | null;
};

// ==================== 视图统计相关类型 ====================

/**
 * 视图统计查询参数
 */
export interface ViewStatisticsWhereUniqueInput {
  id?: number;
  book_id?: number;
}

export interface ViewStatisticsUpdateInput {
  book_id?: number | null;
  view_count?: number | { increment: number } | null;
  last_viewed_at?: Date | null;
}

export interface ViewStatisticsCreateInput {
  book_id?: number | null;
  view_count?: number | null;
  last_viewed_at?: Date | null;
}
