/**
 * 音频相关类型定义 - S3 + TTS方案
 */

// ElevenLabs TTS API 响应类型 (返回音频流)
export interface ElevenLabsTTSResponse {
  // TTS API返回的是音频流Buffer，没有JSON响应
}

// ElevenLabs API 错误响应类型
export interface ElevenLabsErrorResponse {
  detail: {
    status: string;
    message: string;
  };
}

// 音频文件元数据类型
export interface AudioFileMetadata {
  audioUrl: string;
  duration?: number;
  fileSize: number;
  generatedAt: string;
  lastChecked: number;
}

// Redis缓存的音频数据类型
export interface CachedAudioData extends AudioFileMetadata {
  bookId: number;
  locale: string;
  fileName: string;
}

// TTS 生成参数类型
export interface TTSGenerationParams {
  text: string;
  voice_id: string;
  model_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
  output_format?: string; // 音频输出格式，如 'mp3_44100_128'
}

// S3上传响应类型
export interface S3UploadResponse {
  success: boolean;
  url?: string;
  error?: string;
  fileSize?: number;
  key?: string;
}

// API 响应类型
export interface AudioApiResponse {
  success: boolean;
  data?: {
    audioUrl: string;
    duration?: number;
    fileSize?: number;
    cached: boolean;
    generatedAt: string;
    waitedForGeneration?: boolean;
  };
  error?: string;
}

// 音频播放器组件属性类型
export interface AudioPlayerProps {
  bookId: number;
  locale: string;
  isActive: boolean;
  onClose: () => void;
  book: {
    id: number;
    title: string;
    author: string;
    coverImage: string;
  };
}

// 播放按钮组件属性类型
export interface AudioPlayButtonProps {
  bookId: number;
  locale: string;
  onPlay: () => void;
  disabled?: boolean;
  loading?: boolean;
}

// 语言到声音ID的映射类型
export type VoiceIdMap = Record<string, string>;

// 音频生成状态枚举
export enum AudioGenerationStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  GENERATING = 'generating',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 文件检查结果类型
export interface FileExistsResult {
  exists: boolean;
  url?: string;
  fileSize?: number;
  lastModified?: string;
}

// 音频生成配置类型
export interface AudioGenerationConfig {
  maxFileSize: number;
  timeout: number;
  retryAttempts: number;
  cacheTTL: number;
  maxTextLength: number;
  minTextLength: number;
}

// 错误类型枚举
export enum AudioErrorType {
  BOOK_NOT_FOUND = 'BOOK_NOT_FOUND',
  NO_PLOT_SUMMARY = 'NO_PLOT_SUMMARY',
  NO_CHAPTERS_AVAILABLE = 'NO_CHAPTERS_AVAILABLE',
  INVALID_CHAPTER_FORMAT = 'INVALID_CHAPTER_FORMAT',
  CHAPTER_PROCESSING_FAILED = 'CHAPTER_PROCESSING_FAILED',
  TEXT_TOO_LONG = 'TEXT_TOO_LONG',
  TEXT_TOO_SHORT = 'TEXT_TOO_SHORT',
  TTS_API_ERROR = 'TTS_API_ERROR',
  S3_UPLOAD_ERROR = 'S3_UPLOAD_ERROR',
  FILE_CHECK_ERROR = 'FILE_CHECK_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONCURRENCY_ERROR = 'CONCURRENCY_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 音频错误类型
export interface AudioError {
  type: AudioErrorType;
  message: string;
  details?: any;
  retryable: boolean;
}

// 分布式锁结果类型
export interface LockResult {
  acquired: boolean;
  lockKey: string;
  lockValue?: string;
}

// 并发等待结果类型
export interface WaitResult {
  success: boolean;
  audioUrl?: string;
  timeout: boolean;
  waitTime: number;
}
