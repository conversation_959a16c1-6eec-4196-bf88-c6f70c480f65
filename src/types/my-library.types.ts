/**
 * My Library 页面相关类型定义
 */

import { Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

// ==================== Prisma 查询参数类型 ====================

/**
 * 用户音频进度查询参数类型
 */
export type UserAudioProgressFindManyArgs = Prisma.user_audio_progressFindManyArgs;
export type UserAudioProgressCountArgs = Prisma.user_audio_progressCountArgs;

/**
 * 用户收藏查询参数类型
 */
export type UserFavoritesFindManyArgs = Prisma.user_favoritesFindManyArgs;
export type UserFavoritesCountArgs = Prisma.user_favoritesCountArgs;

/**
 * 用户评分查询参数类型
 */
export type RatingsFindManyArgs = Prisma.ratingsCountArgs;
export type RatingsCountArgs = Prisma.ratingsCountArgs;

/**
 * 用户阅读历史查询参数类型
 */
export type UserReadingHistoryFindManyArgs = Prisma.user_reading_historyFindManyArgs;
export type UserReadingHistoryCountArgs = Prisma.user_reading_historyCountArgs;

// ==================== Prisma 查询结果类型 ====================

/**
 * 用户音频进度结果类型
 */
export interface UserAudioProgressWithRelations {
  id: number;
  user_id?: bigint | null;
  book_id?: number | null;
  position_seconds?: number | null;
  is_completed?: number | null;
  last_listened_at?: Date | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  books?: {
    id: number;
    book_translations: Array<{
      id: number;
      title: string;
      subtitle?: string | null;
    }>;
    book_covers: Array<{
      id: number;
      image_url: string;
    }>;
    book_authors: Array<{
      author: {
        author_translations: Array<{
          name: string;
        }>;
      };
    }>;
    audio_files: Array<{
      duration_seconds?: number | null;
    }>;
    ratings: Array<{
      score: Decimal;
    }>;
  } | null;
}

/**
 * 用户收藏结果类型
 */
export interface UserFavoritesWithRelations {
  user_id: bigint;
  book_id: number;
  created_at?: Date | null;
  books?: {
    id: number;
    book_translations: Array<{
      id: number;
      title: string;
      subtitle?: string | null;
    }>;
    book_covers: Array<{
      id: number;
      image_url: string;
    }>;
    book_authors: Array<{
      author: {
        author_translations: Array<{
          name: string;
        }>;
      };
    }>;
    ratings: Array<{
      score: Decimal;
    }>;
  } | null;
}

/**
 * 用户评分结果类型
 */
export interface RatingsWithRelations {
  id: number;
  book_id?: number | null;
  user_id?: bigint | null;
  score: Decimal;
  review_text?: string | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  books?: {
    id: number;
    book_translations: Array<{
      id: number;
      title: string;
      subtitle?: string | null;
    }>;
    book_covers: Array<{
      id: number;
      image_url: string;
    }>;
    book_authors: Array<{
      author: {
        author_translations: Array<{
          name: string;
        }>;
      };
    }>;
  } | null;
}

/**
 * 用户阅读历史结果类型
 */
export interface UserReadingHistoryWithRelations {
  id: number;
  user_id?: bigint | null;
  book_id?: number | null;
  last_read_at?: Date | null;
  created_at?: Date | null;
  books?: {
    id: number;
    book_translations: Array<{
      id: number;
      title: string;
      subtitle?: string | null;
    }>;
    book_covers: Array<{
      id: number;
      image_url: string;
    }>;
    book_authors: Array<{
      author: {
        author_translations: Array<{
          name: string;
        }>;
      };
    }>;
    ratings: Array<{
      score: Decimal;
    }>;
  } | null;
}

// ==================== 业务逻辑类型 ====================

/**
 * My Library 页面书籍项类型
 */
export interface MyLibraryBookItem {
  id: number;
  title: string;
  author: string;
  coverUrl: string;
  description: string;
  rating: number;
  progress?: number;
  timeLeft?: string;
  lastReadAt?: string;
}
