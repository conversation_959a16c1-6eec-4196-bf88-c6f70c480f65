/**
 * 书籍相关类型定义
 */

// 书籍基本信息
export interface BookBasic {
  id: number;
  isbn?: string | null;
  asin?: string | null;
  publicationYear?: number | null;
  publisherId?: number | null;
  originalLanguageId?: string | null;
  isPublished?: number | null;
  publishedAt?: Date | null;
  readingTimeMinutes?: number | null;
}

// 书籍翻译信息
export interface BookTranslation {
  id: number;
  bookId?: number | null;
  languageId?: string | null;
  title: string;
  subtitle?: string | null;
  description?: string | null;
  plotSummary?: string | null;
  synopsis?: string | null;
  isDefault?: number | null;
}

// 书籍封面信息
export interface BookCover {
  id: number;
  bookId?: number | null;
  languageId?: string | null;
  imageUrl: string;
  isPrimary?: number | null;
}

// 书籍音频信息
export interface AudioFile {
  id: number;
  bookId?: number | null;
  languageId?: string | null;
  fileUrl: string;
  durationSeconds?: number | null;
  fileSizeMb?: number | null;
}

// 作者基本信息
export interface AuthorBasic {
  id: number;
}

// 作者翻译信息
export interface AuthorTranslation {
  id: number;
  authorId?: number | null;
  languageId?: string | null;
  name: string;
  biography?: string | null;
  isDefault?: number | null;
}

// 分类基本信息
export interface CategoryBasic {
  id: number;
}

// 分类翻译信息
export interface CategoryTranslation {
  id: number;
  categoryId?: number | null;
  languageId?: string | null;
  name: string;
  description?: string | null;
  isDefault?: number | null;
}

// 评分信息
export interface Rating {
  id: number;
  bookId?: number | null;
  userId?: bigint | null;
  score: number;
  reviewText?: string | null;
}

// 书籍详情（包含翻译和封面）
export interface BookDetail extends BookBasic {
  translation: BookTranslation;
  covers: BookCover[];
  authors: Array<{
    author: AuthorBasic;
    translation: AuthorTranslation | null;
  }>;
  categories: Array<{
    category: CategoryBasic;
    translation: CategoryTranslation | null;
  }>;
  audioFiles?: AudioFile[];
  ratings?: Rating[];
  viewCount?: string;
}

// 书籍列表项（简化版的书籍详情）
export interface BookListItem {
  id: number;
  title: string;
  subtitle?: string | null;
  description?: string | null;
  coverUrl?: string;
  authors: string[];
  categories?: string[]; // 可选，因为某些数据源可能没有分类信息
  publicationYear?: number | null;
  rating?: number;
  viewCount?: string;
}

// 书籍查询参数
export interface BookQueryParams {
  page?: number;
  limit?: number;
  categoryId?: number;
  authorId?: number;
  search?: string;
  language?: string;
  sortBy?: 'newest' | 'popular' | 'rating';
}

// 分页结果
export interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
