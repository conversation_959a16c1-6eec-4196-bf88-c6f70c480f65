/**
 * 音频播放进度相关类型定义
 */

// 播放进度数据类型
export interface PlaybackProgressData {
  bookId: number;
  currentTime: number;        // 当前播放时间(秒)
  duration: number;           // 音频总时长(秒)
  progressPercentage: number; // 播放进度百分比 (0-100)
  playbackRate?: number;      // 播放倍速 (0.5-2.0)
  volume?: number;            // 音量 (0-1)
}

// 播放进度查询参数
export interface PlaybackProgressQuery {
  limit?: number;
  offset?: number;
  includeCompleted?: boolean;
}

// 播放进度响应类型（适配数据库模型）
export interface PlaybackProgressResponse {
  id: number;
  userId: string;             // BigInt转换为string
  bookId: number;
  currentTime: number;
  duration: number | null;
  progressPercentage: number | null;
  isCompleted: boolean;
  lastListenedAt: string;
  playbackRate: number;
  volume: number;
  createdAt: string;
  updatedAt: string;
  book?: {
    id: number;
    title: string;
    cover: {
      imageUrl: string;
    };
  };
}

// 进度保存请求类型
export interface SaveProgressRequest {
  bookId: number;
  currentTime: number;
  duration: number;
  progressPercentage: number;
  playbackRate?: number;
  volume?: number;
}

// 播放统计类型
export interface PlaybackStats {
  totalBooks: number;
  completedBooks: number;
  completionRate: number;     // 完成率百分比
  recentActivity: PlaybackProgressResponse[];
}

// 数据库模型适配器类型
export interface ProgressModelAdapter {
  fromDatabase(dbProgress: any): PlaybackProgressResponse;
  toDatabase(progressData: PlaybackProgressData, userId: number): any;
}
