import createIntlMiddleware from 'next-intl/middleware'
import { routing } from './i18n/routing'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

const intlMiddleware = createIntlMiddleware(routing)

// 定义需要重定向的其他语言路径
const unsupportedLocales = ['zh', 'ja', 'ko', 'de', 'es', 'fr', 'pt', 'tw', 'vi']

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查是否访问了不支持的语言路径
  for (const locale of unsupportedLocales) {
    if (pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`) {
      // 获取路径的剩余部分
      const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/'

      // 创建重定向到英语版本的URL
      const redirectUrl = new URL(pathWithoutLocale, request.url)

      // 使用301永久重定向
      return NextResponse.redirect(redirectUrl, 301)
    }
  }

  const response = await intlMiddleware(request)

  response.headers.set('x-next-url', request.url)

  const utm = request.cookies.get('utm')

  const referer = request.headers.get('referer')

  if (!utm && referer) {
    response.cookies.set('utm', referer, {
      path: '/',
      sameSite: 'lax',
      /** 30 days */
      maxAge: 60 * 60 * 24 * 30
    })
  }

  return response
}

export const config = {
  matcher: [
    // 排除不需要处理的路径
    '/((?!api|self-api|common-api|_next|favicon.ico|.*\\..*).*)'
  ]
}
