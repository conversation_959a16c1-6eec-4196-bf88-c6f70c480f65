'use client'
import { createContext, useContext, useState, useCallback } from 'react'
import { useRouter } from '@/i18n/routing'
import { googleAuth, login } from '@/services/client/authService'
import { ResponseCode } from '@/utils/constants'
import { useToast } from '@/hooks/useToast'
import { useGoogleLogin } from '@react-oauth/google'
import { useLoginDialog } from '@/components/Block/LoginDialog'
import { useAuthModal } from '@/store/authModalStore'

interface AuthContextType {
  loading: boolean
  googleLoading: boolean
  handleLogin: (email: string, password: string, shouldRedirect?: boolean) => Promise<boolean>
  handleGoogleLogin: (shouldRedirect?: boolean) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false)
  const [googleShouldRedirect, setGoogleShouldRedirect] = useState(true)
  const router = useRouter()
  const { showToast } = useToast()

  const { onOpenChange: onLoginDialogOpenChange, onLoginSuccess } = useLoginDialog()

  const googleLogin = useGoogleLogin({
    flow: 'auth-code',
    onSuccess: async (response) => {
      try {
        const res = await googleAuth(response.code)
        if (res.code === ResponseCode.Success) {
          // 关闭登录对话框
          onLoginDialogOpenChange?.(false)
          onLoginSuccess?.()

          // 关闭 authModal 弹窗
          useAuthModal.getState().close()
          // 执行成功回调
          useAuthModal.getState().onSuccess?.()
          // 清除成功回调（防止多次调用）
          useAuthModal.getState().setOnSuccess(undefined)

          // 根据 googleShouldRedirect 决定是否跳转
          if (googleShouldRedirect) {
            const from = new URLSearchParams(window.location.search).get('from')
            if (from) {
              router.push(from)
            } else {
              router.push('/')
            }
          } else {
            // 弹窗模式：直接刷新当前页面，确保所有数据都是最新的
            window.location.reload()
          }
        }
      } finally {
        setGoogleLoading(false)
        router.refresh()
      }
    },
    onError: () => {
      setGoogleLoading(false)
    }
  })

  const handleLogin = useCallback(
    async (email: string, password: string, shouldRedirect = true) => {
      setLoading(true)
      try {
        const response = await login(email, password)
        if (response.code === ResponseCode.Success) {
          if (shouldRedirect) {
            const from = new URLSearchParams(window.location.search).get('from')
            router.refresh()
            if (from) {
              router.push(from)
            } else {
              router.push('/')
            }
          } else {
            // 弹窗模式：直接刷新当前页面，确保所有数据都是最新的
            window.location.reload()
          }
          return true
        } else {
          showToast(response.message, 'error')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      } finally {
        setLoading(false)
      }
    },
    [router, showToast]
  )

  const handleGoogleLogin = useCallback((shouldRedirect = true) => {
    setGoogleShouldRedirect(shouldRedirect)
    setGoogleLoading(true)
    googleLogin()
  }, [googleLogin, setGoogleShouldRedirect])

  return (
    <AuthContext.Provider
      value={{
        loading,
        googleLoading,
        handleLogin,
        handleGoogleLogin
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
