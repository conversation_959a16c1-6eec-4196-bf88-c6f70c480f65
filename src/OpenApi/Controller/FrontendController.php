<?php

declare(strict_types=1);

namespace Website\Common\OpenApi\Controller;

use Carbon\Carbon;
use Hyperf\Redis\Redis;
use Hyperf\Utils\ApplicationContext;
use Lete\MongoDB\MongoClient\MongoDb;
use Qbhy\HyperfAuth\AuthManager;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\UserException;
use Website\Common\FrontendApi\Auth\Guard\SsoGuard;
use Website\Common\Model\Rank;
use Website\Common\Model\Team;
use Website\Common\Model\TeamMember;
use Website\Common\Model\User;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\ApiVersion;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Context\Context;
use Hyperf\Utils\Arr;
use Website\Common\Service\TeamService;
use Website\Common\Service\UserService;

/**
 * @ApiVersion(version="v1")
 * @ApiController(prefix="frontend", tag="前台", server="website-common-open-api")
 */
class FrontendController extends AbstractController
{
    /**
     * @PostApi(path="auth", description="鉴权")
     */
    public function auth()
    {
        try {
            $data = $this->request->all();
            $AuthManager = ApplicationContext::getContainer()->get(AuthManager::class);
            $AuthGuard = $AuthManager->guard('frontend-api-sso');
            /* @var SsoGuard $AuthGuard */
            $AuthGuard->setRequester($data);
            $AuthGuard->simulateSessionStart();
            /* @var User $User */
            $User = $AuthGuard->user();
            if (in_array($User->account_status, [User::ACCOUNT_STATUS_DISABLED, User::ACCOUNT_STATUS_DELETED])) {
                throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
            }

            // 更改用户语言
            if (!empty($data['language']) && $User->language !== $data['language']) {
                $User->update([
                    'language' => $data['language'],
                ]);
            }

            // 用户的套餐
            $Rank = Rank::findFromCache($User->rank_id);

            // 团队信息
            $team = null;
            if (config('website.team') && !empty($data['team_id'])) {
                $team = self::getTeam($data['team_id'], $User->id);
                if ($team['user_id'] === $User->id) {
                    $team['rank_name'] = $Rank->rank_name;
                    $team['permission'] = $Rank->permission;
                }
            }

            $user = $User->toArray();
            $rank = $Rank->toArray();
            foreach (['created_at', 'updated_at'] as $field) {
                $user[$field] && $user[$field] = Carbon::createFromTimeString($user[$field])->timestamp;
                $rank[$field] && $rank[$field] = Carbon::createFromTimeString($rank[$field])->timestamp;
            }

            $user['last_rank_name'] = UserService::getLastRankNameFromCache($User->id);

            $result_data = [
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'user' => $user,
                    'rank' => $rank,
                    'team' => $team,
                ],
            ];
        } catch (\Throwable $e) {
            // 自动登录失败逻辑
            if ($e->getPrevious() instanceof TokenBlacklistException ||
                $e->getPrevious() instanceof TokenExpiredException ||
                $e->getPrevious() instanceof TokenNotActiveException ||
                $e instanceof UserException
            ) {
                /* @var JWT $JWT */
                $JWT = $AuthGuard->getJwtManager()->justParse($data['token']);
                $AuthGuard->markLogin($JWT->getPayload()['uid'], UserService::LOGIN_MODE_AUTOMATIC, false, $e->getMessage());
                $AuthGuard->deletedToken();
            }

            $result_data = [
                'code' => $e->getCode() ?: 400,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }
        $result_data['data']['session_id'] = $AuthGuard ? $AuthGuard->getSessionId() : null;

        return $this->response($result_data['code'], $result_data['message'], $result_data['data']);
    }

    /**
     * @PostApi(path="incr-permission-used", description="扣除权限次数")
     * @FormData(key="user_id|user id", rule="required|integer")
     * @FormData(key="permission_name|permission name", rule="required|string")
     * @FormData(key="used|used", rule="required|integer|min:1")
     * @FormData(key="timezone|timezone", rule="string|nullable")
     * @FormData(key="team_id|team id", rule="integer|nullable")
     */
    public function incrPermissionUsed()
    {
        $started_at = time();
        try {
            $validator_data = Context::get('validator.data');

            if (self::isTeamPermissionIsolation($validator_data['team_id'] ?? null, $validator_data['user_id'])) {
                $result = TeamService::incrPermissionUsed($validator_data['team_id'], $validator_data['user_id'], $validator_data['permission_name'], $validator_data['used'],$validator_data['timezone'] ?? null);
            } else {
                $result = UserService::incrPermissionUsed($validator_data['user_id'], $validator_data['permission_name'], $validator_data['used'],$validator_data['timezone'] ?? null);
            }

            return $this->response(200, 'success', [
                'result' => $result,
            ]);
        } catch (\Throwable $e) {
            $error_message = $e->getMessage();
            return $this->response(400, $error_message);
        } finally {
            $log_data = [
                'validator_data' => $validator_data,
                'result' => $result ?? null,
                'error_message' => $error_message ?? null,
                'started_at' => $started_at,
                'created_at' => time(),
            ];
            go(function () use ($log_data) {
                MongoDb::collection('openapi_incr_permission_used_logs')->insertOne($log_data);
            });
        }
    }

    /**
     * @PostApi(path="get-permission-remaining", description="查询权限的剩余次数")
     * @FormData(key="user_id|user id", rule="required|integer")
     * @FormData(key="permission_name|permission name", rule="required|string")
     * @FormData(key="team_id|team id", rule="integer|nullable")
     */
    public function getPermissionRemaining()
    {
        $validator_data = Context::get('validator.data');

        if (self::isTeamPermissionIsolation($validator_data['team_id'] ?? null, $validator_data['user_id'])) {
            $remaining = TeamService::getPermissionRemaining($validator_data['team_id'], $validator_data['user_id'], $validator_data['permission_name']);
        } else {
            $remaining = UserService::getPermissionRemaining($validator_data['user_id'], $validator_data['permission_name']);
        }

        return $this->response(200, 'success', [
            'remaining' => $remaining,
        ]);
    }

    /**
     * @PostApi(path="decr-permission-used", description="恢复权限次数")
     * @FormData(key="user_id|user id", rule="required|integer")
     * @FormData(key="permission_name|permission name", rule="required|string")
     * @FormData(key="used|used", rule="required|integer|min:1")
     * @FormData(key="team_id|team id", rule="integer|nullable")
     */
    public function decrPermissionUsed()
    {
        $started_at = time();
        try {
            $validator_data = Context::get('validator.data');

            if (self::isTeamPermissionIsolation($validator_data['team_id'] ?? null, $validator_data['user_id'])) {
                $result = TeamService::decrPermissionUsed($validator_data['team_id'], $validator_data['user_id'], $validator_data['permission_name'], $validator_data['used']);
            } else {
                $result = UserService::decrPermissionUsed($validator_data['user_id'], $validator_data['permission_name'], $validator_data['used']);
            }

            return $this->response(200, 'success', [
                'result' => $result,
            ]);
        } catch (\Throwable $e) {
            $error_message = $e->getMessage();
            return $this->response(400, $error_message);
        } finally {
            $log_data = [
                'validator_data' => $validator_data,
                'result' => $result ?? null,
                'error_message' => $error_message ?? null,
                'started_at' => $started_at,
                'created_at' => time(),
            ];
            go(function () use ($log_data) {
                MongoDb::collection('openapi_decr_permission_used_logs')->insertOne($log_data);
            });
        }
    }

    /**
     * 获取团队信息
     * @param int $team_id
     * @param int $user_id
     * @return array|null
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getTeam($team_id, $user_id)
    {
        $team = null;
        $Redis = ApplicationContext::getContainer()->get(Redis::class);
        $redis_key = env('APP_NAME') . ":open-api:team-member:{$team_id}-{$user_id}";
        $redis_result = $Redis->get($redis_key);
        if ($redis_result !== false) {
            $team = $redis_result ? json_decode($redis_result, true) : null;
        } else {
            $TeamMember = TeamMember::query()
                ->with('team:id,name,user_id')
                ->with('role:id,name,permission')
                ->where('team_id', $team_id)
                ->where('user_id', $user_id)
                ->where('status', TeamMember::STATUS_ACTIVATED)
                ->first(['id', 'team_id', 'user_id', 'role_id', 'status']);
            if ($TeamMember) {
                // 团队成员
                $team = [
                    'id' => $TeamMember->team->id,
                    'name' => $TeamMember->team->name,
                    'user_id' => $TeamMember->team->user_id,
                    'rank_name' => $TeamMember->team->owner->rank->rank_name,
                    'permission' => $TeamMember->team->owner->rank->permission,
                    'member' => [
                        'user_id' => $TeamMember->user_id,
                        'status' => $TeamMember->status,
                        'role' => [
                            'id' => $TeamMember->role->id,
                            'name' => $TeamMember->role->name,
                            'permission' => $TeamMember->role->permission,
                        ],
                    ],
                ];
            } else {
                // 团队所有者
                $Team = Team::query()
                    ->where('id', $team_id)
                    ->where('user_id', $user_id)
                    ->first(['id', 'name', 'user_id']);
                if ($Team) {
                    $team = [
                        'id' => $Team->id,
                        'name' => $Team->name,
                        'user_id' => $Team->user_id,
                        'rank_name' => null,
                        'permission' => null,
                        'member' => [
                            'user_id' => $Team->user_id,
                            'status' => TeamMember::STATUS_ACTIVATED,
                            'role' => [
                                'id' => 0,
                                'name' => 'Owner',
                                'permission' => array_map(function ($permission_value) {
                                    return is_bool($permission_value) ? true : $permission_value;
                                }, config('website.team.role.permission_structure', [])),
                            ],
                        ],
                    ];
                }
            }

            if ($team) {
                $team['last_rank_name'] = UserService::getLastRankNameFromCache($team['user_id']);
            }

            // 缓存结果
            $Redis->set($redis_key, $team ? json_encode($team) : $team, 10);
        }
        return $team;
    }

    /**
     * 判断是否团队权限隔离
     * @param int|null $team_id
     * @param int $user_id
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function isTeamPermissionIsolation($team_id, $user_id)
    {
        $result = false;
        if ($team_id && config('website.team.permission_isolation', false)) {
            $team = self::getTeam($team_id, $user_id);
            if ($team && $team['user_id'] !== $user_id) {
                $result = true;
            }
        }
        return $result;
    }
}
