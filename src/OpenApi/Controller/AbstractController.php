<?php

declare(strict_types=1);

namespace Website\Common\OpenApi\Controller;

use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerInterface;

abstract class AbstractController
{
    /**
     * @Inject
     * @var ContainerInterface
     */
    protected $container;
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;
    /**
     * @Inject
     * @var ResponseInterface
     */
    protected $response;

    /**
     * @param int $code
     * @param string $message
     * @param array $data
     * @return \Psr\Http\Message\ResponseInterface
     */
    protected function response(int $code, string $message, array $data = [])
    {
        return $this->response->json([
            'code' => $code,
            'message' => $message,
            'data' => (object) $data,
        ]);
    }
}
