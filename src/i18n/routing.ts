import { defineRouting } from 'next-intl/routing'
import { createNavigation } from 'next-intl/navigation'

export const locales = ['en'] as const

// 从数组值推导类型
export type SupportedLanguage = (typeof locales)[number]

/**
 * 增加语言只需修改locales数组
 */
export const routing = defineRouting({
  locales,
  defaultLocale: 'en' as SupportedLanguage,
  localePrefix: 'as-needed'
})

export const { Link, redirect, usePathname, useRouter } = createNavigation(routing)
