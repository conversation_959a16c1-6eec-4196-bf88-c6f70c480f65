/**
 * 作者相关工具函数
 * 参考 book.utils.ts 的实现模式，提供作者URL生成和解析功能
 */

/**
 * 将作者名转换为URL友好的slug格式
 * 参考书籍标题处理逻辑，保持一致性
 * 
 * 处理规则：
 * 1. 保留原始大小写（不自动转小写）
 * 2. 保留字母、数字、连字符
 * 3. 移除特殊符号（&、@、#等）
 * 4. 连续空格/特殊字符转为单个连字符
 * 5. 去除首尾连字符
 * 6. 70字符限制
 */
export function convertAuthorNameToSlug(name: string): string {
  if (!name || typeof name !== 'string') {
    return 'unknown-author';
  }

  let slug = name
    // 移除HTML标签（如果有）
    .replace(/<[^>]*>/g, '')
    // 将特殊字符和空格替换为连字符
    .replace(/[^\w\s-]/g, '-')
    // 将多个空格或连字符替换为单个连字符
    .replace(/[\s-]+/g, '-')
    // 移除首尾的连字符
    .replace(/^-+|-+$/g, '');

  // 限制长度为70字符
  if (slug.length > 70) {
    // 在单词边界截断，避免破坏单词
    const truncated = slug.substring(0, 70);
    const lastDashIndex = truncated.lastIndexOf('-');
    
    if (lastDashIndex > 50) {
      slug = truncated.substring(0, lastDashIndex);
    } else {
      slug = truncated;
    }
  }

  return slug || 'unknown-author'; // 防止空字符串
}

/**
 * 生成完整的作者 slug（名称 + ID）
 * 例如：generateAuthorSlug('Vishen Lakhiani', 2742) -> 'Vishen-Lakhiani-2742'
 */
export function generateAuthorSlug(name: string, id: number | string): string {
  const nameSlug = convertAuthorNameToSlug(name);
  return `${nameSlug}-${id}`;
}

/**
 * 解析作者 slug，提取名称部分和ID
 * 支持新格式：'Vishen-Lakhiani-2742' -> { name: 'Vishen-Lakhiani', id: 2742 }
 * 支持旧格式：'2742' -> { name: undefined, id: 2742 }
 */
export function parseAuthorSlug(slug: string): { name?: string; id: number } {
  if (!slug || typeof slug !== 'string') {
    throw new Error('Invalid slug provided');
  }

  // 检查是否为纯数字（旧格式）
  const numericMatch = slug.match(/^\d+$/);
  if (numericMatch) {
    return {
      name: undefined,
      id: parseInt(slug, 10)
    };
  }

  // 解析新格式：查找最后一个连字符后的数字
  const match = slug.match(/^(.+)-(\d+)$/);
  if (match) {
    const [, namePart, idPart] = match;
    return {
      name: namePart,
      id: parseInt(idPart, 10)
    };
  }

  // 如果无法解析，抛出错误
  throw new Error(`Unable to parse author slug: ${slug}`);
}

/**
 * 生成作者详情页面URL
 * 优先使用新的slug格式，如果没有name则使用旧格式
 */
export function generateAuthorDetailUrl(author: { id: number | string; name?: string }): string {
  if (author.name) {
    const slug = generateAuthorSlug(author.name, author.id);
    return `/author/${slug}`;
  }

  // 降级到旧格式
  return `/author/${author.id}`;
}

/**
 * 检查slug是否为旧格式（纯数字）
 */
export function isLegacyAuthorSlug(slug: string): boolean {
  return /^\d+$/.test(slug);
}

/**
 * 验证解析后的作者ID是否有效
 */
export function isValidAuthorId(id: number): boolean {
  return Number.isInteger(id) && id > 0;
}

/**
 * 作者数据类型定义（用于URL生成）
 */
export interface AuthorUrlData {
  id: number | string;
  name?: string;
}

/**
 * 批量生成作者URL
 */
export function generateAuthorUrls(authors: AuthorUrlData[]): Array<AuthorUrlData & { detailUrl: string }> {
  return authors.map(author => ({
    ...author,
    detailUrl: generateAuthorDetailUrl(author)
  }));
}

/**
 * 从作者详情URL中提取作者ID
 */
export function extractAuthorIdFromUrl(url: string): number | null {
  try {
    // 提取slug部分
    const match = url.match(/\/author\/(.+)$/);
    if (!match) {
      return null;
    }

    const slug = match[1];
    const parsed = parseAuthorSlug(slug);
    
    if (!isValidAuthorId(parsed.id)) {
      return null;
    }

    return parsed.id;
  } catch (error) {
    console.error('Error extracting author ID from URL:', url, error);
    return null;
  }
}

/**
 * 验证作者URL的有效性
 */
export function isValidAuthorUrl(url: string): boolean {
  return extractAuthorIdFromUrl(url) !== null;
}
