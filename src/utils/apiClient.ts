'use client'
import ky from 'ky'

// 默认前缀
const defaultPrefixUrl = '/common-api/v1'

// 创建API实例的工厂函数
const createApi = (customPrefixUrl?: string) => {
  return ky.create({
    prefixUrl: customPrefixUrl || defaultPrefixUrl,
    credentials: 'include',
    timeout: 30000,
    hooks: {
      beforeRequest: [
        async (request) => {
          if (request.signal?.aborted) {
            throw new Error('Request aborted')
          }
        }
      ],
      afterResponse: [
        async (request, options, response) => {
          const res: { error_code: number; message: string; data: unknown } = await response.json()
          if (res.error_code === 401 || res.error_code === 403) {
            window.location.href = '/login'
          } else if (res.error_code !== 0) {
            console.error('Error:', res.message)
          }
        }
      ]
    }
  })
}

// 默认导出使用默认前缀的实例
export const apiClient = createApi()

export default apiClient

// 导出工厂函数，允许创建自定义前缀的实例
export const createCustomClientApi = createApi
