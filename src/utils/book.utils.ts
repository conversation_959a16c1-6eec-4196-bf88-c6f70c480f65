/**
 * 书籍相关工具函数
 */

/**
 * 将书籍标题转换为 slug 格式
 * 例如：'The Buddha & the Badass' -> 'The-Buddha-the-Badass'
 * 保持原有大小写，只替换空格和特殊字符
 */
export function convertBookTitleToSlug(title: string): string {
  // 添加空值检查和默认值，防止 undefined 或 null 导致的错误
  if (!title || typeof title !== 'string') {
    return 'untitled';
  }

  let slug = title
    .replace(/[^\w\s-]/g, '') // 移除特殊字符，保留字母、数字、空格和连字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/-+/g, '-') // 移除连续的连字符
    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符

  // 限制长度，避免URL过长，保留前70个字符
  if (slug.length > 70) {
    // 尝试在单词边界截断，避免截断单词中间
    const truncated = slug.substring(0, 70);
    const lastDashIndex = truncated.lastIndexOf('-');

    if (lastDashIndex > 30) { // 确保截断后仍有合理长度
      slug = truncated.substring(0, lastDashIndex);
    } else {
      slug = truncated;
    }
  }

  return slug || 'untitled'; // 防止空字符串
}

/**
 * 生成完整的书籍 slug（标题 + ID）
 * 例如：generateBookSlug('The Buddha & the Badass', 482) -> 'The-Buddha-the-Badass-482'
 */
export function generateBookSlug(title: string, id: number | string): string {
  const titleSlug = convertBookTitleToSlug(title);
  return `${titleSlug}-${id}`;
}

/**
 * 解析书籍 slug，提取标题部分和ID
 * 支持新格式：'The-Buddha-the-Badass-482' -> { title: 'The-Buddha-the-Badass', id: 482 }
 * 支持旧格式：'482' -> { title: undefined, id: 482 }
 */
export function parseBookSlug(slug: string): { title?: string; id: number } {
  if (!slug || typeof slug !== 'string') {
    throw new Error('Invalid slug provided');
  }

  // 检查是否为纯数字（旧格式）
  const numericMatch = slug.match(/^\d+$/);
  if (numericMatch) {
    return {
      title: undefined,
      id: parseInt(slug, 10)
    };
  }

  // 解析新格式：查找最后一个连字符后的数字
  const match = slug.match(/^(.+)-(\d+)$/);
  if (match) {
    const [, titlePart, idPart] = match;
    return {
      title: titlePart,
      id: parseInt(idPart, 10)
    };
  }

  // 如果无法解析，抛出错误
  throw new Error(`Unable to parse book slug: ${slug}`);
}

/**
 * 生成书籍详情页面URL
 * 优先使用新的slug格式，如果没有title则使用旧格式
 */
export function generateBookDetailUrl(book: { id: number | string; title?: string }): string {
  if (book.title) {
    const slug = generateBookSlug(book.title, book.id);
    return `/book-summary/${slug}`;
  }

  // 降级到旧格式
  return `/book-summary/${book.id}`;
}

/**
 * 检查slug是否为旧格式（纯数字）
 */
export function isLegacyBookSlug(slug: string): boolean {
  return /^\d+$/.test(slug);
}

/**
 * 验证解析后的书籍ID是否有效
 */
export function isValidBookId(id: number): boolean {
  return Number.isInteger(id) && id > 0;
}

/**
 * 书籍数据类型定义（用于URL生成）
 */
export interface BookUrlData {
  id: number | string;
  title?: string;
}

/**
 * 批量生成书籍详情页面URL
 */
export function generateBookDetailUrls(books: BookUrlData[]): Array<{ book: BookUrlData; url: string }> {
  return books.map(book => ({
    book,
    url: generateBookDetailUrl(book)
  }));
}
