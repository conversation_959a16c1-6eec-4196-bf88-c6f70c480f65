/**
 * 分类相关工具函数
 */

/**
 * 将分类名称转换为 slug 格式
 * 例如：'Business & Finance' -> 'business-finance'
 * 保持与 CategoryModel.convertToSlug() 逻辑一致
 */
export function convertCategoryNameToSlug(name: string): string {
  // 添加空值检查和默认值，防止 undefined 或 null 导致的错误
  if (!name || typeof name !== 'string') {
    return 'uncategorized';
  }

  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/-+/g, '-'); // 移除连续的连字符
}

/**
 * 将分类数据转换为 BrowseDropdown 组件所需的格式
 */
export function convertCategoriesToDropdownFormat(categories: Array<{
  id: number;
  name: string;
  description?: string | null;
  bookCount: number;
}>): Array<{ name: string; href: string }> {
  return categories.map(category => ({
    name: category.name,
    href: `/categories/${convertCategoryNameToSlug(category.name)}`
  }));
}
