/**
 * 书籍工具函数单元测试
 */

import {
  convertBookTitleToSlug,
  generateBookSlug,
  parseBookSlug,
  generateBookDetailUrl,
  isLegacyBookSlug,
  isValidBookId
} from '../book.utils';

describe('convertBookTitleToSlug', () => {
  test('应该正确转换普通标题并保持大小写', () => {
    expect(convertBookTitleToSlug('The Buddha and the Badass')).toBe('The-Buddha-and-the-Badass');
  });

  test('应该处理特殊字符并保持大小写', () => {
    expect(convertBookTitleToSlug('Rich Dad, Poor Dad')).toBe('Rich-Dad-Poor-Dad');
    expect(convertBookTitleToSlug('The 7 Habits of Highly Effective People')).toBe('The-7-Habits-of-Highly-Effective-People');
    expect(convertBookTitleToSlug('Think & Grow Rich')).toBe('Think-Grow-Rich');
  });

  test('应该处理连续空格和连字符', () => {
    expect(convertBookTitleToSlug('The   Power    of   Now')).toBe('The-Power-of-Now');
    expect(convertBookTitleToSlug('The--Power--of--Now')).toBe('The-Power-of-Now');
  });

  test('应该处理长标题（70字符限制）', () => {
    const longTitle = 'This is a very long book title that definitely exceeds seventy characters and should be truncated properly at word boundaries';
    const result = convertBookTitleToSlug(longTitle);
    expect(result.length).toBeLessThanOrEqual(70);
    expect(result).not.toEndWith('-');
  });

  test('应该处理边界情况', () => {
    expect(convertBookTitleToSlug('')).toBe('untitled');
    expect(convertBookTitleToSlug('   ')).toBe('untitled');
    expect(convertBookTitleToSlug('!!!')).toBe('untitled');
  });

  test('应该处理null和undefined', () => {
    expect(convertBookTitleToSlug(null as any)).toBe('untitled');
    expect(convertBookTitleToSlug(undefined as any)).toBe('untitled');
  });
});

describe('generateBookSlug', () => {
  test('应该生成正确的slug格式并保持大小写', () => {
    expect(generateBookSlug('The Buddha and the Badass', 482)).toBe('The-Buddha-and-the-Badass-482');
    expect(generateBookSlug('Rich Dad, Poor Dad', 123)).toBe('Rich-Dad-Poor-Dad-123');
  });

  test('应该处理字符串ID', () => {
    expect(generateBookSlug('Test Book', '456')).toBe('Test-Book-456');
  });
});

describe('parseBookSlug', () => {
  test('应该正确解析新格式slug（保持大小写）', () => {
    const result = parseBookSlug('The-Buddha-and-the-Badass-482');
    expect(result).toEqual({
      title: 'The-Buddha-and-the-Badass',
      id: 482
    });
  });

  test('应该正确解析旧格式slug（纯数字）', () => {
    const result = parseBookSlug('482');
    expect(result).toEqual({
      title: undefined,
      id: 482
    });
  });

  test('应该处理复杂的标题', () => {
    const result = parseBookSlug('Rich-Dad-Poor-Dad-with-Many-Words-123');
    expect(result).toEqual({
      title: 'Rich-Dad-Poor-Dad-with-Many-Words',
      id: 123
    });
  });

  test('应该抛出错误对于无效格式', () => {
    expect(() => parseBookSlug('')).toThrow('Invalid slug provided');
    expect(() => parseBookSlug('invalid-slug-without-id')).toThrow('Unable to parse book slug');
    expect(() => parseBookSlug('no-number-here-abc')).toThrow('Unable to parse book slug');
  });
});

describe('generateBookDetailUrl', () => {
  test('应该生成新格式URL当有标题时（保持大小写）', () => {
    const book = { id: 482, title: 'The Buddha and the Badass' };
    expect(generateBookDetailUrl(book)).toBe('/book-summary/The-Buddha-and-the-Badass-482');
  });

  test('应该降级到旧格式当没有标题时', () => {
    const book = { id: 482 };
    expect(generateBookDetailUrl(book)).toBe('/book-summary/482');
  });

  test('应该处理空标题', () => {
    const book = { id: 482, title: '' };
    expect(generateBookDetailUrl(book)).toBe('/book-summary/482');
  });
});

describe('isLegacyBookSlug', () => {
  test('应该正确识别旧格式', () => {
    expect(isLegacyBookSlug('482')).toBe(true);
    expect(isLegacyBookSlug('123456')).toBe(true);
  });

  test('应该正确识别新格式', () => {
    expect(isLegacyBookSlug('The-Buddha-and-the-Badass-482')).toBe(false);
    expect(isLegacyBookSlug('Book-Title-123')).toBe(false);
  });
});

describe('isValidBookId', () => {
  test('应该验证有效的书籍ID', () => {
    expect(isValidBookId(1)).toBe(true);
    expect(isValidBookId(482)).toBe(true);
    expect(isValidBookId(999999)).toBe(true);
  });

  test('应该拒绝无效的书籍ID', () => {
    expect(isValidBookId(0)).toBe(false);
    expect(isValidBookId(-1)).toBe(false);
    expect(isValidBookId(1.5)).toBe(false);
    expect(isValidBookId(NaN)).toBe(false);
  });
});
