'use server'
import ky from 'ky'
import { destinationUrl } from './constants'
import { cookies } from 'next/headers'

// 检测是否在构建阶段
const isBuilding =
  process.env.NEXT_PHASE === 'phase-production-build' ||
  process.env.NEXT_PHASE === 'phase-export'

console.log('isBuilding 11111', isBuilding)

/**
 * 调试日志控制
 *
 * 启用方式：
 * 1. 开发环境：自动启用
 * 2. 生产环境：设置环境变量 ENABLE_API_DEBUG=true
 * 3. 临时启用：在浏览器控制台执行 localStorage.setItem('enableApiDebug', 'true')
 *
 * 关闭方式：
 * 1. 移除环境变量 ENABLE_API_DEBUG
 * 2. 在浏览器控制台执行 localStorage.removeItem('enableApiDebug')
 */
const getDebugEnabled = () => {
  // 开发环境默认启用
  if (process.env.NODE_ENV === 'development') return true

  // 生产环境通过环境变量控制
  if (process.env.ENABLE_API_DEBUG === 'true') return true

  // 客户端临时启用（仅在客户端环境有效）
  if (typeof window !== 'undefined') {
    return localStorage.getItem('enableApiDebug') === 'true'
  }

  return false
}

const createServerApi = (customPrefixUrl?: string) => {
  // 🔍 环境和配置分析
  const debugEnabled = getDebugEnabled()
  const finalPrefixUrl = customPrefixUrl || `${destinationUrl}/common-api/v1`

  if (debugEnabled) {
    console.log('🌍 ===== apiServer Environment Analysis =====')
    console.log('📊 Configuration:', {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
      TOKEN_KEY: process.env.TOKEN_KEY,
      PROJECT_NAME: process.env.PROJECT_NAME,
      destinationUrl,
      finalPrefixUrl,
      isBuilding,
      timestamp: new Date().toISOString()
    })
  }

  return ky.create({
    prefixUrl: finalPrefixUrl,
    credentials: 'include',
    hooks: {
      beforeRequest: [
        async (request) => {
          if (!isBuilding) {
            const cookieStore = await cookies()
            const allCookies = cookieStore.getAll()

            // 🔍 特别关注认证相关的Cookie
            const minutesToken = allCookies.find(c => c.name === 'MINUTES_ACCESS_TOKEN')
            const minutesTokenDev = allCookies.find(c => c.name === 'MINUTES_ACCESS_TOKEN-dev')
            const sessionId = allCookies.find(c => c.name === 'MINUTES_SESSION_ID')

            const cookieString = allCookies
              .map((cookie) => `${cookie.name}=${cookie.value}`)
              .join('; ')

            request.headers.set('cookie', cookieString)

            // 🔧 添加浏览器上下文头信息（修复后端认证问题）
            const baseUrl = destinationUrl || process.env.NEXT_PUBLIC_BASE_URL || 'https://www.15minutes.ai'
            const urlObj = new URL(baseUrl)
            const domain = urlObj.hostname

            // 设置关键的请求头，模拟浏览器环境
            request.headers.set('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            request.headers.set('Accept', 'application/json, text/plain, */*')
            request.headers.set('Host', domain)
            request.headers.set('Origin', baseUrl)
            request.headers.set('Referer', `${baseUrl}/`)
            request.headers.set('X-Forwarded-Host', domain)
            request.headers.set('X-Forwarded-Proto', 'https')

            if (debugEnabled) {
              console.log('🚀 ===== apiServer Request Debug =====')
              console.log('🌐 Request Info:', {
                url: request.url,
                method: request.method,
                timestamp: new Date().toISOString(),
                userAgent: request.headers.get('user-agent') || 'Not set'
              })

              console.log('🍪 Cookie Analysis:', {
                totalCookies: allCookies.length,
                cookieNames: allCookies.map(c => c.name),
                authTokens: {
                  MINUTES_ACCESS_TOKEN: {
                    exists: !!minutesToken,
                    length: minutesToken?.value.length || 0,
                    preview: minutesToken?.value ?
                      minutesToken.value.substring(0, 50) + (minutesToken.value.length > 50 ? '...' : '') : 'N/A'
                  },
                  'MINUTES_ACCESS_TOKEN-dev': {
                    exists: !!minutesTokenDev,
                    length: minutesTokenDev?.value.length || 0,
                    preview: minutesTokenDev?.value ?
                      minutesTokenDev.value.substring(0, 50) + (minutesTokenDev.value.length > 50 ? '...' : '') : 'N/A'
                  },
                  MINUTES_SESSION_ID: {
                    exists: !!sessionId,
                    length: sessionId?.value.length || 0
                  }
                },
                cookieStringLength: cookieString.length,
                cookieStringPreview: cookieString.substring(0, 300) + (cookieString.length > 300 ? '...' : '')
              })

              console.log('📋 All Cookies Detail:', allCookies.map(cookie => ({
                name: cookie.name,
                valueLength: cookie.value.length,
                valuePreview: cookie.name.includes('TOKEN')
                  ? cookie.value.substring(0, 30) + '...'
                  : cookie.value.substring(0, 50),
                hasSpecialChars: /[;,\n\r]/.test(cookie.value),
                isEmpty: !cookie.value
              })))

              console.log('🔧 Request Headers:', {
                cookie: request.headers.get('cookie')?.substring(0, 200) + '...',
                userAgent: request.headers.get('user-agent'),
                accept: request.headers.get('accept'),
                host: request.headers.get('host'),
                origin: request.headers.get('origin'),
                referer: request.headers.get('referer'),
                xForwardedHost: request.headers.get('x-forwarded-host'),
                xForwardedProto: request.headers.get('x-forwarded-proto'),
                contentType: request.headers.get('content-type'),
                authorization: request.headers.get('authorization') ? '[PRESENT]' : '[NOT SET]',
                allHeaderNames: Array.from(request.headers.keys())
              })

              console.log('🌐 Browser Context Headers Added:', {
                baseUrl: baseUrl,
                domain: domain,
                contextHeadersSet: [
                  'User-Agent',
                  'Accept',
                  'Host',
                  'Origin',
                  'Referer',
                  'X-Forwarded-Host',
                  'X-Forwarded-Proto'
                ]
              })
            }
          }
        }
      ],
      afterResponse: [
        async (request, _options, response) => {
          if (debugEnabled) {
            console.log('📥 ===== apiServer Response Debug =====')
            console.log('🌐 Response Info:', {
              url: request.url,
              status: response.status,
              statusText: response.statusText,
              timestamp: new Date().toISOString(),
              responseTime: Date.now() - (request as any).startTime || 'Unknown'
            })

            console.log('📋 Response Headers:', {
              contentType: response.headers.get('content-type'),
              contentLength: response.headers.get('content-length'),
              setCookie: response.headers.get('set-cookie') ? '[PRESENT]' : '[NOT SET]',
              cacheControl: response.headers.get('cache-control'),
              server: response.headers.get('server'),
              allHeaderNames: Array.from(response.headers.keys())
            })
          }

          // 🚨 特别关注401错误的详细分析
          if (response.status === 401) {
            console.error('❌ ===== 401 UNAUTHORIZED ERROR ANALYSIS =====')
            console.error('🌐 Failed Request Details:', {
              url: request.url,
              method: request.method,
              status: response.status,
              statusText: response.statusText,
              timestamp: new Date().toISOString()
            })

            try {
              const responseText = await response.clone().text()
              console.error('📄 Response Body:', responseText)

              // 尝试解析JSON响应
              try {
                const responseJson = JSON.parse(responseText)
                console.error('📊 Parsed Error Response:', {
                  code: responseJson.code,
                  message: responseJson.message,
                  error_code: responseJson.error_code,
                  data: responseJson.data
                })
              } catch (e) {
                console.error('📄 Raw Response (not JSON):', responseText.substring(0, 500))
              }
            } catch (e) {
              console.error('❌ Could not read response body:', e)
            }

            // 🔍 重新检查当前Cookie状态
            try {
              const cookieStore = await cookies()
              const currentToken = cookieStore.get('MINUTES_ACCESS_TOKEN')
              const currentTokenDev = cookieStore.get('MINUTES_ACCESS_TOKEN-dev')
              const allCurrentCookies = cookieStore.getAll()

              console.error('🔍 Current Cookie State Analysis:', {
                totalCookies: allCurrentCookies.length,
                hasMinutesToken: !!currentToken,
                hasMinutesTokenDev: !!currentTokenDev,
                tokenLength: currentToken?.value.length || 0,
                tokenDevLength: currentTokenDev?.value.length || 0,
                allCookieNames: allCurrentCookies.map(c => c.name),
                cookieComparison: {
                  expectedToken: 'MINUTES_ACCESS_TOKEN',
                  actualTokens: allCurrentCookies.filter(c => c.name.includes('TOKEN')).map(c => c.name)
                }
              })

              console.error('🔧 Debug Suggestions:', {
                step1: 'Check if MINUTES_ACCESS_TOKEN exists in cookies',
                step2: 'Verify token is not expired by checking Redis',
                step3: 'Compare with direct browser request to same URL',
                step4: 'Check PHP backend logs for this request',
                step5: 'Verify cookie domain and path settings'
              })
            } catch (cookieError) {
              console.error('❌ Could not check current cookie state:', cookieError)
            }
          }

          // 🎉 记录成功请求的关键信息
          if (response.status === 200 && debugEnabled) {
            console.log('✅ ===== Successful Request =====')
            console.log('🎯 Success Details:', {
              url: request.url,
              status: response.status,
              contentType: response.headers.get('content-type'),
              timestamp: new Date().toISOString()
            })
          }

          return response
        }
      ]
    }
  })
}

export const apiServer = createServerApi()

export default apiServer
