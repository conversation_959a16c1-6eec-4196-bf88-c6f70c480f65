/**
 * Amazon URL 构建工具函数
 * 提供统一的 Amazon 链接生成逻辑
 */

/**
 * 构建 Amazon 搜索 URL
 * 优化策略：直接使用书籍标题和作者进行搜索，避免因 ASIN/ISBN 不存在而导致的 404 错误
 * 
 * @param bookTitle 书籍标题
 * @param author 作者名称（可选）
 * @returns Amazon 搜索页面 URL
 */
export function buildAmazonSearchUrl(bookTitle: string, author?: string): string {
  // 构建搜索关键词：书籍标题 + 作者名（如果有的话）
  let searchQuery = bookTitle.trim();
  
  if (author && author.trim()) {
    searchQuery += ` ${author.trim()}`;
  }
  
  // 使用 Amazon 搜索页面，限定在图书分类，确保用户能看到相关结果
  return `https://www.amazon.com/s?k=${encodeURIComponent(searchQuery)}&i=stripbooks`;
}

/**
 * 构建 Amazon 直接链接（用于向后兼容）
 * 如果有 ASIN 或 ISBN，尝试直接链接；否则降级到搜索
 * 
 * @param asin Amazon 标准识别号
 * @param isbn 国际标准书号
 * @param bookTitle 书籍标题
 * @param author 作者名称（可选）
 * @returns Amazon URL
 */
export function buildAmazonUrl(
  asin?: string, 
  isbn?: string, 
  bookTitle?: string, 
  author?: string
): string {
  // 如果有 ASIN，使用直接链接
  if (asin && asin.trim()) {
    return `https://www.amazon.com/dp/${asin.trim()}`;
  }
  
  // 如果有 ISBN，使用直接链接
  if (isbn && isbn.trim()) {
    return `https://www.amazon.com/dp/${isbn.trim()}`;
  }
  
  // 降级到搜索
  if (bookTitle) {
    return buildAmazonSearchUrl(bookTitle, author);
  }
  
  // 最后的降级选项
  return 'https://www.amazon.com/books';
}

/**
 * 构建优化的 Amazon URL（推荐使用）
 * 直接使用搜索策略，提供更好的用户体验
 * 
 * @param bookTitle 书籍标题
 * @param author 作者名称（可选）
 * @returns Amazon 搜索页面 URL
 */
export function buildOptimizedAmazonUrl(bookTitle: string, author?: string): string {
  return buildAmazonSearchUrl(bookTitle, author);
}

/**
 * Amazon URL 构建选项
 */
export interface AmazonUrlOptions {
  /** 书籍标题 */
  title: string;
  /** 作者名称 */
  author?: string;
  /** Amazon 标准识别号 */
  asin?: string;
  /** 国际标准书号 */
  isbn?: string;
  /** 是否使用优化策略（直接搜索而不是尝试直接链接） */
  useOptimizedStrategy?: boolean;
}

/**
 * 根据选项构建 Amazon URL
 * 
 * @param options Amazon URL 构建选项
 * @returns Amazon URL
 */
export function buildAmazonUrlFromOptions(options: AmazonUrlOptions): string {
  const { title, author, asin, isbn, useOptimizedStrategy = true } = options;
  
  if (useOptimizedStrategy) {
    return buildOptimizedAmazonUrl(title, author);
  } else {
    return buildAmazonUrl(asin, isbn, title, author);
  }
}
