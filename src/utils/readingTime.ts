/**
 * 阅读时长计算工具函数
 * 用于根据文本内容计算预估阅读时间
 */

/**
 * 计算单个文本的阅读时长
 * @param text 文本内容
 * @param wordsPerMinute 每分钟阅读单词数，默认200（适合中等阅读速度）
 * @returns 阅读时长（分钟）
 */
export function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {
  if (!text || typeof text !== 'string') {
    return 0;
  }

  try {
    // 移除HTML标签和特殊字符，只保留文本内容
    const cleanText = text
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/[^\w\s]/g, ' ') // 移除标点符号，替换为空格
      .replace(/\s+/g, ' ') // 合并多个空格为单个空格
      .trim();

    // 如果清理后没有内容，返回0
    if (!cleanText) {
      return 0;
    }

    // 计算单词数（按空格分割）
    const words = cleanText.split(' ').filter(word => word.length > 0);
    const wordCount = words.length;

    // 计算阅读时长（分钟），向上取整
    const readingTime = Math.ceil(wordCount / wordsPerMinute);

    return readingTime;
  } catch (error) {
    console.error('计算单个文本阅读时长失败:', error);
    return 0;
  }
}

/**
 * 计算章节总阅读时长
 * @param chapters 章节数组，每个章节包含summary字段
 * @param wordsPerMinute 每分钟阅读单词数，默认200
 * @returns 总阅读时长（分钟）
 */
export function calculateChaptersReadingTime(
  chapters: Array<{ summary: string }>, 
  wordsPerMinute: number = 200
): number {
  try {
    // 检查输入参数
    if (!chapters || !Array.isArray(chapters) || chapters.length === 0) {
      return 1; // 默认最少1分钟
    }

    // 计算所有章节的总阅读时长
    const totalTime = chapters.reduce((total, chapter) => {
      if (!chapter || !chapter.summary) {
        return total;
      }
      
      const chapterTime = calculateReadingTime(chapter.summary, wordsPerMinute);
      return total + chapterTime;
    }, 0);

    // 确保最少1分钟，避免显示0分钟
    return Math.max(totalTime, 1);
  } catch (error) {
    console.error('计算章节总阅读时长失败:', error);
    return 1; // 出错时返回默认值
  }
}

/**
 * 格式化阅读时长为显示文本
 * @param minutes 阅读时长（分钟）
 * @param includeUnit 是否包含单位，默认true
 * @returns 格式化的时长文本
 */
export function formatReadingTime(minutes: number, includeUnit: boolean = true): string {
  try {
    if (!minutes || minutes <= 0) {
      return includeUnit ? '1 minute' : '1';
    }

    // 如果超过60分钟，显示小时和分钟
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      
      if (remainingMinutes === 0) {
        return includeUnit 
          ? `${hours} hour${hours > 1 ? 's' : ''}`
          : `${hours}h`;
      } else {
        return includeUnit
          ? `${hours}h ${remainingMinutes}m`
          : `${hours}:${remainingMinutes.toString().padStart(2, '0')}`;
      }
    }

    // 小于60分钟，只显示分钟
    return includeUnit 
      ? `${minutes} minute${minutes > 1 ? 's' : ''}`
      : `${minutes}`;
  } catch (error) {
    console.error('格式化阅读时长失败:', error);
    return includeUnit ? '1 minute' : '1';
  }
}

/**
 * 根据文本长度估算阅读难度并调整阅读速度
 * @param text 文本内容
 * @param baseWordsPerMinute 基础阅读速度
 * @returns 调整后的阅读速度
 */
export function adjustReadingSpeedByComplexity(
  text: string, 
  baseWordsPerMinute: number = 200
): number {
  try {
    if (!text || typeof text !== 'string') {
      return baseWordsPerMinute;
    }

    // 计算平均单词长度（复杂度指标）
    const words = text.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0);
    const averageWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;

    // 根据平均单词长度调整阅读速度
    if (averageWordLength > 6) {
      // 复杂文本，降低阅读速度
      return Math.round(baseWordsPerMinute * 0.8);
    } else if (averageWordLength < 4) {
      // 简单文本，提高阅读速度
      return Math.round(baseWordsPerMinute * 1.2);
    }

    // 中等复杂度，使用基础速度
    return baseWordsPerMinute;
  } catch (error) {
    console.error('调整阅读速度失败:', error);
    return baseWordsPerMinute;
  }
}

/**
 * 计算章节总阅读时长（智能版本）
 * 会根据文本复杂度自动调整阅读速度
 * @param chapters 章节数组
 * @param baseWordsPerMinute 基础阅读速度，默认200
 * @returns 总阅读时长（分钟）
 */
export function calculateSmartChaptersReadingTime(
  chapters: Array<{ summary: string }>, 
  baseWordsPerMinute: number = 200
): number {
  try {
    if (!chapters || !Array.isArray(chapters) || chapters.length === 0) {
      return 1;
    }

    // 合并所有章节文本来分析整体复杂度
    const allText = chapters
      .map(chapter => chapter.summary || '')
      .join(' ');

    // 根据文本复杂度调整阅读速度
    const adjustedSpeed = adjustReadingSpeedByComplexity(allText, baseWordsPerMinute);

    // 使用调整后的速度计算阅读时长
    return calculateChaptersReadingTime(chapters, adjustedSpeed);
  } catch (error) {
    console.error('智能计算章节阅读时长失败:', error);
    return calculateChaptersReadingTime(chapters, baseWordsPerMinute);
  }
}
