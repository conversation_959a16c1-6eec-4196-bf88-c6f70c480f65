export const getFirstChar = (username: string) => {
  return username.charAt(0).toUpperCase()
}

export const getBgColor = (str: string) => {
  const colors = ['#4285F4', '#0F9D58', '#F4B400', '#DB4437', '#FF8C00']
  const index = str ? str.charCodeAt(0) % colors.length : 0
  return colors[index]
}

// 导出书籍相关工具函数
export {
  convertBookTitleToSlug,
  generateBookSlug,
  parseBookSlug,
  generateBookDetailUrl,
  isLegacyBookSlug,
  isValidBookId,
  type BookUrlData
} from './book.utils'

// 导出分类相关工具函数
export {
  convertCategoryNameToSlug,
  convertCategoriesToDropdownFormat
} from './category.utils'

