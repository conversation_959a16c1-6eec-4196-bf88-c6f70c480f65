'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useCookies } from 'react-cookie'
import { distributionClick } from '@/services/client/affiliate'

const PROJECT_NAME = process.env.NEXT_PUBLIC_PROJECT || ''

// 内部组件，使用 useSearchParams
function AffiliateTrackerContent() {
  const searchParams = useSearchParams()
  // Define the cookie name explicitly
  const refCodeKey = `${PROJECT_NAME}_REF_CODE`

  // Specify the cookie type with an index signature
  const [cookies, setCookie] = useCookies([refCodeKey])

  useEffect(() => {
    const handleAffiliateTracking = async () => {
      const queryRef = searchParams.get('ref')

      if (queryRef) {
        await distributionClick({
          queryRef: queryRef
        })

        // Access the cookie safely with bracket notation and type assertion
        const existingRefCode = cookies[refCodeKey as keyof typeof cookies]

        if (!existingRefCode) {
          const date = new Date()
          date.setTime(date.getTime() + 90 * 24 * 60 * 60 * 1000)

          setCookie(refCodeKey, queryRef, {
            expires: date,
            path: '/'
          })
        }
      }
    }

    handleAffiliateTracking()
  }, [searchParams, cookies, setCookie, refCodeKey])

  return null // 这个组件不渲染任何内容
}

// 主组件，使用条件渲染
export default function AffiliateTracker() {
  // 使用状态来确保组件只在客户端渲染
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // 如果不是客户端，返回 null
  if (!isClient) return null

  // 只在客户端渲染时渲染内部组件
  return <AffiliateTrackerContent />
}
