'use client'

import { motion, useAnimate, stagger } from 'framer-motion'
import { useEffect, useMemo } from 'react'

interface TextAnimationProps {
  text: string
  className?: string
  onAnimationComplete?: () => void
  loop?: boolean
}

export const TextAnimation = ({
  text,
  className = '',
  onAnimationComplete,
  loop = false
}: TextAnimationProps) => {
  const [scope, animate] = useAnimate()

  const letters = useMemo(() => {
    return text.match(/\S|\s/g) || []
  }, [text])

  useEffect(() => {
    let isMounted = true

    const sequence = async () => {
      if (!isMounted) return

      try {
        await animate([
          ['.letter', { opacity: 0 }, { duration: 0 }],
          ['.line', { opacity: 0, scaleY: 0 }, { duration: 0 }]
        ])

        await animate([
          ['.letter', { opacity: [0, 1] }, { duration: 0.6, delay: stagger(0.034) }],
          ['.line', { opacity: [0, 1], scaleY: [0, 1] }, { duration: 0.6 }]
        ])

        if (!isMounted) return

        if (loop) {
          await animate([
            ['.letter', { opacity: 0 }, { duration: 0.3, delay: 0.6 }],
            ['.line', { opacity: 0, scaleY: 0 }, { duration: 0.3 }]
          ])

          if (!isMounted) return
          sequence()
        } else {
          onAnimationComplete?.()
        }
      } catch (error) {
        console.error('Animation error:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          componentName: 'TextAnimation',
          text
        })
      }
    }

    sequence()

    return () => {
      isMounted = false
    }
  }, [animate, onAnimationComplete, loop, text])

  return (
    <motion.div ref={scope} className={`ml11 ${className}`}>
      <span className="text-wrapper relative inline-block pt-[0.1em] pr-[0.05em] pb-[0.15em]">
        <motion.span className="line line1 opacity-0 absolute left-0 h-full w-[3px] bg-white origin-[0_50%]" />
        <span className="letters whitespace-pre-wrap">
          {letters.map((char, index) => (
            <motion.span key={`${char}-${index}`} className="letter inline-block leading-[1em]">
              {char}
            </motion.span>
          ))}
        </span>
      </span>
    </motion.div>
  )
}
