/**
 * 重构后的AudioPlayer主组件
 */

import React, { useCallback } from 'react';
import { AudioPlayerProps } from './types';
import { useAudioPlayer } from './hooks/useAudioPlayer';
import { useProgressBarDrag } from './hooks/useProgressBarDrag';

// 导入子组件
import ProgressBar from './components/ProgressBar';
import PlaybackControls from './components/PlaybackControls';
import BookInfo from './components/BookInfo';
import StatusIndicators from './components/StatusIndicators';
import CloseButton from './components/CloseButton';

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  book,
  isActive,
  autoPlay = false,
  locale = 'en',
  onClose
}) => {
  // 使用核心音频播放器hook
  const {
    audioRef,
    audioUrl,
    isLoading,
    error,
    isPlaying,
    currentTime,
    duration,
    progress,
    formattedCurrentTime,
    formattedDuration,
    play,
    pause,
    seek,
    skip,
    retry,
    handlePlay,
    handlePause,
    handleLoadedMetaData,
    handleTimeUpdate,
    handleEnded,
    handleError,
    showPlayPrompt,
    setShowPlayPrompt
  } = useAudioPlayer({
    bookId: parseInt(book.id),
    locale,
    autoPlay,
    isActive
  });

  // 使用进度条拖拽hook
  const {
    isDragging,
    dragProgress,
    handleMouseDown,
    handleTouchStart
  } = useProgressBarDrag({
    audioRef,
    onSeek: seek,
    disabled: isLoading || !!error
  });

  // 播放控制处理
  const handlePlayClick = useCallback(async () => {
    try {
      await play();
    } catch (error) {
      console.error('[AudioPlayer] Play failed:', error);
    }
  }, [play]);

  const handleSkipBackward = useCallback((seconds: number) => {
    skip(-seconds);
  }, [skip]);

  const handleSkipForward = useCallback((seconds: number) => {
    skip(seconds);
  }, [skip]);

  // 状态指示器处理
  const handleDismissPrompt = useCallback(() => {
    setShowPlayPrompt(false);
  }, [setShowPlayPrompt]);

  // 如果播放器未激活，不渲染
  if (!isActive) {
    return null;
  }

  // 主容器样式
  const containerStyles: React.CSSProperties = {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    width: '400px',
    maxWidth: 'calc(100vw - 40px)',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
    zIndex: 1000,
    overflow: 'hidden',
  };

  return (
    <div className="audio-player" style={containerStyles}>
      {/* 关闭按钮 */}
      <CloseButton onClose={onClose} />

      {/* 顶部进度条 */}
      <ProgressBar
        progress={progress}
        duration={duration}
        currentTime={currentTime}
        isDragging={isDragging}
        dragProgress={dragProgress}
        disabled={isLoading || !!error}
        onSeek={seek}
        audioRef={audioRef}
        handleMouseDown={handleMouseDown}
        handleTouchStart={handleTouchStart}
      />

      {/* 书籍信息 */}
      <BookInfo book={book} />

      {/* 播放控制 */}
      <PlaybackControls
        isPlaying={isPlaying}
        currentTime={formattedCurrentTime}
        duration={formattedDuration}
        onPlay={handlePlayClick}
        onPause={pause}
        onSkipBackward={handleSkipBackward}
        onSkipForward={handleSkipForward}
      />

      {/* 状态指示器 */}
      <StatusIndicators
        isLoading={isLoading}
        error={error}
        showPlayPrompt={showPlayPrompt}
        onRetry={retry}
        onDismissPrompt={handleDismissPrompt}
      />

      {/* 隐藏的音频元素 */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          preload="metadata"
          onPlay={handlePlay}
          onPause={handlePause}
          onLoadedMetadata={handleLoadedMetaData}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
          onError={handleError}
          style={{ display: 'none' }}
        />
      )}
    </div>
  );
};

export default AudioPlayer;
