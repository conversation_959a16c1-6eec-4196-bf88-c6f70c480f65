// 主组件导出
export { AudioPlayer as default } from './AudioPlayer';
export { AudioPlayer } from './AudioPlayer';

// 类型导出
export type * from './types';

// 子组件导出（显式导出以避免命名冲突）
export { ProgressBar } from './components/ProgressBar';
export { PlaybackControls } from './components/PlaybackControls';
export { BookInfo as BookInfoComponent } from './components/BookInfo';
export { StatusIndicators } from './components/StatusIndicators';
export { CloseButton } from './components/CloseButton';

// Hooks导出
export * from './hooks';

// 工具函数导出
export * from './utils/audioUtils';
