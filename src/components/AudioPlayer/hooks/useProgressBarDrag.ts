/**
 * 进度条拖拽交互Hook
 */

import { useState, useRef, useCallback } from 'react';
import { UseProgressBarDragOptions, UseProgressBarDragReturn } from '../types';
import {
  isAudioReady,
  isDragThresholdExceeded,
  calculateProgressFromPosition,
  calculateTimeFromProgress
} from '../utils/audioUtils';

export function useProgressBarDrag({
  audioRef,
  onSeek,
  disabled = false
}: UseProgressBarDragOptions): UseProgressBarDragReturn {
  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(0);

  // 引用
  const progressBarRef = useRef<HTMLDivElement | null>(null);
  const dragAnimationRef = useRef<number | null>(null);
  const dragStartTimeRef = useRef<number>(0);
  const dragStartPositionRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const isDragThresholdExceededRef = useRef<boolean>(false);
  const currentDragProgressRef = useRef<number>(0);
  const wasPlayingBeforeDragRef = useRef<boolean>(false);

  /**
   * 内部点击处理函数
   */
  const handleProgressClickInternal = useCallback((clientX: number) => {
    const audio = audioRef.current;
    if (!isAudioReady(audio) || !audio || !progressBarRef.current || disabled) {
      return;
    }

    const clickPercentage = calculateProgressFromPosition(clientX, progressBarRef.current);
    const seekTime = calculateTimeFromProgress(clickPercentage, audio.duration);

    console.log('[useProgressBarDrag] Click seek to:', seekTime);
    onSeek(seekTime);
  }, [audioRef, onSeek, disabled]);

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return;

    e.preventDefault();
    e.stopPropagation();

    const audio = audioRef.current;
    if (!isAudioReady(audio) || !audio) {
      console.warn('[useProgressBarDrag] Cannot interact: audio not ready');
      return;
    }

    // 记录拖拽开始状态
    dragStartTimeRef.current = Date.now();
    dragStartPositionRef.current = { x: e.clientX, y: e.clientY };
    isDragThresholdExceededRef.current = false;
    progressBarRef.current = e.currentTarget;
    wasPlayingBeforeDragRef.current = !audio.paused;

    // 全局鼠标移动处理
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const currentTime = Date.now();
      const currentPosition = { x: moveEvent.clientX, y: moveEvent.clientY };

      // 检查是否超过拖拽阈值
      if (isDragThresholdExceeded(
        dragStartTimeRef.current,
        dragStartPositionRef.current,
        currentTime,
        currentPosition
      )) {
        if (!isDragThresholdExceededRef.current) {
          // 开始真正的拖拽
          isDragThresholdExceededRef.current = true;
          setIsDragging(true);
          console.log('[useProgressBarDrag] Drag threshold exceeded, starting drag');
        }

        // 更新拖拽进度
        if (isDragThresholdExceededRef.current && progressBarRef.current) {
          if (dragAnimationRef.current) {
            cancelAnimationFrame(dragAnimationRef.current);
          }

          dragAnimationRef.current = requestAnimationFrame(() => {
            if (!progressBarRef.current) return;

            const dragPercentage = calculateProgressFromPosition(
              moveEvent.clientX,
              progressBarRef.current
            );

            currentDragProgressRef.current = dragPercentage;
            setDragProgress(dragPercentage);
          });
        }
      }
    };

    // 全局鼠标释放处理
    const handleMouseUp = (upEvent: MouseEvent) => {
      // 清理动画帧
      if (dragAnimationRef.current) {
        cancelAnimationFrame(dragAnimationRef.current);
        dragAnimationRef.current = null;
      }

      // 移除全局事件监听
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      const currentTime = Date.now();
      const currentPosition = { x: upEvent.clientX, y: upEvent.clientY };

      // 判断是点击还是拖拽
      if (!isDragThresholdExceededRef.current && !isDragThresholdExceeded(
        dragStartTimeRef.current,
        dragStartPositionRef.current,
        currentTime,
        currentPosition
      )) {
        // 这是点击事件
        console.log('[useProgressBarDrag] Detected click, performing seek');
        handleProgressClickInternal(upEvent.clientX);
      } else if (isDragThresholdExceededRef.current && audio) {
        // 这是拖拽事件
        console.log('[useProgressBarDrag] Drag ended, applying final position');

        const finalDragProgress = currentDragProgressRef.current;
        const finalTime = calculateTimeFromProgress(finalDragProgress, audio.duration);

        onSeek(finalTime);
      }

      // 重置拖拽状态
      setIsDragging(false);
      progressBarRef.current = null;
    };

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

  }, [audioRef, onSeek, disabled, handleProgressClickInternal]);

  /**
   * 处理触摸开始事件
   */
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    if (disabled) return;

    e.preventDefault();
    e.stopPropagation();

    const audio = audioRef.current;
    if (!isAudioReady(audio) || !audio) {
      console.warn('[useProgressBarDrag] Cannot interact: audio not ready');
      return;
    }

    const touch = e.touches[0];

    // 记录拖拽开始状态
    dragStartTimeRef.current = Date.now();
    dragStartPositionRef.current = { x: touch.clientX, y: touch.clientY };
    isDragThresholdExceededRef.current = false;
    progressBarRef.current = e.currentTarget;
    wasPlayingBeforeDragRef.current = !audio.paused;

    // 全局触摸移动处理
    const handleTouchMove = (moveEvent: TouchEvent) => {
      moveEvent.preventDefault();

      const moveTouch = moveEvent.touches[0];
      const currentTime = Date.now();
      const currentPosition = { x: moveTouch.clientX, y: moveTouch.clientY };

      // 检查是否超过拖拽阈值
      if (isDragThresholdExceeded(
        dragStartTimeRef.current,
        dragStartPositionRef.current,
        currentTime,
        currentPosition
      )) {
        if (!isDragThresholdExceededRef.current) {
          // 开始真正的拖拽
          isDragThresholdExceededRef.current = true;
          setIsDragging(true);
          console.log('[useProgressBarDrag] Touch drag threshold exceeded, starting drag');
        }

        // 更新拖拽进度
        if (isDragThresholdExceededRef.current && progressBarRef.current) {
          if (dragAnimationRef.current) {
            cancelAnimationFrame(dragAnimationRef.current);
          }

          dragAnimationRef.current = requestAnimationFrame(() => {
            if (!progressBarRef.current) return;

            const dragPercentage = calculateProgressFromPosition(
              moveTouch.clientX,
              progressBarRef.current
            );

            currentDragProgressRef.current = dragPercentage;
            setDragProgress(dragPercentage);
          });
        }
      }
    };

    // 全局触摸结束处理
    const handleTouchEnd = (endEvent: TouchEvent) => {
      // 清理动画帧
      if (dragAnimationRef.current) {
        cancelAnimationFrame(dragAnimationRef.current);
        dragAnimationRef.current = null;
      }

      // 移除全局事件监听
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      const currentTime = Date.now();
      const endTouch = endEvent.changedTouches[0];
      const currentPosition = { x: endTouch.clientX, y: endTouch.clientY };

      // 判断是点击还是拖拽
      if (!isDragThresholdExceededRef.current && !isDragThresholdExceeded(
        dragStartTimeRef.current,
        dragStartPositionRef.current,
        currentTime,
        currentPosition
      )) {
        // 这是点击事件
        console.log('[useProgressBarDrag] Detected touch tap, performing seek');
        handleProgressClickInternal(endTouch.clientX);
      } else if (isDragThresholdExceededRef.current && audio) {
        // 这是拖拽事件
        console.log('[useProgressBarDrag] Touch drag ended, applying final position');

        const finalDragProgress = currentDragProgressRef.current;
        const finalTime = calculateTimeFromProgress(finalDragProgress, audio.duration);

        onSeek(finalTime);
      }

      // 重置拖拽状态
      setIsDragging(false);
      progressBarRef.current = null;
    };

    // 添加全局事件监听
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);

  }, [audioRef, onSeek, disabled, handleProgressClickInternal]);

  return {
    isDragging,
    dragProgress,
    handleMouseDown,
    handleTouchStart,
    progressBarRef,
  };
}
