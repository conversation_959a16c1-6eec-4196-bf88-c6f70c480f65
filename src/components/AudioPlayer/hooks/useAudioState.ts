/**
 * 音频状态管理Hook
 */

import { useState, useCallback } from 'react';
import { formatTime, calculateProgress } from '../utils/audioUtils';

interface UseAudioStateReturn {
  // 播放状态
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progress: number;
  playbackRate: number;
  volume: number;
  
  // 格式化时间
  formattedCurrentTime: string;
  formattedDuration: string;
  
  // 状态更新方法
  setIsPlaying: (playing: boolean) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  setPlaybackRate: (rate: number) => void;
  setVolume: (volume: number) => void;
  
  // 复合更新方法
  updateAudioState: (state: {
    currentTime?: number;
    duration?: number;
    playbackRate?: number;
    volume?: number;
    isPlaying?: boolean;
  }) => void;
}

export function useAudioState(): UseAudioStateReturn {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [playbackRate, setPlaybackRate] = useState(1.0);
  const [volume, setVolume] = useState(1.0);

  // 计算进度百分比
  const progress = calculateProgress(currentTime, duration);

  // 格式化时间显示
  const formattedCurrentTime = formatTime(currentTime);
  const formattedDuration = formatTime(duration);

  // 复合状态更新方法
  const updateAudioState = useCallback((state: {
    currentTime?: number;
    duration?: number;
    playbackRate?: number;
    volume?: number;
    isPlaying?: boolean;
  }) => {
    if (typeof state.currentTime === 'number') {
      setCurrentTime(state.currentTime);
    }
    if (typeof state.duration === 'number') {
      setDuration(state.duration);
    }
    if (typeof state.playbackRate === 'number') {
      setPlaybackRate(state.playbackRate);
    }
    if (typeof state.volume === 'number') {
      setVolume(state.volume);
    }
    if (typeof state.isPlaying === 'boolean') {
      setIsPlaying(state.isPlaying);
    }
  }, []);

  return {
    // 状态
    isPlaying,
    currentTime,
    duration,
    progress,
    playbackRate,
    volume,
    
    // 格式化时间
    formattedCurrentTime,
    formattedDuration,
    
    // 更新方法
    setIsPlaying,
    setCurrentTime,
    setDuration,
    setPlaybackRate,
    setVolume,
    updateAudioState,
  };
}
