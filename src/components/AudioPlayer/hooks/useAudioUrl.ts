/**
 * 音频URL获取Hook
 */

import { useState, useEffect, useCallback } from 'react';

interface UseAudioUrlOptions {
  bookId: string;
  locale: string;
  isActive: boolean;
}

interface UseAudioUrlReturn {
  audioUrl: string | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useAudioUrl({ bookId, locale, isActive }: UseAudioUrlOptions): UseAudioUrlReturn {
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAudioUrl = useCallback(async () => {
    if (!isActive || audioUrl) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/audio/${bookId}?locale=${locale}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success && data.data.audioUrl) {
        setAudioUrl(data.data.audioUrl);
        console.log('[useAudioUrl] Audio URL loaded:', data.data.audioUrl);
      } else {
        setError(data.error || 'Failed to load audio');
        console.error('[useAudioUrl] Failed to load audio:', data.error);
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('[useAudioUrl] Network error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bookId, locale, isActive, audioUrl]);

  const refetch = useCallback(() => {
    setAudioUrl(null); // 重置URL以触发重新获取
    fetchAudioUrl();
  }, [fetchAudioUrl]);

  // 当播放器激活时获取音频URL
  useEffect(() => {
    if (isActive) {
      fetchAudioUrl();
    }
  }, [isActive, fetchAudioUrl]);

  return {
    audioUrl,
    isLoading,
    error,
    refetch,
  };
}
