/**
 * 音频播放器核心Hook
 */

import { useRef, useState, useCallback, useEffect } from 'react';
import { UseAudioPlayerOptions, UseAudioPlayerReturn } from '../types';
import { useAudioProgress } from '@/hooks/useAudioProgress';
import { useAudioUrl } from './useAudioUrl';
import { useAudioState } from './useAudioState';
import { safeSeekAudio, isAudioReady } from '../utils/audioUtils';

export function useAudioPlayer({
  bookId,
  locale,
  autoPlay,
  isActive
}: UseAudioPlayerOptions): UseAudioPlayerReturn {
  // 转换bookId为字符串，因为API需要字符串类型
  const bookIdStr = bookId.toString();
  // 音频元素引用
  const audioRef = useRef<HTMLAudioElement>(null);

  // 状态管理
  const [showPlayPrompt, setShowPlayPrompt] = useState(false);
  const [autoPlayAttempted, setAutoPlayAttempted] = useState(false);
  const [progressRestored, setProgressRestored] = useState(false);

  // 使用其他hooks
  const { audioUrl, isLoading, error, refetch } = useAudioUrl({
    bookId: bookIdStr,
    locale,
    isActive
  });

  const {
    isPlaying,
    currentTime,
    duration,
    progress,
    formattedCurrentTime,
    formattedDuration,
    setIsPlaying,
    updateAudioState
  } = useAudioState();

  const {
    progress: savedProgress,
    updateProgress,
    updatePlayingState,
    markAsCompleted,
  } = useAudioProgress({
    bookId: bookId, // bookId已经是number类型
    autoSave: true,
    saveInterval: 3000
  });

  /**
   * 播放音频
   */
  const play = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      await audio.play();
      setShowPlayPrompt(false);
    } catch (error) {
      console.error('[useAudioPlayer] Play failed:', error);
      setShowPlayPrompt(true);
      throw error;
    }
  }, []);

  /**
   * 暂停音频
   */
  const pause = useCallback(() => {
    const audio = audioRef.current;
    if (audio && !audio.paused) {
      audio.pause();
    }
  }, []);

  /**
   * 跳转到指定时间
   */
  const seek = useCallback((time: number) => {
    const audio = audioRef.current;
    if (!isAudioReady(audio) || !audio) {
      console.warn('[useAudioPlayer] Cannot seek: audio not ready');
      return;
    }

    const wasPlaying = !audio.paused;

    // 暂停自动保存
    if (wasPlaying) {
      updatePlayingState(false);
    }

    // 执行跳转
    if (safeSeekAudio(audio, time)) {
      // 更新本地状态
      updateAudioState({ currentTime: time });

      // 立即保存进度
      updateProgress(
        time,
        audio.duration,
        audio.playbackRate,
        audio.volume,
        wasPlaying
      );

      // 恢复播放状态
      if (wasPlaying) {
        setTimeout(() => {
          updatePlayingState(true);
        }, 100);
      }

      console.log('[useAudioPlayer] Seek completed to:', time);
    }
  }, [updatePlayingState, updateProgress, updateAudioState]);

  /**
   * 快进/快退
   */
  const skip = useCallback((seconds: number) => {
    const audio = audioRef.current;
    if (!isAudioReady(audio) || !audio) return;

    const newTime = Math.max(0, Math.min(audio.duration, audio.currentTime + seconds));
    seek(newTime);
  }, [seek]);

  /**
   * 重试加载音频
   */
  const retry = useCallback(() => {
    setShowPlayPrompt(false);
    refetch();
  }, [refetch]);

  /**
   * 处理音频播放事件
   */
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    updatePlayingState(true);
    console.log('[useAudioPlayer] Audio started playing');
  }, [setIsPlaying, updatePlayingState]);

  /**
   * 处理音频暂停事件
   */
  const handlePause = useCallback(() => {
    setIsPlaying(false);
    updatePlayingState(false);
    console.log('[useAudioPlayer] Audio paused');
  }, [setIsPlaying, updatePlayingState]);

  /**
   * 处理音频元数据加载
   */
  const handleLoadedMetaData = useCallback(async (e: React.SyntheticEvent<HTMLAudioElement>) => {
    const audio = e.currentTarget;
    const audioDuration = audio.duration;

    if (audioDuration && !isNaN(audioDuration)) {
      updateAudioState({ duration: audioDuration });
      console.log('[useAudioPlayer] Audio metadata loaded, duration:', audioDuration);

      // 恢复播放进度
      setTimeout(() => {
        if (savedProgress && savedProgress.currentTime > 0 && !progressRestored) {
          try {
            const seekTime = Math.min(savedProgress.currentTime, audioDuration);
            if (safeSeekAudio(audio, seekTime)) {
              updateAudioState({ currentTime: seekTime });
              setProgressRestored(true);
              console.log('[useAudioPlayer] Progress restored to:', seekTime);
            }
          } catch (error) {
            console.warn('[useAudioPlayer] Failed to restore progress:', error);
            setProgressRestored(true);
          }
        }
      }, 100);

      // 自动播放处理
      if (autoPlay && !autoPlayAttempted) {
        setAutoPlayAttempted(true);
        try {
          await audio.play();
          setShowPlayPrompt(false);
        } catch (error) {
          setShowPlayPrompt(true);
          console.warn('[useAudioPlayer] Auto play failed:', error);
        }
      }
    }
  }, [savedProgress, progressRestored, autoPlay, autoPlayAttempted, updateAudioState]);

  /**
   * 处理音频时间更新
   */
  const handleTimeUpdate = useCallback((e: React.SyntheticEvent<HTMLAudioElement>) => {
    const audio = e.currentTarget;
    const currentTimeSeconds = audio.currentTime;
    const totalDuration = audio.duration;
    const currentPlaybackRate = audio.playbackRate || 1.0;
    const currentVolume = audio.volume || 1.0;

    if (currentTimeSeconds && !isNaN(currentTimeSeconds)) {
      updateAudioState({
        currentTime: currentTimeSeconds,
        playbackRate: currentPlaybackRate,
        volume: currentVolume
      });

      // 更新播放进度（智能保存）
      if (totalDuration && !isNaN(totalDuration)) {
        updateProgress(
          currentTimeSeconds,
          totalDuration,
          currentPlaybackRate,
          currentVolume,
          !audio.paused
        );
      }
    }
  }, [updateAudioState, updateProgress]);

  /**
   * 处理音频播放结束
   */
  const handleEnded = useCallback(async () => {
    setIsPlaying(false);
    updatePlayingState(false);
    updateAudioState({ currentTime: 0 });
    console.log('[useAudioPlayer] Audio playback ended');

    // 标记为已完成
    try {
      await markAsCompleted();
    } catch (error) {
      console.error('[useAudioPlayer] Failed to mark as completed:', error);
    }
  }, [setIsPlaying, updatePlayingState, updateAudioState, markAsCompleted]);

  /**
   * 处理音频错误
   */
  const handleError = useCallback((e: React.SyntheticEvent<HTMLAudioElement>) => {
    console.error('[useAudioPlayer] Audio playback error:', e);
    setIsPlaying(false);
    updatePlayingState(false);
  }, [setIsPlaying, updatePlayingState]);

  // 重置状态当播放器关闭时
  useEffect(() => {
    if (!isActive) {
      // 暂停音频
      if (audioRef.current && !audioRef.current.paused) {
        audioRef.current.pause();
        console.log('[useAudioPlayer] Audio paused on player deactivate');
      }

      // 重置状态
      setIsPlaying(false);
      updatePlayingState(false);
      setAutoPlayAttempted(false);
      setShowPlayPrompt(false);
    }
  }, [isActive, updatePlayingState]);

  return {
    // 音频状态
    audioRef,
    audioUrl,
    isLoading,
    error,

    // 播放状态
    isPlaying,
    currentTime,
    duration,
    progress,
    formattedCurrentTime,
    formattedDuration,

    // 控制方法
    play,
    pause,
    seek,
    skip,
    retry,

    // 事件处理器
    handlePlay,
    handlePause,
    handleLoadedMetaData,
    handleTimeUpdate,
    handleEnded,
    handleError,

    // 状态管理
    showPlayPrompt,
    setShowPlayPrompt,
  };
}
