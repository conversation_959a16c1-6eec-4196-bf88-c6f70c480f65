/**
 * 音频播放器相关工具函数
 */

/**
 * 格式化时间显示
 */
export const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * 计算进度百分比
 */
export const calculateProgress = (currentTime: number, duration: number): number => {
  if (!duration || isNaN(duration) || duration <= 0) return 0;
  return Math.min(100, Math.max(0, (currentTime / duration) * 100));
};

/**
 * 根据百分比计算时间
 */
export const calculateTimeFromProgress = (progress: number, duration: number): number => {
  if (!duration || isNaN(duration) || duration <= 0) return 0;
  return Math.min(duration, Math.max(0, (progress / 100) * duration));
};

/**
 * 检查音频是否准备就绪
 */
export const isAudioReady = (audio: HTMLAudioElement | null): boolean => {
  return !!(audio && audio.duration && !isNaN(audio.duration));
};

/**
 * 安全地设置音频时间
 */
export const safeSeekAudio = (audio: HTMLAudioElement, time: number): boolean => {
  try {
    if (!isAudioReady(audio)) return false;
    
    const seekTime = Math.min(audio.duration, Math.max(0, time));
    audio.currentTime = seekTime;
    return true;
  } catch (error) {
    console.error('[AudioUtils] Failed to seek audio:', error);
    return false;
  }
};

/**
 * 拖拽阈值检查
 */
export const isDragThresholdExceeded = (
  startTime: number,
  startPosition: { x: number; y: number },
  currentTime: number,
  currentPosition: { x: number; y: number },
  timeThreshold: number = 150,
  distanceThreshold: number = 5
): boolean => {
  const timeDiff = currentTime - startTime;
  const distanceX = Math.abs(currentPosition.x - startPosition.x);
  const distanceY = Math.abs(currentPosition.y - startPosition.y);
  
  return timeDiff > timeThreshold || distanceX > distanceThreshold || distanceY > distanceThreshold;
};

/**
 * 计算点击/拖拽位置的进度百分比
 */
export const calculateProgressFromPosition = (
  clientX: number,
  progressBarElement: HTMLElement
): number => {
  const rect = progressBarElement.getBoundingClientRect();
  const clickX = clientX - rect.left;
  const progressBarWidth = rect.width;
  
  return Math.max(0, Math.min(100, (clickX / progressBarWidth) * 100));
};
