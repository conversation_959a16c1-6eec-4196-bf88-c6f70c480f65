/**
 * 书籍信息组件
 */

import React from 'react';
import { BookInfo as BookInfoType } from '../types';

interface BookInfoProps {
  book: BookInfoType;
}

export const BookInfo: React.FC<BookInfoProps> = ({ book }) => {
  return (
    <div className="flex items-center space-x-3 px-4 py-3">
      {/* 书籍封面 */}
      <div className="flex-shrink-0">
        <img
          src={book.coverImage}
          alt={`${book.title} cover`}
          className="w-12 h-12 rounded-lg object-cover shadow-sm"
          onError={(e) => {
            // 封面加载失败时的处理
            const target = e.target as HTMLImageElement;
            target.src = '/images/default-book-cover.png'; // 默认封面
          }}
        />
      </div>

      {/* 书籍信息 */}
      <div className="flex-1 min-w-0">
        <h3 className="text-sm font-medium text-gray-900 truncate">
          {book.title}
        </h3>
        <p className="text-xs text-gray-500 truncate">
          {book.author}
        </p>
      </div>
    </div>
  );
};

export default BookInfo;
