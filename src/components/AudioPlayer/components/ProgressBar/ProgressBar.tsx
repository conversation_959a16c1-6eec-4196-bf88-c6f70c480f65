/**
 * 进度条组件
 */

import React from 'react';
import { ProgressBarProps } from '../../types';
import { useProgressBarDrag } from '../../hooks/useProgressBarDrag';

interface ProgressBarComponentProps extends ProgressBarProps {
  audioRef?: React.RefObject<HTMLAudioElement>;
  handleMouseDown?: (e: React.MouseEvent<HTMLDivElement>) => void;
  handleTouchStart?: (e: React.TouchEvent<HTMLDivElement>) => void;
}

export const ProgressBar: React.FC<ProgressBarComponentProps> = ({
  progress,
  duration,
  currentTime,
  isDragging: externalIsDragging = false,
  dragProgress: externalDragProgress = 0,
  disabled = false,
  onSeek,
  onDragStart,
  onDragEnd,
  audioRef,
  handleMouseDown: externalHandleMouseDown,
  handleTouchStart: externalHandleTouchStart
}) => {
  // 如果有外部处理器，使用外部的；否则使用内部hook
  const shouldUseInternalHook = !externalHandleMouseDown && !externalHandleTouchStart;

  const {
    isDragging: internalIsDragging,
    dragProgress: internalDragProgress,
    handleMouseDown: internalHandleMouseDown,
    handleTouchStart: internalHandleTouchStart,
    progressBarRef
  } = useProgressBarDrag({
    audioRef: audioRef || { current: null },
    onSeek,
    disabled: disabled || !shouldUseInternalHook
  });

  // 使用外部状态或内部状态
  const isDragging = shouldUseInternalHook ? internalIsDragging : externalIsDragging;
  const dragProgress = shouldUseInternalHook ? internalDragProgress : externalDragProgress;
  const displayProgress = isDragging ? dragProgress : progress;

  // 选择事件处理器
  const handleMouseDown = externalHandleMouseDown || internalHandleMouseDown;
  const handleTouchStart = externalHandleTouchStart || internalHandleTouchStart;

  // 样式定义
  const progressBarStyles: React.CSSProperties = {
    height: '6px',
    backgroundColor: '#e5e7eb',
    borderRadius: '3px',
    overflow: 'hidden',
    cursor: disabled ? 'default' : 'pointer',
    position: 'relative',
    opacity: disabled ? 0.5 : 1,
  };

  const progressFillStyles: React.CSSProperties = {
    height: '100%',
    backgroundColor: '#10b981',
    width: `${displayProgress}%`,
    transition: isDragging ? 'none' : 'width 0.1s linear',
  };

  const dragIndicatorStyles: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: `${dragProgress}%`,
    width: '12px',
    height: '12px',
    backgroundColor: '#10b981',
    borderRadius: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 10,
    border: '2px solid white',
    boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
    opacity: isDragging ? 1 : 0,
    transition: 'opacity 0.2s ease',
  };

  // 处理拖拽开始
  const handleDragStartInternal = (e: React.MouseEvent<HTMLDivElement>) => {
    onDragStart?.();
    if (handleMouseDown) {
      handleMouseDown(e);
    }
  };

  const handleTouchStartInternal = (e: React.TouchEvent<HTMLDivElement>) => {
    onDragStart?.();
    if (handleTouchStart) {
      handleTouchStart(e);
    }
  };

  // 监听拖拽结束
  React.useEffect(() => {
    if (!isDragging && (externalIsDragging !== undefined || internalIsDragging)) {
      onDragEnd?.();
    }
  }, [isDragging, externalIsDragging, internalIsDragging, onDragEnd]);

  return (
    <div
      ref={progressBarRef}
      className="progress-bar w-full hover:bg-gray-200 transition-colors duration-200"
      style={progressBarStyles}
      onMouseDown={!disabled ? handleDragStartInternal : undefined}
      onTouchStart={!disabled ? handleTouchStartInternal : undefined}
      title={
        disabled
          ? "Progress bar disabled"
          : isDragging
            ? "Dragging..."
            : "Click or drag to seek"
      }
    >
      {/* 进度填充 */}
      <div className="progress-fill" style={progressFillStyles} />

      {/* 拖拽指示器 */}
      <div className="drag-indicator" style={dragIndicatorStyles} />
    </div>
  );
};

export default ProgressBar;
