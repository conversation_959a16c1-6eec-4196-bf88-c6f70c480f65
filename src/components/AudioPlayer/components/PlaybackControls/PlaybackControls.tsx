/**
 * 播放控制组件
 */

import React from 'react';
import { PlaybackControlsProps } from '../../types';

export const PlaybackControls: React.FC<PlaybackControlsProps> = ({
  isPlaying,
  currentTime,
  duration,
  onPlay,
  onPause,
  onSkipBackward,
  onSkipForward
}) => {
  // 播放/暂停按钮处理
  const handlePlayPause = () => {
    if (isPlaying) {
      onPause();
    } else {
      onPlay();
    }
  };

  // 快退处理
  const handleSkipBackward = () => {
    onSkipBackward(10); // 快退10秒
  };

  // 快进处理
  const handleSkipForward = () => {
    onSkipForward(10); // 快进10秒
  };

  // 按钮样式
  const buttonBaseStyles = "flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 hover:bg-gray-100 active:bg-gray-200";
  const primaryButtonStyles = "flex items-center justify-center w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 active:bg-green-800 text-white transition-all duration-200";

  return (
    <div className="flex items-center justify-between w-full px-4 py-3">
      {/* 时间显示 */}
      <div className="flex items-center space-x-2 min-w-[100px]">
        <span className="text-sm font-medium text-gray-700 tabular-nums">
          {currentTime}
        </span>
        <span className="text-sm text-gray-400">/</span>
        <span className="text-sm text-gray-500 tabular-nums">
          {duration}
        </span>
      </div>

      {/* 播放控制按钮 */}
      <div className="flex items-center space-x-4">
        {/* 快退按钮 */}
        <button
          onClick={handleSkipBackward}
          className={buttonBaseStyles}
          title="快退10秒"
          type="button"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polygon points="11,19 2,12 11,5" />
            <polygon points="22,19 13,12 22,5" />
          </svg>
        </button>

        {/* 播放/暂停按钮 */}
        <button
          onClick={handlePlayPause}
          className={primaryButtonStyles}
          title={isPlaying ? "暂停" : "播放"}
          type="button"
        >
          {isPlaying ? (
            // 暂停图标
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <rect x="6" y="4" width="4" height="16" />
              <rect x="14" y="4" width="4" height="16" />
            </svg>
          ) : (
            // 播放图标
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <polygon points="5,3 19,12 5,21" />
            </svg>
          )}
        </button>

        {/* 快进按钮 */}
        <button
          onClick={handleSkipForward}
          className={buttonBaseStyles}
          title="快进10秒"
          type="button"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polygon points="13,19 22,12 13,5" />
            <polygon points="2,19 11,12 2,5" />
          </svg>
        </button>
      </div>

      {/* 占位符保持布局平衡 */}
      <div className="min-w-[100px]" />
    </div>
  );
};

export default PlaybackControls;
