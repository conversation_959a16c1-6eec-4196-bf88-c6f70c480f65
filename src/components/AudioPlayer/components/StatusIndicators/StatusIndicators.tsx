/**
 * 状态指示器组件
 */

import React from 'react';
import { StatusIndicatorsProps } from '../../types';

export const StatusIndicators: React.FC<StatusIndicatorsProps> = ({
  isLoading,
  error,
  showPlayPrompt,
  onRetry,
  onDismissPrompt
}) => {
  // 如果没有任何状态需要显示，返回null
  if (!isLoading && !error && !showPlayPrompt) {
    return null;
  }

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-95 z-10">
      {/* 加载状态 */}
      {isLoading && (
        <div className="flex flex-col items-center space-y-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <p className="text-sm text-gray-600">正在加载音频...</p>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="flex flex-col items-center space-y-3 px-4 text-center">
          <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
            <svg
              className="w-6 h-6 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900 mb-1">音频加载失败</p>
            <p className="text-xs text-gray-500 mb-3">{error}</p>
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 播放提示 */}
      {showPlayPrompt && !error && !isLoading && (
        <div className="flex flex-col items-center space-y-3 px-4 text-center">
          <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
            <svg
              className="w-6 h-6 text-green-600"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <polygon points="5,3 19,12 5,21" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900 mb-1">需要手动播放</p>
            <p className="text-xs text-gray-500 mb-3">
              由于浏览器限制，请点击播放按钮开始收听
            </p>
            <button
              onClick={onDismissPrompt}
              className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
            >
              知道了
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusIndicators;
