/**
 * AudioPlayer组件相关类型定义
 */

export interface BookInfo {
  id: string;
  title: string;
  author: string;
  coverImage: string;
}

export interface AudioPlayerProps {
  book: BookInfo;
  isActive: boolean;
  autoPlay?: boolean;
  locale?: string;
  onClose: () => void;
}

export interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progress: number;
  playbackRate: number;
  volume: number;
}

export interface ProgressBarProps {
  progress: number;
  duration: number;
  currentTime: number;
  isDragging?: boolean;
  dragProgress?: number;
  disabled?: boolean;
  onSeek: (time: number) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

export interface PlaybackControlsProps {
  isPlaying: boolean;
  currentTime: string;
  duration: string;
  onPlay: () => void;
  onPause: () => void;
  onSkipBackward: (seconds: number) => void;
  onSkipForward: (seconds: number) => void;
}

export interface StatusIndicatorsProps {
  isLoading: boolean;
  error: string | null;
  showPlayPrompt: boolean;
  onRetry: () => void;
  onDismissPrompt: () => void;
}

export interface UseAudioPlayerOptions {
  bookId: number;
  locale: string;
  autoPlay: boolean;
  isActive: boolean;
}

export interface UseAudioPlayerReturn {
  // 音频状态
  audioRef: React.RefObject<HTMLAudioElement>;
  audioUrl: string | null;
  isLoading: boolean;
  error: string | null;

  // 播放状态
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progress: number;
  formattedCurrentTime: string;
  formattedDuration: string;

  // 控制方法
  play: () => Promise<void>;
  pause: () => void;
  seek: (time: number) => void;
  skip: (seconds: number) => void;
  retry: () => void;

  // 事件处理器
  handlePlay: () => void;
  handlePause: () => void;
  handleLoadedMetaData: (e: React.SyntheticEvent<HTMLAudioElement>) => void;
  handleTimeUpdate: (e: React.SyntheticEvent<HTMLAudioElement>) => void;
  handleEnded: () => void;
  handleError: (e: React.SyntheticEvent<HTMLAudioElement>) => void;

  // 状态管理
  showPlayPrompt: boolean;
  setShowPlayPrompt: (show: boolean) => void;
}

export interface UseProgressBarDragOptions {
  audioRef: React.RefObject<HTMLAudioElement>;
  onSeek: (time: number) => void;
  disabled?: boolean;
}

export interface UseProgressBarDragReturn {
  // 拖拽状态
  isDragging: boolean;
  dragProgress: number;

  // 事件处理器
  handleMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
  handleTouchStart: (e: React.TouchEvent<HTMLDivElement>) => void;

  // 进度条引用
  progressBarRef: React.RefObject<HTMLDivElement>;
}
