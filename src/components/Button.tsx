import React from 'react'

interface ButtonProps {
  type?: 'button' | 'submit' | 'reset'
  variant?: 'text' | 'outline' | 'filled'
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
  className?: string
}

const Button: React.FC<ButtonProps> = ({
  type = 'button',
  variant = 'filled',
  loading = false,
  children,
  onClick,
  className
}) => {
  const baseClasses = 'flex justify-center items-center size-[46px] text-sm font-medium rounded-lg'
  const variantClasses = {
    text: 'text-primary',
    outline: 'border border-gray-300 text-gray-700',
    filled: 'bg-primary text-white'
  }

  return (
    <button
      type={type}
      className={`block w-full ${baseClasses} ${variantClasses[variant]} ${loading ? 'opacity-50 pointer-events-none' : ''} ${className}`}
      onClick={onClick}
      disabled={loading}
    >
      {loading ? (
        <span
          className="animate-spin inline-block size-4 border-[3px] border-current border-t-transparent rounded-full"
          role="status"
          aria-label="loading"
        >
          <span className="sr-only">Loading...</span>
        </span>
      ) : (
        children
      )}
    </button>
  )
}

export default Button
