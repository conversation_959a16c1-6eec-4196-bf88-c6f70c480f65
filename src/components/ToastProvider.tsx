'use client'

import React from 'react'
import { useToast } from '@/hooks/useToast'
import Toast from '@/components/Toast'

const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { toasts } = useToast()

  return (
    <>
      {children}
      <div className="fixed bottom-4 right-4 space-y-2 z-100">
        {toasts.map((toast, index) => (
          <Toast key={index} message={toast.message} type={toast.type} />
        ))}
      </div>
    </>
  )
}

export default ToastProvider
