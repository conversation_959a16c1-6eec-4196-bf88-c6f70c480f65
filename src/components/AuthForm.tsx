import { UseFormRegister, FieldErrors, UseFormHandleSubmit } from 'react-hook-form'
import Button from './Button'
import Input from './Input'
import { Link } from '@/i18n/routing'
import { useTranslations } from 'next-intl'
import { Typography } from '@/components/Typography'
export interface FormData {
  email: string
  password: string
  confirmPassword?: string
}

interface AuthFormProps {
  type: 'reset-password' | 'sign-in' | 'sign-up' | 'password-reset'
  onSubmit: (data: FormData) => Promise<void>
  register: UseFormRegister<FormData>
  handleSubmit: UseFormHandleSubmit<FormData>
  errors: FieldErrors<FormData>
  loading: boolean
  rememberMe?: boolean
  setRememberMe?: (value: boolean) => void
  isResetPassword?: boolean
  watch?: (name: string) => string
  isDialog?: boolean
}

export function AuthForm({
  onSubmit,
  register,
  handleSubmit,
  errors,
  loading,
  rememberMe,
  setRememberMe,
  isResetPassword,
  type,
  watch,
  isDialog = false
}: AuthFormProps) {
  const t = useTranslations('Auth')

  const getButtonText = () => {
    if (type === 'password-reset') return t('buttons.resetPassword')
    if (type === 'reset-password') return t('buttons.resetPassword')
    if (type === 'sign-in') return t('buttons.signIn')
    return t('buttons.signUp')
  }

  return (
    <form noValidate onSubmit={handleSubmit(onSubmit)}>
      {type !== 'password-reset' && (
        <Input
          type="email"
          placeholder={t('email')}
          label={t('email')}
          required
          className={errors.email ? 'border-red-500' : ''}
          error={errors.email?.message}
          register={register('email', {
            required: t('emailRequired'),
            pattern: {
              value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
              message: t('emailInvalid')
            }
          })}
        />
      )}
      {!isResetPassword && type !== 'password-reset' && (
        <>
          <Input
            type="password"
            placeholder={t('password')}
            label={t('password')}
            required
            className={errors.password ? 'border-red-500' : ''}
            error={errors.password?.message}
            register={register('password', {
              required: t('passwordRequired'),
              minLength: {
                value: 6,
                message: t('passwordLength')
              }
            })}
          />
          <div className="flex items-center justify-between mb-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-2 text-primary"
                onChange={() => setRememberMe && setRememberMe(!rememberMe)}
              />
              <Typography variant="span">{t('rememberMe')}</Typography>
            </label>
            <Link
              href="/reset-password"
              className="text-primary"
              target={isDialog ? '_blank' : undefined}
              rel={isDialog ? 'noopener noreferrer' : undefined}
            >
              {t('forgotPassword')}
            </Link>
          </div>
        </>
      )}
      {type === 'password-reset' && (
        <>
          <Input
            type="password"
            placeholder={t('password')}
            label={t('password')}
            required
            className={errors.password ? 'border-red-500' : ''}
            error={errors.password?.message}
            register={register('password', {
              required: t('passwordRequired'),
              minLength: {
                value: 6,
                message: t('passwordLength')
              }
            })}
          />
          <Input
            type="password"
            placeholder={t('confirmPassword')}
            label={t('confirmPassword')}
            required
            className={errors.confirmPassword ? 'border-red-500' : ''}
            error={errors.confirmPassword?.message}
            register={register('confirmPassword', {
              required: t('confirmPasswordRequired'),
              validate: (value) => value === watch?.('password') || t('passwordsMismatch')
            })}
          />
        </>
      )}
      <Button loading={loading} variant="filled" type="submit" className="mt-8">
        <Typography variant="span">{getButtonText()}</Typography>
      </Button>
    </form>
  )
}
