'use client'

import { useState } from 'react'
import { Copy } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

interface CopyButtonProps {
  text: string
  tooltipCopy: string
  tooltipCopied: string
  disabled?: boolean
}

export function ClipboardButton({
  text,
  tooltipCopy,
  tooltipCopied,
  disabled = false
}: CopyButtonProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isCopied, setIsCopied] = useState(false)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setIsCopied(true)
      if (isHovered) {
        setTimeout(() => {
          setIsCopied(false)
        }, 2000)
      }
    } catch (err) {
      console.warn('复制失败', err)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    setIsCopied(false)
  }

  return (
    <Tooltip delayDuration={0} open={isHovered || isCopied}>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          onClick={copyToClipboard}
          disabled={disabled}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={handleMouseLeave}
        >
          <Copy className="w-5 h-5" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{isCopied ? tooltipCopied : tooltipCopy}</p>
      </TooltipContent>
    </Tooltip>
  )
}
