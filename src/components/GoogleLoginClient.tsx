'use client'
import { useRouter } from '@/i18n/routing'
import { googleAuthCredential } from '@/services/client/authService'
import { ResponseCode } from '@/utils/constants'
import { useGoogleOneTapLogin } from '@react-oauth/google'

export function GoogleLoginClient({ user }: { user: User | null }) {
  const router = useRouter()

  useGoogleOneTapLogin({
    auto_select: true,
    disabled: !!user,
    onSuccess: async (response) => {
      const res = await googleAuthCredential(response.credential!)
      res.code === ResponseCode.Success && router.refresh()
    },
    onError: () => {
      console.error('Google Login Error')
    }
  })
  return <></>
}
