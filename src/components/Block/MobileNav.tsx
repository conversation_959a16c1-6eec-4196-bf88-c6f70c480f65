'use client'

import { useState } from 'react'
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Menu } from 'lucide-react'
import { Avatar, UserInfo } from './Avatar'
import { Language } from '../Language'
import { Nav } from './Nav'
import { useTranslations } from 'next-intl'
import { Logo } from '../Logo'
import { Typography } from '../Typography'

export function MobileNav({ }: { userInfo?: UserInfo | null }) {
  const t = useTranslations('Header')
  const [isOpen, setIsOpen] = useState(false)

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="md:hidden"
          aria-label={t('toggleNavigation')}
        >
          <Menu className="h-4 w-4" />
          <Typography variant="span" className="sr-only">
            {t('toggleNavigation')}
          </Typography>
        </Button>
      </SheetTrigger>
      <SheetContent side="top" className="md:hidden fixed w-full p-0">
        <div className="flex p-6 flex-col gap-6 w-[100vw]">
          <Logo />
          <Nav />
          <div className="flex flex-col gap-4">
            <Avatar />
            <Language isMobile={true} />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
