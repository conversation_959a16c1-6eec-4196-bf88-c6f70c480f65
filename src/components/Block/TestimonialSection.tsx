'use client'

import { useTranslations } from 'next-intl'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'
import { Typography } from '@/components/Typography'
export interface Voice {
  name: string
  description: string
  comment: string
  avatar: string
}

interface TestimonialSectionProps {
  voices: Voice[]
  title?: string
  className?: string
}

export function TestimonialSection({ voices, title, className }: TestimonialSectionProps) {
  const t2 = useTranslations('Home')

  return (
    <section className={cn('container mx-auto px-4 mb-6 max-w-7xl', className)}>
      <Typography variant="h2" className="font-bold text-center mb-8">
        {title || t2('customerReviews')}
      </Typography>

      <div className="columns-1 md:columns-2 lg:columns-3 gap-8 [column-fill:_balance] mx-auto">
        {voices.map((voice, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="mb-8 break-inside-avoid"
          >
            <Card className={cn('transition-all hover:shadow-lg h-fit')}>
              <CardHeader className="flex flex-row items-center gap-4 pb-3">
                <Avatar>
                  <AvatarImage src={voice.avatar} alt={voice.name} />
                  <AvatarFallback>{voice.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <Typography variant="h3" className="text-lg font-semibold">
                    {voice.name}
                  </Typography>
                  <Typography variant="p" className="text-sm text-muted-foreground">
                    {voice.description}
                  </Typography>
                </div>
              </CardHeader>
              <CardContent>
                <Typography variant="p" className="text-sm leading-relaxed">
                  {voice.comment}
                </Typography>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </section>
  )
}
