import { Link } from '@/i18n/routing'
import { getTranslations } from 'next-intl/server'
import { Logo } from '../Logo'

import { Typography } from '@/components/Typography'

export async function Footer() {
  const t = await getTranslations('Footer')

  return (
    <footer className="mt-auto max-w-[85rem] py-10 w-4/5 xl:w-[1200px] sm:px-6 lg:px-8 mx-auto">
      <div className="flex flex-col gap-8 justify-between items-start">
        <Link
          className="flex items-center gap-2 font-semibold text-xl text-black focus:outline-hidden focus:opacity-80 dark:text-white"
          href="/"
          aria-label={t('ariaLabel')}
        >
          <div className="shrink-0">
            <Logo size={32} text />
          </div>
        </Link>
        {/* 网格布局容器 */}
        <div className="w-full grid grid-cols-4 gap-8">
          {/* 特色功能标题 - 跨越3列 */}
          <div className="col-span-3 pb-4 border-b border-gray-100">
            <Typography variant="h4" className="font-medium">
              {t('SectionOne')}
            </Typography>
          </div>

          {/* 关于标题 - 占1列 */}
          <div className="col-span-1 pb-4 border-b border-gray-100">
            <Typography variant="h4" className="font-medium">
              {t('SectionTwo')}
            </Typography>
          </div>

          <div>
            <Link href="/translate/text">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnOneTextOne')}
              </Typography>
            </Link>
            <Link href="/translate/text">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnOneTextTwo')}
              </Typography>
            </Link>
          </div>

          <div>
            <Link href="/translate/images">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnTwoTextOne')}
              </Typography>
            </Link>
            <Link href="/translate/images">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnTwoTextTwo')}
              </Typography>
            </Link>
          </div>

          <div>
            <Link href="/translate/pdf">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnThreeTextOne')}
              </Typography>
            </Link>
            <Link href="/translate/pdf">
              <Typography variant="h5" className="font-medium mb-4">
                {t('SectionOneColomnThreeTextTwo')}
              </Typography>
            </Link>
          </div>

          {/* 关于部分 */}
          <div className="flex flex-col gap-3 text-gray-500">
            <Link href="/affiliation">{t('affiliation')}</Link>
            <Link href="/privacy">{t('privacy')}</Link>
            <Link href="/terms">{t('terms')}</Link>
            <Link href="/pricing">{t('pricing')}</Link>
          </div>
        </div>
      </div>

      {/* 版权信息 */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <Typography variant="p" className="text-gray-500 text-sm">
          {t('copyright')}
        </Typography>
      </div>
    </footer>
  )
}
