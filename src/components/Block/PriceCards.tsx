'use client'
import { usePayment } from '@/hooks/usePayment'
import { CheckCircle2, XCircle } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { User } from '@/store/userStore'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Typography } from '../Typography'

interface Feature {
  text: string
  available: boolean
}

interface PricePlan {
  id?: number
  title: string
  name: string
  price?: number
  description: string
  features: Feature[]
  isPopular?: boolean
}

export interface PriceCard {
  duration: string
  first_price: number
  id: number
  price: number
  original_price: number
  trial_days: number
  rank_name: string
}

// 修改套餐等级枚举
enum PlanRank {
  FREE = 'free',
  STARTER = 'starter',
  STANDARD = 'standard',
  PREMIUM = 'premium'
}

// 修改套餐等级顺序
const PLAN_RANK_ORDER = {
  [PlanRank.FREE]: 0,
  [PlanRank.STARTER]: 1,
  [PlanRank.STANDARD]: 2,
  [PlanRank.PREMIUM]: 3
}

interface ButtonState {
  text: string
  tooltip?: string
  show: boolean
  disabled?: boolean
}

export function PriceCards({
  cards,
  user,
  disableHeader = false
}: {
  cards: PriceCard[]
  user: User | null
  disableHeader?: boolean
}) {
  const existPlans = cards.map((c) => c.rank_name.toLowerCase())
  const t = useTranslations('Price')

  const PRICE_PLANS: PricePlan[] = [
    {
      title: 'free',
      name: t('freePlan'),
      price: 0,
      description: t('freeDescription'),
      features: [
        { text: t('freeFeature1'), available: true },
        { text: t('freeFeature2'), available: true },
        { text: t('freeFeature3'), available: false },
        { text: t('freeFeature4'), available: true },
        { text: t('freeFeature5'), available: true },
        { text: t('freeFeature6'), available: false },
        { text: t('freeFeature7'), available: true }
      ]
    },
    {
      title: 'starter',
      name: t('starterPlan'),
      description: t('starterDescription'),
      features: [
        { text: t('starterFeature1'), available: true },
        { text: t('starterFeature2'), available: true },
        { text: t('starterFeature3'), available: true },
        { text: t('starterFeature4'), available: true },
        { text: t('starterFeature5'), available: true },
        { text: t('starterFeature6'), available: true },
        { text: t('starterFeature7'), available: true }
      ]
    },
    {
      title: 'standard',
      name: t('standardPlan'),
      description: t('standardDescription'),
      features: [
        { text: t('standardFeature1'), available: true },
        { text: t('standardFeature2'), available: true },
        { text: t('standardFeature3'), available: true },
        { text: t('standardFeature4'), available: true },
        { text: t('standardFeature5'), available: true },
        { text: t('standardFeature6'), available: true },
        { text: t('standardFeature7'), available: true },
        { text: t('standardFeature8'), available: true }
      ],
      isPopular: true
    },
    {
      title: 'premium',
      name: t('premiumPlan'),
      description: t('premiumDescription'),
      features: [
        { text: t('premiumFeature1'), available: true },
        { text: t('premiumFeature2'), available: true },
        { text: t('premiumFeature3'), available: true },
        { text: t('premiumFeature4'), available: true },
        { text: t('premiumFeature5'), available: true },
        { text: t('premiumFeature6'), available: true },
        { text: t('premiumFeature7'), available: true },
        { text: t('premiumFeature8'), available: true }
      ]
    }
  ]

  const renderPlans = PRICE_PLANS.filter((p) => {
    return existPlans.includes(p.title.toLowerCase())
  }).map((plan) => {
    const p = cards.find((c) => c.rank_name.toLowerCase() === plan.title.toLowerCase())
    if (!p) throw new Error(`No matching card found for plan ${plan.title}`)
    return { ...plan, ...p } as const
  })

  const locale = useLocale()

  // 使用新的支付 Hook
  const { handlePayment } = usePayment({
    successUrl: `${typeof window !== 'undefined' ? window.location.origin : ''}/pay-success`,
    cancelUrl: `${typeof window !== 'undefined' ? window.location.origin : ''}/${locale}/pricing`
  })

  const handleSubscribe = async (plan: PricePlan & PriceCard) => {
    if (plan.id) {
      await handlePayment(plan.id, user)
    }
  }

  return (
    <>
      {/** Title */}
      {!disableHeader ? (
        <div className="max-w-2xl mx-auto text-center">
          <Typography
            variant="h2"
            className="text-3xl leading-tight font-bold md:leading-tight lg:text-5xl lg:leading-tight bg-clip-text bg-linear-to-r from-primary to-[#FFA07A] text-transparent"
          >
            {t('pricingTitle')}
          </Typography>
          <Typography variant="p" className="mt-2 lg:text-lg text-gray-800 dark:text-neutral-200">
            {t('pricingSubtitle')}
          </Typography>
        </div>
      ) : null}

      {/** Grid */}
      <div
        className="mt-6 md:mt-12 grid gap-3 md:gap-6 lg:gap-3 xl:gap-6 lg:items-start max-w-7xl mx-auto"
        style={{
          gridTemplateColumns: `repeat(auto-fit, minmax(280px, 1fr))`,
          maxWidth: `${Math.min(renderPlans.length * 400, 1280)}px`
        }}
      >
        {renderPlans.map((plan) => (
          <PriceCard key={plan.name} plan={plan} user={user} onSubscribe={handleSubscribe} />
        ))}
      </div>
    </>
  )
}

interface PriceCardProps {
  plan: PricePlan & PriceCard
  user: User | null
  onSubscribe: (plan: PricePlan & PriceCard) => void
}

function PriceCard({ plan, user, onSubscribe }: PriceCardProps) {
  const t = useTranslations('Price')

  function getButtonState(currentPlan: string, targetPlan: string): ButtonState {
    // Free 套餐不显示按钮
    if (targetPlan.toLowerCase() === PlanRank.FREE) {
      return { show: false, text: '' }
    }

    if (currentPlan === targetPlan) {
      return { show: true, text: t('currentPlan'), disabled: true }
    }

    // 如果当前是免费套餐，其他套餐都显示"订阅"
    if (currentPlan.toLowerCase() === PlanRank.FREE) {
      return { show: true, text: t('subscribe') }
    }

    const currentRank = PLAN_RANK_ORDER[currentPlan as PlanRank] ?? -1
    const targetRank = PLAN_RANK_ORDER[targetPlan as PlanRank] ?? -1

    // 降级
    if (currentRank > targetRank) {
      return {
        show: true,
        text: t('downgrade'),
        tooltip: t('downgradeTooltip')
      }
    }

    // 升级
    return {
      show: true,
      text: t('upgrade'),
      tooltip: t('upgradeTooltip')
    }
  }

  const buttonState = getButtonState(
    user?.rank_name?.toLowerCase() ?? PlanRank.FREE,
    plan.title.toLowerCase()
  )

  return (
    <div
      className={`relative flex flex-col justify-between bg-white text-center rounded-2xl p-4 md:p-8 dark:bg-neutral-900 h-[600px]
      ${plan.isPopular
          ? 'border-2 border-primary shadow-xl dark:border-primary'
          : 'border border-gray-200 dark:border-neutral-800'
        }`}
    >
      <div>
        {plan.isPopular && (
          <Typography
            variant="span"
            className="absolute top-0 right-0 mb-3 rounded-tr-[14px] rounded-bl-[14px] bg-red-200"
          >
            <Typography
              variant="span"
              className="inline-flex items-center gap-1.5 py-1.5 px-3 text-xs uppercase font-semibold"
            >
              {t('mostPopular')}
            </Typography>
          </Typography>
        )}

        <div className="h-32">
          <Typography variant="h4" className="font-medium text-gray-800 dark:text-neutral-200">
            {plan.name}
          </Typography>
          <Typography
            variant="span"
            className="mt-7 font-bold text-3xl md:text-4xl xl:text-5xl text-gray-800 dark:text-neutral-200"
          >
            ${plan.price}
          </Typography>
          <Typography variant="p" className="mt-2 text-sm text-gray-500 dark:text-neutral-500">
            {plan.description}
          </Typography>
        </div>

        <ul className="mt-7 space-y-2.5 text-sm">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex gap-x-2">
              {feature.available ? (
                <CheckCircle2 className="h-4 w-4 text-green-500 shrink-0 mt-0.5" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500 shrink-0 mt-0.5" />
              )}
              <Typography
                variant="p"
                className={`text-start leading-5 ${feature.available ? 'text-gray-700 dark:text-neutral-200' : 'text-gray-400 dark:text-neutral-500'}`}
              >
                {feature.text}
              </Typography>
            </li>
          ))}
        </ul>
      </div>

      {buttonState.show && (
        <div className="relative">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={() => onSubscribe(plan)}
                disabled={buttonState.disabled}
                className={`mt-5 py-3 px-4 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg
                    ${plan.isPopular
                    ? 'border border-transparent bg-primary text-white hover:bg-primary'
                    : 'border border-primary text-primary hover:border-primary hover:text-primary'
                  }
                    ${buttonState.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <Typography variant="span">{buttonState.text}</Typography>
              </button>
            </TooltipTrigger>
            {buttonState.tooltip && (
              <TooltipContent>
                <Typography variant="p">{buttonState.tooltip}</Typography>
              </TooltipContent>
            )}
          </Tooltip>
        </div>
      )}
    </div>
  )
}
