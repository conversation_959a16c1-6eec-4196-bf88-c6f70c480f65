import { getTranslations } from 'next-intl/server'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

import { Typography } from '@/components/Typography'

export type FAQItem = {
  question: string | React.ReactNode
  answer: string | React.ReactNode
  id: string
}

type FAQProps = {
  title?: string
  description?: string
  items: FAQItem[]
  layout?: 'vertical' | 'horizontal'
}

export async function FAQ({
  title: propTitle,
  description: propDescription,
  items,
  layout = 'vertical'
}: FAQProps) {
  const t = await getTranslations('FAQ')
  const title = propTitle ?? t('Home.title')
  const description = propDescription
  return (
    <div className="flex flex-col max-w-[85rem] px-4 py-6 sm:px-6 lg:px-8 lg:py-10 mx-auto mt-0">
      {layout === 'vertical' && title ? (
        <div className="flex flex-col items-center justify-center gap-2 mb-10">
          {/* <h2 className="flex gap-2 text-2xl text-center font-bold md:text-4xl md:leading-tight dark:text-white">
            <WandIcon />
            {title}
          </h2> */}
          <Typography variant="h2" className="flex gap-2 text-center font-bold">
            {title}
          </Typography>
          {description && (
            <Typography variant="p" className=" text-gray-600 dark:text-neutral-400">
              {description}
            </Typography>
          )}
        </div>
      ) : null}
      <div className={`grid gap-10 ${layout === 'horizontal' ? 'md:grid-cols-5' : 'grid-cols-1'}`}>
        {layout === 'horizontal' && title && (
          <div className="md:col-span-2">
            <div className="max-w-xs">
              <Typography variant="h2" className="flex gap-2 text-center font-bold">
                {title}
              </Typography>
              {description && (
                <Typography variant="p" className=" text-gray-600 dark:text-neutral-400">
                  {description}
                </Typography>
              )}
            </div>
          </div>
        )}

        <div className={title ? 'md:col-span-3' : 'w-full'}>
          <Accordion type="single" collapsible defaultValue={items[0]?.id} className="space-y-4">
            {items.map((item) => (
              <AccordionItem
                key={item.id}
                value={item.id}
                className="rounded-lg border-none px-4 py-2 data-[state=open]:bg-gray-100 dark:data-[state=open]:bg-neutral-800"
              >
                <AccordionTrigger className="md:text-lg font-semibold text-gray-800 hover:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 dark:text-neutral-400 whitespace-pre-line">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  )
}
