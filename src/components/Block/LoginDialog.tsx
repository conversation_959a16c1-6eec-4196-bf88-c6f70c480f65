'use client'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { LoginForm } from '@/components/LoginForm'
import { create } from 'zustand'

interface LoginDialogStore {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess?: () => void
  setOnLoginSuccess: (callback?: () => void) => void
}

export const useLoginDialog = create<LoginDialogStore>((set) => ({
  isOpen: false,
  onOpenChange: (open: boolean) => set({ isOpen: open }),
  onLoginSuccess: undefined,
  setOnLoginSuccess: (callback?: () => void) => set({ onLoginSuccess: callback })
}))

export function LoginDialog() {
  const { isOpen, onOpenChange, onLoginSuccess } = useLoginDialog()

  const handleSuccess = () => {
    onOpenChange(false)
    onLoginSuccess?.()
    useLoginDialog.getState().setOnLoginSuccess(undefined)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <LoginForm onSuccess={handleSuccess} shouldRedirect={false} isDialog={true} />
      </DialogContent>
    </Dialog>
  )
}
