'use client'

import { Card } from '@/components/ui/card'
import { Typography } from '@/components/Typography'

interface Step {
  id: number
  title: string
  desc: string
  image?: string
  link?: string
}

export default function StepsShowcase({ steps, title }: { steps: Step[]; title: string }) {

  return (
    <div className="container relative mt-16 mx-auto px-4 pb-4 max-w-7xl">
      <Card className="rounded-[40px] py-8 mx-0 sm:mx-8">
        <Typography variant="h2" className="text-4xl font-bold text-center mb-2 px-4">
          {title}
        </Typography>
        <ul className="relative pt-8 flex flex-col md:flex-row gap-4 justify-center px-4">
          {steps.map((step) => (
            <li
              key={step.id}
              className="md:shrink md:basis-0 flex-1 group relative flex gap-x-4 md:block text-center"
            >
              <div className="relative flex flex-col items-center hover:transform hover:scale-105 transition-transform duration-300">
                <Typography
                  variant="span"
                  className="size-10 sm:size-12 md:size-14 z-10 font-bold text-xl sm:text-xl md:text-2xl flex justify-center items-center shrink-0 bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-full shadow-md transition-all duration-300 group-hover:shadow-orange-300/50 dark:from-orange-600 dark:to-orange-700"
                >
                  {step.id}
                </Typography>
                <div className="absolute hidden md:block top-1/2 start-1/2 w-full h-0.5 -translate-y-1/2 bg-gradient-to-r from-orange-200 to-orange-300 group-last:hidden dark:from-orange-800 dark:to-orange-700"></div>
              </div>
              <div className="grow md:grow-0 md:mt-4 pb-5 text-center hover:transform hover:scale-105 transition-transform duration-300">
                <Typography
                  variant="span"
                  className="block text-base font-semibold text-gray-800 dark:text-white mb-1"
                >
                  {step.title}
                </Typography>
                <Typography variant="p" className="text-sm text-gray-500 dark:text-neutral-400">
                  {step.desc}
                </Typography>
              </div>
            </li>
          ))}
        </ul>
      </Card>
    </div>
  )
}
