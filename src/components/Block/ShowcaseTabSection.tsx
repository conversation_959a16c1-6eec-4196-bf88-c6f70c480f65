'use client'

import { useState } from 'react'
import { cn } from '@/lib/utils'
import { Typography } from '@/components/Typography'
export type TabItem = {
  text: string
  type: number
}

interface ShowcaseTabSectionProps {
  tabs: TabItem[]
  content: Record<number, React.ReactNode>
  className?: string
}

export function ShowcaseTabSection({ tabs, content, className }: ShowcaseTabSectionProps) {
  const [activeType, setActiveType] = useState(tabs[0]?.type)

  return (
    <div className={cn('flex flex-col gap-8', className)}>
      <div className="flex flex-wrap justify-center gap-4 mt-4 align-center">
        {tabs.map((tab) => (
          <div
            key={`tag-${tab.type}`}
            className={cn(
              'flex items-center gap-2 cursor-pointer px-4 py-1.5 transition-all',
              activeType === tab.type
                ? 'text-black dark:text-white shadow-[inset_0_-4px_0_0] shadow-primary-600'
                : 'text-neutral-500 dark:text-neutral-400 hover:text-black dark:hover:text-white hover:shadow-[inset_0_-4px_0_0] hover:shadow-primary-600'
            )}
            onClick={() => setActiveType(tab.type)}
          >
            <Typography variant="span">{tab.text}</Typography>
          </div>
        ))}
      </div>

      <div className="flex flex-col items-center gap-8 md:grid lg:gap-24 xl:gap-24">
        {content[activeType]}
      </div>
    </div>
  )
}
