'use client'

import { useChat, type UseChatOptions } from '@ai-sdk/react'
import { cn } from '@/lib/utils'
import { Chat } from '@/components/ui/chat'

type ChatPanelProps = {
  initialMessages?: UseChatOptions['initialMessages']
}

const GEMINI_API_KEY = 'AIzaSyBD5XN_EcJIE9db1xtjC2LCb6Uriwg9AH8'

export default function ChatPanel(props: ChatPanelProps) {
  const { messages, input, handleInputChange, handleSubmit, append, stop, status, setMessages } =
    useChat({
      ...props,
      api: '/api/chat',
      body: {
        apiKey: GEMINI_API_KEY
      }
    })

  return (
    <div className={cn('flex', 'flex-col', 'h-[500px]', 'w-full')}>
      <div className={cn('flex', 'justify-end', 'mb-2')}></div>

      <Chat
        className="grow"
        // TODO：后续根据实际类型修改
        // @ts-expect-error
        messages={messages}
        handleSubmit={handleSubmit}
        input={input}
        handleInputChange={handleInputChange}
        isGenerating={status === 'submitted'}
        stop={stop}
        append={append}
        setMessages={setMessages}
        suggestions={[
          'What is the weather in San Francisco?',
          'Explain step-by-step how to solve this math problem: If x² + 6x + 9 = 25, what is x?',
          'Design a simple algorithm to find the longest palindrome in a string.'
        ]}
      />
    </div>
  )
}
