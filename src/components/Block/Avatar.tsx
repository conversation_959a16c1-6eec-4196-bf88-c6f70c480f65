import Image from 'next/image'
import { getUserInfo } from '@/services/server/userService'
import { getBgColor, getFirstChar } from '@/utils'
import { Link } from '@/i18n/routing'
import { Logout } from '../Logout'
import { getTranslations } from 'next-intl/server'
import { Typography } from '@/components/Typography'

export interface UserInfo {
  username: string
  avatar?: string
  account: string
  language: string
  company: string | null
  country: string | null
  province: string | null
  city: string | null
  postal: string | null
  address: string | null
  phone: string | null
  vat: string | null
  distribution_code: string
  created_at: number
  delete_task_plan_executed_at: number | null
  rank_name: string
  last_rank_name: string | null
  email: string
}

interface MenuLinkProps {
  href: string
  children: React.ReactNode
}

function MenuLink({ href, children }: MenuLinkProps) {
  return (
    <Link
      className="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
      href={href}
    >
      <Typography variant="span">{children}</Typography>
    </Link>
  )
}

export async function Avatar() {
  const t = await getTranslations('Avatar')
  const userInfo = await getUserInfo()

  return (
    <>
      {userInfo ? (
        <div className="group relative inline-flex">
          <Link href="/profile">
            <div
              className="p-2 flex items-center text-sm text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
              aria-haspopup="menu"
              aria-expanded="false"
              aria-label="Dropdown"
            >
              <div className="w-6 h-6">
                {userInfo.avatar ? (
                  <Image
                    src={userInfo.avatar}
                    className="w-full rounded-full object-cover object-center"
                    alt="avatar"
                    width={24}
                    height={24}
                  />
                ) : (
                  <div
                    className="w-full h-full inline-flex justify-center items-center rounded-full dark:bg-neutral-800"
                    style={{ backgroundColor: getBgColor(userInfo.username) }}
                  >
                    <Typography
                      variant="span"
                      className="inline-flex justify-center items-center leading-none text-white text-sm"
                    >
                      {getFirstChar(userInfo.username)}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </Link>
          <div
            className="absolute left-0 top-full transition-all duration-300 opacity-0 invisible group-hover:opacity-100 group-hover:visible min-w-60 bg-white shadow-md rounded-lg mt-2 dark:bg-neutral-800 dark:border dark:border-neutral-700 dark:divide-neutral-700"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="dropdown-menu"
          >
            <div className="p-1 space-y-0.5">
              <MenuLink href="/profile">{t('profile')}</MenuLink>
              <MenuLink href="/terms">{t('terms')}</MenuLink>
              <MenuLink href="/privacy">{t('privacy')}</MenuLink>
              <Logout />
            </div>
          </div>
        </div>
      ) : (
        <Link
          className="py-[7px] px-2.5 inline-flex items-center font-medium text-sm rounded-lg border border-gray-200 bg-transparent text-gray-800 shadow-xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
          href="/login"
        >
          {t('SignIn')}
        </Link>
      )}
    </>
  )
}
