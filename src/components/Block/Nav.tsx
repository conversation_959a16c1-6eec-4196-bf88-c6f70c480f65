'use client'
import { usePathname } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { Link } from '@/i18n/routing'
import { FileText, Image, FileType2, Wallet } from 'lucide-react'

import { Typography } from '@/components/Typography'

interface NavItem {
  href: string
  label: string
  icon: React.ReactNode
}

type NavLinkProps = NavItem

export function Nav() {
  const t = useTranslations('Header')
  const path = usePathname()
  const locale = useLocale()

  const navItems: NavItem[] = [
    {
      href: '/translate/text',
      label: t('NavOne'),
      icon: <FileText className="shrink-0 size-4 me-3 md:me-2 block md:hidden" />
    },
    {
      href: '/translate/images',
      label: t('NavTwo'),
      // eslint-disable-next-line jsx-a11y/alt-text
      icon: <Image className="shrink-0 size-4 me-3 md:me-2 block md:hidden" />
    },
    {
      href: '/translate/pdf',
      label: t('NavThree'),
      icon: <FileType2 className="shrink-0 size-4 me-3 md:me-2 block md:hidden" />
    },
    {
      href: '/pricing',
      label: t('NavFour'),
      icon: <Wallet className="shrink-0 size-4 me-3 md:me-2 block md:hidden" />
    }
  ]

  const NavLink: React.FC<NavLinkProps> = ({ href, label, icon }) => (
    <Link
      className={`p-2 flex items-center text-base text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 ${path.includes(`/${locale}${href}`)
        ? 'bg-gray-100 dark:bg-neutral-600 active:bg-gray-200 dark:active:bg-neutral-500'
        : 'dark:bg-neutral-700'
        }`}
      href={href}
    >
      {icon}
      <Typography variant="span">{label}</Typography>
    </Link>
  )

  return (
    <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-2">
      {navItems.map((item) => (
        <NavLink key={item.href} {...item} />
      ))}
    </div>
  )
}
