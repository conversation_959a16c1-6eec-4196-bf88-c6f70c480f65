import { Bg } from '@/components/Bg'
import { CTA } from '@/components/Block/CTALink'
import { Download } from 'lucide-react'
import { Typography } from '@/components/Typography'

interface HeroProps {
  title: string
  subtitle: string
  productDescription: string
  linkText: string
  linkHref: string
  linkSize: 'lg' | 'md' | 'sm'
  feature: React.ReactNode
}

export function Hero({
  title,
  subtitle,
  productDescription,
  linkText,
  linkHref,
  linkSize,
  feature
}: HeroProps) {
  return (
    <div className="relative">
      {/** Hero */}
      <div className="relative z-10 pt-28 pb-16 -mt-16 max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8">
        {/** Grid */}
        <div className="relative grid md:grid-cols-2 gap-4 md:gap-8 xl:gap-20 md:items-center">
          <div>
            <Typography
              variant="h1"
              className="block font-bold text-gray-800 lg:leading-tight dark:text-white whitespace-pre-line"
            >
              {title}
            </Typography>
            <Typography variant="p" className="mt-3 text-lg text-gray-500 dark:text-neutral-400">
              {subtitle}
            </Typography>

            {/** Buttons */}
            <div className="mt-7 mb-4">
              <CTA href={linkHref} icon={Download} text={linkText} variant={linkSize} />
            </div>
            <Typography variant="p" className="flex items-center gap-1 text-sm text-primary my-2">
              {productDescription}
            </Typography>
            {/** End Buttons */}
          </div>
          {/** End Col */}

          <div className="relative ms-4">
            <div className="h-full w-full">{feature}</div>
            {/** End SVG*/}
          </div>
          {/** End Col */}
        </div>
        {/** End Grid */}
      </div>
      <Bg />
      {/** End Hero */}
    </div>
  )
}
