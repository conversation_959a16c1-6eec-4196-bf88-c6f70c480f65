'use client'
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { PriceCard, PriceCards } from '@/components/Block/PriceCards'

import ToastProvider from '@/components/ToastProvider'
import { useToast } from '@/hooks/useToast'

import { create } from 'zustand'
import { User } from '@/store/userStore'
import { useTranslations } from 'next-intl'

import { useEffect } from 'react'

interface PriceDialogStore {
  isOpen: boolean
  priceData: PriceCard[] | null
  userInfo: User | null
  open: (prices: PriceCard[], user: User) => void
  close: () => void
}

export const usePriceDialog = create<PriceDialogStore>((set) => ({
  isOpen: false,
  priceData: null,
  userInfo: null,
  open: (prices, user) => set({ isOpen: true, priceData: prices, userInfo: user }),
  close: () => set({ isOpen: false })
}))

export function PriceDialog() {
  const { isOpen, close, priceData, userInfo } = usePriceDialog()
  const { showToast } = useToast()

  const t = useTranslations('Price')

  useEffect(() => {
    if (isOpen) {
      showToast(t('error'), 'error')
    }
  }, [isOpen])

  if (!priceData || !userInfo) return null

  return (
    <ToastProvider>
      <Dialog open={isOpen} onOpenChange={close}>
        <DialogContent className="max-w-[85rem] min-w-[1280px] w-[90vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
          <DialogTitle className="sr-only">{t('pricingTitle')}</DialogTitle>
          <DialogDescription className="sr-only">{t('pricingSubtitle')}</DialogDescription>
          <div className="p-10 w-full">
            <PriceCards cards={priceData} user={userInfo} />
          </div>
        </DialogContent>
      </Dialog>
    </ToastProvider>
  )
}
