'use client'

import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'
import Link from 'next/link'
import { Typography } from '@/components/Typography'

interface CTAProps {
  href: string
  icon: LucideIcon
  text: string
  variant?: 'sm' | 'md' | 'lg'
  className?: string
}

export function CTA({ href, icon: Icon, text, variant = 'md', className }: CTAProps) {
  const sizeClasses = {
    sm: 'h-14 text-lg',
    md: 'h-18 text-xl',
    lg: 'h-22 text-2xl'
  }

  const iconSize = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const isExternal = href.startsWith('http')

  return (
    <Link
      href={href}
      className={cn(
        'inline-flex items-center gap-2 px-8 rounded-full border border-transparent bg-primary text-white hover:bg-primary/90 transition-colors',
        sizeClasses[variant],
        className
      )}
      target={isExternal ? '_blank' : undefined}
      rel={isExternal ? 'noopener noreferrer' : undefined}
    >
      <Icon className={iconSize[variant]} />
      <Typography variant="span">{text}</Typography>
    </Link>
  )
}
