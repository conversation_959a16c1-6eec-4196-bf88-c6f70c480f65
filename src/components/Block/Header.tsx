import { Logo } from '@/components/Logo'
import { Nav } from './Nav'
import { Avatar, UserInfo } from './Avatar'
import { getTranslations } from 'next-intl/server'
import { Link } from '@/i18n/routing'
import { Language } from '../Language'
import { MobileNav } from './MobileNav'

export async function Header({ userInfo }: { userInfo: UserInfo | null }) {
  const t = await getTranslations('Header')
  // FIXME: storybook中mock会导致发送无限请求
  // const data = await fetch('/common-api/user').then(res => res.json())
  return (
    <header className="sticky bg-opacity-40 top-0 backdrop-blur-lg flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full bg-transparent dark:border-neutral-700">
      <nav className="relative max-w-7xl w-full mx-auto md:flex md:items-center md:justify-between md:gap-3 py-2 px-4 sm:px-6 lg:px-4">
        <div className="flex justify-between items-center gap-x-1 shrink-0">
          <Link
            className="flex items-center gap-2 font-semibold text-xl text-black focus:outline-hidden focus:opacity-80 dark:text-white"
            href="/"
            aria-label={t('LogoAriaLabel')}
          >
            <div className="shrink-0">
              <Logo size={32} text />
            </div>
          </Link>

          {/* 移动端菜单 */}
          <MobileNav userInfo={userInfo} />
        </div>

        {/* 桌面端导航 */}
        <div className="hidden md:flex md:items-center md:justify-between md:flex-1">
          <Nav />

          <div className="flex items-center gap-4">
            <Avatar />
            <Language />
          </div>
        </div>
      </nav>
    </header>
  )
}
