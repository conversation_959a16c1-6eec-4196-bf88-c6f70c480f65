'use client'
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog'


import { create } from 'zustand'
import { useTranslations } from 'next-intl'
import { SubscribeCompare } from './SubscribeCompare'
import { TopButton } from '@/app/[locale]/home/<USER>/TopButton'

import { useState } from 'react'
import { PriceCard, PriceCards } from './Block/PriceCards'

interface PriceDialogStore {
  isOpen: boolean
  priceData: PriceCard[] | null
  userInfo: User | null
  open: (prices: PriceCard[], user: User) => void
  close: () => void
}

export const usePriceDialog = create<PriceDialogStore>((set) => ({
  isOpen: false,
  priceData: null,
  userInfo: null,
  open: (prices, user) => set({ isOpen: true, priceData: prices, userInfo: user }),
  close: () => set({ isOpen: false })
}))

export function PriceDialog() {
  const { isOpen, close, priceData, userInfo } = usePriceDialog()
  const [contentEl, setContentEl] = useState<HTMLDivElement | null>(null)
  const t = useTranslations('Price')
  const t2 = useTranslations('FAQ.Welcome')

  if (!priceData || !userInfo) return null

  return (
    <Dialog open={isOpen} onOpenChange={close}>
      <DialogContent
        ref={setContentEl}
        className="max-w-[85rem] min-w-[1280px] w-[90vw] max-h-[90vh] overflow-y-auto overflow-x-hidden"
      >
        <DialogTitle className="sr-only">{t('pricingTitle')}</DialogTitle>
        <DialogDescription className="sr-only">{t('pricingSubtitle')}</DialogDescription>
        <div className="py-10 w-full">
          <PriceCards cards={priceData} user={userInfo} />
          <SubscribeCompare />
          {/* <WaterfallClientVoice /> */}

          <div className="w-full my-10 flex justify-center">
            <TopButton scrollTarget={contentEl}>{t2('upgradeNow')}</TopButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
