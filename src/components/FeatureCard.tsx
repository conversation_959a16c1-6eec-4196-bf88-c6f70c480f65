import { ReactNode } from 'react'

export interface FeatureCardProps {
  title: string
  description: string
  icon: ReactNode
  layout?: 'vertical' | 'horizontal'
}

export default function FeatureCard({
  title,
  description,
  icon,
  layout = 'vertical'
}: FeatureCardProps) {
  return layout === 'vertical' ? (
    <div className="flex flex-col relative overflow-hidden h-auto text-foreground bg-white dark:bg-gray-800 rounded-xl shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)] hover:shadow-[0_8px_30px_-4px_rgba(0,0,0,0.15)] transition-all duration-300 p-4">
      <div className="p-3 z-10 w-full flex flex-col items-center justify-center gap-4">
        <p className="inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-tl from-blue-500 to-blue-400 text-xl font-medium shadow-[0_4px_20px_-4px_rgba(59,130,246,0.3)]">
          {icon}
        </p>
        <h3 className="text-center text-lg font-semibold text-gray-800 dark:text-white">{title}</h3>
      </div>
      <div className="w-full p-3 flex-auto flex flex-col gap-2">
        <p className="w-full text-base text-gray-600 dark:text-gray-300">{description}</p>
      </div>
    </div>
  ) : (
    <div className="flex flex-col relative overflow-hidden h-auto text-foreground bg-white dark:bg-gray-800 rounded-xl shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)] hover:shadow-[0_8px_30px_-4px_rgba(0,0,0,0.15)] transition-all duration-300 p-4">
      <div className="flex flex-col items-start justify-between gap-8 p-4 md:flex-row md:p-6">
        <p className="inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-tl from-blue-500 to-blue-400 text-xl font-medium shadow-[0_4px_20px_-4px_rgba(59,130,246,0.3)]">
          {icon}
        </p>
        <div className="flex flex-1 flex-col items-start gap-2">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white">{title}</h3>
          <p className="w-full text-gray-600 dark:text-gray-300">{description}</p>
        </div>
      </div>
    </div>
  )
}
