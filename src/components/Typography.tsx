import { cn } from '@/lib/utils'

// 非必要尽量不在className中使用text-xl，text-2x，之类的标签，避免冲突

// radix-ui 的 size 比较黑箱，同时还可能导致和className冲突，于是决定原生实现（参考shadcn）
export const Typography = ({
  variant,
  children,
  className
}: {
  variant: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span'
  children: React.ReactNode
  className?: string
}) => {
  switch (variant) {
    case 'h1':
      return (
        <h1
          className={cn(
            'scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl',
            className
          )}
        >
          {children}
        </h1>
      )
    case 'h2':
      return (
        <h2
          className={cn('scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0', className)}
        >
          {children}
        </h2>
      )
    case 'h3':
      return (
        <h3 className={cn('scroll-m-20 text-2xl font-semibold tracking-tight', className)}>
          {children}
        </h3>
      )
    case 'h4':
      return <h4 className={cn('scroll-m-20 text-xl tracking-tight', className)}>{children}</h4>
    case 'h5':
      return (
        <h5 className={cn('scroll-m-20 font-semibold tracking-tight', className)}>{children}</h5>
      )
    case 'h6':
      return (
        <h6 className={cn('scroll-m-20 text-base font-semibold tracking-tight', className)}>
          {children}
        </h6>
      )
    case 'p':
      return <p className={cn('leading-7', className)}>{children}</p>
    case 'span':
      return <span className={cn(className)}>{children}</span>
    default:
      return <p className={cn(className)}>{children}</p>
  }
}
