export function Start5() {
  return (
    <svg
      width="100"
      height="20"
      viewBox="0 0 100 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.56111 16.2518L6.64981 17.2115C6.43928 17.2808 6.21529 17.2992 5.99626 17.2652C5.77723 17.2311 5.56943 17.1454 5.38995 17.0153C5.21048 16.8852 5.06446 16.7144 4.96391 16.5168C4.86337 16.3193 4.81117 16.1007 4.81161 15.879L4.81756 12.7696C4.81817 12.4738 4.72505 12.1853 4.55156 11.9457L2.72701 9.42572C2.59756 9.24694 2.51221 9.04009 2.47792 8.82204C2.44364 8.604 2.46141 8.38094 2.52976 8.17106C2.59811 7.96119 2.71512 7.77045 2.87124 7.61441C3.02735 7.45838 3.21815 7.34146 3.42806 7.27322L6.36106 6.31947C6.64451 6.22716 6.89109 6.04679 7.06491 5.80462L8.86216 3.30212C8.9918 3.1216 9.16254 2.97454 9.36028 2.8731C9.55802 2.77166 9.77707 2.71875 9.99931 2.71875C10.2216 2.71875 10.4406 2.77166 10.6383 2.8731C10.8361 2.97454 11.0068 3.1216 11.1365 3.30212L12.9337 5.80462C13.1076 6.04666 13.3542 6.2269 13.6376 6.31912L16.5706 7.27322C16.7805 7.34146 16.9713 7.45838 17.1274 7.61441C17.2835 7.77045 17.4005 7.96119 17.4689 8.17106C17.5372 8.38094 17.555 8.604 17.5207 8.82204C17.4864 9.04009 17.4011 9.24694 17.2716 9.42572L15.4471 11.9457C15.2736 12.1853 15.1804 12.4738 15.1811 12.7696L15.187 15.879C15.1875 16.1007 15.1353 16.3193 15.0347 16.5168C14.9342 16.7144 14.7881 16.8852 14.6087 17.0153C14.4292 17.1454 14.2214 17.2311 14.0024 17.2652C13.7833 17.2992 13.5593 17.2808 13.3488 17.2115L10.4375 16.2518C10.1529 16.158 9.84572 16.158 9.56111 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M29.5611 16.2518L26.6498 17.2115C26.4393 17.2808 26.2153 17.2992 25.9963 17.2652C25.7772 17.2311 25.5694 17.1454 25.39 17.0153C25.2105 16.8852 25.0645 16.7144 24.9639 16.5168C24.8634 16.3193 24.8112 16.1007 24.8116 15.879L24.8176 12.7696C24.8182 12.4738 24.7251 12.1853 24.5516 11.9457L22.727 9.42572C22.5976 9.24694 22.5122 9.04009 22.4779 8.82204C22.4436 8.604 22.4614 8.38094 22.5298 8.17106C22.5981 7.96119 22.7151 7.77045 22.8712 7.61441C23.0274 7.45838 23.2182 7.34146 23.4281 7.27322L26.3611 6.31947C26.6445 6.22716 26.8911 6.04679 27.0649 5.80462L28.8622 3.30212C28.9918 3.1216 29.1625 2.97454 29.3603 2.8731C29.558 2.77166 29.7771 2.71875 29.9993 2.71875C30.2216 2.71875 30.4406 2.77166 30.6383 2.8731C30.8361 2.97454 31.0068 3.1216 31.1365 3.30212L32.9337 5.80462C33.1076 6.04666 33.3542 6.2269 33.6376 6.31912L36.5706 7.27322C36.7805 7.34146 36.9713 7.45838 37.1274 7.61441C37.2835 7.77045 37.4005 7.96119 37.4689 8.17106C37.5372 8.38094 37.555 8.604 37.5207 8.82204C37.4864 9.04009 37.4011 9.24694 37.2716 9.42572L35.4471 11.9457C35.2736 12.1853 35.1804 12.4738 35.1811 12.7696L35.187 15.879C35.1875 16.1007 35.1353 16.3193 35.0347 16.5168C34.9342 16.7144 34.7881 16.8852 34.6087 17.0153C34.4292 17.1454 34.2214 17.2311 34.0024 17.2652C33.7833 17.2992 33.5593 17.2808 33.3488 17.2115L30.4375 16.2518C30.1529 16.158 29.8457 16.158 29.5611 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M49.5611 16.2518L46.6498 17.2115C46.4393 17.2808 46.2153 17.2992 45.9963 17.2652C45.7772 17.2311 45.5694 17.1454 45.39 17.0153C45.2105 16.8852 45.0645 16.7144 44.9639 16.5168C44.8634 16.3193 44.8112 16.1007 44.8116 15.879L44.8176 12.7696C44.8182 12.4738 44.7251 12.1853 44.5516 11.9457L42.727 9.42572C42.5976 9.24694 42.5122 9.04009 42.4779 8.82204C42.4436 8.604 42.4614 8.38094 42.5298 8.17106C42.5981 7.96119 42.7151 7.77045 42.8712 7.61441C43.0274 7.45838 43.2182 7.34146 43.4281 7.27322L46.3611 6.31947C46.6445 6.22716 46.8911 6.04679 47.0649 5.80462L48.8622 3.30212C48.9918 3.1216 49.1625 2.97454 49.3603 2.8731C49.558 2.77166 49.7771 2.71875 49.9993 2.71875C50.2216 2.71875 50.4406 2.77166 50.6383 2.8731C50.8361 2.97454 51.0068 3.1216 51.1365 3.30212L52.9337 5.80462C53.1076 6.04666 53.3542 6.2269 53.6376 6.31912L56.5706 7.27322C56.7805 7.34146 56.9713 7.45838 57.1274 7.61441C57.2835 7.77045 57.4005 7.96119 57.4689 8.17106C57.5372 8.38094 57.555 8.604 57.5207 8.82204C57.4864 9.04009 57.4011 9.24694 57.2716 9.42572L55.4471 11.9457C55.2736 12.1853 55.1804 12.4738 55.1811 12.7696L55.187 15.879C55.1875 16.1007 55.1353 16.3193 55.0347 16.5168C54.9342 16.7144 54.7881 16.8852 54.6087 17.0153C54.4292 17.1454 54.2214 17.2311 54.0024 17.2652C53.7833 17.2992 53.5593 17.2808 53.3488 17.2115L50.4375 16.2518C50.1529 16.158 49.8457 16.158 49.5611 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M69.5611 16.2518L66.6498 17.2115C66.4393 17.2808 66.2153 17.2992 65.9963 17.2652C65.7772 17.2311 65.5694 17.1454 65.39 17.0153C65.2105 16.8852 65.0645 16.7144 64.9639 16.5168C64.8634 16.3193 64.8112 16.1007 64.8116 15.879L64.8176 12.7696C64.8182 12.4738 64.7251 12.1853 64.5516 11.9457L62.727 9.42572C62.5976 9.24694 62.5122 9.04009 62.4779 8.82204C62.4436 8.604 62.4614 8.38094 62.5298 8.17106C62.5981 7.96119 62.7151 7.77045 62.8712 7.61441C63.0274 7.45838 63.2182 7.34146 63.4281 7.27322L66.3611 6.31947C66.6445 6.22716 66.8911 6.04679 67.0649 5.80462L68.8622 3.30212C68.9918 3.1216 69.1625 2.97454 69.3603 2.8731C69.558 2.77166 69.7771 2.71875 69.9993 2.71875C70.2216 2.71875 70.4406 2.77166 70.6383 2.8731C70.8361 2.97454 71.0068 3.1216 71.1365 3.30212L72.9337 5.80462C73.1076 6.04666 73.3542 6.2269 73.6376 6.31912L76.5706 7.27322C76.7805 7.34146 76.9713 7.45838 77.1274 7.61441C77.2835 7.77045 77.4005 7.96119 77.4689 8.17106C77.5372 8.38094 77.555 8.604 77.5207 8.82204C77.4864 9.04009 77.4011 9.24694 77.2716 9.42572L75.4471 11.9457C75.2736 12.1853 75.1804 12.4738 75.1811 12.7696L75.187 15.879C75.1875 16.1007 75.1353 16.3193 75.0347 16.5168C74.9342 16.7144 74.7881 16.8852 74.6087 17.0153C74.4292 17.1454 74.2214 17.2311 74.0024 17.2652C73.7833 17.2992 73.5593 17.2808 73.3488 17.2115L70.4375 16.2518C70.1529 16.158 69.8457 16.158 69.5611 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M89.5611 16.2518L86.6498 17.2115C86.4393 17.2808 86.2153 17.2992 85.9963 17.2652C85.7772 17.2311 85.5694 17.1454 85.39 17.0153C85.2105 16.8852 85.0645 16.7144 84.9639 16.5168C84.8634 16.3193 84.8112 16.1007 84.8116 15.879L84.8176 12.7696C84.8182 12.4738 84.7251 12.1853 84.5516 11.9457L82.727 9.42572C82.5976 9.24694 82.5122 9.04009 82.4779 8.82204C82.4436 8.604 82.4614 8.38094 82.5298 8.17106C82.5981 7.96119 82.7151 7.77045 82.8712 7.61441C83.0274 7.45838 83.2182 7.34146 83.4281 7.27322L86.3611 6.31947C86.6445 6.22716 86.8911 6.04679 87.0649 5.80462L88.8622 3.30212C88.9918 3.1216 89.1625 2.97454 89.3603 2.8731C89.558 2.77166 89.7771 2.71875 89.9993 2.71875C90.2216 2.71875 90.4406 2.77166 90.6383 2.8731C90.8361 2.97454 91.0068 3.1216 91.1365 3.30212L92.9337 5.80462C93.1076 6.04666 93.3542 6.2269 93.6376 6.31912L96.5706 7.27322C96.7805 7.34146 96.9713 7.45838 97.1274 7.61441C97.2835 7.77045 97.4005 7.96119 97.4689 8.17106C97.5372 8.38094 97.555 8.604 97.5207 8.82204C97.4864 9.04009 97.4011 9.24694 97.2716 9.42572L95.4471 11.9457C95.2736 12.1853 95.1804 12.4738 95.1811 12.7696L95.187 15.879C95.1875 16.1007 95.1353 16.3193 95.0347 16.5168C94.9342 16.7144 94.7881 16.8852 94.6087 17.0153C94.4292 17.1454 94.2214 17.2311 94.0024 17.2652C93.7833 17.2992 93.5593 17.2808 93.3488 17.2115L90.4375 16.2518C90.1529 16.158 89.8457 16.158 89.5611 16.2518Z"
        fill="#FFB60C"
      />
    </svg>
  )
}
