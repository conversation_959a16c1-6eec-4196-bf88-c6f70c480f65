export function GoogleTranslatorIcon({ height, width }: IconProps) {
  return (
    <svg
      width={width || '20'}
      height={height || '20'}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.25" y="0.25" width="19.5" height="19.5" rx="9.75" fill="white" />
      <rect
        x="0.25"
        y="0.25"
        width="19.5"
        height="19.5"
        rx="9.75"
        stroke="#E9E9E9"
        strokeWidth="0.5"
      />
      <g clipPath="url(#clip0_1042_3003)">
        <path
          d="M15.244 16.0026H10.5557L9.06641 6.38281H15.244C15.6615 6.38281 16.0001 6.72191 16.0001 7.14001V15.2421C16.0001 15.6635 15.6615 16.0026 15.244 16.0026Z"
          fill="#E5E5E5"
        />
        <path
          d="M15.244 16.0026H10.5557L9.06641 6.38281H10.3913L16.0001 11.9598V15.2421C16.0001 15.6635 15.6615 16.0026 15.244 16.0026Z"
          fill="#D9D9D9"
        />
        <path d="M10.5552 15.9994L12.6166 13.7344H9.7793L10.5552 15.9994Z" fill="#4455B7" />
        <path
          d="M4.75617 4H9.48385L12.6137 13.735H4.75617C4.33863 13.735 4 13.3959 4 12.9778V4.7572C4 4.33909 4.33863 4 4.75617 4Z"
          fill="#518FF5"
        />
        <path
          d="M14.254 9.67246H15.2403V9.20826H12.9915V8.46094H12.2354V9.21814H11.0156V9.66917H13.7049C13.7049 9.66917 13.5438 10.4395 12.8929 11.1506C12.3997 10.5679 12.2649 10.2354 12.2649 10.2354H11.6666C11.6666 10.2354 11.7817 10.5153 12.1597 11.0782C12.2288 11.1803 12.3636 11.3416 12.5312 11.526C12.0348 12.0362 11.7586 12.2865 11.7586 12.2865L12.1367 12.6025C12.1367 12.6025 12.4852 12.2865 12.8699 11.8947C13.4978 12.5597 14.2803 13.3301 14.2803 13.3301L14.6386 12.9416C14.6386 12.9416 14.1751 12.5004 13.3236 11.6247C13.2907 11.5885 13.2578 11.5556 13.2249 11.5227C13.3663 11.3679 13.4978 11.2132 13.6063 11.0683C14.1093 10.4198 14.254 9.67246 14.254 9.67246Z"
          fill="#607B88"
        />
        <path
          d="M7.35464 8.43705C7.35464 8.69384 7.35464 8.95063 7.35464 9.20742C7.713 9.20742 8.07135 9.20742 8.42642 9.20742C8.38368 9.45433 8.23903 9.6782 8.0319 9.81647C7.90039 9.90536 7.75245 9.96133 7.59793 9.98767C7.44341 10.014 7.28231 10.0173 7.12779 9.98767C6.96998 9.95474 6.82204 9.89219 6.69053 9.79672C6.48012 9.64857 6.32231 9.43458 6.24012 9.19096C6.15464 8.94404 6.15464 8.67079 6.24012 8.42388C6.2993 8.24939 6.39793 8.09137 6.52615 7.95968C6.68395 7.79836 6.89108 7.67985 7.11464 7.63376C7.30532 7.59425 7.50587 7.60083 7.69327 7.6568C7.85108 7.70618 7.99903 7.79178 8.11738 7.90701C8.23903 7.7852 8.35738 7.66668 8.47903 7.54487C8.54149 7.47902 8.61053 7.41647 8.66971 7.35063C8.4856 7.17943 8.2719 7.04446 8.03519 6.95886C7.61108 6.80413 7.13437 6.80083 6.71026 6.94569C6.23025 7.1103 5.82258 7.46915 5.59573 7.92347C5.51683 8.08149 5.45765 8.2461 5.42477 8.41729C5.33929 8.84528 5.39847 9.30289 5.59573 9.69137C5.72395 9.94487 5.90806 10.172 6.13162 10.3498C6.34204 10.5177 6.58861 10.6428 6.84834 10.7119C7.17711 10.8008 7.5256 10.7975 7.85437 10.7218C8.15355 10.6527 8.43629 10.5111 8.65985 10.3004C8.89656 10.0798 9.06752 9.79343 9.15629 9.48067C9.25492 9.14157 9.26807 8.77943 9.2056 8.43376C8.58752 8.43705 7.96944 8.43705 7.35464 8.43705Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1042_3003">
          <rect width="12" height="12" fill="white" transform="translate(4 4)" />
        </clipPath>
      </defs>
    </svg>
  )
}
