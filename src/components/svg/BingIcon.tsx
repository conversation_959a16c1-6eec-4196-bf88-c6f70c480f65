export function BingIcon({ height, width }: IconProps) {
  return (
    <svg
      width={width || '20'}
      height={height || '20'}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.25" y="0.25" width="19.5" height="19.5" rx="9.75" fill="white" />
      <rect
        x="0.25"
        y="0.25"
        width="19.5"
        height="19.5"
        rx="9.75"
        stroke="#E9E9E9"
        strokeWidth="0.5"
      />
      <g clipPath="url(#clip0_1042_2550)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.0452 7.78241C9.8224 7.80821 9.65236 7.98779 9.6365 8.21433C9.62974 8.31186 9.63182 8.31857 9.8549 8.88801C10.3622 10.1833 10.4851 10.4952 10.5057 10.5391C10.5559 10.6454 10.6261 10.7452 10.7142 10.835C10.7818 10.9039 10.8262 10.9408 10.9014 10.9909C11.0337 11.0786 11.0995 11.1028 11.6143 11.254C12.1158 11.4014 12.3899 11.4994 12.6262 11.6158C12.9322 11.7665 13.1457 11.9378 13.2806 12.1413C13.3776 12.2874 13.4634 12.549 13.5006 12.8125C13.5151 12.9154 13.5154 13.143 13.5008 13.2361C13.4694 13.4381 13.4065 13.6074 13.3103 13.7491C13.259 13.8244 13.277 13.8118 13.3513 13.7199C13.5619 13.4601 13.7764 13.016 13.8859 12.6133C14.0182 12.1259 14.0364 11.6026 13.9376 11.1072C13.7455 10.1425 13.1319 9.31013 12.2677 8.84183C12.2133 8.81241 12.0066 8.70482 11.7261 8.56007C11.6835 8.53814 11.6255 8.50795 11.5971 8.49325C11.5688 8.47854 11.5108 8.44835 11.4682 8.42642C11.4255 8.40449 11.3031 8.34102 11.196 8.2858C11.0888 8.23033 10.969 8.1684 10.9295 8.14802C10.8094 8.08584 10.7288 8.04404 10.6687 8.01282C10.3908 7.86859 10.2732 7.81002 10.2397 7.79995C10.2046 7.78937 10.1152 7.7757 10.0928 7.77751C10.0881 7.77751 10.0668 7.78009 10.0452 7.78241Z"
          fill="url(#paint0_radial_1042_2550)"
        />
        <path
          d="M10.65 12.6193C10.6347 12.6283 10.6131 12.6415 10.6019 12.6485C10.591 12.6554 10.5663 12.6707 10.5476 12.682C10.4782 12.7243 10.2936 12.8371 10.135 12.9344C10.0307 12.9981 10.0154 13.0076 9.88278 13.0892C9.83546 13.1183 9.78529 13.149 9.77099 13.1573C9.75669 13.1658 9.69611 13.2027 9.63631 13.2396C9.57651 13.2765 9.47199 13.3405 9.40413 13.3818C9.33627 13.423 9.21511 13.4973 9.13477 13.5466C9.05443 13.5959 8.94861 13.6607 8.89973 13.6903C8.85086 13.72 8.80588 13.7487 8.79938 13.7541C8.79002 13.7621 8.35556 14.0291 8.13742 14.161C7.9718 14.2611 7.78019 14.3279 7.58415 14.3542C7.49289 14.3664 7.31999 14.3666 7.22899 14.3542C6.98226 14.3212 6.75476 14.2299 6.56002 14.0856C6.48358 14.0291 6.3398 13.8864 6.28494 13.8126C6.15546 13.639 6.07149 13.453 6.02807 13.2422C6.01819 13.1937 6.00857 13.1526 6.00701 13.1511C6.00285 13.147 6.01039 13.2202 6.02339 13.316C6.03717 13.4156 6.06655 13.5595 6.09801 13.6829C6.34214 14.6362 7.03686 15.4116 7.97752 15.7805C8.24844 15.8868 8.5217 15.9537 8.81914 15.9864C8.93093 15.9988 9.24709 16.0037 9.36383 15.995C9.89864 15.9547 10.3643 15.7986 10.8419 15.499C10.8846 15.4725 10.9644 15.4224 11.0195 15.3878C11.0746 15.3533 11.1443 15.3097 11.1742 15.2906C11.2041 15.2715 11.2402 15.249 11.2545 15.2405C11.2688 15.232 11.2972 15.2145 11.3174 15.2013C11.338 15.1881 11.4256 15.1332 11.5122 15.079L11.8588 14.8617L11.9778 14.7872L11.982 14.7846L11.995 14.7763L12.0012 14.7725L12.0889 14.7175L12.3918 14.5276C12.7776 14.2871 12.8925 14.2025 13.0719 14.0284C13.1465 13.9558 13.2594 13.8317 13.2648 13.816C13.2659 13.8129 13.2859 13.7822 13.3093 13.7479C13.4042 13.6086 13.4674 13.438 13.4988 13.2368C13.5134 13.1436 13.5131 12.916 13.4986 12.8131C13.4702 12.6139 13.4063 12.3881 13.3371 12.2444C13.2237 12.0086 12.9825 11.7944 12.6356 11.6218C12.54 11.5741 12.4409 11.5305 12.43 11.5313C12.4248 11.5315 12.1021 11.7274 11.7126 11.9663C11.3234 12.2052 10.9857 12.4126 10.962 12.4274C10.9384 12.4418 10.8978 12.4666 10.8718 12.4821L10.65 12.6193Z"
          fill="url(#paint1_radial_1042_2550)"
        />
        <path
          d="M6.00234 11.4733L6.0039 13.138L6.02574 13.2348C6.0936 13.5369 6.21138 13.7549 6.41574 13.9575C6.51193 14.0527 6.58551 14.11 6.68977 14.1714C6.91025 14.3009 7.14763 14.3649 7.40762 14.3646C7.67984 14.3644 7.91566 14.297 8.15824 14.1497C8.19932 14.1249 8.35973 14.0269 8.51495 13.9317L8.79705 13.7588V11.7837V9.80855V8.00166C8.79705 6.84884 8.79497 6.16432 8.79107 6.11039C8.76793 5.77291 8.62571 5.46251 8.38651 5.22797C8.31319 5.15599 8.25028 5.10774 8.0636 4.98028C7.97052 4.91681 7.80048 4.80044 7.6853 4.72174C7.57038 4.64305 7.38084 4.51352 7.26411 4.4338C7.14737 4.35407 6.98123 4.24028 6.89439 4.18094C6.71395 4.05735 6.69965 4.04884 6.64505 4.02949C6.57407 4.0042 6.49893 3.99491 6.42718 4.00265C6.21866 4.0251 6.05174 4.17449 6.00832 4.37755C6.00156 4.40903 6.00026 4.8296 6.00026 7.11176V9.80881H6L6.00234 11.4733Z"
          fill="url(#paint2_linear_1042_2550)"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_1042_2550"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(13.7267 12.4628) rotate(-131.126) scale(6.22062 4.48076)"
        >
          <stop stopColor="#00CACC" />
          <stop offset="1" stopColor="#048FCE" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_1042_2550"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(7.04846 14.7234) rotate(-23.0373) scale(6.7499 11.1896)"
        >
          <stop stopColor="#00BBEC" />
          <stop offset="1" stopColor="#2756A9" />
        </radialGradient>
        <linearGradient
          id="paint2_linear_1042_2550"
          x1="7.39904"
          y1="3.99972"
          x2="7.39904"
          y2="14.3644"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00BBEC" />
          <stop offset="1" stopColor="#2756A9" />
        </linearGradient>
        <clipPath id="clip0_1042_2550">
          <rect width="12" height="12" fill="white" transform="translate(4 4)" />
        </clipPath>
      </defs>
    </svg>
  )
}
