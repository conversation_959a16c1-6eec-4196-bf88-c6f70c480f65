export function Start4_5() {
  return (
    <svg
      width="101"
      height="20"
      viewBox="0 0 101 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.2276 16.2518L7.31632 17.2115C7.10579 17.2808 6.88179 17.2992 6.66276 17.2652C6.44373 17.2311 6.23593 17.1454 6.05646 17.0153C5.87698 16.8852 5.73096 16.7144 5.63042 16.5168C5.52987 16.3193 5.47767 16.1007 5.47811 15.879L5.48406 12.7696C5.48468 12.4738 5.39156 12.1853 5.21807 11.9457L3.39351 9.42572C3.26406 9.24694 3.17871 9.04009 3.14443 8.82204C3.11015 8.604 3.12791 8.38094 3.19626 8.17106C3.26462 7.96119 3.38163 7.77045 3.53774 7.61441C3.69386 7.45838 3.88466 7.34146 4.09456 7.27322L7.02757 6.31947C7.31101 6.22716 7.55759 6.04679 7.73141 5.80462L9.52867 3.30212C9.6583 3.1216 9.82905 2.97454 10.0268 2.8731C10.2245 2.77166 10.4436 2.71875 10.6658 2.71875C10.8881 2.71875 11.1071 2.77166 11.3048 2.8731C11.5026 2.97454 11.6733 3.1216 11.803 3.30212L13.6002 5.80462C13.7741 6.04666 14.0207 6.2269 14.3041 6.31912L17.2371 7.27322C17.447 7.34146 17.6378 7.45838 17.7939 7.61441C17.95 7.77045 18.067 7.96119 18.1354 8.17106C18.2037 8.38094 18.2215 8.604 18.1872 8.82204C18.1529 9.04009 18.0676 9.24694 17.9381 9.42572L16.1136 11.9457C15.9401 12.1853 15.847 12.4738 15.8476 12.7696L15.8535 15.879C15.854 16.1007 15.8018 16.3193 15.7012 16.5168C15.6007 16.7144 15.4546 16.8852 15.2752 17.0153C15.0957 17.1454 14.8879 17.2311 14.6689 17.2652C14.4498 17.2992 14.2258 17.2808 14.0153 17.2115L11.104 16.2518C10.8194 16.158 10.5122 16.158 10.2276 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M30.2276 16.2518L27.3163 17.2115C27.1058 17.2808 26.8818 17.2992 26.6628 17.2652C26.4437 17.2311 26.2359 17.1454 26.0565 17.0153C25.877 16.8852 25.731 16.7144 25.6304 16.5168C25.5299 16.3193 25.4777 16.1007 25.4781 15.879L25.4841 12.7696C25.4847 12.4738 25.3916 12.1853 25.2181 11.9457L23.3935 9.42572C23.2641 9.24694 23.1787 9.04009 23.1444 8.82204C23.1101 8.604 23.1279 8.38094 23.1963 8.17106C23.2646 7.96119 23.3816 7.77045 23.5377 7.61441C23.6939 7.45838 23.8847 7.34146 24.0946 7.27322L27.0276 6.31947C27.311 6.22716 27.5576 6.04679 27.7314 5.80462L29.5287 3.30212C29.6583 3.1216 29.829 2.97454 30.0268 2.8731C30.2245 2.77166 30.4436 2.71875 30.6658 2.71875C30.8881 2.71875 31.1071 2.77166 31.3048 2.8731C31.5026 2.97454 31.6733 3.1216 31.803 3.30212L33.6002 5.80462C33.7741 6.04666 34.0207 6.2269 34.3041 6.31912L37.2371 7.27322C37.447 7.34146 37.6378 7.45838 37.7939 7.61441C37.95 7.77045 38.067 7.96119 38.1354 8.17106C38.2037 8.38094 38.2215 8.604 38.1872 8.82204C38.1529 9.04009 38.0676 9.24694 37.9381 9.42572L36.1136 11.9457C35.9401 12.1853 35.847 12.4738 35.8476 12.7696L35.8535 15.879C35.854 16.1007 35.8018 16.3193 35.7012 16.5168C35.6007 16.7144 35.4546 16.8852 35.2752 17.0153C35.0957 17.1454 34.8879 17.2311 34.6689 17.2652C34.4498 17.2992 34.2258 17.2808 34.0153 17.2115L31.104 16.2518C30.8194 16.158 30.5122 16.158 30.2276 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M50.2276 16.2518L47.3163 17.2115C47.1058 17.2808 46.8818 17.2992 46.6628 17.2652C46.4437 17.2311 46.2359 17.1454 46.0565 17.0153C45.877 16.8852 45.731 16.7144 45.6304 16.5168C45.5299 16.3193 45.4777 16.1007 45.4781 15.879L45.4841 12.7696C45.4847 12.4738 45.3916 12.1853 45.2181 11.9457L43.3935 9.42572C43.2641 9.24694 43.1787 9.04009 43.1444 8.82204C43.1101 8.604 43.1279 8.38094 43.1963 8.17106C43.2646 7.96119 43.3816 7.77045 43.5377 7.61441C43.6939 7.45838 43.8847 7.34146 44.0946 7.27322L47.0276 6.31947C47.311 6.22716 47.5576 6.04679 47.7314 5.80462L49.5287 3.30212C49.6583 3.1216 49.829 2.97454 50.0268 2.8731C50.2245 2.77166 50.4436 2.71875 50.6658 2.71875C50.8881 2.71875 51.1071 2.77166 51.3048 2.8731C51.5026 2.97454 51.6733 3.1216 51.803 3.30212L53.6002 5.80462C53.7741 6.04666 54.0207 6.2269 54.3041 6.31912L57.2371 7.27322C57.447 7.34146 57.6378 7.45838 57.7939 7.61441C57.95 7.77045 58.067 7.96119 58.1354 8.17106C58.2037 8.38094 58.2215 8.604 58.1872 8.82204C58.1529 9.04009 58.0676 9.24694 57.9381 9.42572L56.1136 11.9457C55.9401 12.1853 55.847 12.4738 55.8476 12.7696L55.8535 15.879C55.854 16.1007 55.8018 16.3193 55.7012 16.5168C55.6007 16.7144 55.4546 16.8852 55.2752 17.0153C55.0957 17.1454 54.8879 17.2311 54.6689 17.2652C54.4498 17.2992 54.2258 17.2808 54.0153 17.2115L51.104 16.2518C50.8194 16.158 50.5122 16.158 50.2276 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M70.2276 16.2518L67.3163 17.2115C67.1058 17.2808 66.8818 17.2992 66.6628 17.2652C66.4437 17.2311 66.2359 17.1454 66.0565 17.0153C65.877 16.8852 65.731 16.7144 65.6304 16.5168C65.5299 16.3193 65.4777 16.1007 65.4781 15.879L65.4841 12.7696C65.4847 12.4738 65.3916 12.1853 65.2181 11.9457L63.3935 9.42572C63.2641 9.24694 63.1787 9.04009 63.1444 8.82204C63.1101 8.604 63.1279 8.38094 63.1963 8.17106C63.2646 7.96119 63.3816 7.77045 63.5377 7.61441C63.6939 7.45838 63.8847 7.34146 64.0946 7.27322L67.0276 6.31947C67.311 6.22716 67.5576 6.04679 67.7314 5.80462L69.5287 3.30212C69.6583 3.1216 69.829 2.97454 70.0268 2.8731C70.2245 2.77166 70.4436 2.71875 70.6658 2.71875C70.8881 2.71875 71.1071 2.77166 71.3048 2.8731C71.5026 2.97454 71.6733 3.1216 71.803 3.30212L73.6002 5.80462C73.7741 6.04666 74.0207 6.2269 74.3041 6.31912L77.2371 7.27322C77.447 7.34146 77.6378 7.45838 77.7939 7.61441C77.95 7.77045 78.067 7.96119 78.1354 8.17106C78.2037 8.38094 78.2215 8.604 78.1872 8.82204C78.1529 9.04009 78.0676 9.24694 77.9381 9.42572L76.1136 11.9457C75.9401 12.1853 75.847 12.4738 75.8476 12.7696L75.8535 15.879C75.854 16.1007 75.8018 16.3193 75.7012 16.5168C75.6007 16.7144 75.4546 16.8852 75.2752 17.0153C75.0957 17.1454 74.8879 17.2311 74.6689 17.2652C74.4498 17.2992 74.2258 17.2808 74.0153 17.2115L71.104 16.2518C70.8194 16.158 70.5122 16.158 70.2276 16.2518Z"
        fill="#FFB60C"
      />
      <path
        d="M90.2276 16.2518L87.3163 17.2115C87.1058 17.2808 86.8818 17.2992 86.6628 17.2652C86.4437 17.2311 86.2359 17.1454 86.0565 17.0153C85.877 16.8852 85.731 16.7144 85.6304 16.5168C85.5299 16.3193 85.4777 16.1007 85.4781 15.879L85.4841 12.7696C85.4847 12.4738 85.3916 12.1853 85.2181 11.9457L83.3935 9.42572C83.2641 9.24694 83.1787 9.04009 83.1444 8.82204C83.1101 8.604 83.1279 8.38094 83.1963 8.17106C83.2646 7.96119 83.3816 7.77045 83.5377 7.61441C83.6939 7.45838 83.8847 7.34146 84.0946 7.27322L87.0276 6.31947C87.311 6.22716 87.5576 6.04679 87.7314 5.80462L89.5287 3.30212C89.6583 3.1216 89.829 2.97454 90.0268 2.8731C90.2245 2.77166 90.4436 2.71875 90.6658 2.71875C90.8881 2.71875 91.1071 2.77166 91.3048 2.8731C91.5026 2.97454 91.6733 3.1216 91.803 3.30212L93.6002 5.80462C93.7741 6.04666 94.0207 6.2269 94.3041 6.31912L97.2371 7.27322C97.447 7.34146 97.6378 7.45838 97.7939 7.61441C97.95 7.77045 98.067 7.96119 98.1354 8.17106C98.2037 8.38094 98.2215 8.604 98.1872 8.82204C98.1529 9.04009 98.0676 9.24694 97.9381 9.42572L96.1136 11.9457C95.9401 12.1853 95.847 12.4738 95.8476 12.7696L95.8535 15.879C95.854 16.1007 95.8018 16.3193 95.7012 16.5168C95.6007 16.7144 95.4546 16.8852 95.2752 17.0153C95.0957 17.1454 94.8879 17.2311 94.6689 17.2652C94.4498 17.2992 94.2258 17.2808 94.0153 17.2115L91.104 16.2518C90.8194 16.158 90.5122 16.158 90.2276 16.2518Z"
        fill="#E2E2E2"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M90.6665 2.71875V16.1814C90.5183 16.1813 90.3701 16.2048 90.2276 16.2518L87.3163 17.2115C87.1058 17.2808 86.8818 17.2992 86.6628 17.2652C86.4437 17.2311 86.2359 17.1454 86.0565 17.0153C85.877 16.8852 85.731 16.7144 85.6304 16.5168C85.5299 16.3193 85.4777 16.1007 85.4781 15.879L85.4841 12.7696C85.4847 12.4738 85.3916 12.1853 85.2181 11.9457L83.3935 9.42572C83.2641 9.24694 83.1787 9.04009 83.1444 8.82204C83.1101 8.604 83.1279 8.38094 83.1963 8.17106C83.2646 7.96119 83.3816 7.77045 83.5377 7.61441C83.6939 7.45838 83.8847 7.34146 84.0946 7.27322L87.0276 6.31947C87.311 6.22716 87.5576 6.04679 87.7314 5.80462L89.5287 3.30212C89.6583 3.1216 89.829 2.97454 90.0268 2.8731C90.2245 2.77166 90.4436 2.71875 90.6658 2.71875C90.666 2.71875 90.6663 2.71875 90.6665 2.71875Z"
        fill="#FFB60C"
      />
    </svg>
  )
}
