export function GeminiIcon({ height, width }: IconProps) {
  return (
    <svg
      width={width || '20'}
      height={height || '20'}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.25" y="0.25" width="19.5" height="19.5" rx="9.75" fill="white" />
      <rect
        x="0.25"
        y="0.25"
        width="19.5"
        height="19.5"
        rx="9.75"
        stroke="#E9E9E9"
        strokeWidth="0.5"
      />
      <g clipPath="url(#clip0_1042_6742)">
        <path
          d="M10 16C10 15.17 9.84001 14.39 9.52 13.66C9.21001 12.93 8.785 12.295 8.245 11.755C7.705 11.215 7.07 10.79 6.34 10.48C5.61 10.16 4.83 10 4 10C4.83 10 5.61 9.84499 6.34 9.535C7.07 9.21499 7.705 8.785 8.245 8.245C8.785 7.705 9.21001 7.07 9.52 6.34C9.84001 5.61 10 4.83 10 4C10 4.83 10.155 5.61 10.465 6.34C10.785 7.07 11.215 7.705 11.755 8.245C12.295 8.785 12.93 9.21499 13.66 9.535C14.39 9.84499 15.17 10 16 10C15.17 10 14.39 10.16 13.66 10.48C12.93 10.79 12.295 11.215 11.755 11.755C11.215 12.295 10.785 12.93 10.465 13.66C10.155 14.39 10 15.17 10 16Z"
          fill="url(#paint0_radial_1042_6742)"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_1042_6742"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.1909 8.87693) rotate(18.6832) scale(12.7725 102.316)"
        >
          <stop offset="0.0671246" stopColor="#9168C0" />
          <stop offset="0.342551" stopColor="#5684D1" />
          <stop offset="0.672076" stopColor="#1BA1E3" />
        </radialGradient>
        <clipPath id="clip0_1042_6742">
          <rect width="12" height="12" fill="white" transform="translate(4 4)" />
        </clipPath>
      </defs>
    </svg>
  )
}
