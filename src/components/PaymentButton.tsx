'use client'
import React from 'react'
import { usePayment, UsePaymentOptions } from '@/hooks/usePayment'
import { User } from '@/store/userStore'
import { Loading } from '@/components/Loading'

/**
 * 支付按钮组件属性
 */
export interface PaymentButtonProps extends UsePaymentOptions {
  /** 支付计划ID */
  planId?: number
  /** 用户信息 */
  user: User | null
  /** 按钮内容 */
  children: React.ReactNode
  /** 自定义样式类名 */
  className?: string
  /** 是否禁用按钮 */
  disabled?: boolean
  /** 点击事件（在支付处理之前触发） */
  onClick?: () => void
}

/**
 * 可复用的支付按钮组件
 * 
 * 封装了支付逻辑，支持自定义样式和内容
 * 可以在不同场景中复用：首页定价、独立定价页面、支付弹窗等
 * 
 * @param props 组件属性
 * @returns 支付按钮组件
 */
export default function PaymentButton({
  planId,
  user,
  children,
  className = '',
  disabled = false,
  onClick,
  successUrl,
  cancelUrl,
  onSuccess,
  onError
}: PaymentButtonProps) {
  const { isLoading, handlePayment } = usePayment({
    successUrl,
    cancelUrl,
    onSuccess,
    onError
  })

  /**
   * 处理按钮点击事件
   */
  const handleClick = async () => {
    // 如果按钮被禁用或正在加载，则不处理
    if (disabled || isLoading) return

    // 触发外部点击事件
    onClick?.()

    // 如果没有计划ID，则不处理支付
    if (!planId) {
      console.warn('PaymentButton: planId is required for payment processing')
      return
    }

    // 处理支付
    await handlePayment(planId, user)
  }

  return (
    <div
      onClick={handleClick}
      className={`relative ${
        isLoading || disabled ? 'cursor-wait' : 'cursor-pointer'
      } ${className}`}
    >
      {/* 加载状态遮罩 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10 rounded">
          <Loading />
        </div>
      )}
      
      {/* 按钮内容 */}
      <div className={disabled ? 'opacity-50' : ''}>
        {children}
      </div>
    </div>
  )
}
