'use client'
import { logout } from '@/services/client/authService'
import { googleLogout } from '@react-oauth/google'
import { useTranslations } from 'next-intl'

export function Logout() {
  const t = useTranslations('Avatar')

  const handleLogout = async () => {
    await logout()
    await googleLogout()
    // 退出登录后直接刷新页面，确保所有数据都是最新的
    window.location.reload()
  }

  return (
    <>
      <div
        className="cursor-pointer flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
        onClick={handleLogout}
      >
        {t('logout')}
      </div>
    </>
  )
}
