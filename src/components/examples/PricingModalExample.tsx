'use client'
import React from 'react'
import { usePricingModal } from '@/hooks/usePricingModal'

/**
 * 价格弹窗使用示例组件
 * 
 * 展示如何在不同场景中使用全局价格弹窗
 */
export default function PricingModalExample() {
  const { openPricingModalWithData } = usePricingModal()

  /**
   * 示例1: 简单触发价格弹窗
   */
  const handleUpgradeClick = async () => {
    await openPricingModalWithData(() => {
      console.log('用户完成支付后的回调')
      // 这里可以执行支付成功后的逻辑，比如：
      // - 刷新用户信息
      // - 重新加载页面数据
      // - 显示成功提示
      // - 跳转到特定页面
    })
  }

  /**
   * 示例2: 付费功能访问拦截
   */
  const handlePremiumFeatureClick = async () => {
    // 模拟检查用户权限
    const userHasPremium = false // 这里应该从实际的用户状态获取

    if (!userHasPremium) {
      // 用户没有权限，显示价格弹窗
      await openPricingModalWithData(() => {
        console.log('用户升级后，可以重新尝试访问付费功能')
        // 支付成功后可以重新执行原本的功能
        executePremiumFeature()
      })
      return
    }

    // 用户有权限，直接执行功能
    executePremiumFeature()
  }

  const executePremiumFeature = () => {
    console.log('执行付费功能')
    // 这里是实际的付费功能逻辑
  }

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold mb-4">价格弹窗使用示例</h2>
      
      <div className="space-y-4">
        {/* 示例1: 升级按钮 */}
        <div className="p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">示例1: 升级按钮</h3>
          <p className="text-gray-600 mb-3">
            直接触发价格弹窗，让用户选择订阅计划
          </p>
          <button
            onClick={handleUpgradeClick}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            升级到 Premium
          </button>
        </div>

        {/* 示例2: 付费功能拦截 */}
        <div className="p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">示例2: 付费功能拦截</h3>
          <p className="text-gray-600 mb-3">
            当用户尝试访问付费功能时，自动弹出价格弹窗
          </p>
          <button
            onClick={handlePremiumFeatureClick}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            访问高级功能
          </button>
        </div>

        {/* 使用说明 */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">使用说明</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 使用 <code>usePricingModal</code> Hook 来控制价格弹窗</li>
            <li>• 调用 <code>openPricingModalWithData()</code> 自动获取数据并打开弹窗</li>
            <li>• 支持传入成功回调函数，在用户完成支付后执行</li>
            <li>• 如果数据获取失败，会自动降级到 /pricing 页面</li>
            <li>• 弹窗样式与首页定价区域完全一致</li>
            <li>• 支持响应式设计，在移动端和桌面端都有良好体验</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
