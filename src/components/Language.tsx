'use client'
// 导入已注释，因为当前只支持英语
// import { Link, usePathname } from '@/i18n/routing'
// import { useLocale } from 'next-intl'
// import { languagesOptions } from '@/app/config'
// import { Globe, ChevronDown } from 'lucide-react'
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger
// } from '@/components/ui/dropdown-menu'
// import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
// import { useState } from 'react'

interface LanguageProps {
  isMobile?: boolean
}

export function Language({ isMobile = false }: LanguageProps) {
  // 由于现在只支持英语，隐藏语言选择器
  // 如果将来需要重新启用多语言支持，可以取消注释下面的代码

  // 暂时返回null，不显示语言选择器
  return null

  /*
  // 原始的多语言选择器代码（已禁用）
  const locale = useLocale()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const currentLanguage = languagesOptions.find((lang) => lang.value === locale)

  // 移动端模式下使用折叠面板
  if (isMobile) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
        <CollapsibleTrigger asChild>
          <button className="flex w-full items-center justify-between rounded-md p-2 text-sm hover:bg-gray-100 dark:hover:bg-neutral-800">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              <span>{currentLanguage?.label}</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            />
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-1 space-y-1">
          <div className="ml-6 flex flex-col space-y-1 border-l pl-2">
            {languagesOptions.map((lang) => (
              <Link
                key={lang.value}
                className={`text-sm rounded-md px-2 py-1.5 hover:bg-gray-100 dark:hover:bg-neutral-800 ${
                  lang.value === locale ? 'font-medium' : 'text-muted-foreground'
                }`}
                href={pathname}
                locale={lang.value}
              >
                {lang.label}
              </Link>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    )
  }

  // 桌面端使用下拉菜单
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center gap-1 rounded-lg p-2 text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:ring-2 focus:ring-gray-200 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:ring-neutral-700">
        <Globe className="h-3.5 w-3.5" />
        <span className="mx-1">{currentLanguage?.label}</span>
        <ChevronDown className="h-4 w-4" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languagesOptions.map((lang) => (
          <DropdownMenuItem key={lang.value} asChild>
            <Link className="cursor-pointer w-full" href={pathname} locale={lang.value}>
              {lang.label}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
  */
}
