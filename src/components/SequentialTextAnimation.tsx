'use client'

import { useState } from 'react'
import { AnimatePresence, motion } from 'framer-motion'
import { TextAnimation } from './TextAnimation'

interface SequentialTextAnimationProps {
  texts: string[]
  interval?: number // 每个文本显示的时间间隔
  className?: string
}

export const SequentialTextAnimation = ({
  texts,
  className = ''
}: SequentialTextAnimationProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [key, setKey] = useState(0)

  const handleAnimationComplete = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
    setKey((prev) => prev + 1)
  }

  return (
    <div className="relative">
      <AnimatePresence mode="wait">
        <motion.div
          key={key}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <TextAnimation
            text={texts[currentIndex]}
            className={className}
            onAnimationComplete={handleAnimationComplete}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
