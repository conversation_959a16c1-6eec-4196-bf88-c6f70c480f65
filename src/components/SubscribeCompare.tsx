'use client'
import { Card } from '@/components/ui/card'
import { motion } from 'framer-motion'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { useTranslations } from 'next-intl'
import { XCircle, CheckCircle2 } from 'lucide-react'

interface TranslationPair {
  original: string
  traditional: string
  ai: string
}

interface CompareSection {
  title: string
  examples: TranslationPair[]
}

export function SubscribeCompare() {
  const t = useTranslations('Welcome')

  const compareSections: CompareSection[] = [
    {
      title: t('compare.compare1.title'),
      examples: Array.from({ length: 6 }, (_, i) => ({
        original: t(`compare.compare1.example${i + 1}.original`),
        traditional: t(`compare.compare1.example${i + 1}.traditional`),
        ai: t(`compare.compare1.example${i + 1}.ai`)
      }))
    },
    {
      title: t('compare.compare2.title'),
      examples: Array.from({ length: 2 }, (_, i) => ({
        original: t(`compare.compare2.example${i + 1}.original`),
        traditional: t(`compare.compare2.example${i + 1}.traditional`),
        ai: t(`compare.compare2.example${i + 1}.ai`)
      }))
    },
    {
      title: t('compare.compare3.title'),
      examples: Array.from({ length: 3 }, (_, i) => ({
        original: t(`compare.compare3.example${i + 1}.original`),
        traditional: t(`compare.compare3.example${i + 1}.traditional`),
        ai: t(`compare.compare3.example${i + 1}.ai`)
      }))
    }
  ]

  return (
    <div className="mt-20">
      <h2 className="text-center text-3xl mb-4 leading-tight font-bold md:text-xl md:leading-tight lg:text-5xl lg:leading-tight">
        {t('compare.title')}
      </h2>

      <p className="text-orange-600 text-center text-lg font-medium mb-2">
        {t('compare.subtitle1')}
      </p>
      {/* <p className="text-orange-600 text-center text-lg font-medium">
        {t("compare.subtitle2")}
      </p> */}

      {compareSections.map((section, sectionIndex) => (
        <div key={sectionIndex} className="mt-16 mb-10">
          <h3 className="text-center text-2xl font-bold mb-4">{section.title}</h3>
          <Card className="p-2 sm:p-6 mb-20">
            <Table className="w-full border-collapse">
              <TableHeader>
                <TableRow className="border-b-2 border-gray-200">
                  <TableHead className="w-[33%] text-center text-xs sm:text-base lg:text-lg font-medium border-r border-gray-200">
                    {t('compare.headers.original')}
                  </TableHead>
                  <TableHead className="w-[33%] text-center text-xs sm:text-base lg:text-lg font-medium border-r border-gray-200">
                    {t('compare.headers.traditional')}
                  </TableHead>
                  <TableHead className="w-[34%] text-center text-xs sm:text-base lg:text-lg font-medium">
                    {t('compare.headers.ai')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {section.examples.map((item, index) => (
                  <motion.tr
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 }}
                    className="hover:bg-muted/50 border-b border-gray-200 last:border-b-0"
                  >
                    <TableCell className="align-middle p-1 sm:p-2 lg:p-4 border-r border-gray-200">
                      <div className="text-xs sm:text-sm lg:text-base break-words p-1 sm:p-2 lg:p-3 rounded-md">
                        {item.original}
                      </div>
                    </TableCell>
                    <TableCell className="align-middle p-1 sm:p-2 lg:p-4 border-r border-gray-200">
                      <div className="text-xs sm:text-sm lg:text-base break-words bg-blue-100/50 p-1 sm:p-2 lg:p-3 rounded-md flex items-start gap-2">
                        <XCircle className="h-4 w-4 text-red-500 shrink-0 mt-1" />
                        <span className="flex-1">{item.traditional}</span>
                      </div>
                    </TableCell>
                    <TableCell className="align-middle p-1 sm:p-2 lg:p-4">
                      <div className="text-xs sm:text-sm lg:text-base break-words bg-green-200/50 p-1 sm:p-2 lg:p-3 rounded-md flex items-start gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500 shrink-0 mt-1" />
                        <span className="flex-1">{item.ai}</span>
                      </div>
                    </TableCell>
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>
      ))}
    </div>
  )
}
