import FeatureCard, { FeatureCardProps } from './FeatureCard'

export interface FeatureSectionProps {
  title: string
  description?: string
  features: FeatureCardProps[]
  columns?: 2 | 3 | 4
  className?: string
  layout?: 'vertical' | 'horizontal'
}

export default function FeatureSection({
  title,
  description,
  features,
  columns = 3,
  className = '',
  layout = 'vertical'
}: FeatureSectionProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }

  return (
    <section className={`py-12 px-4 sm:px-6 lg:px-8 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl">{title}</h2>
          {description && (
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">{description}</p>
          )}
        </div>
        <div className={`grid gap-8 ${gridCols[columns]}`}>
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              layout={layout}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
