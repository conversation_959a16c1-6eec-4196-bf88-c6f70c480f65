'use client'
import { useForm } from 'react-hook-form'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/routing'
import { GoogleIcon } from '@/components/svg/GoogleIcon'
import { AuthForm, FormData } from '@/components/AuthForm'
import Button from '@/components/Button'
import { useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { useCookies } from 'react-cookie'
import { Typography } from '@/components/Typography'
interface LoginFormProps {
  onSuccess?: () => void
  shouldRedirect?: boolean
  isDialog?: boolean
}

export function LoginForm({ onSuccess, shouldRedirect = true, isDialog = false }: LoginFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<FormData>()

  const [rememberMe, setRememberMe] = useState(false)
  const [cookies, setCookie] = useCookies(['accountForm', 'rememberMe'])
  const t = useTranslations('Login')
  const { loading, googleLoading, handleLogin, handleGoogleLogin } = useAuth()

  useEffect(() => {
    if (cookies.rememberMe) {
      setRememberMe(true)
      if (cookies.accountForm) {
        setValue('email', cookies.accountForm.email)
        setValue('password', cookies.accountForm.password)
      }
    }
  }, [cookies, setValue])

  const onSubmit = async (data: FormData): Promise<void> => {
    if (rememberMe) {
      setCookie('accountForm', { email: data.email, password: data.password }, { path: '/' })
    }
    const success = await handleLogin(data.email, data.password, shouldRedirect)
    if (success) {
      onSuccess?.()
    }
  }

  return (
    <div className="w-full space-y-4">
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <p className="text-start mt-2 text-gray-500">{t('description')}</p>
      <Button variant="outline" type="button" onClick={handleGoogleLogin} loading={googleLoading}>
        <GoogleIcon size={16} />
        <Typography variant="span" className="ml-2">
          {t('continueWithGoogle')}
        </Typography>
      </Button>
      <div className="flex items-center my-4">
        <hr className="grow border-t border-gray-300" />
        <Typography variant="span" className="mx-2 text-gray-500">
          {t('orContinueWith')}
        </Typography>
        <hr className="grow border-t border-gray-300" />
      </div>
      <AuthForm
        type="sign-in"
        onSubmit={onSubmit}
        register={register}
        handleSubmit={handleSubmit}
        errors={errors}
        loading={loading}
        rememberMe={rememberMe}
        setRememberMe={setRememberMe}
      />
      <Typography variant="p" className="mt-4 text-center">
        {t('noAccount')}
        <Link
          href="/register"
          className="text-primary ml-2"
          target={isDialog ? '_blank' : undefined}
          rel={isDialog ? 'noopener noreferrer' : undefined}
        >
          {t('signUp')}
        </Link>
      </Typography>
    </div>
  )
}
