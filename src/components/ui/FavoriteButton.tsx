'use client'

/**
 * 收藏按钮组件
 * 纯UI组件，负责渲染不同状态的收藏按钮
 */

import React from 'react'

// 收藏图标组件
interface FavoriteIconProps {
  isFavorited: boolean
  isLoading: boolean
  className?: string
}

const FavoriteIcon: React.FC<FavoriteIconProps> = ({ isFavorited, isLoading, className = "w-5 h-5" }) => {
  if (isLoading) {
    return (
      <svg className={`${className} animate-spin`} fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    )
  }

  if (isFavorited) {
    // 已收藏 - 实心书签图标
    return (
      <svg
        className={`w-5 h-5 mr-2 transition-colors text-yellow-500 fill-current`}
        fill={'currentColor'}
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
        >
        </path>
      </svg>
    )
  }

  // 未收藏 - 空心书签图标
  return (
    <svg
      className={`w-5 h-5 mr-2 transition-colors text-green-500`}
      fill={'none'}
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
      >
      </path>
    </svg>
  )
}

// 收藏按钮Props
interface FavoriteButtonProps {
  isFavorited: boolean
  isLoading: boolean
  disabled: boolean
  text: string
  onClick: () => void
  className?: string
}

/**
 * 收藏按钮组件
 */
const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  isFavorited,
  isLoading,
  disabled,
  text,
  onClick,
  className = ""
}) => {
  // 按钮样式类
  const getButtonClasses = () => {
    const baseClasses = "px-6 py-3 flex items-center justify-center text-sm font-bold transition duration-200 rounded-lg focus:ring focus:ring-green-300"

    if (disabled) {
      return `${baseClasses} bg-gray-300 text-gray-500 cursor-not-allowed`
    }

    if (isFavorited) {
      return `${baseClasses} bg-green-50 border border-green-500 text-green-500 hover:bg-green-100`
    }

    return `${baseClasses} border border-green-500 text-green-500 hover:bg-green-50`
  }

  return (
    <button
      className={`${getButtonClasses()} ${className}`}
      onClick={onClick}
      disabled={disabled}
      type="button"
    >
      <FavoriteIcon
        isFavorited={isFavorited}
        isLoading={isLoading}
        className="w-5 h-5 mr-2"
      />
      {text}
    </button>
  )
}

export default FavoriteButton
