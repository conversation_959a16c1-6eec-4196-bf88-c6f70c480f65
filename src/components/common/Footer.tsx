import { Link } from "next-view-transitions";
import { getLocale } from 'next-intl/server';
import * as authorService from '@/services/author.service';
import * as bookService from '@/services/book.service';
import { AuthorListItem } from '@/models/author.model';
import { BookListItem } from '@/types/book.types';
import { urlGenerator } from '@/services/url.service';

export default async function Footer() {
  // 获取当前语言
  const locale = await getLocale();
  const language = locale || 'en';

  // 并行获取热门作者和热门书籍数据，添加错误处理
  let popularAuthors: <AUTHORS>
  let popularBooks: BookListItem[] = [];

  try {
    [popularAuthors, popularBooks] = await Promise.all([
      authorService.getPopularAuthors(10, language).catch((error) => {
        console.error('Failed to fetch popular authors for footer:', error);
        return [];
      }),
      bookService.getPopularBooks(10, language).catch((error) => {
        console.error('Failed to fetch popular books for footer:', error);
        return [];
      })
    ]);
  } catch (error) {
    console.error('Failed to fetch footer data:', error);
    // 如果并行获取失败，保持空数组
  }

  return <section className="py-20">
    <div className="container mx-auto px-4">
      <div className="flex flex-wrap -m-4 mb-24">
        <div className="w-full lg:w-1/4 p-4"><Link className="inline-block" href="/"><img src="/logo.svg" alt="" /></Link></div>
        <div className="w-full lg:w-3/4 p-4">
          <div className="flex flex-wrap -m-4">
            <div className="w-full md:w-1/2 lg:w-1/4 p-4">
              <p className="font-bold mb-6">Popular Authors</p>
              <ul>
                {popularAuthors.length > 0 ? (
                  popularAuthors.map((author) => (
                    <li key={author.id} className="mb-3">
                      <Link
                        className="text-gray-500 hover:text-gray-700 text-sm transition duration-200"
                        href={urlGenerator.author.detail({ id: author.id, name: author.name })}
                        title={`${author.name} (${author.bookCount} books)`}
                      >
                        {author.name}
                      </Link>
                    </li>
                  ))
                ) : (
                  <li className="mb-3">
                    <span className="text-gray-400 text-sm">No authors available</span>
                  </li>
                )}
              </ul>
            </div>
            <div className="w-full md:w-1/2 lg:w-1/4 p-4">
              <p className="font-bold mb-6">Hot Summaries</p>
              <ul>
                {popularBooks.length > 0 ? (
                  popularBooks.map((book) => (
                    <li key={book.id} className="mb-3">
                      <Link
                        className="text-gray-500 hover:text-gray-700 text-sm transition duration-200"
                        href={urlGenerator.book.detail({ id: book.id, title: book.title })}
                        title={`${book.title} by ${book.authors.join(', ')}`}
                      >
                        {book.title}
                      </Link>
                    </li>
                  ))
                ) : (
                  <li className="mb-3">
                    <span className="text-gray-400 text-sm">No books available</span>
                  </li>
                )}
              </ul>
            </div>
            <div className="w-full md:w-1/2 lg:w-1/4 p-4">
              <p className="font-bold mb-6">Company</p>
              <ul>
                <li className="mb-3"><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/about-us">About</Link></li>
                <li className="mb-3"><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/contact-us">Contact Us</Link></li>
                <li className="mb-3"><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/pricing">Pricing</Link></li>
              </ul>
            </div>
            <div className="w-full md:w-1/2 lg:w-1/4 p-4">
              <p className="font-bold mb-6">Legal</p>
              <ul>
                <li className="mb-3"><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/terms">Terms of Use</Link></li>
                <li className="mb-3"><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/privacy">Policy Privacy</Link></li>
                <li><Link className="text-gray-500 hover:text-gray-700 text-sm transition duration-200" href="/licenses">Licenses</Link></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <p className="text-center text-gray-500 text-sm">All rights reserved © 15minutes 2025</p>
    </div>
  </section>
}
