'use client';
import { useAuthModal } from '@/store/authModalStore';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { useCookies } from 'react-cookie';
import { useAuth } from '@/contexts/AuthContext';
import { FormData } from '@/components/AuthForm';
import { useRouter } from '@/i18n/routing';

export default function LoginContent({ showCloseIcon = true }: { showCloseIcon?: boolean }) {
  const { close, openRegister } = useAuthModal();
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<FormData>();

  const [rememberMe, setRememberMe] = useState(false);
  const [cookies, setCookie] = useCookies(['accountForm', 'rememberMe']);
  const { loading, googleLoading, handleLogin, handleGoogleLogin } = useAuth();

  useEffect(() => {
    if (cookies.rememberMe) {
      setRememberMe(true);
      if (cookies.accountForm) {
        setValue('email', cookies.accountForm.email);
        setValue('password', cookies.accountForm.password);
      }
    }
  }, [cookies, setValue]);

  const onSubmit = async (data: FormData): Promise<void> => {
    if (rememberMe) {
      setCookie('accountForm', { email: data.email, password: data.password }, { path: '/' });
    }

    // 根据是否为页面模式决定是否跳转
    const shouldRedirect = !showCloseIcon; // 页面模式需要跳转，弹窗模式不跳转
    const success = await handleLogin(data.email, data.password, shouldRedirect);

    if (success) {
      close();
      // 执行成功回调
      useAuthModal.getState().onSuccess?.();
      // 清除成功回调（防止多次调用）
      useAuthModal.getState().setOnSuccess(undefined);
    }
  };

  const handleGoogleSignin = () => {
    handleGoogleLogin(!showCloseIcon); // 页面模式需要跳转
  };

  const handleSignupClick = () => {
    close();
    openRegister();
  };

  const handleForgotPassword = () => {
    close(); // 关闭弹窗
    router.push('/reset-password'); // 路由跳转
  };

  const handleTermsClick = () => {
    close(); // 关闭弹窗
    router.push('/terms'); // 路由跳转
  };

  const handlePrivacyClick = () => {
    close(); // 关闭弹窗
    router.push('/privacy'); // 路由跳转
  };

  return <div>
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-heading text-2xl font-bold">Log in to 15minutes</h2>
        {
          showCloseIcon && <button onClick={() => close()} className="cursor-pointer text-gray-400 hover:text-gray-600">
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        }

      </div>

      <button
        onClick={handleGoogleSignin}
        disabled={googleLoading}
        className="cursor-pointer flex items-center justify-center w-full py-3 mb-4 bg-white border-[#dddddd] border-solid border-1 text-[#444444] rounded-md font-medium hover:bg-gray-50 transition duration-200"
      >
        <img className="h-6 mr-3" src="/logos/google-icon.png" alt="Google logo" />
        {googleLoading ? 'Loading...' : 'Continue with Google'}
      </button>

      <div className="flex items-center my-4">
        <div className="flex-grow h-px bg-gray-200"></div>
        <p className="mx-4 text-sm text-gray-400">Or continue with</p>
        <div className="flex-grow h-px bg-gray-200"></div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <div className="mb-4">
          <input
            className={`px-4 py-3 w-full border ${errors.email ? 'border-red-500' : 'border-gray-200'} rounded-md placeholder-gray-400 text-sm focus:ring focus:ring-green-200 transition duration-200 outline-none`}
            type="email"
            placeholder="Email"
            required
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
                message: 'Invalid email address'
              }
            })}
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
        </div>
        <div className="mb-2">
          <input
            className={`px-4 py-3 w-full border ${errors.password ? 'border-red-500' : 'border-gray-200'} rounded-md placeholder-gray-400 text-sm focus:ring focus:ring-green-200 transition duration-200 outline-none`}
            type="password"
            placeholder="Password"
            required
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters'
              }
            })}
          />
          {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>}
        </div>
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-green-500 focus:ring-green-400 border-gray-300 rounded"
              checked={rememberMe}
              onChange={() => setRememberMe(!rememberMe)}
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-600">Remember me</label>
          </div>
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-green-500 hover:text-green-600 cursor-pointer"
          >
            Forgot your password?
          </button>
        </div>
        <button
          className="px-6 py-3 mb-4 block text-center w-full bg-green-500 text-white text-sm font-bold rounded-md hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200"
          type="submit"
          disabled={loading}
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
      </form>

      <p className="text-center text-sm text-gray-500 mt-4">
        Don&apos;t have an account? {' '}
        <button onClick={handleSignupClick} className="text-green-500 hover:text-green-600 font-medium">Sign up</button>
      </p>

      <p className="text-xs text-gray-400 mt-6 text-center">
        By clicking continue, you agree to our{' '}
        <button
          type="button"
          onClick={handleTermsClick}
          className="text-green-500 hover:underline cursor-pointer"
        >
          Terms of Service
        </button>{' '}
        and{' '}
        <button
          type="button"
          onClick={handlePrivacyClick}
          className="text-green-500 hover:underline cursor-pointer"
        >
          Privacy Policy
        </button>.
      </p>
    </div>
  </div>
}
