'use client'

import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog'
import { useAuthModal } from '@/store/authModalStore'
import LoginContent from './LoginContent'
import RegisterContent from './RegisterContent'

/**
 * 认证弹窗组件
 * 根据状态显示登录或注册表单
 */
export function AuthModal() {
  // 获取认证弹窗状态
  const { isOpen, type, close } = useAuthModal()

  // 处理成功回调
  // const handleSuccess = () => {
  //   // 关闭弹窗
  //   close()
  //   // 执行成功回调
  //   onSuccess?.()
  //   // 清除成功回调（防止多次调用）
  //   useAuthModal.getState().setOnSuccess(undefined)
  // }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && close()}>
      <DialogTitle></DialogTitle>
      <DialogDescription></DialogDescription>
      <DialogContent className="sm:max-w-[500px] [&>.absolute.right-4.top-4]:hidden">
        {/* 根据类型渲染不同的表单 */}
        {type === 'login' && (
          <LoginContent />
        )}
        {type === 'register' && (
          <RegisterContent />
        )}
      </DialogContent>
    </Dialog>
  )
}
