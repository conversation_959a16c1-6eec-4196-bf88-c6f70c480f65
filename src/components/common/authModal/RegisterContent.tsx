'use client';
import { useAuthModal } from '@/store/authModalStore';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from '@/i18n/routing';
import { signup } from '@/services/client/authService';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';

// 定义表单数据类型
interface FormData {
  email: string;
  password: string;
}

export default function RegisterContent({ showCloseIcon = true }: { showCloseIcon?: boolean }) {
  const { close, openLogin } = useAuthModal();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { showToast } = useToast();
  const { handleGoogleLogin, googleLoading } = useAuth();

  // 初始化表单
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>();

  // 处理登录按钮点击
  const handleLoginClick = () => {
    close();
    openLogin();
  };

  const handleTermsClick = () => {
    close(); // 关闭弹窗
    router.push('/terms'); // 路由跳转
  };

  const handlePrivacyClick = () => {
    close(); // 关闭弹窗
    router.push('/privacy'); // 路由跳转
  };

  // 处理Google登录
  const handleGoogleSignup = () => {
    handleGoogleLogin(!showCloseIcon); // 页面模式需要跳转
  };

  // 处理表单提交
  const onSubmit = async (data: FormData): Promise<void> => {
    setLoading(true);
    try {
      const response = await signup(data.email, data.password);
      setLoading(false);
      if (response.code === 200) {
        // 关闭弹窗
        close();
        // 执行成功回调
        useAuthModal.getState().onSuccess?.();
        // 清除成功回调（防止多次调用）
        useAuthModal.getState().setOnSuccess(undefined);
        // 导航到验证页面
        router.push(`/register/verification?email=${data.email}`);
      } else {
        showToast(response.message, 'error');
      }
    } catch (_error) {
      setLoading(false);
      showToast('注册失败，请稍后重试', 'error');
    }
  };

  return <div>
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-heading text-2xl font-bold">Create your account</h2>
        {
          showCloseIcon && <button onClick={() => close()} className="cursor-pointer text-gray-400 hover:text-gray-600">
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        }
      </div>

      <button
        onClick={handleGoogleSignup}
        disabled={googleLoading}
        className="cursor-pointer flex items-center justify-center w-full bg-white border-[#dddddd] border-solid border-1 text-[#444444] py-3 mb-4 google-btn rounded-md font-medium hover:bg-gray-50 transition duration-200"
      >
        <img className="h-6 mr-3" src="/logos/google-icon.png" alt="Google logo" />
        {googleLoading ? 'Loading...' : 'Continue with Google'}
      </button>

      <div className="flex items-center my-4">
        <div className="flex-grow h-px bg-gray-200"></div>
        <p className="mx-4 text-sm text-gray-400">Or continue with</p>
        <div className="flex-grow h-px bg-gray-200"></div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <div className="mb-4">
          <input
            className={`px-4 py-3 w-full border ${errors.email ? 'border-red-500' : 'border-gray-200'} rounded-md placeholder-gray-400 text-sm focus:ring focus:ring-green-200 transition duration-200 outline-none`}
            type="email"
            placeholder="Email"
            required
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
                message: 'Invalid email address'
              }
            })}
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
        </div>
        <div className="mb-6">
          <input
            className={`px-4 py-3 w-full border ${errors.password ? 'border-red-500' : 'border-gray-200'} rounded-md placeholder-gray-400 text-sm focus:ring focus:ring-green-200 transition duration-200 outline-none`}
            type="password"
            placeholder="Password"
            required
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters'
              }
            })}
          />
          {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>}
        </div>
        <button
          className="px-6 py-3 mb-4 block text-center w-full bg-green-500 text-white text-sm font-bold rounded-md hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200"
          type="submit"
          disabled={loading}
        >
          {loading ? 'Signing up...' : 'Sign Up'}
        </button>
      </form>

      <p className="text-center text-sm text-gray-500 mt-4">
        Already have an account? {' '}
        <button onClick={handleLoginClick} className="text-green-500 hover:text-green-600 font-medium">Log in</button>
      </p>

      <p className="text-xs text-gray-400 mt-6 text-center">
        By clicking continue, you agree to our{' '}
        <button
          type="button"
          onClick={handleTermsClick}
          className="text-green-500 hover:underline cursor-pointer"
        >
          Terms of Service
        </button>{' '}
        and{' '}
        <button
          type="button"
          onClick={handlePrivacyClick}
          className="text-green-500 hover:underline cursor-pointer"
        >
          Privacy Policy
        </button>.
      </p>
    </div>
  </div>
}
