import type { Metadata } from 'next'
import Header from '@/components/common/header'
import Footer from '@/components/common/Footer'
import { getTranslations } from 'next-intl/server'
import AffiliateTracker from '@/components/AffiliateTracker'
import '@/app/[locale]/globals.css'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Metadata')

  return {
    title: t('title'),
    description: t('description')
  }
}

/**
 * 公共布局组件, 区分路由组使用
 * @param param0
 * @returns
 */

export default async function CommonRootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className="flex flex-col min-h-screen">
      <AffiliateTracker />
      <Header />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  )
}
