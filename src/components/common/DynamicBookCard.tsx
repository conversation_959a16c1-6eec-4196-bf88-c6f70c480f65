'use client'

import React, { useState, useCallback } from 'react'
import { Link } from 'next-view-transitions'
import BookCover from './BookCover'
import { urlGenerator } from '@/services/url.service'

interface DynamicBookCardProps {
  id: number | string
  coverUrl?: string | null
  title: string
  authors?: string[]
  author?: string
  description?: string | null
  subtitle?: string | null
  className?: string
  href?: string
  priority?: boolean
  fallbackColor?: string
}

/**
 * 三层结构的动态书籍卡片组件
 * 1. 背景色层：从封面图片提取主色，30%透明度
 * 2. 底图模板层：book-cover.png 作为统一模板
 * 3. 实际封面层：后端返回的封面图片
 * 4. 内容层：文字信息
 */
export default function DynamicBookCard({
  id,
  coverUrl,
  title,
  authors,
  author,
  description,
  subtitle,
  className = '',
  href,
  priority = false,
  fallbackColor = 'e9e1cc'
}: DynamicBookCardProps) {
  const [dominantColor, setDominantColor] = useState<string | null>(null)

  const handleColorExtracted = useCallback((color: string | null) => {
    console.log(`🎯 Color extracted for "${title}":`, color)
    setDominantColor(color)
  }, [title])

  // 调试：检查图片URL和颜色状态
  React.useEffect(() => {
    console.log(`📋 DynamicBookCard for "${title}":`, {
      coverUrl,
      dominantColor,
      enableColorExtraction: true
    })
  }, [title, coverUrl, dominantColor])

  // 立即测试：直接设置测试颜色验证功能
  React.useEffect(() => {
    console.log(`🚀 Component mounted for "${title}"`)

    // 立即设置测试颜色，基于书籍ID
    const testColors = [
      'rgb(220, 38, 127)',  // 粉红色
      'rgb(59, 130, 246)',   // 蓝色
      'rgb(16, 185, 129)',   // 绿色
      'rgb(245, 158, 11)',   // 橙色
      'rgb(139, 92, 246)',   // 紫色
    ]

    const colorIndex = (typeof id === 'number' ? id : 0) % testColors.length
    const testColor = testColors[colorIndex]

    console.log(`🧪 Setting immediate test color for "${title}": ${testColor}`)
    setDominantColor(testColor)
  }, [title, id])



  const displayAuthor = authors ? authors.join(', ') : author || 'Unknown Author'
  const displayDescription = description || subtitle || 'No description available'
  const bookHref = href || urlGenerator.book.detail({
    id,
    title
  })

  return (
    <div
      className={`
        book-card-layered
        bg-white rounded-lg shadow-md overflow-hidden
        transition-transform duration-300 hover:shadow-lg hover:-translate-y-1
        ${className}
      `}
      style={{
        '--extracted-color': dominantColor || 'transparent'
      } as React.CSSProperties}
      data-extracted-color={dominantColor || 'none'} // 用于调试
    >
      {/* 底图模板层：直接使用提取的颜色作为背景 */}
      <div className="book-card-template" />

      {/* 第3层：实际封面图片 */}
      <div className="book-card-cover-area">
        <BookCover
          coverUrl={coverUrl}
          title={title}
          width={164}
          height={243}
          className="w-full h-full"
          fallbackColor={fallbackColor}
          priority={priority}
          enableColorExtraction={true}
          onColorExtracted={handleColorExtracted}
          style={{
            borderRadius: '4px'
          }}
        />
      </div>

      {/* 第4层：文本内容 - 恢复正常布局 */}
      <div className="book-card-content-layer h-full flex flex-col">
        {/* 图片区域占位 - 为三层图片结构预留空间 */}
        <div className="h-72 flex-shrink-0"></div>

        {/* 文本内容区域 - 使用正常的flex布局，填充剩余空间 */}
        <div className="book-card-text-content flex-grow">
          <h3 className="font-bold mb-2 text-gray-900 line-clamp-2">
            {title}
          </h3>
          <p className="text-sm text-gray-600 mb-2 line-clamp-1">
            {displayAuthor}
          </p>
          <p className="text-sm text-gray-500 mb-4 flex-grow line-clamp-3">
            {displayDescription}
          </p>
          <Link
            className="text-green-500 font-semibold text-sm hover:text-green-600 mt-auto transition-colors"
            href={bookHref}
          >
            Read summary →
          </Link>
        </div>
      </div>
    </div>
  )
}
