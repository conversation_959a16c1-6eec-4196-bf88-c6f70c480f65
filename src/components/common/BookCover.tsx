'use client'

import { useState } from 'react'
import Image from 'next/image'

interface BookCoverProps {
  coverUrl?: string | null
  title: string
  width?: number
  height?: number
  className?: string
  style?: React.CSSProperties
  priority?: boolean
  fallbackColor?: string // 自定义占位图颜色
  enableColorExtraction?: boolean // 是否启用颜色提取
  onColorExtracted?: (color: string | null) => void // 颜色提取回调
}

/**
 * 通用书籍封面组件
 * 处理图片加载失败，自动显示默认占位图
 */
export default function BookCover({
  coverUrl,
  title,
  width = 176,
  height = 256,
  className = '',
  style,
  priority = false,
  fallbackColor = 'cce4cc',
  enableColorExtraction = false,
  onColorExtracted
}: BookCoverProps) {
  // 生成初始图片源（带智能缓存控制）
  const generateInitialSrc = (): string => {
    // 处理相对路径，确保以 / 开头
    if (coverUrl) {
      if (coverUrl.startsWith('http://') || coverUrl.startsWith('https://')) {
        // 只对CDN图片添加构建时间版本号，解决缓存问题
        if (coverUrl.includes('cdn.15minutes.ai')) {
          try {
            const url = new URL(coverUrl);
            url.searchParams.set('v', process.env.NEXT_BUILD_TIME || '');
            return url.toString();
          } catch {
            // 如果URL解析失败，返回原始URL
            return coverUrl;
          }
        }
        return coverUrl;
      } else if (!coverUrl.startsWith('/')) {
        return `/${coverUrl}`; // 添加前导斜杠
      }
      return coverUrl;
    }

    // 优先使用本地默认图片
    return '/images/book-templates/book-cover.png';
  };

  // 使用稳定的初始值避免水合不匹配
  const initialSrc = generateInitialSrc();
  const [imgSrc, setImgSrc] = useState<string>(initialSrc);
  const [isError, setIsError] = useState(false);

  // 默认占位图URL
  const defaultPlaceholder = `https://placehold.co/${width}x${height}/cce4cc/333333?text=Default%20Book`

  // 处理图片加载错误
  const handleError = () => {
    if (!isError) {
      setIsError(true)

      // 如果是相对路径且不以斜杠开头，尝试添加斜杠后重试
      if (imgSrc && !imgSrc.startsWith('http') && !imgSrc.startsWith('/') && !imgSrc.startsWith('https://placehold.co')) {
        const correctedPath = `/${imgSrc}`;
        console.log(`Image load failed, trying with corrected path: ${correctedPath}`);
        setImgSrc(correctedPath);
        return;
      }

      // 如果是本地默认图片失败，尝试动态占位图
      if (imgSrc === '/images/book-templates/book-cover.png') {
        console.log(`Default book cover failed, using dynamic placeholder`);
        setImgSrc(`https://placehold.co/${width}x${height}/${fallbackColor}/333333?text=${encodeURIComponent(title.substring(0, 20))}`);
        return;
      }

      // 最终fallback：使用静态占位图
      console.log(`Image load failed for: ${imgSrc}, using final placeholder`);
      setImgSrc(defaultPlaceholder);
    }
  }

  // 添加加载状态
  const [isLoading, setIsLoading] = useState(true);

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);

    // 如果启用了颜色提取，调用回调函数
    // 注意：这里只是为了类型兼容性，实际的颜色提取逻辑在 DynamicBookCard 中实现
    if (enableColorExtraction && onColorExtracted) {
      // 简单的占位实现，实际颜色提取在父组件中处理
      onColorExtracted(null);
    }
  };

  return (
    <div className={`relative ${className}`} style={{ width, height, ...style }}>
      {/* 加载状态指示器 */}
      {isLoading && (
        <div
          className="absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse"
          style={{ borderRadius: '0.125rem' }}
        >
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}

      <Image
        src={imgSrc}
        alt={`${title} cover`}
        fill
        sizes={`(max-width: 768px) 100vw, ${width}px`}
        style={{
          objectFit: 'cover',
          opacity: isLoading ? 0 : 1,
          transition: 'opacity 0.2s ease-in-out'
        }}
        onError={handleError}
        onLoad={handleLoad}
        priority={priority}
        className="rounded-sm"
        unoptimized={imgSrc.startsWith('/')} // 只对本地图片禁用优化，CDN图片继续享受Next.js优化
      />
    </div>
  )
}
