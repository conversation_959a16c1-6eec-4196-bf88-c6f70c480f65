'use client'

import { useState } from 'react'
import Image from 'next/image'

interface AuthorAvatarProps {
  avatarUrl?: string | null
  authorName: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | number
  className?: string
  style?: React.CSSProperties
  priority?: boolean
  showLoadingState?: boolean
}

/**
 * 统一的作者头像组件
 * 处理图片加载失败，自动显示默认头像
 * 保持与现有样式完全一致
 */
export default function AuthorAvatar({
  avatarUrl,
  authorName,
  size = 'md',
  className = '',
  style,
  priority = false,
  showLoadingState = true
}: AuthorAvatarProps) {
  // 尺寸映射
  const getSizeInPixels = (): number => {
    if (typeof size === 'number') return size
    
    switch (size) {
      case 'sm': return 32
      case 'md': return 48
      case 'lg': return 100
      case 'xl': return 128
      default: return 48
    }
  }

  const sizeInPixels = getSizeInPixels()

  // 生成初始图片源（带智能缓存控制）
  const generateInitialSrc = (): string => {
    if (avatarUrl) {
      if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
        // 只对CDN图片添加构建时间版本号，解决缓存问题
        if (avatarUrl.includes('cdn.15minutes.ai')) {
          try {
            const url = new URL(avatarUrl);
            url.searchParams.set('v', process.env.NEXT_BUILD_TIME || '');
            return url.toString();
          } catch {
            return avatarUrl;
          }
        }
        return avatarUrl;
      } else if (!avatarUrl.startsWith('/')) {
        return `/${avatarUrl}`; // 添加前导斜杠
      }
      return avatarUrl;
    }

    // 优先使用本地默认头像
    return '/images/default-author-avatar.svg';
  };

  const initialSrc = generateInitialSrc();
  const [imgSrc, setImgSrc] = useState<string>(initialSrc);
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 处理图片加载错误
  const handleError = () => {
    if (!isError) {
      setIsError(true)

      // 如果是相对路径且不以斜杠开头，尝试添加斜杠后重试
      if (imgSrc && !imgSrc.startsWith('http') && !imgSrc.startsWith('/') && !imgSrc.includes('default-author-avatar')) {
        const correctedPath = `/${imgSrc}`;
        console.log(`Author avatar load failed, trying with corrected path: ${correctedPath}`);
        setImgSrc(correctedPath);
        return;
      }

      // 最终fallback：使用默认头像
      console.log(`Author avatar load failed for: ${imgSrc}, using default avatar`);
      setImgSrc('/images/default-author-avatar.svg');
    }
  }

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div 
      className={`rounded-full overflow-hidden flex-shrink-0 ${className}`} 
      style={{ 
        width: sizeInPixels, 
        height: sizeInPixels, 
        ...style 
      }}
    >
      {/* 加载状态指示器 */}
      {isLoading && showLoadingState && (
        <div
          className="absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse rounded-full"
          style={{ width: sizeInPixels, height: sizeInPixels }}
        >
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}

      <Image
        className="object-cover"
        src={imgSrc}
        alt={`${authorName} Avatar`}
        width={sizeInPixels}
        height={sizeInPixels}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: isLoading && showLoadingState ? 0 : 1,
          transition: 'opacity 0.2s ease-in-out'
        }}
        onError={handleError}
        onLoad={handleLoad}
        priority={priority}
        unoptimized={imgSrc.startsWith('/')} // 只对本地图片禁用优化
      />
    </div>
  )
}
