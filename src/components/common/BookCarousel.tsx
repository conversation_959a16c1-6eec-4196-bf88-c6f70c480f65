'use client'

import React, { useRef, useState, useEffect } from 'react'
import BookCarouselCard from './BookCarouselCard'

interface Book {
  coverImage: string
  title: string
  author: string
  description: string
}

interface BookCarouselProps {
  books?: Book[]
  children?: React.ReactNode
  containerRef?: string
  leftBtnClassName?: string
  rightBtnClassName?: string
  itemsPerRow?: {
    sm?: number;  // 小屏幕每行显示的项目数
    md?: number;  // 中等屏幕每行显示的项目数
    lg?: number;  // 大屏幕每行显示的项目数
  }
}
// 通用轮播组件
export default function BookCarousel({
  books,
  children,
  leftBtnClassName,
  rightBtnClassName,
  // containerRef 参数未使用
  // containerRef = 'scrollContainer',
  itemsPerRow = { sm: 2, md: 3, lg: 4 }
}: BookCarouselProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAtStart, setIsAtStart] = useState(true)
  const [isAtEnd, setIsAtEnd] = useState(false)
  const [itemWidth, setItemWidth] = useState(0)
  const [visibleItems, setVisibleItems] = useState(4)

  // 计算总项目数
  const totalItems = children
    ? React.Children.count(children)
    : books ? books.length : 0

  useEffect(() => {
    const updateItemWidthAndVisibleItems = () => {
      if (scrollContainerRef.current && scrollContainerRef.current.children.length > 0) {
        const item = scrollContainerRef.current.children[0] as HTMLElement
        const style = window.getComputedStyle(item)
        const marginRight = parseFloat(style.marginRight) || 0
        setItemWidth(item.offsetWidth + marginRight)
      }

      if (window.innerWidth < 640) {
        setVisibleItems(1)
      } else if (window.innerWidth < 768) {
        setVisibleItems(itemsPerRow.sm || 2)
      } else if (window.innerWidth < 1024) {
        setVisibleItems(itemsPerRow.md || 3)
      } else {
        setVisibleItems(itemsPerRow.lg || 4)
      }
    }

    const checkScrollPosition = () => {
      setIsAtStart(currentIndex === 0)
      setIsAtEnd(currentIndex >= totalItems - visibleItems)

      // Safety check for small number of items
      if (totalItems <= visibleItems) {
        setIsAtStart(true)
        setIsAtEnd(true)
      }
    }

    updateItemWidthAndVisibleItems()
    checkScrollPosition()

    window.addEventListener('resize', () => {
      updateItemWidthAndVisibleItems()
      checkScrollPosition()
    })

    return () => {
      window.removeEventListener('resize', updateItemWidthAndVisibleItems)
    }
  }, [currentIndex, totalItems, visibleItems, itemsPerRow])

  const scrollNext = () => {
    if (currentIndex < totalItems - visibleItems) {
      setCurrentIndex(prev => prev + 1)
    }
  }

  const scrollPrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1)
    }
  }

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        left: currentIndex * itemWidth,
        behavior: 'smooth'
      })
    }
  }, [currentIndex, itemWidth])

  return (
    <div className="relative">
      {/* Carousel Items */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto scroll-smooth snap-x snap-mandatory scrollbar-hide pb-4 -mx-2"
        style={{
          paddingLeft: 'calc(0.5rem + (100% - 100vw + 1rem) / 2)',
          paddingRight: 'calc(0.5rem + (100% - 100vw + 1rem) / 2)'
        }}
      >
        {children ? (
          // 如果提供了children，直接使用
          React.Children.map(children, (child, index) => {
            // 根据itemsPerRow计算响应式宽度类
            const smWidth = itemsPerRow.sm ? `sm:w-1/${itemsPerRow.sm}` : 'sm:w-1/2';
            const mdWidth = itemsPerRow.md ? `md:w-1/${itemsPerRow.md}` : 'md:w-1/3';
            const lgWidth = itemsPerRow.lg ? `lg:w-1/${itemsPerRow.lg}` : 'lg:w-1/4';

            return (
              <div
                key={index}
                className={`snap-start shrink-0 w-full ${smWidth} ${mdWidth} ${lgWidth} px-2`}
                style={{ flex: '0 0 auto' }}
              >
                {child}
              </div>
            );
          })
        ) : (
          // 否则使用books渲染BookCard（向后兼容）
          books?.map((book, index) => (
            <BookCarouselCard
              key={index}
              coverImage={book.coverImage}
              title={book.title}
              author={book.author}
              description={book.description}
            />
          ))
        )}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={scrollPrev}
        disabled={isAtStart}
        className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-white/70 hover:bg-white rounded-full shadow-md disabled:opacity-50 disabled:cursor-not-allowed transform -translate-x-1/2 md:-translate-x-full ${leftBtnClassName}`}
      >
        <svg className="h-6 w-6 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button
        onClick={scrollNext}
        disabled={isAtEnd}
        className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-white/70 hover:bg-white rounded-full shadow-md disabled:opacity-50 disabled:cursor-not-allowed transform translate-x-1/2 md:translate-x-full ${rightBtnClassName}`}
      >
        <svg className="h-6 w-6 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  )
}
