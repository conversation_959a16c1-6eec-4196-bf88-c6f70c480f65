'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const Pagination = (props: PaginationProps) => {
  const { currentPage, totalPages, onPageChange } = props;
  const [inputPage, setInputPage] = useState('');
  const [inputPosition, setInputPosition] = useState<'start' | 'end' | null>(null);

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const handlePrevClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // 只允许输入数字
    if (/^\d*$/.test(value)) {
      setInputPage(value);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const page = parseInt(inputPage, 10);
      if (!isNaN(page) && page >= 1 && page <= totalPages) {
        onPageChange(page);
        setInputPosition(null);
      }
    } else if (e.key === 'Escape') {
      setInputPosition(null);
    }
  };

  const handleInputBlur = () => {
    setInputPosition(null);
  };

  // 生成要显示的页码
  const getPageNumbers = () => {
    // 如果总页数小于等于5，显示所有页码
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // 否则，显示当前页附近的页码和首尾页
    const pageNumbers = [];

    // 始终显示第一页
    pageNumbers.push(1);

    // 当前页附近的页码
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // 如果当前页靠近开始，多显示几个后面的页码
    if (currentPage <= 3) {
      endPage = Math.min(totalPages - 1, 4);
    }

    // 如果当前页靠近结束，多显示几个前面的页码
    if (currentPage >= totalPages - 2) {
      startPage = Math.max(2, totalPages - 3);
    }

    // 添加省略号
    if (startPage > 2) {
      pageNumbers.push('ellipsis-start');
    }

    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // 添加省略号
    if (endPage < totalPages - 1) {
      pageNumbers.push('ellipsis-end');
    }

    // 始终显示最后一页
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="flex justify-center">
      <nav className="inline-flex rounded-md shadow-sm">
        <motion.a
          className="px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
          href="#"
          onClick={handlePrevClick}
          whileHover={{
            backgroundColor: 'rgb(249, 250, 251)',
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.95 }}
          animate={{ opacity: currentPage > 1 ? 1 : 0.6 }}
        >
          Previous
        </motion.a>

        {pageNumbers.map((page, index) => {
          // 处理省略号和输入框
          if (page === 'ellipsis-start' || page === 'ellipsis-end') {
            const isActive =
              (page === 'ellipsis-start' && inputPosition === 'start') ||
              (page === 'ellipsis-end' && inputPosition === 'end');

            return (
              <div
                key={page}
                className="relative inline-flex items-center border-t border-b border-gray-300 bg-white"
                style={{ minWidth: '40px', height: '38px' }}
              >
                <AnimatePresence initial={false} mode="wait">
                  {isActive ? (
                    <motion.div
                      key="input"
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.2 }}
                      className="px-1 flex items-center justify-center"
                    >
                      <input
                        id={`page-jump-input-${page}`}
                        type="text"
                        value={inputPage}
                        onChange={handleInputChange}
                        onKeyDown={handleInputKeyDown}
                        onBlur={handleInputBlur}
                        className="w-12 h-8 px-2 text-center border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder={currentPage.toString()}
                        autoFocus
                      />
                    </motion.div>
                  ) : (
                    <motion.a
                      key="ellipsis"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="px-3 py-2 flex items-center justify-center h-full text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        setInputPosition(page === 'ellipsis-start' ? 'start' : 'end');
                        setInputPage('');
                      }}
                    >
                      ...
                    </motion.a>
                  )}
                </AnimatePresence>
              </div>
            );
          }

          // 处理普通页码
          return (
            <motion.a
              key={page}
              className={`
                px-3 py-2
                ${index === 0 ? 'border' : 'border-t border-b'}
                ${index === pageNumbers.length - 1 && index !== 0 ? 'border-r' : ''}
                border-gray-300
                ${currentPage === page
                  ? 'bg-green-50 text-green-600 hover:bg-green-100'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
                }
                text-sm font-medium
              `}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handlePageClick(Number(page));
              }}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
            >
              {page}
            </motion.a>
          );
        })}

        <motion.a
          className="px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
          href="#"
          onClick={handleNextClick}
          whileHover={{
            backgroundColor: 'rgb(249, 250, 251)',
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.95 }}
          animate={{ opacity: currentPage < totalPages ? 1 : 0.6 }}
        >
          Next
        </motion.a>
      </nav>
    </div>
  );
};

export default Pagination;
