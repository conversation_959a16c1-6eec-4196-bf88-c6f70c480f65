'use client'
import React from 'react'
import PaymentButton from '@/components/PaymentButton'
import { User } from '@/store/userStore'
import { PricePlan } from '@/services/server/payService'

/**
 * 价格卡片组件属性
 */
interface PricingCardProps {
  /** 价格计划数据 */
  plan: PricePlan
  /** 用户信息 */
  user: User | null
  /** 显示模式：页面模式或弹窗模式 */
  mode?: 'page' | 'modal'
  /** 支付成功回调 */
  onPaymentSuccess?: () => void
  /** 自定义样式类名 */
  className?: string
}

/**
 * 可复用的价格卡片组件
 * 
 * 从 PricingSectionClient 中抽离出来，支持在不同场景中使用：
 * - 首页定价区域
 * - 价格弹窗
 * - 独立定价页面
 * 
 * 保持完全一致的视觉设计和交互逻辑
 */
export default function PricingCard({
  plan,
  user,
  mode = 'page',
  onPaymentSuccess,
  className = ''
}: PricingCardProps) {
  // 根据计划类型确定是否为年度计划
  const isYearly = plan.duration === '1 years'

  // 计算月度价格（年度计划显示平均月价）
  const monthlyPrice = isYearly ? (plan.price / 12).toFixed(2) : plan.price.toFixed(2)

  // 根据模式调整样式
  const cardClasses = mode === 'modal'
    ? 'w-full' // 弹窗模式：全宽
    : 'w-full lg:w-1/3' // 页面模式：响应式宽度

  return (
    <div className={`p-4 ${cardClasses} ${className}`}>
      <div className={`p-6 ${isYearly ? 'bg-green-500' : 'bg-gray-50'}`}>
        {/* 计划标题 */}
        <p className={`text-center text-xl font-bold mb-4 ${isYearly ? 'text-green-300' : 'text-gray-900'
          }`}>
          {isYearly ? 'Yearly' : 'Monthly'}
        </p>

        {/* 价格显示 */}
        <div className={`font-heading text-center text-5xl font-bold mb-1 ${isYearly ? 'text-white' : 'text-green-500'
          }`}>
          ${monthlyPrice}
          {isYearly && <span className="text-3xl font-normal">/mo</span>}
        </div>

        {/* 年度计划显示总价 */}
        {isYearly && (
          <p className="text-center text-green-200 text-sm mb-4">
            Billed annually (${plan.price}/year)
          </p>
        )}

        {/* 功能列表 */}
        <ul className="mb-6">
          <li className="flex gap-2 items-center mb-3">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 11.5L11 14.5L17 8.5"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
            <p className={isYearly ? 'text-gray-50' : 'text-gray-500'}>
              10,000+ book summaries
            </p>
          </li>
          <li className="flex gap-2 items-center mb-3">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 11.5L11 14.5L17 8.5"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
            <p className={isYearly ? 'text-gray-50' : 'text-gray-500'}>
              PDF & EPUB downloads
            </p>
          </li>
          <li className="flex gap-2 items-center mb-3">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 11.5L11 14.5L17 8.5"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
            <p className={isYearly ? 'text-gray-50' : 'text-gray-500'}>
              Read online anytime
            </p>
          </li>
          <li className="flex gap-2 items-center mb-3">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 11.5L11 14.5L17 8.5"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
            <p className={isYearly ? 'text-gray-50' : 'text-gray-500'}>
              Listen to audiobook summaries
            </p>
          </li>
          <li className="flex gap-2 items-center">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 11.5L11 14.5L17 8.5"
                  stroke={isYearly ? "#99C0B9" : "#006251"}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
            <p className={isYearly ? 'text-gray-50' : 'text-gray-500'}>
              Premium Support
            </p>
          </li>
        </ul>

        {/* 支付按钮 */}
        <PaymentButton
          planId={plan.id}
          user={user}
          className="w-full"
          onSuccess={onPaymentSuccess}
        >
          <span className={`px-6 py-3 block text-center w-full sm:w-auto text-sm font-bold transition duration-200 ${isYearly
              ? 'bg-white text-gray-900 hover:bg-gray-100 focus:ring focus:ring-gray-200'
              : 'bg-green-500 text-white hover:bg-green-600 focus:ring focus:ring-green-300'
            }`}>
            Start Now
          </span>
        </PaymentButton>
      </div>
    </div>
  )
}
