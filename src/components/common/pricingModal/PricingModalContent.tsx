'use client'
import React from 'react'

import { User } from '@/store/userStore'
import { PricePlan } from '@/services/server/payService'
import PricingCard from './PricingCard'

/**
 * 价格弹窗内容组件属性
 */
interface PricingModalContentProps {
  /** 价格计划数据 */
  plans: PricePlan[]
  /** 用户信息 */
  user: User | null
  /** 支付成功回调 */
  onPaymentSuccess?: () => void
}

/**
 * 价格弹窗内容组件
 *
 * 负责渲染弹窗内部的所有内容，包括：
 * - 标题和描述
 * - 价格卡片展示
 * - 响应式布局
 */
export default function PricingModalContent({
  plans,
  user,
  onPaymentSuccess
}: PricingModalContentProps) {

  // 找到年度和月度计划
  const yearlyPlan = plans?.find(plan => plan.duration === '1 years')
  const monthlyPlan = plans?.find(plan => plan.duration === '1 months')

  // 如果没有找到计划，显示错误信息
  if (!yearlyPlan && !monthlyPlan) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No pricing plans available</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      {/* 标题区域 */}
      <div className="text-center mb-8">
        <h2 className="font-heading text-3xl font-bold mb-4">
          Choose Your Plan
        </h2>
      </div>

      {/* 价格卡片区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
        {/* 年度计划 */}
        {yearlyPlan && (
          <PricingCard
            plan={yearlyPlan}
            user={user}
            mode="modal"
            onPaymentSuccess={onPaymentSuccess}
          />
        )}

        {/* 月度计划 */}
        {monthlyPlan && (
          <PricingCard
            plan={monthlyPlan}
            user={user}
            mode="modal"
            onPaymentSuccess={onPaymentSuccess}
          />
        )}
      </div>
    </div>
  )
}
