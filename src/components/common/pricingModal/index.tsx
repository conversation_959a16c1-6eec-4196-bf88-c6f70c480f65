'use client'
import React from 'react'
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { usePricingModalStore } from '@/store/pricingModalStore'
import PricingModalContent from './PricingModalContent'

/**
 * 全局价格弹窗组件
 * 
 * 参考 AuthModal 的实现模式，提供全局的价格弹窗功能
 * 支持在任何页面触发，展示定价计划并处理支付流程
 */
export function PricingModal() {
  // 获取价格弹窗状态
  const { isOpen, plans, user, close, onSuccess, setOnSuccess } = usePricingModalStore()

  /**
   * 处理支付成功回调
   */
  const handlePaymentSuccess = () => {
    // 关闭弹窗
    close()
    // 执行成功回调
    onSuccess?.()
    // 清除成功回调（防止多次调用）
    setOnSuccess(undefined)
  }

  // 如果没有数据，不渲染弹窗
  if (!plans) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && close()}>
      <DialogTitle className="sr-only">Choose Your Plan</DialogTitle>
      <DialogDescription className="sr-only">
        Upgrade to unlock premium features and get access to book summaries
      </DialogDescription>
      <DialogContent className="max-w-5xl w-[95vw] max-h-[90vh] overflow-y-auto p-6 sm:p-8">
        <PricingModalContent
          plans={plans}
          user={user}
          onPaymentSuccess={handlePaymentSuccess}
        />
      </DialogContent>
    </Dialog>
  )
}
