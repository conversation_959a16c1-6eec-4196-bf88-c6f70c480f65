import React from 'react';
import { Link } from 'next-view-transitions';

interface BrowseDropdownProps {
  categories: { name: string; href: string }[];
}

const BrowseDropdown: React.FC<BrowseDropdownProps> = ({ categories }) => {
  // 如果没有分类数据，不显示 Browse 按钮
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="relative group">
      <button className="text-gray-600 hover:text-green-500 flex items-center">
        Browse
        <svg
          className="h-4 w-4 ml-1 transition-transform duration-200 group-hover:rotate-180"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>

      {/* 添加一个不可见的桥接元素，连接按钮和下拉菜单 */}
      <div className="absolute left-0 w-full h-2 -bottom-2"></div>

      <div
        className="absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md invisible opacity-0 group-hover:visible group-hover:opacity-100 z-50 transition-all duration-300 ease-in-out transform origin-top scale-95 group-hover:scale-100"
        style={{ maxHeight: '750px' }}
      >
        <div className="p-4 max-h-135 overflow-y-auto">
          <div className="grid grid-cols-1 gap-2">
            {categories.map((category, index) => (
              <Link
                key={`${category.name}-${index}`}
                className="text-gray-600 hover:text-green-500 text-sm py-1 px-2 rounded hover:bg-gray-50 transition-colors duration-150"
                href={category.href}
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrowseDropdown;
