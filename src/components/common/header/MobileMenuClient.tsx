'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuthModal } from '@/store/authModalStore';

// 移动菜单组件
function MobileMenu({
  isOpen,
  onClose,
  categories = []
}: {
  isOpen: boolean;
  onClose: () => void;
  categories?: { name: string; href: string }[];
}) {
  const [browseOpen, setBrowseOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  const { openLogin, openRegister } = useAuthModal();

  // 处理菜单项点击
  const handleMenuItemClick = () => {
    onClose();
  };

  // 处理登录按钮点击
  const handleLoginClick = () => {
    openLogin();
    onClose();
  };

  // 处理注册按钮点击
  const handleRegisterClick = () => {
    openRegister();
    onClose();
  };

  return (
    <div
      className={`md:hidden fixed top-[60px] left-0 right-0 bg-white z-50 shadow-lg transition-all duration-300 ${isOpen ? 'open' : 'closed'}`}
      style={{
        transition: 'all 0.3s ease',
        opacity: isOpen ? 1 : 0,
        transform: isOpen ? 'translateY(0)' : 'translateY(-1rem)',
        visibility: isOpen ? 'visible' : 'hidden',
        maxHeight: isOpen ? '80vh' : '0',
        overflowY: 'auto'
      }}
    >
      <div className="py-4 px-4 space-y-4 border-t border-gray-200">
        <Link className="block py-2 text-gray-600 hover:text-green-500" href="/" onClick={handleMenuItemClick}>Home</Link>
        <Link className="block py-2 text-gray-600 hover:text-green-500" href="/my-library" onClick={handleMenuItemClick}>My Library</Link>

        <div>
          <button
            className="flex items-center justify-between w-full py-2 text-gray-600 hover:text-green-500"
            onClick={(e) => {
              e.stopPropagation(); // 防止冒泡触发关闭菜单
              setBrowseOpen(!browseOpen);
            }}
          >
            Browse
            <svg
              className={`h-4 w-4 ml-1 transform transition-transform duration-200 ${browseOpen ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div
            className={`mt-2 space-y-2 pl-4 transition-all duration-300 ${browseOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}
          >
            {categories.length > 0 ? (
              categories.map((category, index) => (
                <Link
                  key={`${category.name}-${index}`}
                  className="block py-1 text-gray-600 hover:text-green-500 text-sm"
                  href={category.href}
                  onClick={handleMenuItemClick}
                >
                  {category.name}
                </Link>
              ))
            ) : (
              <div className="block py-1 text-gray-400 text-sm">No categories available</div>
            )}
          </div>
        </div>

        <form
          className="relative"
          onSubmit={(e) => {
            e.preventDefault();
            if (!searchQuery.trim()) return;

            // 跳转到搜索结果页面
            router.push(`/search?query=${encodeURIComponent(searchQuery)}&page=1`);
            onClose(); // 关闭移动菜单
          }}
        >
          <input
            className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring focus:ring-green-200"
            type="text"
            placeholder="搜索书籍..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onClick={(e) => e.stopPropagation()} // 防止点击输入框时关闭菜单
          />
          <button
            type="submit"
            className="absolute right-3 top-2.5 text-gray-400 hover:text-green-500"
            onClick={(e) => e.stopPropagation()} // 防止点击按钮时关闭菜单
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>
        </form>

        <Link className="block py-2 text-gray-600 hover:text-green-500" href="/pricing" onClick={handleMenuItemClick}>Pricing</Link>

        <div className="flex flex-col gap-3 pt-2">
          <span className="cursor-pointer block py-2 text-gray-600 hover:text-green-500" onClick={handleLoginClick}>Login</span>
          <button className="block w-full px-4 py-2 bg-green-500 text-center text-white font-bold rounded-md hover:bg-green-600 transition duration-200" onClick={handleRegisterClick}>Start for Free</button>
        </div>
      </div>
    </div>
  );
}


export function MobileMenuClient({ categories = [] }: { categories?: { name: string; href: string }[] }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // 当菜单打开时，禁止背景滚动
  React.useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  // 关闭菜单的函数
  const handleCloseMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <>
      <button
        className="md:hidden flex items-center"
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        aria-label="Toggle mobile menu"
      >
        <svg className="h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M4 6h16M4 12h16M4 18h16"
            style={{ display: mobileMenuOpen ? 'none' : 'block' }}
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M6 18L18 6M6 6l12 12"
            style={{ display: mobileMenuOpen ? 'block' : 'none' }}
          />
        </svg>
      </button>

      <MobileMenu isOpen={mobileMenuOpen} onClose={handleCloseMenu} categories={categories} />
    </>
  );
}
