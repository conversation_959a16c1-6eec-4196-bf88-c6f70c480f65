'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

/**
 * 导航栏搜索表单组件
 * 用于在导航栏中搜索书籍
 */
const SearchForm = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  /**
   * 处理搜索表单提交
   */
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    // 跳转到搜索结果页面
    router.push(`/search?query=${encodeURIComponent(searchQuery)}&page=1`);
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <input
        className="px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring focus:ring-green-200 w-64"
        type="text"
        placeholder="Search books..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <button
        type="submit"
        className="absolute right-3 top-2.5 text-gray-400 hover:text-green-500"
        aria-label="search"
      >
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </button>
    </form>
  );
};

export default SearchForm;
