import { Link } from "next-view-transitions"

interface BookCardProps {
  coverImage: string
  title: string
  author: string
  description: string
}
// 书籍卡片
export default function BookCarouselCard({ coverImage, title, author, description }: BookCardProps) {
  return (
    <div className="snap-start shrink-0 w-full sm:w-1/2 md:w-1/3 lg:w-1/4 px-2" style={{ flex: '0 0 auto' }}>
      <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col">
        <div className="p-4 flex justify-center">
          <img className="max-w-[176px] max-h-[256px]" src={coverImage} alt="Book cover" />
        </div>
        <div className="p-4 flex flex-col flex-grow">
          <h3 className="font-bold mb-2">{title}</h3>
          <p className="text-sm text-gray-600 mb-2">{author}</p>
          <p className="text-sm text-gray-500 mb-4 flex-grow">{description}</p>
          <Link className="text-green-500 font-semibold text-sm hover:text-green-600 mt-auto" href={`/book-summary/${title}`}>
            Read summary →
          </Link>
        </div>
      </div>
    </div>
  )
}
