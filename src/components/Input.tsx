import { FC } from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

export interface InputProps {
  type?: string
  placeholder: string
  className?: string
  error?: string
  register: UseFormRegisterReturn
  label?: string
  required?: boolean
}

const Input: FC<InputProps> = ({
  type,
  placeholder,
  className,
  error,
  register,
  label,
  required
}) => {
  return (
    <div className="mb-2">
      {label && (
        <label className="block mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        className={`w-full py-2 px-3 border outline-hidden transition-all focus:border-primary focus:ring-primary shadow-none rounded-lg transition-border ${className} ${error ? 'border-red-500' : ''}`}
        {...register}
      />
      {error && <p className="text-red-500">{error}</p>}
    </div>
  )
}

export default Input
