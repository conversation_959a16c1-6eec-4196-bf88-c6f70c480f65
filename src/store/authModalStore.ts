import { create } from 'zustand'

// 认证弹窗类型：登录或注册
type AuthModalType = 'login' | 'register' | null

// 认证弹窗状态存储接口
interface AuthModalStore {
  // 状态
  isOpen: boolean          // 弹窗是否打开
  type: AuthModalType      // 弹窗类型：登录/注册
  onSuccess?: () => void   // 成功回调函数
  
  // 操作方法
  openLogin: (onSuccess?: () => void) => void    // 打开登录弹窗
  openRegister: (onSuccess?: () => void) => void // 打开注册弹窗
  close: () => void                              // 关闭弹窗
  setOnSuccess: (callback?: () => void) => void  // 设置成功回调
}

// 创建认证弹窗状态管理器
export const useAuthModal = create<AuthModalStore>((set) => ({
  // 初始状态
  isOpen: false,
  type: null,
  onSuccess: undefined,
  
  // 打开登录弹窗
  openLogin: (onSuccess?: () => void) => set({ 
    isOpen: true, 
    type: 'login', 
    onSuccess 
  }),
  
  // 打开注册弹窗
  openRegister: (onSuccess?: () => void) => set({ 
    isOpen: true, 
    type: 'register', 
    onSuccess 
  }),
  
  // 关闭弹窗
  close: () => set({ 
    isOpen: false 
  }),
  
  // 设置成功回调
  setOnSuccess: (callback?: () => void) => set({ 
    onSuccess: callback 
  })
}))
