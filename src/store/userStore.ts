import { create } from 'zustand'

export interface User {
  id: string
  email: string
  username: string
  avatar: string | null
  /** Optional */
  account?: string
  language?: string
  company?: string
  country?: string
  province?: string
  city?: string
  postal?: string
  address?: string
  phone?: string
  vat?: string
  distribution_code?: string
  created_at?: number
  vip_expired_at?: number
  delete_task_plan_executed_at?: number | null
  last_rank_name?: string | null
  rank_name?: string
}

export interface Rank {
  rank_name: 'Free'
  permissions: []
}

interface UserStore {
  user: User | null
  setUser: (user: User) => void
  rank: Rank | null
  setRank: (rank: Rank) => void
}

export const useUserStore = create<UserStore>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  rank: null,
  setRank: (rank) => set({ rank })
}))

export interface PermissionQuota {
  limit: number
  remaining: number
  reset_at: number
  permission_name: string
  used: number
}
