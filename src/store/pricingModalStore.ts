import { create } from 'zustand'
import { User } from '@/store/userStore'
import { PricePlan } from '@/services/server/payService'

/**
 * 价格弹窗状态存储接口
 */
interface PricingModalStore {
  // 状态
  isOpen: boolean                    // 弹窗是否打开
  plans: PricePlan[] | null         // 价格计划数据
  user: User | null                 // 用户信息
  onSuccess?: () => void            // 支付成功回调函数
  
  // 操作方法
  open: (plans: PricePlan[], user: User | null, onSuccess?: () => void) => void    // 打开价格弹窗
  close: () => void                                                                // 关闭弹窗
  setOnSuccess: (callback?: () => void) => void                                   // 设置成功回调
}

/**
 * 创建价格弹窗状态管理器
 * 
 * 参考 AuthModal 的实现模式，提供统一的状态管理接口
 * 支持在任何页面触发价格弹窗，并处理支付成功回调
 */
export const usePricingModalStore = create<PricingModalStore>((set) => ({
  // 初始状态
  isOpen: false,
  plans: null,
  user: null,
  onSuccess: undefined,
  
  // 打开价格弹窗
  open: (plans: PricePlan[], user: User | null, onSuccess?: () => void) => set({ 
    isOpen: true, 
    plans,
    user,
    onSuccess 
  }),
  
  // 关闭弹窗
  close: () => set({ 
    isOpen: false,
    // 保留数据，避免关闭时闪烁
    // plans: null,
    // user: null,
    // onSuccess: undefined
  }),
  
  // 设置成功回调
  setOnSuccess: (callback?: () => void) => set({ 
    onSuccess: callback 
  })
}))

/**
 * 便捷的价格弹窗Hook
 * 
 * 提供简化的API来操作价格弹窗
 */
export function usePricingModal() {
  const { open, close, setOnSuccess } = usePricingModalStore()
  
  return {
    open,
    close,
    setOnSuccess
  }
}
