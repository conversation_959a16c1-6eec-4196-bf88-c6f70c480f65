import { create } from 'zustand'

interface TranslationStore {
  serviceRef: React.RefObject<HTMLButtonElement> | null
  styleRef: React.RefObject<HTMLButtonElement> | null
  setServiceRef: (ref: React.RefObject<HTMLButtonElement>) => void
  setStyleRef: (ref: React.RefObject<HTMLButtonElement>) => void
  scrollAndOpenService: () => void
  scrollAndOpenStyle: () => void
  scroll: () => void
}

export const useTranslationStore = create<TranslationStore>((set, get) => ({
  serviceRef: null,
  styleRef: null,
  setServiceRef: (ref) => set({ serviceRef: ref }),
  setStyleRef: (ref) => set({ styleRef: ref }),
  scrollAndOpenService: () => {
    const { serviceRef } = get()
    window.scrollTo({ top: 0, behavior: 'smooth' })
    setTimeout(() => {
      serviceRef?.current?.click()
    }, 500)
  },
  scrollAndOpenStyle: () => {
    const { styleRef } = get()
    window.scrollTo({ top: 0, behavior: 'smooth' })
    setTimeout(() => {
      styleRef?.current?.click()
    }, 500)
  },
  scroll: () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}))
