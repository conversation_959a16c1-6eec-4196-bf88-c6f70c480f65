'use client'
import { useEffect, useRef } from 'react'
import confetti from 'canvas-confetti'

/**
 * 支付成功特效配置选项
 */
export interface UsePaymentSuccessOptions {
  /** 是否启用特效，默认为 true */
  enabled?: boolean
  /** 延迟触发时间（毫秒），默认为 800ms */
  delay?: number
  /** 是否为移动端，会自动检测，也可手动指定 */
  isMobile?: boolean
}

/**
 * 支付成功特效Hook返回值
 */
export interface UsePaymentSuccessReturn {
  /** 手动触发特效的函数 */
  triggerSuccessConfetti: () => void
}

/**
 * 支付成功庆祝特效Hook
 * 使用 canvas-confetti 库实现支付成功的视觉反馈
 * 
 * @param shouldTrigger 是否应该触发特效的条件
 * @param options 特效配置选项
 * @returns 手动触发特效的函数
 */
export function usePaymentSuccess(
  shouldTrigger: boolean,
  options: UsePaymentSuccessOptions = {}
): UsePaymentSuccessReturn {
  const hasTriggeredConfetti = useRef(false)
  
  const {
    enabled = true,
    delay = 800,
    isMobile = typeof window !== 'undefined' ? window.innerWidth < 640 : false
  } = options

  /**
   * 触发支付成功特效
   */
  const triggerSuccessConfetti = () => {
    try {
      // 检查用户是否偏好减少动画
      if (typeof window !== 'undefined') {
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
        if (prefersReducedMotion) return
      }

      // 成功主题的绿色系配色
      const successColors = ['#10B981', '#059669', '#34D399', '#6EE7B7']

      // 根据屏幕尺寸调整特效强度
      const particleCount = isMobile ? 60 : 100
      const spread = isMobile ? 50 : 70

      // 经典彩带效果
      confetti({
        particleCount,
        spread,
        origin: { y: 0.4 }, // 从屏幕中上部开始
        colors: successColors,
        scalar: isMobile ? 0.8 : 1,
        gravity: 0.6,
        drift: 0.1,
        startVelocity: 30,
        ticks: 200
      })

      // 延迟第二波特效，增加层次感
      setTimeout(() => {
        confetti({
          particleCount: isMobile ? 30 : 50,
          spread: isMobile ? 30 : 50,
          origin: { y: 0.5 }, // 从屏幕中部开始
          colors: successColors,
          scalar: isMobile ? 0.6 : 0.8,
          gravity: 0.8,
          startVelocity: 20,
          ticks: 150
        })
      }, 300)
    } catch (error) {
      console.warn('Confetti effect failed:', error)
      // 静默失败，不影响核心功能
    }
  }

  // 自动触发特效
  useEffect(() => {
    if (!enabled || !shouldTrigger || hasTriggeredConfetti.current) {
      return
    }

    // 延迟触发，让用户先看到成功状态
    const timer = setTimeout(() => {
      triggerSuccessConfetti()
      hasTriggeredConfetti.current = true
    }, delay)

    return () => clearTimeout(timer)
  }, [shouldTrigger, enabled, delay])

  // 重置触发状态（当 shouldTrigger 变为 false 时）
  useEffect(() => {
    if (!shouldTrigger) {
      hasTriggeredConfetti.current = false
    }
  }, [shouldTrigger])

  return {
    triggerSuccessConfetti
  }
}
