'use client'

/**
 * 收藏功能自定义Hook
 * 封装所有收藏相关的状态管理和业务逻辑
 *
 * 采用无状态认证检查模式，与 RatingSystem 和 Listen 按钮保持一致
 * 每次操作都实时检查认证状态，避免状态同步问题
 */

import { useState, useEffect, useCallback } from 'react'
import { getFavoriteStatus, toggleFavorite } from '@/services/actions/favorites.action'
import { useToast } from '@/hooks/useToast'
import { useAuthModal } from '@/store/authModalStore'
import { checkAuthStatus } from '@/lib/auth/server-actions'

// 收藏按钮状态类型
interface FavoriteButtonState {
  variant: 'add' | 'remove' | 'loading'
  disabled: boolean
  text: string
}

// Hook返回类型
interface UseFavoriteReturn {
  // 状态
  isFavorited: boolean
  isLoading: boolean
  isInitialLoading: boolean

  // 操作
  toggleFavorite: () => Promise<void>

  // 计算属性
  buttonState: FavoriteButtonState
}

/**
 * 收藏功能Hook
 * @param bookId 书籍ID
 * @returns 收藏状态和操作函数
 */
export function useFavorite(bookId: number): UseFavoriteReturn {
  // 基础状态（移除认证状态管理，采用实时检查）
  const [isFavorited, setIsFavorited] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // 全局Toast和登录弹窗
  const { showToast } = useToast()
  const { openLogin, setOnSuccess } = useAuthModal()

  // 初始化获取收藏状态（只获取收藏状态，不管理认证状态）
  const loadFavoriteStatus = useCallback(async () => {
    try {
      setIsInitialLoading(true)
      const result = await getFavoriteStatus(bookId)

      if (result.success) {
        setIsFavorited(result.isFavorited)
        // 移除认证状态设置，采用实时检查模式
      }
    } catch (error) {
      console.error('Failed to load favorite status:', error)
      // 初始加载失败时不显示错误，使用默认状态
    } finally {
      setIsInitialLoading(false)
    }
  }, [bookId])

  // 组件挂载时加载收藏状态
  useEffect(() => {
    loadFavoriteStatus()
  }, [loadFavoriteStatus])

  // 切换收藏状态（采用无状态认证检查，类似 RatingSystem 模式）
  const handleToggleFavorite = useCallback(async () => {
    if (isLoading) return

    // 实时检查认证状态（不依赖组件状态）
    const isLoggedIn = await checkAuthStatus()

    if (!isLoggedIn) {
      // 设置登录成功后的回调
      setOnSuccess(() => {
        // 登录成功后重新调用收藏函数（递归调用，重新检查认证）
        handleToggleFavorite()
      })
      // 打开登录弹窗
      openLogin()
      return
    }

    // 已认证，执行收藏操作
    try {
      setIsLoading(true)

      // 乐观更新
      const optimisticState = !isFavorited
      setIsFavorited(optimisticState)

      // 调用Server Action
      const result = await toggleFavorite(bookId)

      if (result.success) {
        // 服务器确认，保持乐观更新的状态
        setIsFavorited(result.isFavorited)

        // 操作成功后重新加载状态，确保数据同步
        await loadFavoriteStatus()
      } else {
        // 操作失败，回滚状态
        setIsFavorited(!optimisticState)

        // 显示错误Toast
        const errorMessage = getErrorMessage(result.error)
        showToast(errorMessage, 'error')
      }
    } catch (error) {
      // 网络错误，回滚状态
      setIsFavorited(!isFavorited)

      // 显示错误Toast
      showToast('Network connection failed. Please try again later', 'error')
      console.error('Network error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [bookId, isFavorited, isLoading, showToast, openLogin, setOnSuccess, loadFavoriteStatus])

  // 错误消息映射
  const getErrorMessage = (error?: string): string => {
    if (!error) return 'The operation failed. Please try again later'

    if (error.includes('Authentication') || error.includes('认证')) {
      return 'Please login first'
    }
    if (error.includes('Network') || error.includes('网络')) {
      return 'Network connection failed. Please try again later'
    }
    if (error.includes('Server') || error.includes('服务器')) {
      return 'Internal server error. Please try again later'
    }

    return 'The operation failed. Please try again later'
  }

  // 计算按钮状态
  const buttonState: FavoriteButtonState = {
    variant: isLoading ? 'loading' : isFavorited ? 'remove' : 'add',
    disabled: isLoading || isInitialLoading,
    text: isLoading
      ? '处理中...'
      : isFavorited
        ? 'Saved to Favorites'
        : 'Add to Favorites'
  }

  return {
    isFavorited,
    isLoading,
    isInitialLoading,
    toggleFavorite: handleToggleFavorite,
    buttonState
  }
}
