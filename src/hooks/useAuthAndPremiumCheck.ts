'use client'

/**
 * 认证和付费验证自定义Hook
 *
 * 提供可复用的登录检查和付费权限验证逻辑
 * 适用于需要付费权限的功能（如听书、下载等）
 *
 * 重构后的简化版本：只负责状态检查，不执行业务逻辑
 */

import { useCallback } from 'react'
import { checkAuthStatus, getFullUserInfo } from '@/lib/auth/server-actions'
import { useAuthModal } from '@/store/authModalStore'
import { usePricingModal } from '@/hooks/usePricingModal'
import { User } from '@/store/userStore'

/**
 * Hook配置选项
 */
interface UseAuthAndPremiumCheckOptions {
  /** 功能名称，用于日志记录 */
  featureName?: string
}

/**
 * Hook返回类型
 */
interface UseAuthAndPremiumCheckReturn {
  /** 检查认证和付费状态的函数 */
  checkAuthAndPremium: () => Promise<boolean>
  /** 仅检查登录状态的函数 */
  checkAuthOnly: () => Promise<boolean>
  /** 仅检查付费状态的函数（假设已登录） */
  checkPremiumOnly: () => Promise<boolean>
  /** 付费状态检查工具函数 */
  hasActivePremium: (user: User) => boolean
}

/**
 * 认证和付费验证Hook
 *
 * @param options Hook配置选项
 * @returns 验证函数和工具函数
 */
export function useAuthAndPremiumCheck(
  options: UseAuthAndPremiumCheckOptions = {}
): UseAuthAndPremiumCheckReturn {
  const {
    featureName = 'premium feature'
  } = options

  // 获取全局弹窗控制
  const { openLogin } = useAuthModal()
  const { openPricingModalWithData } = usePricingModal()

  /**
   * 检查用户是否有有效的付费套餐
   *
   * @param user 用户信息
   * @returns boolean 是否有付费权限
   */
  const hasActivePremium = useCallback((user: User): boolean => {
    // 检查用户等级 - 非 Free 用户视为付费用户
    if (user.rank_name && user.rank_name !== 'Free') {
      return true
    }

    return false
  }, [])

  /**
   * 仅检查登录状态
   * 如果未登录，显示登录弹窗并返回false
   *
   * @returns Promise<boolean> 是否已登录
   */
  const checkAuthOnly = useCallback(async (): Promise<boolean> => {
    try {
      const isLoggedIn = await checkAuthStatus()

      if (!isLoggedIn) {
        console.log(`${featureName}: User not logged in, showing login modal`)
        openLogin()
        return false
      }

      console.log(`${featureName}: User is logged in`)
      return true
    } catch (error) {
      console.error(`${featureName}: Authentication check failed:`, error)
      return false
    }
  }, [featureName, openLogin])

  /**
   * 仅检查付费状态（假设用户已登录）
   * 如果未付费，显示价格弹窗并返回false
   *
   * @returns Promise<boolean> 是否有付费权限
   */
  const checkPremiumOnly = useCallback(async (): Promise<boolean> => {
    try {
      // 获取完整的用户信息
      const currentUser = await getFullUserInfo()
      console.log(`${featureName}: Retrieved user info for premium check:`, currentUser)

      if (!currentUser || !hasActivePremium(currentUser)) {
        console.log(`${featureName}: User has no premium access, showing pricing modal`)

        // 用户没有付费权限，显示价格弹窗（不传回调函数）
        await openPricingModalWithData()
        return false
      }

      console.log(`${featureName}: User has premium access`)
      return true
    } catch (error) {
      console.error(`${featureName}: Failed to check premium status:`, error)
      return false
    }
  }, [featureName, hasActivePremium, openPricingModalWithData])

  /**
   * 检查认证和付费状态
   * 按顺序检查登录状态和付费状态，任一失败都会显示相应弹窗并返回false
   *
   * @returns Promise<boolean> 是否通过所有检查
   */
  const checkAuthAndPremium = useCallback(async (): Promise<boolean> => {
    // 第一步：检查登录状态
    const isLoggedIn = await checkAuthOnly()
    if (!isLoggedIn) {
      return false
    }

    // 第二步：检查付费状态
    const hasPremium = await checkPremiumOnly()
    if (!hasPremium) {
      return false
    }

    console.log(`${featureName}: All checks passed`)
    return true
  }, [featureName, checkAuthOnly, checkPremiumOnly])

  return {
    checkAuthAndPremium,
    checkAuthOnly,
    checkPremiumOnly,
    hasActivePremium
  }
}
