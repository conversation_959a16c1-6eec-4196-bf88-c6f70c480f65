'use client'
import { useState } from 'react'
import { useRouter } from '@/i18n/routing'
import { useLocale } from 'next-intl'
import { subscribe, openStripePortal } from '@/services/client/payService'
import { useToast } from '@/hooks/useToast'
import { User } from '@/store/userStore'

/**
 * 支付配置选项
 */
export interface UsePaymentOptions {
  /** 支付成功后的重定向URL，默认为当前域名下的 /{locale}/pay-success */
  successUrl?: string
  /** 支付取消后的重定向URL，默认为当前页面 */
  cancelUrl?: string
  /** 支付成功回调 */
  onSuccess?: () => void
  /** 支付错误回调 */
  onError?: (error: string) => void
}

/**
 * 支付Hook返回值
 */
export interface UsePaymentReturn {
  /** 是否正在处理支付 */
  isLoading: boolean
  /** 处理支付的函数 */
  handlePayment: (planId: number, user: User | null) => Promise<void>
}

/**
 * 可复用的支付逻辑Hook
 *
 * @param options 支付配置选项
 * @returns 支付状态和处理函数
 */
export function usePayment(options: UsePaymentOptions = {}): UsePaymentReturn {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { showToast } = useToast()
  const locale = useLocale()

  const {
    successUrl = `${typeof window !== 'undefined' ? window.location.origin : ''}/${locale}/pay-success`,
    cancelUrl = typeof window !== 'undefined' ? window.location.href : '',
    onSuccess,
    onError
  } = options

  /**
   * 处理支付流程
   *
   * @param planId 支付计划ID
   * @param user 用户信息
   */
  const handlePayment = async (planId: number, user: User | null): Promise<void> => {
    // 防止重复点击
    if (isLoading) return

    // 检查用户登录状态
    if (!user) {
      router.push(`/${locale}/login?from=pricing`)
      return
    }

    setIsLoading(true)

    try {
      const response = await subscribe({
        planId: planId,
        successUrl,
        cancelUrl
      })

      if (response?.data?.stripe) {
        // 支付成功，重定向到Stripe支付页面
        window.location.href = response.data.stripe.url
        onSuccess?.()
      } else if (
        response?.code === 400015 &&
        response?.message?.includes('You need to unsubscribe before purchasing.')
      ) {
        // 用户需要先取消现有订阅
        showToast('Redirecting to subscription management...', 'normal')

        // 尝试打开Stripe门户
        const success = await openStripePortal()

        if (!success) {
          const errorMsg = 'Please unsubscribe before purchasing a new plan.'
          showToast(errorMsg, 'error')
          onError?.(errorMsg)
        }
      } else if (
        response?.code === 400 &&
        response?.message?.includes('No valid payment method types for this Checkout Session')
      ) {
        const errorMsg = 'Payment method is not available.'
        showToast(errorMsg, 'error')
        onError?.(errorMsg)
      } else {
        const errorMsg = response?.message || 'Payment failed.'
        showToast(errorMsg, 'error')
        onError?.(errorMsg)
      }
    } catch (error) {
      console.error('Payment error:', error)
      const errorMsg = 'Payment processing error.'
      showToast(errorMsg, 'error')
      onError?.(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    handlePayment
  }
}
