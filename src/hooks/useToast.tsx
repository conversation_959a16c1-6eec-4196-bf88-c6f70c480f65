'use client'
import { useCallback } from 'react'
import { create } from 'zustand'

interface Toast {
  message: string
  type: 'normal' | 'success' | 'error' | 'warning'
}

interface ToastStore {
  toasts: Toast[]
  addToast: (
    message: string,
    type: 'normal' | 'success' | 'error' | 'warning',
    duration?: number
  ) => void
  removeToast: (toast: Toast) => void
}

const useToastStore = create<ToastStore>((set) => ({
  toasts: [],
  addToast: (message, type, duration = 2000) => {
    const newToast = { message, type }
    set((state) => ({ toasts: [...state.toasts, newToast] }))
    setTimeout(() => {
      set((state) => ({
        toasts: state.toasts.filter((toast) => toast !== newToast)
      }))
    }, duration)
  },
  removeToast: (toast) => {
    set((state) => ({
      toasts: state.toasts.filter((t) => t !== toast)
    }))
  }
}))

export const useToast = () => {
  const { toasts, addToast, removeToast } = useToastStore()

  const showToast = useCallback(
    (message: string, type: 'normal' | 'success' | 'error' | 'warning', duration?: number) => {
      addToast(message, type, duration)
    },
    [addToast]
  )

  return {
    toasts,
    showToast,
    removeToast
  }
}
