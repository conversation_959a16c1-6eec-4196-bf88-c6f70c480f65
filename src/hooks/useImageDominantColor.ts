'use client'

import { useState, useEffect, useCallback } from 'react'

interface UseImageDominantColorResult {
  dominantColor: string | null
  isLoading: boolean
  error: string | null
}

/**
 * Hook for extracting dominant color from an image URL
 * @param imageUrl - The image URL to extract color from
 * @returns Object containing dominantColor, isLoading, and error states
 */
export function useImageDominantColor(imageUrl: string | null | undefined): UseImageDominantColorResult {
  const [dominantColor, setDominantColor] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const extractColor = useCallback(async (url: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          
          if (!ctx) {
            setError('Canvas context not available')
            setIsLoading(false)
            return
          }
          
          // 缩放到小尺寸提高性能
          const targetSize = 100
          canvas.width = targetSize
          canvas.height = targetSize
          ctx.drawImage(img, 0, 0, targetSize, targetSize)
          
          const imageData = ctx.getImageData(0, 0, targetSize, targetSize)
          const data = imageData.data
          
          const colorMap = new Map<string, number>()
          
          // 采样像素（每4个像素采样一次）
          for (let i = 0; i < data.length; i += 16) { // RGBA = 4 bytes, 每4个像素 = 16 bytes
            const r = data[i]
            const g = data[i + 1]
            const b = data[i + 2]
            const a = data[i + 3]
            
            // 跳过透明像素
            if (a < 128) continue
            
            // 跳过过于接近白色的像素（通常是背景）
            if (r > 240 && g > 240 && b > 240) continue
            
            // 跳过过于接近黑色的像素（通常是文字或边框）
            if (r < 20 && g < 20 && b < 20) continue
            
            // 将相似颜色归类（减少颜色精度以合并相似色彩）
            const bucketR = Math.round(r / 15) * 15
            const bucketG = Math.round(g / 15) * 15
            const bucketB = Math.round(b / 15) * 15
            
            const colorKey = `${bucketR},${bucketG},${bucketB}`
            colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1)
          }
          
          // 找到出现频率最高的颜色
          let bestColor = ''
          let maxCount = 0
          
          for (const [color, count] of colorMap.entries()) {
            if (count > maxCount) {
              maxCount = count
              bestColor = color
            }
          }
          
          if (bestColor) {
            const [r, g, b] = bestColor.split(',').map(Number)
            // 返回rgb格式，透明度将在CSS中处理
            setDominantColor(`rgb(${r}, ${g}, ${b})`)
          } else {
            setDominantColor(null)
          }
          
          setIsLoading(false)
        } catch (err) {
          console.warn('Color extraction failed:', err)
          setError('Color extraction failed')
          setDominantColor(null)
          setIsLoading(false)
        }
      }
      
      img.onerror = () => {
        setError('Image load failed')
        setDominantColor(null)
        setIsLoading(false)
      }
      
      img.src = url
    } catch (err) {
      setError('Unexpected error')
      setDominantColor(null)
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    if (imageUrl) {
      extractColor(imageUrl)
    } else {
      setDominantColor(null)
      setIsLoading(false)
      setError(null)
    }
  }, [imageUrl, extractColor])

  return { dominantColor, isLoading, error }
}
