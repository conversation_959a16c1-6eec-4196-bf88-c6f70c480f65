import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

type UseCopyToClipboardProps = {
  text: string
  copyMessage?: string
}

export function useCopyToClipboard({
  text,
  copyMessage = 'Copied to clipboard!'
}: UseCopyToClipboardProps) {
  const [isCopied, setIsCopied] = useState(false)

  useEffect(() => {
    if (isCopied) {
      const timer = setTimeout(() => {
        setIsCopied(false)
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [isCopied])

  const handleCopy = useCallback(() => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success(copyMessage)
        setIsCopied(true)
      })
      .catch(() => {
        toast.error('Failed to copy to clipboard.')
      })
  }, [text, copyMessage])

  return { isCopied, handleCopy }
}
