/**
 * 音频播放进度管理Hook
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { PlaybackProgressData, PlaybackProgressResponse } from '@/types/audio-progress.types';

interface UseAudioProgressOptions {
  bookId: number;
  autoSave?: boolean;
  saveInterval?: number; // 保存间隔(毫秒)
}

export function useAudioProgress({
  bookId,
  autoSave = true,
  saveInterval = 3000 // 默认3秒保存一次
}: UseAudioProgressOptions) {
  const [progress, setProgress] = useState<PlaybackProgressResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 新增：播放状态管理
  const [isPlaying, setIsPlaying] = useState(false);

  // 新增：定时器和状态管理
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isAutoSaveActiveRef = useRef(false);

  /**
   * 保存播放进度
   */
  const saveProgress = useCallback(async (progressData: PlaybackProgressData) => {
    try {
      const response = await fetch('/api/audio/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(progressData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const text = await response.text();
      if (!text) {
        throw new Error('Empty response from server');
      }

      const data = JSON.parse(text);

      if (data.success) {
        setProgress(data.data);
        return data.data;
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (err) {
      setError('Failed to save progress');
      throw err;
    }
  }, [setProgress, setError]); // 修复：添加状态更新函数依赖

  /**
   * 带重试机制的保存函数
   */
  const saveProgressWithRetry = useCallback(async (
    progressData: PlaybackProgressData,
    retryCount: number = 0
  ): Promise<void> => {
    const maxRetries = 3;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // 指数退避，最大5秒

    try {
      await saveProgress(progressData);
    } catch (error) {
      console.error(`❌ [AudioProgress] Save failed (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries) {
        console.log(`🔄 [AudioProgress] Retrying in ${retryDelay}ms`);

        return new Promise((resolve, reject) => {
          setTimeout(async () => {
            try {
              await saveProgressWithRetry(progressData, retryCount + 1);
              resolve();
            } catch (retryError) {
              reject(retryError);
            }
          }, retryDelay);
        });
      } else {
        console.error('💥 [AudioProgress] Max retries exceeded');
        throw error;
      }
    }
  }, [saveProgress]);

  // 使用useRef存储最新的播放进度，避免状态更新导致重新渲染
  const latestProgressRef = useRef<PlaybackProgressData | null>(null);
  const lastSaveTimeRef = useRef<number>(0);

  // 防重复保存机制
  const lastSaveRequestRef = useRef<Promise<any> | null>(null);
  const isSavingRef = useRef(false);

  /**
   * 递归调度保存函数
   */
  const scheduleNextSave = useCallback(async () => {
    // 清除可能存在的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
      saveTimeoutRef.current = null;
    }

    // 检查是否应该继续自动保存
    if (!autoSave || !isAutoSaveActiveRef.current) {
      return;
    }

    // 设置下一次保存的定时器
    saveTimeoutRef.current = setTimeout(async () => {
      try {
        // 检查是否有数据需要保存
        if (latestProgressRef.current) {
          const now = Date.now();
          const timeSinceLastSave = now - lastSaveTimeRef.current;

          // 确保最小间隔
          if (timeSinceLastSave >= 1000) {
            // 执行保存请求
            await saveProgressWithRetry(latestProgressRef.current);
            lastSaveTimeRef.current = now;

          } else {
            console.warn('⏸️ [AudioProgress] Skipping save - too soon since last save');
          }
        }

        // 请求完成后，递归调用安排下一次保存
        scheduleNextSave();

      } catch (error) {
        console.error('❌ [AudioProgress] Save failed:', error);

        // 即使失败也要继续调度下一次保存
        scheduleNextSave();
      }
    }, saveInterval);

  }, [autoSave, saveInterval, saveProgressWithRetry]);

  /**
   * 启动自动保存
   */
  const startAutoSave = useCallback(() => {
    if (isAutoSaveActiveRef.current) {
      return;
    }

    isAutoSaveActiveRef.current = true;
    scheduleNextSave();
  }, [scheduleNextSave]);

  /**
   * 停止自动保存
   */
  const stopAutoSave = useCallback(() => {
    isAutoSaveActiveRef.current = false;

    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
      saveTimeoutRef.current = null;
    }
  }, []);

  /**
   * 播放状态更新（带防重复保存保护）
   */
  const updatePlayingState = useCallback((playing: boolean) => {
    setIsPlaying(playing);

    if (playing) {
      // 开始播放时启动自动保存
      startAutoSave();
    } else {
      // 暂停时停止自动保存，但立即保存当前进度
      stopAutoSave();

      // 防重复保存：如果正在保存或刚刚保存过，跳过
      if (isSavingRef.current || !latestProgressRef.current) {
        return;
      }

      const now = Date.now();
      const timeSinceLastSave = now - lastSaveTimeRef.current;

      // 如果距离上次保存不到1秒，跳过
      if (timeSinceLastSave < 1000) {
        console.log(`⏸️ [AudioProgress] Save skipped - too soon since last save (${timeSinceLastSave}ms)`);
        return;
      }

      isSavingRef.current = true;

      const savePromise = saveProgressWithRetry(latestProgressRef.current)
        .then(() => {
          lastSaveTimeRef.current = now;
        })
        .catch(error => {
          console.error('❌ [AudioProgress] Pause save failed:', error);
        })
        .finally(() => {
          isSavingRef.current = false;
          lastSaveRequestRef.current = null;
        });

      lastSaveRequestRef.current = savePromise;
    }
  }, [startAutoSave, stopAutoSave, saveProgressWithRetry]);

  /**
   * 获取播放进度
   */
  const fetchProgress = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/audio/progress/${bookId}`, {
        credentials: 'include'
      });

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 检查响应是否有内容
      const text = await response.text();
      if (!text) {
        console.log('[AudioProgress] No progress found for book:', bookId);
        setProgress(null);
        return;
      }

      // 解析JSON
      const data = JSON.parse(text);

      if (data.success) {
        setProgress(data.data);
        console.log('[AudioProgress] Progress fetched:', data.data);
      } else {
        setError(data.error || 'Unknown error');
      }
    } catch (err) {
      setError('Failed to fetch progress');
      console.error('[AudioProgress] Fetch progress error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bookId, setProgress, setError, setIsLoading]);



  /**
   * 更新播放进度（带自动保存）
   */
  const updateProgress = useCallback((
    currentTime: number,
    duration: number,
    playbackRate?: number,
    volume?: number,
    isCurrentlyPlaying?: boolean // 新增可选参数
  ) => {
    const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

    const progressData: PlaybackProgressData = {
      bookId,
      currentTime,
      duration,
      progressPercentage,
      playbackRate,
      volume
    };

    // 更新播放状态（如果提供）
    if (typeof isCurrentlyPlaying === 'boolean') {
      updatePlayingState(isCurrentlyPlaying);
    }

    // 只在进度变化超过0.5%或时间间隔超过1秒时才更新本地状态，避免频繁重新渲染
    const shouldUpdateState = !progress ||
      Math.abs(progressPercentage - (progress.progressPercentage || 0)) > 0.5 ||
      Math.abs(currentTime - (progress.currentTime || 0)) > 1;

    if (shouldUpdateState) {
      setProgress(prev => prev ? {
        ...prev,
        currentTime,
        duration,
        progressPercentage,
        playbackRate: playbackRate ?? prev.playbackRate,
        volume: volume ?? prev.volume
      } : null);
    } else {
    }

    // 存储最新进度到ref（调度器会处理保存）
    latestProgressRef.current = progressData;

    return progressData;
  }, [bookId, updatePlayingState, setProgress, progress]);

  /**
   * 标记为完成
   */
  const markAsCompleted = useCallback(async () => {
    try {
      const response = await fetch(`/api/audio/progress/${bookId}/complete`, {
        method: 'POST',
        credentials: 'include'
      });

      const data = await response.json();

      if (data.success) {
        setProgress(data.data);
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      setError('Failed to mark as completed');
      throw err;
    }
  }, [bookId, setProgress, setError]);



  // 组件挂载时获取进度
  useEffect(() => {
    fetchProgress();
  }, [fetchProgress]);

  // 生命周期管理
  useEffect(() => {

    return () => {
      stopAutoSave();

      // 如果有正在进行的保存请求，等待完成
      if (lastSaveRequestRef.current) {
        console.log('⏳ [AudioProgress] Waiting for ongoing save to complete');
        lastSaveRequestRef.current.finally(() => {
          console.log('✅ [AudioProgress] Ongoing save completed during cleanup');
        });
      }

      // 如果有未保存的数据且没有正在保存，尝试最后一次保存
      if (latestProgressRef.current && !isSavingRef.current && !isAutoSaveActiveRef.current) {
        saveProgressWithRetry(latestProgressRef.current).catch(error => {
          console.error('❌ [AudioProgress] Final save failed:', error);
        });
      }
    };
  }, [stopAutoSave, saveProgressWithRetry]);

  // 监听 autoSave 配置变化
  useEffect(() => {
    if (autoSave && isPlaying) {
      startAutoSave();
    } else {
      stopAutoSave();
    }
  }, [autoSave, isPlaying, startAutoSave, stopAutoSave]);

  return {
    progress,
    isLoading,
    error,
    isPlaying,
    isAutoSaveActive: isAutoSaveActiveRef.current,
    fetchProgress,
    saveProgress,
    updateProgress,
    updatePlayingState,
    startAutoSave,
    stopAutoSave,
    markAsCompleted
  };
}
