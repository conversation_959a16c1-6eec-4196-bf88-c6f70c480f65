'use client'
import { useRouter } from '@/i18n/routing'
import { useLocale } from 'next-intl'
import { usePricingModalStore } from '@/store/pricingModalStore'
import { useToast } from '@/hooks/useToast'

/**
 * 便捷的价格弹窗Hook
 *
 * 提供简化的API来操作价格弹窗，自动处理数据获取和错误处理
 * 支持降级策略：如果数据加载失败，自动跳转到定价页面
 */
export function usePricingModal() {
  const { open, close, setOnSuccess } = usePricingModalStore()
  const router = useRouter()
  const locale = useLocale()
  const { showToast } = useToast()

  /**
   * 打开价格弹窗
   *
   * 如果没有提供数据，则降级到定价页面
   *
   * @param plans 价格计划数据（可选）
   * @param user 用户信息（可选）
   * @param onSuccess 支付成功后的回调函数
   */
  const openPricingModal = (plans?: any[], user?: any, onSuccess?: () => void) => {
    // 如果没有提供数据，降级到定价页面
    if (!plans || plans.length === 0) {
      showToast('Redirecting to pricing page...', 'normal')
      router.push(`/${locale}/pricing`)
      return
    }

    // 打开弹窗
    open(plans, user || null, onSuccess)
  }

  /**
   * 关闭价格弹窗
   */
  const closePricingModal = () => {
    close()
  }

  /**
   * 设置支付成功回调
   *
   * @param callback 回调函数
   */
  const setPricingModalOnSuccess = (callback?: () => void) => {
    setOnSuccess(callback)
  }

  /**
   * 自动获取数据并打开价格弹窗
   *
   * 通过 API 路由获取价格数据和用户信息，然后打开弹窗
   * 如果数据获取失败，降级到定价页面
   *
   * @param onSuccess 支付成功后的回调函数
   */
  const openPricingModalWithData = async (onSuccess?: () => void) => {
    try {
      // 通过 fetch 获取价格数据（需要创建对应的 API 路由）
      const response = await fetch('/api/pricing-data')
      if (!response.ok) {
        throw new Error('Failed to fetch pricing data')
      }

      const data = await response.json()
      const { plans, user } = data

      // 检查是否获取到有效的价格数据
      if (!plans || plans.length === 0) {
        throw new Error('No pricing plans available')
      }

      // 打开弹窗
      open(plans, user, onSuccess)
    } catch (error) {
      console.error('Failed to load pricing data:', error)

      // 显示错误提示
      showToast('Loading pricing data failed, redirecting to pricing page...', 'error')

      // 降级处理：跳转到定价页面
      router.push(`/${locale}/pricing`)
    }
  }

  return {
    openPricingModal,
    openPricingModalWithData,
    closePricingModal,
    setPricingModalOnSuccess
  }
}
