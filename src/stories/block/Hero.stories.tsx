import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Hero } from '../../components/Block/Hero'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import Image from 'next/image'

// 模拟 useRouter hook
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

// 模拟翻译数据
const messages = {
  Hero: {
    title: '打造你的\nAI 视频创作平台',
    subtitle: '使用 AI 技术，轻松创建专业级视频内容。无需专业技能，让创意自由流动。',
    startInstall: '立即开始',
    productDescription: '已有超过 10,000+ 创作者在使用我们的产品'
  }
}

// 功能展示组件
const feature1 = (
  <div>
    <Image src="/avatar/avatar1.jpg" alt="Hero" width={500} height={500} className="rounded-full" />
  </div>
)

const feature2 = (
  <div>
    <Image src="/avatar/avatar2.jpg" alt="Hero" width={500} height={500} className="rounded-2xl" />
  </div>
)

const feature3 = (
  <div>
    <Image src="/avatar/avatar3.jpg" alt="Hero" width={500} height={500} className="rounded-2xl" />
  </div>
)

// 默认参数
const defaultArgs = {
  title: '打造你的\nAI 视频创作平台',
  subtitle: '使用 AI 技术，轻松创建专业级视频内容。无需专业技能，让创意自由流动。',
  productDescription: '已有超过 10,000+ 创作者在使用我们的产品',
  linkText: '立即开始',
  linkHref: 'https://www.google.com',
  linkSize: 'md' as const,
  feature: feature1
}

const meta = {
  title: 'Block/Hero',
  component: Hero,
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <Story />
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ],
  argTypes: {
    linkSize: {
      control: 'select',
      options: ['sm', 'md', 'lg']
    },
    feature: {
      control: 'select',
      options: ['feature1', 'feature2', 'feature3'],
      mapping: {
        feature1,
        feature2,
        feature3
      }
    }
  }
} satisfies Meta<typeof Hero>

export default meta
type Story = StoryObj<typeof meta>

// 基础 Hero 组件
export const Default: Story = {
  args: defaultArgs
}

// 大尺寸按钮
export const LargeButton: Story = {
  args: {
    ...defaultArgs,
    linkSize: 'lg'
  }
}

// 小尺寸按钮
export const SmallButton: Story = {
  args: {
    ...defaultArgs,
    linkSize: 'sm'
  }
}

// 自定义功能区域
export const CustomFeature: Story = {
  args: {
    ...defaultArgs,
    feature: (
      <div className="w-full h-64 bg-primary/10 rounded-lg flex items-center justify-center">
        <span className="text-primary font-bold">自定义功能展示</span>
      </div>
    )
  }
}

// 暗色主题 Hero 组件
export const DarkTheme: Story = {
  args: defaultArgs,
  decorators: [
    (Story) => (
      <div className="dark bg-neutral-900">
        <Story />
      </div>
    )
  ]
}

// 响应式布局
export const Responsive: Story = {
  args: defaultArgs,
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
