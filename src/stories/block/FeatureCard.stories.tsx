import type { Meta, StoryObj } from '@storybook/react'
import FeatureCard from '@/components/FeatureCard'

const meta: Meta<typeof FeatureCard> = {
  title: 'Block/FeatureCard',
  component: FeatureCard,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    layout: {
      control: 'radio',
      options: ['vertical', 'horizontal']
    }
  },
  decorators: [
    (Story) => (
      <div className="max-w-sm">
        <Story />
      </div>
    )
  ]
}

export default meta
type Story = StoryObj<typeof FeatureCard>

const VideoIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    role="img"
    className="text-white"
    width="46"
    height="46"
    viewBox="0 0 24 24"
  >
    <path
      fill="currentColor"
      d="M8.001 4h8v1.997h2V4A2 2 0 0 1 20 6v12a2 2 0 0 1-1.999 2v-2.003h-2V20h-8v-2.003h-2V20H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h.001v1.997h2zM10 15l4.5-3L10 9zm8.001.997v-3h-2v3zm0-5v-3h-2v3zm-10 5v-3h-2v3zm0-5v-3h-2v3z"
    ></path>
  </svg>
)

const CloudIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    role="img"
    className="text-white"
    width="46"
    height="46"
    viewBox="0 0 24 24"
  >
    <path
      fill="currentColor"
      d="M19.35 10.04A7.49 7.49 0 0 0 12 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 0 0 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5c0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5l5 5h-3z"
    ></path>
  </svg>
)

export const Default: Story = {
  args: {
    title: 'Create Videos with No Skills Required',
    description:
      'Bring your ideas to life with the AI video creation tools. Quickly generate professional videos for social media, presentations, or personal projects.',
    icon: <VideoIcon />,
    layout: 'vertical'
  }
}

export const Horizontal: Story = {
  args: {
    title: 'Social Media Content',
    description:
      'Create engaging short videos for platforms like TikTok, designed to drive interaction with your audience.',
    icon: <VideoIcon />,
    layout: 'horizontal'
  }
}

export const CloudFeature: Story = {
  args: {
    title: 'Cloud Storage & Sync',
    description:
      'Access your files from anywhere with our secure cloud storage. Automatic syncing ensures your work is always up to date.',
    icon: <CloudIcon />,
    layout: 'vertical'
  }
}

export const CloudFeatureHorizontal: Story = {
  args: {
    title: 'Cloud Storage & Sync',
    description:
      'Access your files from anywhere with our secure cloud storage. Automatic syncing ensures your work is always up to date.',
    icon: <CloudIcon />,
    layout: 'horizontal'
  }
}

export const WithBackground: Story = {
  decorators: [
    (Story) => (
      <div className="max-w-sm p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <Story />
      </div>
    )
  ]
}

export const InGrid: Story = {
  decorators: [
    (Story) => (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl p-8">
        <Story />
        <Story />
        <Story />
      </div>
    )
  ]
}

export const DarkTheme: Story = {
  parameters: {
    themes: {
      defaultTheme: 'dark'
    }
  }
}

export const Responsive: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  },
  decorators: [
    (Story) => (
      <div className="w-full p-4">
        <Story />
      </div>
    )
  ]
}
