import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import ChatPanel from '../../components/Block/ChatPanel'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { Message } from '@ai-sdk/react'
import { delay, http, HttpResponse } from 'msw'
import { GoogleGenAI, Type } from '@google/genai'
import { JSONParser } from '@streamparser/json'

// 模拟 useRouter hook
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

// 模拟翻译数据
const messages = {
  Chat: {
    placeholder: '输入消息...',
    send: '发送',
    stop: '停止',
    clear: '清除对话'
  }
}

// 模拟初始消息
const initialMessages: Message[] = [
  {
    id: '1',
    role: 'assistant' as const,
    content: '你好！我是 AI 助手，有什么我可以帮你的吗？'
  }
]

// 模拟加载中的消息
const loadingMessages: Message[] = [
  ...initialMessages,
  {
    id: '2',
    role: 'user' as const,
    content: '请帮我写一个 React 组件'
  },
  {
    id: '3',
    role: 'assistant' as const,
    content: '好的，我来帮你写一个简单的 React 组件...'
  }
]

// 模拟长对话
const longConversation: Message[] = [
  ...initialMessages,
  {
    id: '2',
    role: 'user' as const,
    content: '请介绍一下 React Hooks'
  },
  {
    id: '3',
    role: 'assistant' as const,
    content:
      'React Hooks 是 React 16.8 引入的新特性，让我们可以在函数组件中使用状态和其他 React 特性。主要的 Hooks 包括：\n\n1. useState - 用于管理状态\n2. useEffect - 用于处理副作用\n3. useContext - 用于使用上下文\n4. useReducer - 用于复杂状态管理\n5. useCallback - 用于性能优化\n6. useMemo - 用于缓存计算结果\n7. useRef - 用于保存引用\n8. useLayoutEffect - 用于同步副作用'
  },
  {
    id: '4',
    role: 'user' as const,
    content: '能详细解释一下 useState 吗？'
  },
  {
    id: '5',
    role: 'assistant' as const,
    content:
      'useState 是最基础的 Hook，用于在函数组件中添加状态。它的基本用法是：\n\n```jsx\nconst [state, setState] = useState(initialState);\n```\n\n例如：\n\n```jsx\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  \n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={() => setCount(count + 1)}>\n        增加\n      </button>\n    </div>\n  );\n}\n```\n\nuseState 返回一个数组，包含：\n1. 当前状态值\n2. 更新状态的函数\n\n每次调用 setState 都会触发组件重新渲染。'
  }
]

// 模拟 API 路由
const handlers = [
  http.post('/api/chat', async ({ request }) => {
    const body = (await request.json()) as {
      messages: Message[]
      model: string
      apiKey: string
      apiUrl: string
    }

    console.log('body', body)

    const ai = new GoogleGenAI({ apiKey: 'AIzaSyBD5XN_EcJIE9db1xtjC2LCb6Uriwg9AH8' })

    // 创建流式响应
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const parser1 = new JSONParser()

          parser1.onValue = ({ value, key, parent, stack }) => {
            if (key === 'recipeName') {
              console.log('value', value)
              controller.enqueue(encoder.encode(`0:"recipeName is: ${value + '\\n'}"\n`))
            }
          }

          const originalStreamResponse = await ai.models.generateContentStream({
            model: 'gemini-2.0-flash',
            config: {
              responseMimeType: 'application/json',
              responseSchema: {
                type: Type.ARRAY,
                items: {
                  type: Type.OBJECT,
                  properties: {
                    recipeName: {
                      type: Type.STRING,
                      description: 'Name of the recipe',
                      nullable: false
                    }
                  },
                  required: ['recipeName']
                }
              }
            },
            contents: 'List a few popular cookie recipes using this JSON schema'
          })

          // 发送消息 ID
          controller.enqueue(encoder.encode(`f:{"messageId":"msg-${Date.now()}"}\n`))

          // 处理流式响应
          for await (const chunk of originalStreamResponse) {
            const text = chunk.text
            parser1.write(encoder.encode(text))
          }

          // 发送结束标记
          controller.enqueue(
            encoder.encode(
              `e:{"finishReason":"stop","usage":{"promptTokens":913,"completionTokens":10},"isContinued":false}\n`
            )
          )
          controller.enqueue(
            encoder.encode(
              `d:{"finishReason":"stop","usage":{"promptTokens":913,"completionTokens":10}}\n`
            )
          )
          controller.close()
        } catch (error) {
          console.error('Error:', error)
          controller.error(error)
        }
      }
    })

    // 模拟一个本地的JSON ReadableStream
    // const localStream = new ReadableStream({
    //   async start(localController) {
    //     try {

    //       const response = await fetch('/api/json');
    //       if (!response.ok) {
    //         throw new Error(`HTTP error! status: ${response.status}`);
    //       }

    //       const localStreamReader = response.body?.getReader();

    //       const parser = new JSONParser();

    //       parser.onValue = ({value, key, parent, stack}) => {
    //         // TODO process element
    //         // 要设置一个缓冲区，然后根据缓冲区来输出，不然会有背压

    //         switch (key) {
    //           case 'tarot_reader':
    //             // TODO:模拟打字延迟
    //             localController.enqueue(encoder.encode(`0:"你选择的塔罗师是: ${value + '\\n'}"\n`));
    //             break;
    //           case 'question_essence':
    //             localController.enqueue(encoder.encode(`0:"你提出的问题是: ${value + '\\n'}"\n`));
    //             break;
    //           case 'spread_energy':
    //             localController.enqueue(encoder.encode(`0:"所选塔罗师的性格是: ${value + '\\n'}"\n`));
    //             break;

    //           case 'position':
    //             console.log("position parent", parent,stack)
    //             localController.enqueue(encoder.encode(`0:"第一张牌的地位是: ${value + '\\n'}"\n`));
    //             break;
    //           default:
    //             break;
    //         }

    //       };

    //       while (true) {
    //         const { done, value } = await localStreamReader?.read();
    //         console.log('done', done)
    //         // 这里的value为 Unit8Array类型
    //         // console.log('value', value)
    //         if (done) {
    //           break;
    //         }
    //         parser.write(value)
    //       }

    //       // const json = await response.json();
    //       // const encoder = new TextEncoder();

    //       // // 发送开始标记
    //       // controller.enqueue(encoder.encode(JSON.stringify({ type: 'start', messageId: `msg-${Date.now()}` }) + '\n'));

    //       // // 发送内容
    //       // for (const char of JSON.stringify(json)) {
    //       //   controller.enqueue(encoder.encode(JSON.stringify({ type: 'content', text: char }) + '\n'));
    //       // }

    //       // 发送结束标记
    //       localController.enqueue(encoder.encode(JSON.stringify({
    //         type: 'end',
    //         finishReason: 'stop',
    //         usage: {
    //           promptTokens: 913,
    //           completionTokens: 10
    //         }
    //       }) + '\n'));

    //       localController.close();
    //     } catch (error) {
    //       console.error('Error:', error);
    //       localController.error(error);
    //     }
    //   },
    // });

    const response = new HttpResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive'
      }
    })

    // const reader = response.body?.getReader();
    // const decoder = new TextDecoder();

    // while (true) {
    //   const { done, value } = await reader?.read();
    //   if (done) {
    //     break;
    //   }
    //   console.log('value', decoder.decode(value, { stream: true }))
    // }

    return response
  }),
  http.get('/api/json', () => {
    const encoder = new TextEncoder()
    const localReadableStream = new ReadableStream({
      async start(jsonController) {
        jsonController.enqueue(encoder.encode('{\n'))
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '"reading_overview": {\n"tarot_reader": "艾莉",\n"question_essence": "失去宠物后的心灵慰藉与宠物在另一世界的状态",\n"spread_energy": "温暖而柔和的能量，带有一丝怀念与不舍，但整体呈现出平和与接纳"\n},\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '"card_readings": [\n{\n"position": "思想",\n"card": "太阳（正位）",\n"position_meaning": "代表你对宠物离世的思考方式和认知",\n"card_interpretation": "亲爱的，正位太阳在思想位置告诉我们，你心中已经开始接受这个事实，并试图用阳光和温暖的记忆来思考你的宠物。太阳代表纯粹的喜悦和生命力，这表明你的宠物在你心中留下了无比美好的印记。你正尝试用积极的方式记住它，相信它现在处于光明之中。这张牌也暗示着真相和清晰，你内心深处已经感受到某种答案。"\n},\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '{\n"position": "情感",\n"card": "权杖三（逆位）",\n"position_meaning": "展现你目前的情感状态和内心感受",\n"card_interpretation": "权杖三逆位显示你的情感世界正经历一段停滞期，亲爱的。这张牌在正位时代表成长和扩展，但逆位则表明你的悲伤可能让你感到无法前行。你可能感到失落，仿佛失去了生活中的一部分活力。这很自然，我们与宠物建立的情感纽带是如此特别。这张牌也暗示你可能在质疑自己是否已经尽力，或者是否有更多可以为宠物做的事。请记住，爱永远不会真正消失，它只是改变了形式。"\n},\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '{\n"position": "行为",\n"card": "圣杯女王（正位）",\n"position_meaning": "指引你如何行动及外在表现",\n"card_int'
          )
        )
        await delay(500)
        // 测试断开的键 card_interpretation => card_int + erpretation
        jsonController.enqueue(
          encoder.encode(
            'erpretation": "圣杯女王出现在行为位置，多么美丽的指引啊。这位女王代表着深刻的情感智慧、同理心和滋养。她告诉我们，你正在或将要通过关爱他人和自我疗愈来处理这份失落。也许是照顾其他动物，或者支持那些经历类似失去的人。圣杯女王鼓励你拥抱自己的感受，同时也保持心灵的开放和温柔。她提醒你，爱的能力是你最大的力量，而这份爱不会因为物理分离而减弱。"\n}\n],\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '"connections_analysis": [\n{\n"connected_cards": ["太阳（正位）", "权杖三（逆位）"],\n"relationship": "思想上的光明与情感上的停滞形成了有趣的对比。这表明虽然你理智上明白要向前看，但情感上仍需要时间来处理这份失去。太阳的能量正在努力照亮权杖三逆位的阴影，就像你的积极思考正在努力安抚内心的伤痛。"\n},\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '{\n"connected_cards": ["权杖三（逆位）", "圣杯女王（正位）"],\n"relationship": "从情感的停滞到行为上的滋养与疗愈，这个转变显示了一条美丽的成长路径。圣杯女王正在教导你如何将悲伤转化为更深层次的爱与关怀，如何从情感的困境中找到前进的方向。"\n},\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '{\n"connected_cards": ["太阳（正位）", "圣杯女王（正位）"],\n"relationship": "太阳与圣杯女王都带有强烈的正能量，它们共同指向一个充满爱与光明的未来。你的思想和行为都朝向积极的方向发展，这预示着虽然中间有悲伤的过渡，但最终你会找到平静和接纳。"\n}\n],\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '"holistic_interpretation": "亲爱的，这个牌阵向我们展示了一幅充满希望的画面。太阳在思想位置告诉我，你的宠物确实在一个充满光明和温暖的地方。就像太阳不断给予生命和能量，你的宠物现在也沐浴在纯粹的喜悦之中。虽然权杖'
          )
        )
        await delay(500)
        // 测试断开的 文字value
        jsonController.enqueue(
          encoder.encode(
            '三逆位显示你的情感世界正经历一段调整期，但这是完全自然的过程。圣杯女王的出现是最美的礼物，她提醒我们爱永远不会真正离去。你与宠物之间的连接超越了物理世界的限制，那份爱依然流动着。整体而言，牌阵传递出一个明确的信息：你的宠物确实在宠物星球上快乐地生活着，而且它希望你能够慢慢疗愈，继续用爱去滋养自己和周围的生命。",\n'
          )
        )
        await delay(500)
        jsonController.enqueue(
          encoder.encode(
            '"practical_advice": "尝试创建一个小小的纪念仪式，也许是点燃一支蜡烛，或者制作一本充满美好回忆的相册。当思念涌来时，不要抗拒这些情感，而是像圣杯女王一样温柔地接纳它们。考虑将你的爱延伸到其他需要帮助的动物身上，这不是替代，而是拓展你爱的能力。最重要的是，相信那份连接依然存在，因为真正的爱超越时间和空间，就像太阳的光芒永远不会消失。"\n'
          )
        )
        await delay(500)
        jsonController.enqueue(encoder.encode('}'))
        jsonController.close()
      }
    })
    return new HttpResponse(localReadableStream, {
      headers: { 'Content-Type': 'text/event-stream' }
    })
  })
]

const meta = {
  title: 'Block/ChatPanel',
  component: ChatPanel,
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    },
    msw: {
      handlers
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <div className="w-[800px] h-[600px]">
            <Story />
          </div>
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ]
} satisfies Meta<typeof ChatPanel>

export default meta
type Story = StoryObj<typeof meta>

// 基础聊天面板
export const Default: Story = {
  args: {
    initialMessages: []
  },
  parameters: {
    msw: {
      handlers
    }
  }
}

// 带有初始消息的聊天面板
export const WithInitialMessage: Story = {
  args: {
    initialMessages
  },
  parameters: {
    msw: {
      handlers
    }
  }
}

// 加载中的聊天面板
export const Loading: Story = {
  args: {
    initialMessages: loadingMessages
  },
  parameters: {
    msw: {
      handlers
    }
  }
}

// 长对话聊天面板
export const LongConversation: Story = {
  args: {
    initialMessages: longConversation
  },
  parameters: {
    msw: {
      handlers
    }
  }
}

// 暗色主题
export const DarkTheme: Story = {
  args: {
    initialMessages: []
  },
  parameters: {
    msw: {
      handlers
    }
  },
  decorators: [
    (Story) => (
      <div className="dark bg-neutral-900">
        <Story />
      </div>
    )
  ]
}

// 响应式布局
export const Responsive: Story = {
  args: {
    initialMessages: []
  },
  parameters: {
    msw: {
      handlers
    },
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
