import type { Meta, StoryObj } from '@storybook/react'
import { Nav } from '../../components/Block/Nav'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'

// 模拟翻译数据
const messages = {
  Header: {
    TextTranslate: '文本翻译',
    ImageTranslate: '图片翻译',
    PDFTranslate: 'PDF翻译',
    Pricing: '价格'
  }
}

// 模拟路由
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

const meta = {
  title: 'Block/Nav',
  component: Nav,
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <Story />
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ]
} satisfies Meta<typeof Nav>

export default meta
type Story = StoryObj<typeof meta>

// 默认导航
export const Default: Story = {
  render: () => (
    <div className="w-full max-w-md">
      <Nav />
    </div>
  )
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => (
    <div className="dark bg-neutral-900 p-4 w-full max-w-md">
      <Nav />
    </div>
  )
}

// 桌面端布局
export const DesktopLayout: Story = {
  render: () => (
    <div className="w-[800px]">
      <Nav />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'desktop'
    }
  }
}

// 当前页面高亮
export const ActiveLink: Story = {
  render: () => (
    <div className="w-full max-w-md">
      <Nav />
    </div>
  ),
  parameters: {
    nextjs: {
      navigation: {
        pathname: '/zh/translate/text',
        query: {}
      }
    }
  }
}
