import type { Meta, StoryObj } from '@storybook/react'
import { FAQ } from '@/components/Block/FAQ'
import { NextIntlClientProvider } from 'next-intl'

// 模拟翻译数据
const messages = {
  FAQ: {
    Home: {
      title: '常见问题'
    }
  }
}

const meta: Meta<typeof FAQ> = {
  title: 'Block/FAQ',
  component: FAQ,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="zh" messages={messages}>
        <div className="min-h-screen flex flex-col">
          <div className="w-4/5 xl:w-[1000px] mx-auto">
            <Story />
          </div>
        </div>
      </NextIntlClientProvider>
    )
  ]
}

export default meta
type Story = StoryObj<typeof FAQ>

// 模拟 FAQ 数据
const faqItems = [
  {
    id: '1',
    question: '什么是 AI 助手？',
    answer:
      'AI 助手是一个基于人工智能的智能对话系统，可以帮助用户完成各种任务，如回答问题、提供建议、生成内容等。'
  },
  {
    id: '2',
    question: '如何使用 AI 助手？',
    answer:
      '您可以通过以下方式使用 AI 助手：\n1. 在对话框中输入您的问题或需求\n2. 等待 AI 助手回复\n3. 根据需要进行进一步的对话'
  },
  {
    id: '3',
    question: 'AI 助手支持哪些功能？',
    answer:
      'AI 助手支持多种功能，包括但不限于：\n- 智能问答\n- 内容生成\n- 代码编写\n- 文本翻译\n- 数据分析'
  },
  {
    id: '4',
    question: '如何保证数据安全？',
    answer:
      '我们采用多种措施保护用户数据安全：\n1. 数据加密存储\n2. 严格的访问控制\n3. 定期安全审计\n4. 符合相关数据保护法规'
  }
]

export const VerticalLayout: Story = {
  args: {
    items: faqItems,
    layout: 'vertical',
    title: '常见问题',
    description: '了解关于 AI 助手的常见问题解答'
  }
}

export const LongContent: Story = {
  args: {
    items: [
      ...faqItems,
      {
        id: '5',
        question: 'AI 助手的技术原理是什么？',
        answer:
          'AI 助手基于大型语言模型（LLM）构建，通过以下技术实现：\n\n1. 自然语言处理（NLP）\n   - 文本理解\n   - 语义分析\n   - 上下文理解\n\n2. 机器学习\n   - 监督学习\n   - 强化学习\n   - 迁移学习\n\n3. 知识图谱\n   - 结构化知识\n   - 关系推理\n   - 知识更新\n\n4. 对话管理\n   - 上下文维护\n   - 意图识别\n   - 多轮对话'
      }
    ],
    layout: 'vertical',
    title: '技术相关问题'
  }
}
