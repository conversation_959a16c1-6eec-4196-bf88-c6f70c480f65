import { Button } from '@/components/ui/button'

export const Example1 = () => {
  return (
    <div className="flex flex-col items-center gap-8">
      <div className="text-center">
        <p className="mt-3 text-base">类型一示例</p>
      </div>
      <div className="text-center max-w-2xl">
        <h3 className="mb-8 text-2xl font-bold leading-normal tracking-tight text-gray-900 md:text-4xl dark:text-white">
          AI 纹身设计1
        </h3>
        <p className="mb-4">
          使用 AI
          技术将您的照片转换为独特的纹身设计。我们的算法可以分析照片中的元素，并生成适合纹身的艺术风格设计。
        </p>
        <Button className="mt-4">
          <span>立即尝试</span>
        </Button>
      </div>
    </div>
  )
}

export const Example2 = () => {
  return (
    <div className="flex flex-col items-center gap-8">
      <div className="text-center">
        <p className="mt-3 text-base">类型二示例</p>
      </div>
      <div className="text-center max-w-2xl">
        <h3 className="mb-8 text-2xl font-bold leading-normal tracking-tight text-gray-900 md:text-4xl dark:text-white">
          AI 纹身设计2
        </h3>
        <p className="mb-4">
          使用 AI
          技术将您的照片转换为独特的纹身设计。我们的算法可以分析照片中的元素，并生成适合纹身的艺术风格设计。
        </p>
        <Button className="mt-4">
          <span>立即尝试</span>
        </Button>
      </div>
    </div>
  )
}

export const Example3 = () => {
  return (
    <div className="flex flex-col items-center gap-8">
      <div className="text-center">
        <p className="mt-3 text-base">类型三示例</p>
      </div>
      <div className="text-center max-w-2xl">
        <h3 className="mb-8 text-2xl font-bold leading-normal tracking-tight text-gray-900 md:text-4xl dark:text-white">
          AI 纹身设计3
        </h3>
        <p className="mb-4">
          使用 AI
          技术将您的照片转换为独特的纹身设计。我们的算法可以分析照片中的元素，并生成适合纹身的艺术风格设计。
        </p>
        <Button className="mt-4">
          <span>立即尝试</span>
        </Button>
      </div>
    </div>
  )
}

export const Example4 = () => {
  return (
    <div className="flex flex-col items-center gap-8">
      <div className="text-center">
        <p className="mt-3 text-base">类型四示例</p>
      </div>
      <div className="text-center max-w-2xl">
        <h3 className="mb-8 text-2xl font-bold leading-normal tracking-tight text-gray-900 md:text-4xl dark:text-white">
          AI 纹身设计4
        </h3>
        <p className="mb-4">
          使用 AI
          技术将您的照片转换为独特的纹身设计。我们的算法可以分析照片中的元素，并生成适合纹身的艺术风格设计。
        </p>
        <Button className="mt-4">
          <span>立即尝试</span>
        </Button>
      </div>
    </div>
  )
}
