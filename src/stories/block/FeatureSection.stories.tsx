import type { Meta, StoryObj } from '@storybook/react'
import FeatureSection from '../../components/FeatureSection'

const meta: Meta<typeof FeatureSection> = {
  title: 'Block/FeatureSection',
  component: FeatureSection,
  parameters: {
    layout: 'fullscreen'
  },
  tags: ['autodocs'],
  argTypes: {
    columns: {
      control: { type: 'radio' },
      options: [2, 3, 4]
    },
    layout: {
      control: { type: 'radio' },
      options: ['vertical', 'horizontal']
    }
  }
}

export default meta
type Story = StoryObj<typeof FeatureSection>

const VideoIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    role="img"
    className="text-white"
    width="46"
    height="46"
    viewBox="0 0 24 24"
  >
    <path
      fill="currentColor"
      d="M8.001 4h8v1.997h2V4A2 2 0 0 1 20 6v12a2 2 0 0 1-1.999 2v-2.003h-2V20h-8v-2.003h-2V20H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h.001v1.997h2zM10 15l4.5-3L10 9zm8.001.997v-3h-2v3zm0-5v-3h-2v3zm-10 5v-3h-2v3zm0-5v-3h-2v3z"
    ></path>
  </svg>
)

const CloudIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    role="img"
    className="text-white"
    width="46"
    height="46"
    viewBox="0 0 24 24"
  >
    <path
      fill="currentColor"
      d="M19.35 10.04A7.49 7.49 0 0 0 12 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 0 0 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5c0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5l5 5h-3z"
    ></path>
  </svg>
)

const SecurityIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    role="img"
    className="text-white"
    width="46"
    height="46"
    viewBox="0 0 24 24"
  >
    <path
      fill="currentColor"
      d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12c5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"
    ></path>
  </svg>
)

const features = [
  {
    title: 'Create Videos with No Skills Required',
    description:
      'Bring your ideas to life with the AI video creation tools. Quickly generate professional videos for social media, presentations, or personal projects.',
    icon: <VideoIcon />
  },
  {
    title: 'Cloud Storage & Sync',
    description:
      'Access your files from anywhere with our secure cloud storage. Automatic syncing ensures your work is always up to date.',
    icon: <CloudIcon />
  },
  {
    title: 'Enterprise-Grade Security',
    description:
      'Your data is protected with industry-leading security measures. We use end-to-end encryption and regular security audits.',
    icon: <SecurityIcon />
  }
]

export const Default: Story = {
  args: {
    title: 'Powerful Features',
    description: 'Everything you need to create amazing content',
    features,
    columns: 3
  }
}

export const Horizontal: Story = {
  args: {
    title: 'Key Features',
    description: 'Essential tools for your workflow',
    features,
    layout: 'horizontal',
    columns: 2
  },
  decorators: [
    (Story) => (
      <div className="min-w-screen mx-auto">
        <Story />
      </div>
    )
  ]
}

export const TwoColumns: Story = {
  args: {
    title: 'Key Features',
    description: 'Essential tools for your workflow',
    features: features.slice(0, 2),
    columns: 2
  }
}

export const FourColumns: Story = {
  args: {
    title: 'Complete Feature Set',
    description: 'Comprehensive tools for professionals',
    features: [
      ...features,
      {
        title: 'Advanced Analytics',
        description:
          'Get detailed insights into your content performance with our advanced analytics dashboard.',
        icon: <CloudIcon />
      }
    ],
    columns: 4
  }
}
