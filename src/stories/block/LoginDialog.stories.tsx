import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { LoginDialog, useLoginDialog } from '@/components/Block/LoginDialog'
import { Button } from '@/components/ui/button'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { TooltipProvider } from '@/components/ui/tooltip'
import { AuthProvider } from '@/contexts/AuthContext'
import { GoogleOAuthProvider } from '@react-oauth/google'

// 模拟 useRouter hook
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

// 模拟翻译数据
const messages = {
  Login: {
    title: '登录',
    email: '邮箱',
    password: '密码',
    submit: '登录',
    forgotPassword: '忘记密码？',
    noAccount: '还没有账号？',
    signUp: '立即注册'
  }
}

const meta = {
  title: 'Block/LoginDialog',
  component: LoginDialog,
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <TooltipProvider>
            <GoogleOAuthProvider clientId={process.env.CLIENT_ID!}>
              <AuthProvider>
                <div className="min-h-screen p-4">
                  <Story />
                </div>
              </AuthProvider>
            </GoogleOAuthProvider>
          </TooltipProvider>
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ]
} satisfies Meta<typeof LoginDialog>

export default meta
type Story = StoryObj<typeof meta>

// 基础登录对话框
export const Default: Story = {
  render: () => {
    const { onOpenChange } = useLoginDialog()
    return (
      <div className="space-y-4">
        <Button onClick={() => onOpenChange(true)}>打开登录对话框</Button>
        <LoginDialog />
      </div>
    )
  }
}

// 带登录成功回调的对话框
export const WithSuccessCallback: Story = {
  render: () => {
    const { onOpenChange, setOnLoginSuccess } = useLoginDialog()

    const handleOpen = () => {
      setOnLoginSuccess(() => {
        console.log('登录成功回调')
      })
      onOpenChange(true)
    }

    return (
      <div className="space-y-4">
        <Button onClick={handleOpen}>打开登录对话框（带回调）</Button>
        <LoginDialog />
      </div>
    )
  }
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => {
    const { onOpenChange } = useLoginDialog()
    return (
      <div className="dark bg-neutral-900 p-4">
        <Button onClick={() => onOpenChange(true)}>打开登录对话框</Button>
        <LoginDialog />
      </div>
    )
  }
}

// 响应式布局
export const Responsive: Story = {
  render: () => {
    const { onOpenChange } = useLoginDialog()
    return (
      <div className="space-y-4">
        <Button onClick={() => onOpenChange(true)}>打开登录对话框</Button>
        <LoginDialog />
      </div>
    )
  },
  parameters: {
    viewport: {
      defaultViewport: 'responsive'
    }
  }
}
