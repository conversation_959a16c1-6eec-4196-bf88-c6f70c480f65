import type { Meta, StoryObj } from '@storybook/react'
import { TestimonialSection } from '@/components/Block/TestimonialSection'
import { NextIntlClientProvider } from 'next-intl'

// 模拟翻译数据
const messages = {
  Home: {
    customerReviews: '客户评价'
  }
}

const meta: Meta<typeof TestimonialSection> = {
  title: 'Block/TestimonialSection',
  component: TestimonialSection,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="zh" messages={messages}>
        <div className="w-4/5 xl:w-[1000px] mx-auto">
          <Story />
        </div>
      </NextIntlClientProvider>
    )
  ]
}

export default meta
type Story = StoryObj<typeof TestimonialSection>

// 模拟数据
const mockVoices = [
  {
    name: '张三',
    description: '纹身爱好者',
    comment: '这个 AI 纹身设计工具太棒了！它完美地捕捉了我想要的设计风格，生成的图案非常独特。',
    avatar: '/avatar/avatar1.jpg'
  },
  {
    name: '李四',
    description: '纹身师',
    comment:
      '作为一个专业的纹身师，这个工具给我的工作带来了很大的帮助。它能够快速生成多种设计选项，让客户有更多选择。',
    avatar: '/avatar/avatar2.jpg'
  },
  {
    name: '王五',
    description: '艺术设计师',
    comment:
      'AI 纹身设计工具让我能够探索更多的创意可能性。它的算法非常智能，能够理解我的设计意图。',
    avatar: '/avatar/avatar3.jpg'
  },
  {
    name: '赵六',
    description: '纹身店老板',
    comment: '自从使用了这个工具，我们店的客户满意度提高了不少。它帮助我们更好地理解客户的需求。',
    avatar: '/avatar/avatar4.jpg'
  },
  {
    name: '钱七',
    description: '纹身爱好者',
    comment: '这个工具让我能够预览不同风格的纹身效果，帮助我做出更好的决定。',
    avatar: '/avatar/avatar5.jpg'
  },
  {
    name: '孙八',
    description: '纹身师',
    comment: 'AI 纹身设计工具是我见过的最好的设计辅助工具之一。它节省了我大量的设计时间。',
    avatar: '/avatar/avatar6.jpg'
  }
]

export const Default: Story = {
  args: {
    voices: mockVoices
  }
}

export const CustomTitle: Story = {
  args: {
    voices: mockVoices,
    title: '用户评价'
  }
}

export const FewVoices: Story = {
  args: {
    voices: mockVoices.slice(0, 2)
  }
}

export const CustomStyle: Story = {
  args: {
    voices: mockVoices,
    className: 'bg-gray-50 dark:bg-gray-900 p-8 rounded-lg'
  }
}
