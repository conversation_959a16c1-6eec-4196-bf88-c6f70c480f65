import type { Met<PERSON>, StoryObj } from '@storybook/react'
import { PriceCards, PriceCard as PriceCardType } from '../../components/Block/PriceCards'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { TooltipProvider } from '@/components/ui/tooltip'

// 模拟 useRouter hook
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

// 模拟翻译数据
const messages = {
  Price: {
    freePlan: '免费套餐',
    starterPlan: '入门套餐',
    standardPlan: '标准套餐',
    premiumPlan: '高级套餐',
    freeDescription: '适合个人用户的基础功能',
    starterDescription: '适合小型团队的基础功能',
    standardDescription: '适合中型团队的专业功能',
    premiumDescription: '适合大型企业的完整功能',
    freeFeature1: '基础功能 1',
    freeFeature2: '基础功能 2',
    freeFeature3: '基础功能 3',
    freeFeature4: '基础功能 4',
    freeFeature5: '基础功能 5',
    freeFeature6: '基础功能 6',
    freeFeature7: '基础功能 7',
    starterFeature1: '入门功能 1',
    starterFeature2: '入门功能 2',
    starterFeature3: '入门功能 3',
    starterFeature4: '入门功能 4',
    starterFeature5: '入门功能 5',
    starterFeature6: '入门功能 6',
    starterFeature7: '入门功能 7',
    standardFeature1: '标准功能 1',
    standardFeature2: '标准功能 2',
    standardFeature3: '标准功能 3',
    standardFeature4: '标准功能 4',
    standardFeature5: '标准功能 5',
    standardFeature6: '标准功能 6',
    standardFeature7: '标准功能 7',
    standardFeature8: '标准功能 8',
    premiumFeature1: '高级功能 1',
    premiumFeature2: '高级功能 2',
    premiumFeature3: '高级功能 3',
    premiumFeature4: '高级功能 4',
    premiumFeature5: '高级功能 5',
    premiumFeature6: '高级功能 6',
    premiumFeature7: '高级功能 7',
    premiumFeature8: '高级功能 8',
    pricingTitle: '选择适合您的套餐',
    pricingSubtitle: '灵活的定价方案，满足不同需求',
    mostPopular: '最受欢迎',
    currentPlan: '当前套餐',
    subscribe: '订阅',
    upgrade: '升级',
    downgrade: '降级',
    upgradeTooltip: '升级到更高级的套餐',
    downgradeTooltip: '降级到更基础的套餐',
    paymentFailed: '支付失败'
  }
}

const meta = {
  title: 'Block/PriceCards',
  component: PriceCards,
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <TooltipProvider>
            <Story />
          </TooltipProvider>
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ]
} satisfies Meta<typeof PriceCards>

export default meta
type Story = StoryObj<typeof meta>

// 模拟价格卡片数据
const mockCards: PriceCardType[] = [
  {
    duration: 'monthly',
    first_price: 0,
    id: 1,
    price: 0,
    original_price: 0,
    trial_days: 0,
    rank_name: 'free'
  },
  {
    duration: 'monthly',
    first_price: 9.99,
    id: 2,
    price: 9.99,
    original_price: 19.99,
    trial_days: 7,
    rank_name: 'starter'
  },
  {
    duration: 'monthly',
    first_price: 19.99,
    id: 3,
    price: 19.99,
    original_price: 39.99,
    trial_days: 14,
    rank_name: 'standard'
  },
  {
    duration: 'monthly',
    first_price: 49.99,
    id: 4,
    price: 49.99,
    original_price: 99.99,
    trial_days: 30,
    rank_name: 'premium'
  }
]

// 基础价格卡片
export const Default: Story = {
  args: {
    cards: mockCards,
    user: null
  },
  render: (args) => (
    <div className="p-4">
      <PriceCards {...args} />
    </div>
  )
}

// 已登录用户查看价格卡片
export const LoggedInUser: Story = {
  args: {
    cards: mockCards,
    user: {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      rank_name: 'starter',
      avatar: null
    }
  },
  render: (args) => (
    <div className="max-w-[85rem] px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
      <PriceCards {...args} />
    </div>
  )
}

// 仅显示免费和标准套餐
export const LimitedPlans: Story = {
  args: {
    cards: mockCards.filter((card) => ['free', 'standard'].includes(card.rank_name.toLowerCase())),
    user: null
  },
  render: (args) => (
    <div className="p-4">
      <PriceCards {...args} />
    </div>
  )
}

// 隐藏标题的价格卡片
export const WithoutHeader: Story = {
  args: {
    cards: mockCards,
    user: null,
    disableHeader: true
  },
  render: (args) => (
    <div className="p-4">
      <PriceCards {...args} />
    </div>
  )
}

// 暗色主题价格卡片
export const DarkTheme: Story = {
  args: {
    cards: mockCards,
    user: null
  },
  render: (args) => (
    <div className="p-4 dark bg-neutral-900">
      <PriceCards {...args} />
    </div>
  )
}

// 响应式布局
export const Responsive: Story = {
  args: {
    cards: mockCards,
    user: null
  },
  render: (args) => (
    <div className="p-4 w-full max-w-7xl mx-auto">
      <PriceCards {...args} />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'responsive'
    }
  }
}
