import type { Meta, StoryObj } from '@storybook/react'
import { MobileNav } from '../../components/Block/MobileNav'
import { NextIntlClientProvider } from 'next-intl'
import { AppRouterContext } from 'next/dist/shared/lib/app-router-context.shared-runtime'

// 模拟翻译数据
const messages = {
  Header: {
    toggleNavigation: '切换导航'
  }
}

// 模拟路由
const mockRouter = {
  push: (path: string) => console.log('Navigating to:', path),
  replace: (path: string) => console.log('Replacing with:', path),
  back: () => console.log('Going back'),
  forward: () => console.log('Going forward'),
  refresh: () => console.log('Refreshing'),
  prefetch: (path: string) => console.log('Prefetching:', path)
}

const meta = {
  title: 'Block/MobileNav',
  component: MobileNav,
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    }
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <AppRouterContext.Provider value={mockRouter}>
        <NextIntlClientProvider locale="zh" messages={messages}>
          <Story />
        </NextIntlClientProvider>
      </AppRouterContext.Provider>
    )
  ]
} satisfies Meta<typeof MobileNav>

export default meta
type Story = StoryObj<typeof meta>

// 默认状态
export const Default: Story = {
  render: () => <MobileNav />,
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => (
    <div className="dark bg-neutral-900 p-4">
      <MobileNav />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}

// 响应式布局
export const Responsive: Story = {
  render: () => (
    <div className="w-full max-w-md mx-auto">
      <MobileNav />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
