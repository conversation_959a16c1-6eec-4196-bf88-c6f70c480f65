import type { Meta, StoryObj } from '@storybook/react'
import { Footer } from '@/components/Block/Footer'
import { NextIntlClientProvider } from 'next-intl'

// 模拟翻译数据
const messages = {
  Footer: {
    SectionOne: '分区一',
    SectionTwo: '分区二',
    SectionOneColomnOneTextOne: '文字一',
    SectionOneColomnTwoTextOne: '文字二',
    SectionOneColomnThreeTextOne: '文字三',
    SectionOneColomnOneTextTwo: '文字四',
    SectionOneColomnTwoTextTwo: '文字五',
    SectionOneColomnThreeTextTwo: '文字六',
    copyright: '© 2024 cli. 保留所有权利。',
    affiliation: '合作伙伴',
    privacy: '隐私政策',
    terms: '服务条款',
    pricing: '价格方案',
    ariaLabel: 'cli'
  }
}

const meta: Meta<typeof Footer> = {
  title: 'Block/Footer',
  component: Footer,
  parameters: {
    layout: 'fullscreen'
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="zh" messages={messages}>
        <div>
          <Story />
        </div>
      </NextIntlClientProvider>
    )
  ]
}

export default meta
type Story = StoryObj<typeof Footer>

export const Default: Story = {
  args: {}
}

export const DarkTheme: Story = {
  args: {},
  parameters: {
    themes: {
      defaultTheme: 'dark'
    }
  }
}

export const CustomStyle: Story = {
  args: {},
  decorators: [
    (Story) => (
      <div className="bg-gray-50 dark:bg-gray-900">
        <Story />
      </div>
    )
  ]
}

export const Responsive: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
