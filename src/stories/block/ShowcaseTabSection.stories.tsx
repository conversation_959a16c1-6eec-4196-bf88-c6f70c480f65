import type { Meta, StoryObj } from '@storybook/react'
import { ShowcaseTabSection } from '@/components/Block/ShowcaseTabSection'
import { NextIntlClientProvider } from 'next-intl'
import { Example1, Example2, Example3, Example4 } from './ShowcaseContent/example'

// 模拟翻译数据
const messages = {
  index: {
    section2Type1: '类型一',
    section2Type2: '类型二',
    section2Type3: '类型三',
    section2Type4: '类型四',
    section2tip1: '类型一示例',
    section2tip2: '类型二示例',
    section2tip3: '类型三示例',
    section2tip4: '类型四示例',
    section2Title2: 'AI 纹身设计',
    section2Desc2:
      '使用 AI 技术将您的照片转换为独特的纹身设计。我们的算法可以分析照片中的元素，并生成适合纹身的艺术风格设计。',
    section2Btn: '立即尝试'
  }
}

const meta: Meta<typeof ShowcaseTabSection> = {
  title: 'Block/ShowcaseTabSection',
  component: ShowcaseTabSection,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="zh" messages={messages}>
        <div className="w-4/5 xl:w-[1000px] mx-auto">
          <Story />
        </div>
      </NextIntlClientProvider>
    )
  ]
}

export default meta
type Story = StoryObj<typeof ShowcaseTabSection>

// 模拟数据
const tabs = [
  { text: '类型一', type: 1 },
  { text: '类型二', type: 2 },
  { text: '类型三', type: 3 },
  { text: '类型四', type: 4 }
]

const content = {
  1: <Example1 />,
  2: <Example2 />,
  3: <Example3 />,
  4: <Example4 />
}

export const Default: Story = {
  args: {
    tabs,
    content
  }
}

export const TwoTabs: Story = {
  args: {
    tabs: tabs.slice(0, 2),
    content: {
      1: content[1],
      2: content[2]
    }
  }
}

export const CustomStyle: Story = {
  args: {
    tabs,
    content,
    className: 'bg-gray-50 dark:bg-gray-900 p-8 rounded-lg'
  }
}
