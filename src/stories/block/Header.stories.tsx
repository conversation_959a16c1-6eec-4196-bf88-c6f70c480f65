import type { <PERSON>a, StoryObj } from '@storybook/react'
// import { HeaderClient } from '@/components/Block/HeaderClient'
import { Header } from '@/components/Block/Header'
import { NextIntlClientProvider } from 'next-intl'

// 模拟翻译数据
const messages = {
  Header: {
    LogoAriaLabel: 'Lufe Translator 首页',
    toggleNavigation: '切换导航',
    NavOne: '导航一',
    NavTwo: '导航二',
    NavThree: '导航三',
    NavFour: '导航四'
  },
  Avatar: {
    Profile: '个人资料',
    Terms: '服务条款',
    Privacy: '隐私政策',
    SignIn: '登录',
    logout: '退出'
  }
}

// 模拟用户数据
const mockUserInfo = {
  account: '<EMAIL>',
  username: '杨舒越',
  email: '<EMAIL>',
  language: 'zh-CN',
  company: null,
  country: null,
  province: null,
  city: null,
  postal: null,
  address: null,
  phone: null,
  vat: null,
  distribution_code: '',
  created_at: +new Date(),
  delete_task_plan_executed_at: null,
  avatar: '/avatar/avatar1.jpg',
  rank_name: 'Free',
  last_rank_name: null
}

// 模拟 API 处理器
// const handlers = [
//   http.get('/common-api/user', () => {
//     return HttpResponse.json(mockUserInfo)
//   })
// ]

const meta: Meta<typeof Header> = {
  title: 'Block/Header',
  component: Header,
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
        query: {}
      }
    },
    // msw: {
    //   handlers,
    // },
    userInfo: mockUserInfo
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      return (
        <NextIntlClientProvider locale="zh" messages={messages}>
          <div className="min-h-screen">
            <Story />
          </div>
        </NextIntlClientProvider>
      )
    }
  ]
}

export default meta
type Story = StoryObj<typeof Header>

export const Default: Story = {
  parameters: {
    // msw: {
    //   handlers,
    // },
  },
  args: {
    userInfo: mockUserInfo
  }
}

export const NotLoggedIn: Story = {
  args: {
    userInfo: null
  }
}

export const NoAvatar: Story = {
  args: {
    userInfo: {
      ...mockUserInfo,
      avatar: undefined
    }
  }
}

export const CustomStyle: Story = {
  args: {
    userInfo: mockUserInfo
  },
  decorators: [
    (Story) => (
      <div className="bg-gray-50 dark:bg-gray-900">
        <Story />
      </div>
    )
  ]
}

export const Responsive: Story = {
  args: {
    userInfo: mockUserInfo
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
