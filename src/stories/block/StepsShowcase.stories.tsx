import type { Meta, StoryObj } from '@storybook/react'
import StepsShowcase from '@/components/Block/StepsShowcase'
import { NextIntlClientProvider } from 'next-intl'

const meta: Meta<typeof StepsShowcase> = {
  title: 'Block/StepsShowcase',
  component: StepsShowcase,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <NextIntlClientProvider locale="zh" messages={{}}>
        <Story />
      </NextIntlClientProvider>
    )
  ]
}

export default meta
type Story = StoryObj<typeof StepsShowcase>

const steps = [
  {
    id: 1,
    title: '准备阶段',
    desc: '在开始之前，请确保您已经准备好需要翻译的图片',
    duration: '约 1 分钟',
    tips: ['建议使用清晰、高分辨率的图片', '确保图片中的文字清晰可见', '支持的格式：JPG、PNG、PDF']
  },
  {
    id: 2,
    title: '上传与识别',
    desc: '上传图片后，系统会自动识别图片中的文字内容',
    duration: '约 2-3 分钟',
    tips: ['上传后请耐心等待识别完成', '可以预览识别结果', '如有错误可手动调整']
  },
  {
    id: 3,
    title: '翻译与优化',
    desc: '选择目标语言，系统会自动翻译并优化排版',
    duration: '约 1-2 分钟',
    tips: ['支持 100+ 种语言互译', '可调整字体大小和样式', '支持实时预览效果']
  }
]

export const Default: Story = {
  args: {
    title: '图片翻译三步指南',
    steps
  }
}

export const WithTips: Story = {
  args: {
    title: '详细操作指南',
    steps: [
      {
        id: 1,
        title: '准备工作',
        desc: '确保您的设备和网络环境正常'
      },
      {
        id: 2,
        title: '上传文件',
        desc: '选择并上传需要翻译的图片'
      },
      {
        id: 3,
        title: '处理结果',
        desc: '查看并下载翻译后的文件'
      }
    ]
  }
}

export const DarkTheme: Story = {
  args: {
    title: '深色模式示例',
    steps
  },
  parameters: {
    themes: {
      defaultTheme: 'dark'
    }
  }
}

export const Responsive: Story = {
  args: {
    title: '移动端适配',
    steps
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
