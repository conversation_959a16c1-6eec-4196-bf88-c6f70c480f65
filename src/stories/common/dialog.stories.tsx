import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription
} from '../../components/ui/dialog'
import { But<PERSON> } from '../../components/ui/button'

const meta = {
  title: 'Common/Dialog',
  component: Dialog,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Dialog>

export default meta
type Story = StoryObj<typeof meta>

// 基础对话框
export const Basic: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>打开对话框</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>标题</DialogTitle>
            <DialogDescription>
              这是一个基础对话框示例，用于展示信息或收集用户输入。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>对话框内容区域</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setOpen(false)}>确认</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
}

// 表单对话框
export const FormDialog: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>打开表单</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>用户信息</DialogTitle>
            <DialogDescription>请填写以下信息以完成注册。</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="name" className="text-right">
                姓名
              </label>
              <input
                id="name"
                className="col-span-3 rounded-md border p-2"
                placeholder="请输入姓名"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="email" className="text-right">
                邮箱
              </label>
              <input
                id="email"
                className="col-span-3 rounded-md border p-2"
                placeholder="请输入邮箱"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setOpen(false)}>提交</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
}

// 警告对话框
export const AlertDialog: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button variant="destructive">删除</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>此操作不可撤销，确定要删除吗？</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={() => setOpen(false)}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
}

// 自定义样式对话框
export const CustomStyle: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>自定义样式</Button>
        </DialogTrigger>
        <DialogContent className="bg-linear-to-br from-blue-50 to-purple-50">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-blue-600">自定义标题</DialogTitle>
            <DialogDescription className="text-purple-600">
              这是一个带有自定义样式的对话框
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-700">自定义内容区域</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button
              className="bg-linear-to-r from-blue-500 to-purple-500"
              onClick={() => setOpen(false)}
            >
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
}

// 暗色主题对话框
export const DarkTheme: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <div className="dark bg-neutral-900 p-4">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button>打开对话框</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>暗色主题</DialogTitle>
              <DialogDescription>这是一个暗色主题的对话框示例</DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p>对话框内容区域</p>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button onClick={() => setOpen(false)}>确认</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    )
  }
}

// 响应式对话框
export const Responsive: Story = {
  render: () => {
    const [open, setOpen] = useState(false)
    return (
      <div className="w-full max-w-md">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="w-full">打开对话框</Button>
          </DialogTrigger>
          <DialogContent className="w-[90vw] max-w-[400px]">
            <DialogHeader>
              <DialogTitle>响应式对话框</DialogTitle>
              <DialogDescription>这是一个响应式布局的对话框示例</DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p>对话框内容区域</p>
            </div>
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button variant="outline" onClick={() => setOpen(false)} className="w-full sm:w-auto">
                取消
              </Button>
              <Button onClick={() => setOpen(false)} className="w-full sm:w-auto">
                确认
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    )
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
