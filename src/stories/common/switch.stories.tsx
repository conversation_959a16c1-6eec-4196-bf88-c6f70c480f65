import type { Meta, StoryObj } from '@storybook/react'
import { Switch } from '../../components/ui/switch'

const meta = {
  title: 'Common/Switch',
  component: Switch,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Switch>

export default meta
type Story = StoryObj<typeof meta>

// 默认状态
export const Default: Story = {
  args: {
    defaultChecked: false
  }
}

// 选中状态
export const Checked: Story = {
  args: {
    defaultChecked: true
  }
}

// 禁用状态
export const Disabled: Story = {
  args: {
    disabled: true
  }
}

// 禁用且选中
export const DisabledChecked: Story = {
  args: {
    disabled: true,
    defaultChecked: true
  }
}

// 自定义样式
export const CustomStyle: Story = {
  args: {
    className: 'data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500'
  }
}

// 暗色主题
export const DarkTheme: Story = {
  args: {
    defaultChecked: true
  },
  render: (args) => (
    <div className="dark bg-neutral-900 p-4">
      <Switch {...args} />
    </div>
  )
}
