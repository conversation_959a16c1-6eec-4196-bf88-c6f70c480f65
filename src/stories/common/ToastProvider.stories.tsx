import type { Meta, StoryObj } from '@storybook/react'
import ToastProvider from '../../components/ToastProvider'
import { useToast } from '@/hooks/useToast'

// 模拟使用 Toast 的组件
const ToastDemo = () => {
  const { showToast } = useToast()

  return (
    <div className="space-y-4">
      <button
        onClick={() => showToast('普通提示', 'normal')}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        显示普通提示
      </button>
      <button
        onClick={() => showToast('成功提示', 'success')}
        className="px-4 py-2 bg-green-500 text-white rounded"
      >
        显示成功提示
      </button>
      <button
        onClick={() => showToast('错误提示', 'error')}
        className="px-4 py-2 bg-red-500 text-white rounded"
      >
        显示错误提示
      </button>
      <button
        onClick={() => showToast('警告提示', 'warning')}
        className="px-4 py-2 bg-yellow-500 text-white rounded"
      >
        显示警告提示
      </button>
    </div>
  )
}

const meta = {
  title: 'Common/ToastProvider',
  component: ToastProvider,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof ToastProvider>

export default meta
type Story = StoryObj<typeof meta>

// 默认状态
export const Default: Story = {
  args: {
    children: <ToastDemo />
  },
  render: (args) => <ToastProvider>{args.children}</ToastProvider>
}

// 暗色主题
export const DarkTheme: Story = {
  args: {
    children: <ToastDemo />
  },
  render: (args) => (
    <div className="dark bg-neutral-900 p-4">
      <ToastProvider>{args.children}</ToastProvider>
    </div>
  )
}

// 多个提示
export const MultipleToasts: Story = {
  args: {
    children: (
      <div className="space-y-4">
        <button
          onClick={() => {
            const { showToast } = useToast()
            showToast('第一个提示', 'normal')
            showToast('第二个提示', 'success')
            showToast('第三个提示', 'error')
            showToast('第四个提示', 'warning')
          }}
          className="px-4 py-2 bg-purple-500 text-white rounded"
        >
          显示多个提示
        </button>
      </div>
    )
  },
  render: (args) => <ToastProvider>{args.children}</ToastProvider>
}

// 响应式布局
export const Responsive: Story = {
  args: {
    children: <ToastDemo />
  },
  render: (args) => (
    <div className="w-full max-w-md mx-auto">
      <ToastProvider>{args.children}</ToastProvider>
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
