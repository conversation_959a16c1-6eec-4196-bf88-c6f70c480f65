import type { Meta } from '@storybook/react'
import { useForm } from 'react-hook-form'
import Input, { InputProps } from '../../components/Input'

const meta = {
  title: 'Common/Input',
  component: Input,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'tel']
    },
    required: {
      control: 'boolean'
    }
  }
} satisfies Meta<typeof Input>

export default meta

// 包装组件以提供 form context
const InputWrapper = (props: Omit<InputProps, 'register'>) => {
  const { register } = useForm()
  return <Input {...props} register={register('test')} />
}

// 基础输入框
export const Default = {
  render: () => <InputWrapper type="text" placeholder="请输入内容" label="用户名" />
}

// 带错误提示的输入框
export const WithError = {
  render: () => (
    <InputWrapper type="text" placeholder="请输入内容" label="用户名" error="用户名不能为空" />
  )
}

// 必填输入框
export const Required = {
  render: () => <InputWrapper type="text" placeholder="请输入内容" label="用户名" required={true} />
}

// 密码输入框
export const Password = {
  render: () => <InputWrapper type="password" placeholder="请输入密码" label="密码" />
}

// 邮箱输入框
export const Email = {
  render: () => {
    const {
      register,
      formState: { errors }
    } = useForm()

    return (
      <Input
        type="email"
        placeholder={'email'}
        label={'email'}
        required
        className={errors.email ? 'border-red-500' : ''}
        error={errors.email?.message as string}
        register={register('email', {
          required: 'emailRequired',
          pattern: {
            value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
            message: 'emailInvalid'
          }
        })}
      />
    )
  }
}

// 自定义样式
export const CustomStyle = {
  render: () => (
    <InputWrapper
      type="text"
      placeholder="自定义样式"
      label="自定义"
      className="bg-gray-100 dark:bg-neutral-800"
    />
  )
}
