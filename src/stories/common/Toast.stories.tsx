import type { Meta, StoryObj } from '@storybook/react'
import Toast from '../../components/Toast'

const meta = {
  title: 'Common/Toast',
  component: Toast,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Toast>

export default meta
type Story = StoryObj<typeof meta>

// 普通提示
export const Normal: Story = {
  args: {
    message: '这是一条普通提示消息',
    type: 'normal'
  }
}

// 成功提示
export const Success: Story = {
  args: {
    message: '操作成功完成！',
    type: 'success'
  }
}

// 错误提示
export const Error: Story = {
  args: {
    message: '操作失败，请重试！',
    type: 'error'
  }
}

// 警告提示
export const Warning: Story = {
  args: {
    message: '请注意，这是一个警告消息！',
    type: 'warning'
  }
}

// 长文本提示
export const LongMessage: Story = {
  args: {
    message:
      '这是一条非常长的提示消息，用于测试文本溢出和换行的情况。这条消息应该会自动换行并保持良好的可读性。',
    type: 'normal'
  }
}

// 暗色主题
export const DarkTheme: Story = {
  args: {
    message: '暗色主题下的提示消息',
    type: 'normal'
  },
  render: (args: { message: string; type: 'normal' | 'success' | 'error' | 'warning' }) => (
    <div className="dark bg-neutral-900 p-4">
      <Toast {...args} />
    </div>
  )
}

// 多语言支持
export const Internationalization: Story = {
  args: {
    message: '国际化提示消息',
    type: 'normal'
  },
  parameters: {
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/zh',
        query: {}
      }
    }
  }
}
