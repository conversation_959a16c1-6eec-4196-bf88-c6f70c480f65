import type { Meta, StoryObj } from '@storybook/react'
import { Textarea } from '../../components/ui/textarea'

const meta = {
  title: 'Common/Textarea',
  component: Textarea,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Textarea>

export default meta
type Story = StoryObj<typeof meta>

// 基础文本域
export const Basic: Story = {
  args: {
    placeholder: '请输入内容...'
  }
}

// 禁用状态
export const Disabled: Story = {
  args: {
    placeholder: '禁用状态',
    disabled: true
  }
}

// 只读状态
export const ReadOnly: Story = {
  args: {
    value: '只读内容',
    readOnly: true
  }
}

// 自定义大小
export const CustomSize: Story = {
  args: {
    placeholder: '自定义大小',
    className: 'min-h-[200px] w-[400px]'
  }
}

// 自定义样式
export const CustomStyle: Story = {
  args: {
    placeholder: '自定义样式',
    className: 'border-2 border-blue-500 focus:border-blue-700 rounded-lg'
  }
}

// 暗色主题
export const DarkTheme: Story = {
  args: {
    placeholder: '暗色主题'
  },
  render: (args) => (
    <div className="dark bg-neutral-900 p-4">
      <Textarea {...args} />
    </div>
  )
}

// 响应式布局
export const Responsive: Story = {
  args: {
    placeholder: '响应式布局'
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <Textarea {...args} />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
