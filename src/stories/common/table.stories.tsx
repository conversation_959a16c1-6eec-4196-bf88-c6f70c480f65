import type { <PERSON>a, StoryObj } from '@storybook/react'
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption
} from '../../components/ui/table'

const meta = {
  title: 'Common/Table',
  component: Table,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Table>

export default meta
type Story = StoryObj<typeof meta>

// 基础表格
export const Basic: Story = {
  render: () => (
    <Table>
      <TableCaption>用户列表</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead>姓名</TableHead>
          <TableHead>邮箱</TableHead>
          <TableHead>角色</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell>张三</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>管理员</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>李四</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>用户</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  )
}

// 带页脚的表格
export const WithFooter: Story = {
  render: () => (
    <Table>
      <TableCaption>销售数据</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead>产品</TableHead>
          <TableHead>数量</TableHead>
          <TableHead>单价</TableHead>
          <TableHead>总价</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell>产品A</TableCell>
          <TableCell>2</TableCell>
          <TableCell>¥100</TableCell>
          <TableCell>¥200</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>产品B</TableCell>
          <TableCell>3</TableCell>
          <TableCell>¥150</TableCell>
          <TableCell>¥450</TableCell>
        </TableRow>
      </TableBody>
      <TableFooter>
        <TableRow>
          <TableCell colSpan={3}>总计</TableCell>
          <TableCell>¥650</TableCell>
        </TableRow>
      </TableFooter>
    </Table>
  )
}

// 自定义样式
export const CustomStyle: Story = {
  render: () => (
    <Table className="border">
      <TableCaption className="text-lg font-bold">自定义样式表格</TableCaption>
      <TableHeader>
        <TableRow className="bg-neutral-100">
          <TableHead className="font-bold">姓名</TableHead>
          <TableHead className="font-bold">邮箱</TableHead>
          <TableHead className="font-bold">角色</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow className="hover:bg-neutral-50">
          <TableCell>张三</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>管理员</TableCell>
        </TableRow>
        <TableRow className="hover:bg-neutral-50">
          <TableCell>李四</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>用户</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  )
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => (
    <div className="dark bg-neutral-900 p-4">
      <Table>
        <TableCaption>暗色主题表格</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>姓名</TableHead>
            <TableHead>邮箱</TableHead>
            <TableHead>角色</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>张三</TableCell>
            <TableCell><EMAIL></TableCell>
            <TableCell>管理员</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>李四</TableCell>
            <TableCell><EMAIL></TableCell>
            <TableCell>用户</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  )
}

// 响应式布局
export const Responsive: Story = {
  render: () => (
    <div className="w-full max-w-md overflow-x-auto">
      <Table>
        <TableCaption>响应式表格</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>姓名</TableHead>
            <TableHead>邮箱</TableHead>
            <TableHead>角色</TableHead>
            <TableHead>部门</TableHead>
            <TableHead>入职日期</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>张三</TableCell>
            <TableCell><EMAIL></TableCell>
            <TableCell>管理员</TableCell>
            <TableCell>技术部</TableCell>
            <TableCell>2023-01-01</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>李四</TableCell>
            <TableCell><EMAIL></TableCell>
            <TableCell>用户</TableCell>
            <TableCell>市场部</TableCell>
            <TableCell>2023-02-01</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
