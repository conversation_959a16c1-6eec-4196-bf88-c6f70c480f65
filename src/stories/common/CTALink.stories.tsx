import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { CTA } from '@/components/Block/CTALink'
import { Download, ArrowRight, Star } from 'lucide-react'

const meta: Meta<typeof CTA> = {
  title: 'UI/CTALink',
  component: CTA,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
}

export default meta
type Story = StoryObj<typeof CTA>

export const Default: Story = {
  args: {
    href: 'https://www.google.com',
    icon: Download,
    text: '立即尝试',
    variant: 'md'
  }
}

export const Small: Story = {
  args: {
    href: 'https://www.google.com',
    icon: Download,
    text: '小号按钮',
    variant: 'sm'
  }
}

export const Large: Story = {
  args: {
    href: 'https://www.google.com',
    icon: Download,
    text: '大号按钮',
    variant: 'lg'
  }
}

export const WithArrow: Story = {
  args: {
    href: 'https://www.google.com',
    icon: ArrowRight,
    text: '下一步',
    variant: 'md'
  }
}

export const WithStar: Story = {
  args: {
    href: 'https://www.google.com',
    icon: Star,
    text: '收藏',
    variant: 'md'
  }
}

export const CustomStyle: Story = {
  args: {
    href: 'https://www.google.com',
    icon: Download,
    text: '自定义样式',
    variant: 'md',
    className: 'bg-gradient-to-r from-primary to-primary/80'
  }
}
