import type { Meta, StoryObj } from '@storybook/react'
import { Skeleton } from '../../components/ui/skeleton'
import { useState } from 'react'

const meta = {
  title: 'Common/Skeleton',
  component: Skeleton,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Skeleton>

export default meta
type Story = StoryObj<typeof meta>

// 基础骨架屏
export const Basic: Story = {
  render: () => (
    <div className="flex items-center space-x-4">
      <Skeleton className="h-12 w-12 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-[250px]" />
        <Skeleton className="h-4 w-[200px]" />
      </div>
    </div>
  )
}

// 页面加载骨架屏
export const PageLoading: Story = {
  render: () => (
    <div className="space-y-8 p-4">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-10 w-[100px]" />
      </div>

      {/* 搜索栏 */}
      <div className="flex items-center space-x-4">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-[100px]" />
      </div>

      {/* 卡片列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="space-y-3 p-4 border rounded-lg">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <div className="flex justify-between items-center">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 列表加载骨架屏
export const ListLoading: Story = {
  render: () => (
    <div className="space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      ))}
    </div>
  )
}

// 表格加载骨架屏
export const TableLoading: Story = {
  render: () => (
    <div className="space-y-2">
      {/* 表头 */}
      <div className="flex space-x-4">
        <Skeleton className="h-8 flex-1" />
        <Skeleton className="h-8 flex-1" />
        <Skeleton className="h-8 flex-1" />
        <Skeleton className="h-8 w-20" />
      </div>
      {/* 表格内容 */}
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex space-x-4">
          <Skeleton className="h-12 flex-1" />
          <Skeleton className="h-12 flex-1" />
          <Skeleton className="h-12 flex-1" />
          <Skeleton className="h-12 w-20" />
        </div>
      ))}
    </div>
  )
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => (
    <div className="dark bg-neutral-900 p-4">
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
        <Skeleton className="h-4 w-[250px]" />
        <div className="flex space-x-4">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-20" />
        </div>
      </div>
    </div>
  )
}

// 响应式布局
export const Responsive: Story = {
  render: () => (
    <div className="w-full max-w-md">
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="flex space-x-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-20" />
        </div>
      </div>
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}

// 交互式加载
export const InteractiveLoading: Story = {
  render: () => {
    const [isLoading, setIsLoading] = useState(false)
    const [data, setData] = useState<Array<{ id: number; name: string; email: string }> | null>(
      null
    )

    const fetchData = async () => {
      setIsLoading(true)
      // 模拟 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setData([
        { id: 1, name: '张三', email: '<EMAIL>' },
        { id: 2, name: '李四', email: '<EMAIL>' },
        { id: 3, name: '王五', email: '<EMAIL>' }
      ])
      setIsLoading(false)
    }

    return (
      <div className="space-y-4 w-[400px]">
        <button
          onClick={fetchData}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {isLoading ? '加载中...' : '获取数据'}
        </button>

        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : data ? (
          <div className="space-y-4">
            {data.map((item) => (
              <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="h-12 w-12 rounded-full bg-neutral-200 flex items-center justify-center">
                  {item.name[0]}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-sm text-neutral-500">{item.email}</div>
                </div>
              </div>
            ))}
          </div>
        ) : null}
      </div>
    )
  }
}
