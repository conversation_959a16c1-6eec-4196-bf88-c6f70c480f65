import type { <PERSON>a, StoryObj } from '@storybook/react'
import {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent
} from '../../components/ui/card'

const meta = {
  title: 'Common/Card',
  component: Card,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Card>

export default meta
type Story = StoryObj<typeof meta>

// 基础卡片
export const Default: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>基础卡片</CardTitle>
        <CardDescription>这是一个基础卡片的描述</CardDescription>
      </CardHeader>
      <CardContent>
        <p>卡片内容区域</p>
      </CardContent>
      <CardFooter>
        <p>卡片底部</p>
      </CardFooter>
    </Card>
  )
}

// 带图片的卡片
export const WithImage: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <img
          src="https://picsum.photos/350/200"
          alt="示例图片"
          className="rounded-t-xl w-full h-[200px] object-cover"
        />
        <CardTitle className="mt-4">带图片的卡片</CardTitle>
        <CardDescription>展示带图片的卡片样式</CardDescription>
      </CardHeader>
      <CardContent>
        <p>这是一张带有图片的卡片，图片位于卡片顶部。</p>
      </CardContent>
    </Card>
  )
}

// 带操作的卡片
export const WithActions: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>带操作的卡片</CardTitle>
        <CardDescription>展示带操作按钮的卡片</CardDescription>
      </CardHeader>
      <CardContent>
        <p>这是一个带有操作按钮的卡片示例。</p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
          取消
        </button>
        <button className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary/90">
          确认
        </button>
      </CardFooter>
    </Card>
  )
}

// 带图标的卡片
export const WithIcon: Story = {
  render: () => (
    <Card className="w-[350px]">
      <CardHeader>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
            <svg
              className="w-4 h-4 text-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
          <CardTitle>带图标的卡片</CardTitle>
        </div>
        <CardDescription>展示带图标的卡片样式</CardDescription>
      </CardHeader>
      <CardContent>
        <p>这是一个带有图标的卡片示例。</p>
      </CardContent>
    </Card>
  )
}

// 暗色主题卡片
export const DarkTheme: Story = {
  render: () => (
    <Card className="w-[350px] bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">暗色主题卡片</CardTitle>
        <CardDescription className="text-neutral-400">展示暗色主题的卡片样式</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-neutral-300">这是一个暗色主题的卡片示例。</p>
      </CardContent>
      <CardFooter>
        <button className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary/90">
          操作按钮
        </button>
      </CardFooter>
    </Card>
  )
}

// 可点击的卡片
export const Clickable: Story = {
  render: () => (
    <Card className="w-[350px] cursor-pointer hover:shadow-lg transition-shadow">
      <CardHeader>
        <CardTitle>可点击的卡片</CardTitle>
        <CardDescription>展示可点击的卡片样式</CardDescription>
      </CardHeader>
      <CardContent>
        <p>这是一个可点击的卡片示例，hover 时会有阴影效果。</p>
      </CardContent>
    </Card>
  )
}
