import type { Meta, StoryObj } from '@storybook/react'
import { Typography } from '@/components/Typography'

const meta = {
  title: 'Common/Typography',
  component: Typography,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span']
    },
    children: {
      control: 'text'
    },
    className: {
      control: 'text'
    }
  }
} satisfies Meta<typeof Typography>

export default meta
type Story = StoryObj<typeof meta>

// 全部的展示
export const All = {
  render: () => {
    return (
      <div className="flex flex-col gap-4">
        <Typography variant="h1">Heading 1 - 最大的标题</Typography>
        <Typography variant="h2">Heading 2 - 次大的标题</Typography>
        <Typography variant="h3">Heading 3 - 中等大小的标题</Typography>
        <Typography variant="h4">Heading 4 - 较小的标题</Typography>
        <Typography variant="h5">Heading 5 - 更小的标题</Typography>
        <Typography variant="h6">Heading 6 - 最小的标题</Typography>
        <Typography variant="p">
          这是一个段落文本。它使用了默认的样式，适合长文本内容的展示。段落文本会自动换行，并且有合适的行高和间距。
        </Typography>
        <Typography variant="span">这是一个行内文本</Typography>
      </div>
    )
  }
}

// 基础标题
export const Heading1: Story = {
  args: {
    variant: 'h1',
    children: 'Heading 1 - 最大的标题'
  }
}

export const Heading2: Story = {
  args: {
    variant: 'h2',
    children: 'Heading 2 - 次大的标题'
  }
}

export const Heading3: Story = {
  args: {
    variant: 'h3',
    children: 'Heading 3 - 中等大小的标题'
  }
}

export const Heading4: Story = {
  args: {
    variant: 'h4',
    children: 'Heading 4 - 较小的标题'
  }
}

export const Heading5: Story = {
  args: {
    variant: 'h5',
    children: 'Heading 5 - 更小的标题'
  }
}

export const Heading6: Story = {
  args: {
    variant: 'h6',
    children: 'Heading 6 - 最小的标题'
  }
}

// 段落文本
export const Paragraph: Story = {
  args: {
    variant: 'p',
    children:
      '这是一个段落文本。它使用了默认的样式，适合长文本内容的展示。段落文本会自动换行，并且有合适的行高和间距。'
  }
}

// 行内文本
export const Inline: Story = {
  args: {
    variant: 'span',
    children: '这是一个行内文本',
    className: 'font-bold'
  }
}

// 自定义样式
export const CustomStyle: Story = {
  args: {
    variant: 'h1',
    children: '自定义样式的标题',
    className: 'text-blue-600'
  }
}
