import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { But<PERSON> } from '../../components/ui/button'
import { ArrowRight, Download } from 'lucide-react'
import { motion } from 'framer-motion'

const meta: Meta<typeof Button> = {
  title: 'UI/CTAButton',
  component: Button,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
}

export default meta
type Story = StoryObj<typeof Button>

export const Primary: Story = {
  render: () => (
    <div className="flex gap-3 cursor-pointer">
      <motion.div
        className="min-w-[140px] cursor-pointer h-11 w-fit p-1 inline-flex pr-2 items-center text-sm font-medium rounded-full border border-transparent bg-primary text-white hover:bg-primary/90 focus:outline-none focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group"
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        <span className="ml-4 flex items-center">
          <Download className="w-4 h-4 mr-2" />
        </span>
        <span className="mr-2">Get Started</span>
        <motion.div
          variants={{
            initial: { x: 0 },
            hover: {
              x: [0, 4, 0],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }
          }}
        >
          <ArrowRight className="w-4 h-4" />
        </motion.div>
      </motion.div>
    </div>
  )
}

export const Secondary: Story = {
  render: () => (
    <div className="flex gap-3 cursor-pointer">
      <motion.div
        className="min-w-[140px] cursor-pointer h-11 w-fit p-1 inline-flex pr-2 items-center text-sm font-medium rounded-full border border-primary bg-transparent text-primary hover:bg-primary/10 focus:outline-none focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group"
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        <span className="ml-4 flex items-center">
          <Download className="w-4 h-4 mr-2" />
        </span>
        <span className="mr-2">Learn More</span>
        <motion.div
          variants={{
            initial: { x: 0 },
            hover: {
              x: [0, 4, 0],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }
          }}
        >
          <ArrowRight className="w-4 h-4" />
        </motion.div>
      </motion.div>
    </div>
  )
}

export const Outline: Story = {
  render: () => (
    <div className="flex gap-3 cursor-pointer">
      <motion.div
        className="min-w-[140px] cursor-pointer h-11 w-fit p-1 inline-flex pr-2 items-center text-sm font-medium rounded-full border border-neutral-200 bg-transparent text-neutral-900 hover:bg-neutral-100 focus:outline-none focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group dark:border-neutral-800 dark:text-neutral-100 dark:hover:bg-neutral-800"
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        <span className="ml-4 flex items-center">
          <Download className="w-4 h-4 mr-2" />
        </span>
        <span className="mr-2">Contact Us</span>
        <motion.div
          variants={{
            initial: { x: 0 },
            hover: {
              x: [0, 4, 0],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }
          }}
        >
          <ArrowRight className="w-4 h-4" />
        </motion.div>
      </motion.div>
    </div>
  )
}

export const CustomSize: Story = {
  render: () => (
    <div className="flex gap-3 cursor-pointer">
      <motion.div
        className="min-w-[180px] cursor-pointer h-14 w-fit p-1 inline-flex pr-2 items-center text-base font-medium rounded-full border border-transparent bg-primary text-white hover:bg-primary/90 focus:outline-none focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group"
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        <span className="ml-4 flex items-center">
          <Download className="w-5 h-5 mr-2" />
        </span>
        <span className="mr-2">Custom Size</span>
        <motion.div
          variants={{
            initial: { x: 0 },
            hover: {
              x: [0, 4, 0],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }
          }}
        >
          <ArrowRight className="w-5 h-5" />
        </motion.div>
      </motion.div>
    </div>
  )
}

export const FullWidth: Story = {
  render: () => (
    <div className="flex gap-3 cursor-pointer w-full">
      <motion.div
        className="min-w-[140px] cursor-pointer h-11 w-full p-1 inline-flex pr-2 items-center text-sm font-medium rounded-full border border-transparent bg-primary text-white hover:bg-primary/90 focus:outline-none focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group"
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        <span className="ml-4 flex items-center">
          <Download className="w-4 h-4 mr-2" />
        </span>
        <span className="mr-2">Full Width Button</span>
        <motion.div
          variants={{
            initial: { x: 0 },
            hover: {
              x: [0, 4, 0],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }
          }}
        >
          <ArrowRight className="w-4 h-4" />
        </motion.div>
      </motion.div>
    </div>
  )
}
