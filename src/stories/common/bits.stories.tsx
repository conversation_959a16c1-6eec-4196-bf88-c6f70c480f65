import type { Meta, StoryObj } from '@storybook/react'
import CountUp from '../../components/bits/CountUp/CountUp'
import GradientText from '../../components/bits/GradientText/GradientText'
import SpotlightCard from '../../components/bits/SpotlightCard/SpotlightCard'
import Squares from '../../components/bits/Squares/Squares'

const meta = {
  title: 'Common/Bits',
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta

export default meta
type Story = StoryObj<typeof meta>

// CountUp 组件
export const CountUpDemo: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="text-4xl font-bold">
        <CountUp to={1000} duration={2} />
      </div>
      <div className="text-2xl">
        <CountUp to={500} duration={1.5} />
      </div>
      <div className="text-xl">
        <CountUp to={99.9} duration={2} />
      </div>
    </div>
  )
}

// GradientText 组件
export const GradientTextDemo: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <GradientText className="text-4xl font-bold">渐变色文字标题</GradientText>
      <GradientText showBorder className="text-3xl">
        展示边框
      </GradientText>
      <GradientText className="text-2xl" colors={['#3b82f6', '#8b5cf6', '#3b82f6']}>
        自定义渐变色
      </GradientText>
      <GradientText className="text-xl" colors={['#34d399', '#0d9488', '#34d399']}>
        另一个渐变色示例
      </GradientText>
    </div>
  )
}

// SpotlightCard 组件
export const SpotlightCardDemo: Story = {
  render: () => (
    <div>
      <SpotlightCard>
        <div className="p-6">
          <h3 className="text-white text-xl font-bold mb-2">基础卡片</h3>
          <p className="text-gray-600">这是一个基础的 Spotlight 卡片示例</p>
        </div>
      </SpotlightCard>
    </div>
  )
}

// Squares 组件
export const SquaresDemo: Story = {
  render: () => (
    <div className="w-full max-w-4xl p-4">
      <Squares />
    </div>
  )
}

// 所有组件组合展示
export const AllComponents: Story = {
  render: () => (
    <div className="space-y-8 p-4">
      <section>
        <h2 className="text-2xl font-bold mb-4">CountUp 示例</h2>
        <div className="flex gap-4">
          <div className="text-4xl font-bold">
            <CountUp to={1000} duration={2} />
          </div>
          <div className="text-2xl">
            <CountUp to={500} duration={1.5} />
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-4">GradientText 示例</h2>
        <div className="space-y-2">
          <GradientText className="text-4xl font-bold">渐变色文字标题</GradientText>
          <GradientText className="text-2xl" colors={['#3b82f6', '#8b5cf6']}>
            自定义渐变色
          </GradientText>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-4">SpotlightCard 示例</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SpotlightCard>
            <div className="p-6">
              <h3 className="text-white text-xl font-bold mb-2">基础卡片</h3>
              <p className="text-gray-600">这是一个基础的 Spotlight 卡片示例</p>
            </div>
          </SpotlightCard>
          <SpotlightCard>
            <div className="p-6">
              <h3 className="text-white text-xl font-bold mb-2">基础卡片</h3>
              <p className="text-gray-600">这是一个基础的 Spotlight 卡片示例</p>
            </div>
          </SpotlightCard>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-4">Squares 示例</h2>
        <div className="w-full max-w-2xl">
          <Squares />
        </div>
      </section>
    </div>
  )
}
