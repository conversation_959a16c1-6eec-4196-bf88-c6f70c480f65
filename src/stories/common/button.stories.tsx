import type { Meta, StoryObj } from '@storybook/react'
import { Button } from '../../components/ui/button'
import { Mail, Loader2 } from 'lucide-react'

const meta = {
  title: 'Common/Button',
  component: Button,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link']
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg', 'icon']
    },
    disabled: {
      control: 'boolean'
    }
  }
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof meta>

// 默认按钮
export const Default: Story = {
  args: {
    children: '默认按钮',
    variant: 'default'
  }
}

// 不同变体
export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="default">默认按钮</Button>
      <Button variant="destructive">危险按钮</Button>
      <Button variant="outline">轮廓按钮</Button>
      <Button variant="secondary">次要按钮</Button>
      <Button variant="ghost">幽灵按钮</Button>
      <Button variant="link">链接按钮</Button>
    </div>
  )
}

// 不同尺寸
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm">小按钮</Button>
      <Button size="default">默认按钮</Button>
      <Button size="lg">大按钮</Button>
      <Button size="icon">
        <Mail className="h-4 w-4" />
      </Button>
    </div>
  )
}

// 禁用状态
export const Disabled: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="default" disabled>
        禁用默认
      </Button>
      <Button variant="destructive" disabled>
        禁用危险
      </Button>
      <Button variant="outline" disabled>
        禁用轮廓
      </Button>
      <Button variant="secondary" disabled>
        禁用次要
      </Button>
      <Button variant="ghost" disabled>
        禁用幽灵
      </Button>
      <Button variant="link" disabled>
        禁用链接
      </Button>
    </div>
  )
}

// 加载状态
export const Loading: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button disabled>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        加载中...
      </Button>
      <Button variant="outline" disabled>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        加载中...
      </Button>
    </div>
  )
}

// 带图标的按钮
export const WithIcon: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>
        <Mail className="mr-2 h-4 w-4" />
        发送邮件
      </Button>
      <Button variant="outline">
        发送邮件
        <Mail className="ml-2 h-4 w-4" />
      </Button>
    </div>
  )
}

// 组合示例
export const Combinations: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button size="lg" variant="default">
          <Mail className="mr-2 h-4 w-4" />
          大号默认按钮
        </Button>
        <Button size="sm" variant="outline">
          <Mail className="mr-2 h-4 w-4" />
          小号轮廓按钮
        </Button>
      </div>
      <div className="flex flex-wrap gap-4">
        <Button size="lg" variant="destructive" disabled>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          禁用的大号危险按钮
        </Button>
        <Button size="sm" variant="ghost" disabled>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          禁用的小号幽灵按钮
        </Button>
      </div>
    </div>
  )
}
