import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { Reddit } from '../../components/svg/Reddit'
import { RightArrow } from '../../components/svg/RightArrow'
import { Start4 } from '../../components/svg/Start4'
import { Start4_5 } from '../../components/svg/Start4_5'
import { Start5 } from '../../components/svg/Start5'
import { WandIcon } from '../../components/svg/WandIcon'
import { YandexIcon } from '../../components/svg/YandexIcon'
import { Youtube } from '../../components/svg/Youtube'
import { BingIcon } from '../../components/svg/BingIcon'
import { ChatGPTIcon } from '../../components/svg/ChatGPTIcon'
import { Checked } from '../../components/svg/Checked'
import { ClaudeIcon } from '../../components/svg/ClaudeIcon'
import { GeminiIcon } from '../../components/svg/GeminiIcon'
import { GoogleTranslatorIcon } from '../../components/svg/GoogleTranslatorIcon'
import { HandWrite } from '../../components/svg/HandWrite'
import { OpenIcon } from '../../components/svg/OpenIcon'

import { AtomIcon } from '../../components/svg/landing/AtomIcon'
import { CopyIcon } from '../../components/svg/landing/CopyIcon'
import { FreeIcon } from '../../components/svg/landing/FreeIcon'
import { GlobalIcon } from '../../components/svg/landing/GlobalIcon'
import { SpeechIcon } from '../../components/svg/landing/SpeechIcon'
import { TranslationIcon } from '../../components/svg/landing/TranslationIcon'

const meta = {
  title: 'Common/SVG Icons',
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta

export default meta
type Story = StoryObj<typeof meta>

// 社交媒体图标
export const SocialMediaIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <Reddit />
        <span className="text-sm">Reddit</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Youtube />
        <span className="text-sm">Youtube</span>
      </div>
    </div>
  )
}

// AI 服务图标
export const AIServiceIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <ChatGPTIcon />
        <span className="text-sm">ChatGPT</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ClaudeIcon />
        <span className="text-sm">Claude</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GeminiIcon />
        <span className="text-sm">Gemini</span>
      </div>
    </div>
  )
}

// 搜索引擎图标
export const SearchEngineIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <BingIcon />
        <span className="text-sm">Bing</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <YandexIcon />
        <span className="text-sm">Yandex</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GoogleTranslatorIcon />
        <span className="text-sm">Google Translator</span>
      </div>
    </div>
  )
}

// 功能图标
export const FunctionalIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <RightArrow />
        <span className="text-sm">Right Arrow</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <OpenIcon />
        <span className="text-sm">Open</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Checked />
        <span className="text-sm">Checked</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <WandIcon />
        <span className="text-sm">Wand</span>
      </div>
    </div>
  )
}

// 评分图标
export const RatingIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <Start4 />
        <span className="text-sm">4 Stars</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Start4_5 />
        <span className="text-sm">4.5 Stars</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Start5 />
        <span className="text-sm">5 Stars</span>
      </div>
    </div>
  )
}

// 手写图标
export const HandWriteIcon: Story = {
  render: () => (
    <div className="flex flex-col items-center gap-2 p-4">
      <HandWrite />
      <span className="text-sm">Hand Write</span>
    </div>
  )
}

// landing 图标
export const LandingIcons: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 p-4">
      <div className="flex flex-col items-center gap-2">
        <AtomIcon />
        <span className="text-sm">Atom</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <CopyIcon />
        <span className="text-sm">Copy</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <FreeIcon />
        <span className="text-sm">Free</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GlobalIcon />
        <span className="text-sm">Global</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SpeechIcon />
        <span className="text-sm">Speech</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <TranslationIcon />
        <span className="text-sm">Translation</span>
      </div>
    </div>
  )
}
// 所有图标
export const AllIcons: Story = {
  render: () => (
    <div className="grid grid-cols-4 gap-6 p-8">
      <div className="flex flex-col items-center gap-2">
        <Reddit />
        <span className="text-sm">Reddit</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <RightArrow />
        <span className="text-sm">Right Arrow</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Start4 />
        <span className="text-sm">4 Stars</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Start4_5 />
        <span>4.5 Stars</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Start5 />
        <span>5 Stars</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <WandIcon />
        <span className="text-sm">Wand</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <YandexIcon />
        <span className="text-sm">Yandex</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Youtube />
        <span className="text-sm">Youtube</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <BingIcon />
        <span className="text-sm">Bing</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ChatGPTIcon />
        <span className="text-sm">ChatGPT</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Checked />
        <span className="text-sm">Checked</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <ClaudeIcon />
        <span className="text-sm">Claude</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GeminiIcon />
        <span className="text-sm">Gemini</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GoogleTranslatorIcon />
        <span className="text-sm">Google Translator</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <HandWrite />
        <span className="text-sm">Hand Write</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <OpenIcon />
        <span className="text-sm">Open</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <AtomIcon />
        <span className="text-sm">Atom</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <CopyIcon />
        <span className="text-sm">Copy</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <FreeIcon />
        <span className="text-sm">Free</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <GlobalIcon />
        <span className="text-sm">Global</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <SpeechIcon />
        <span className="text-sm">Speech</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <TranslationIcon />
        <span className="text-sm">Translation</span>
      </div>
    </div>
  )
}
