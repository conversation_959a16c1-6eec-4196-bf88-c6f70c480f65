import type { <PERSON>a, StoryObj } from '@storybook/react'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue
} from '../../components/ui/select'

const meta = {
  title: 'Common/Select',
  component: Select,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      control: 'boolean'
    },
    defaultValue: {
      control: 'text'
    }
  }
} satisfies Meta<typeof Select>

export default meta
type Story = StoryObj<typeof meta>

// 基础选择器
export const Basic: Story = {
  args: {
    defaultValue: 'apple'
  },
  render: (args) => (
    <Select {...args}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="选择一个水果" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>水果</SelectLabel>
          <SelectItem value="apple">苹果</SelectItem>
          <SelectItem value="banana">香蕉</SelectItem>
          <SelectItem value="orange">橙子</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

// 分组选择器
export const WithGroups: Story = {
  render: () => (
    <Select>
      <SelectTrigger className="w-[280px]">
        <SelectValue placeholder="选择一个选项" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>水果</SelectLabel>
          <SelectItem value="apple">苹果</SelectItem>
          <SelectItem value="banana">香蕉</SelectItem>
          <SelectItem value="orange">橙子</SelectItem>
        </SelectGroup>
        <SelectSeparator />
        <SelectGroup>
          <SelectLabel>蔬菜</SelectLabel>
          <SelectItem value="carrot">胡萝卜</SelectItem>
          <SelectItem value="potato">土豆</SelectItem>
          <SelectItem value="tomato">番茄</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

// 禁用状态
export const Disabled: Story = {
  args: {
    disabled: true,
    defaultValue: 'apple'
  },
  render: (args) => (
    <Select {...args}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="选择一个水果" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>水果</SelectLabel>
          <SelectItem value="apple">苹果</SelectItem>
          <SelectItem value="banana">香蕉</SelectItem>
          <SelectItem value="orange">橙子</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

// 长列表选择器
export const LongList: Story = {
  render: () => (
    <Select>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="选择一个国家" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>亚洲</SelectLabel>
          <SelectItem value="china">中国</SelectItem>
          <SelectItem value="japan">日本</SelectItem>
          <SelectItem value="korea">韩国</SelectItem>
          <SelectItem value="india">印度</SelectItem>
          <SelectItem value="thailand">泰国</SelectItem>
        </SelectGroup>
        <SelectSeparator />
        <SelectGroup>
          <SelectLabel>欧洲</SelectLabel>
          <SelectItem value="france">法国</SelectItem>
          <SelectItem value="germany">德国</SelectItem>
          <SelectItem value="italy">意大利</SelectItem>
          <SelectItem value="spain">西班牙</SelectItem>
          <SelectItem value="uk">英国</SelectItem>
        </SelectGroup>
        <SelectSeparator />
        <SelectGroup>
          <SelectLabel>美洲</SelectLabel>
          <SelectItem value="usa">美国</SelectItem>
          <SelectItem value="canada">加拿大</SelectItem>
          <SelectItem value="brazil">巴西</SelectItem>
          <SelectItem value="mexico">墨西哥</SelectItem>
          <SelectItem value="argentina">阿根廷</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

// 自定义样式
export const CustomStyle: Story = {
  render: () => (
    <Select>
      <SelectTrigger className="w-[180px] bg-primary text-primary-foreground">
        <SelectValue placeholder="选择一个主题" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>主题</SelectLabel>
          <SelectItem value="light">浅色主题</SelectItem>
          <SelectItem value="dark">深色主题</SelectItem>
          <SelectItem value="system">系统主题</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

// 组合示例
export const Combinations: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Select defaultValue="apple">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="选择一个水果" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>水果</SelectLabel>
              <SelectItem value="apple">苹果</SelectItem>
              <SelectItem value="banana">香蕉</SelectItem>
              <SelectItem value="orange">橙子</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        <Select disabled>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="禁用的选择器" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>选项</SelectLabel>
              <SelectItem value="option1">选项 1</SelectItem>
              <SelectItem value="option2">选项 2</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      <div className="flex gap-4">
        <Select>
          <SelectTrigger className="w-[280px]">
            <SelectValue placeholder="带分组的选择器" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>水果</SelectLabel>
              <SelectItem value="apple">苹果</SelectItem>
              <SelectItem value="banana">香蕉</SelectItem>
            </SelectGroup>
            <SelectSeparator />
            <SelectGroup>
              <SelectLabel>蔬菜</SelectLabel>
              <SelectItem value="carrot">胡萝卜</SelectItem>
              <SelectItem value="potato">土豆</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
