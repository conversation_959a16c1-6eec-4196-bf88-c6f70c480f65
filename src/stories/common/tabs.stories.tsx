import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '../../components/ui/tabs'

const meta = {
  title: 'Common/Tabs',
  component: Tabs,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs']
} satisfies Meta<typeof Tabs>

export default meta
type Story = StoryObj<typeof meta>

// 基础标签页
export const Basic: Story = {
  render: () => (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="account">账户</TabsTrigger>
        <TabsTrigger value="password">密码</TabsTrigger>
        <TabsTrigger value="settings">设置</TabsTrigger>
      </TabsList>
      <TabsContent value="account">账户信息内容</TabsContent>
      <TabsContent value="password">密码设置内容</TabsContent>
      <TabsContent value="settings">系统设置内容</TabsContent>
    </Tabs>
  )
}

// 禁用标签页
export const Disabled: Story = {
  render: () => (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="account">账户</TabsTrigger>
        <TabsTrigger value="password" disabled>
          密码
        </TabsTrigger>
        <TabsTrigger value="settings">设置</TabsTrigger>
      </TabsList>
      <TabsContent value="account">账户信息内容</TabsContent>
      <TabsContent value="password">密码设置内容</TabsContent>
      <TabsContent value="settings">系统设置内容</TabsContent>
    </Tabs>
  )
}

// 自定义样式
export const CustomStyle: Story = {
  render: () => (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList className="bg-neutral-100">
        <TabsTrigger
          value="account"
          className="data-[state=active]:bg-white data-[state=active]:text-black"
        >
          账户
        </TabsTrigger>
        <TabsTrigger
          value="password"
          className="data-[state=active]:bg-white data-[state=active]:text-black"
        >
          密码
        </TabsTrigger>
        <TabsTrigger
          value="settings"
          className="data-[state=active]:bg-white data-[state=active]:text-black"
        >
          设置
        </TabsTrigger>
      </TabsList>
      <TabsContent value="account" className="p-4 bg-white rounded-b">
        账户信息内容
      </TabsContent>
      <TabsContent value="password" className="p-4 bg-white rounded-b">
        密码设置内容
      </TabsContent>
      <TabsContent value="settings" className="p-4 bg-white rounded-b">
        系统设置内容
      </TabsContent>
    </Tabs>
  )
}

// 暗色主题
export const DarkTheme: Story = {
  render: () => (
    <div className="dark bg-neutral-900 p-4">
      <Tabs defaultValue="account" className="w-[400px]">
        <TabsList>
          <TabsTrigger value="account">账户</TabsTrigger>
          <TabsTrigger value="password">密码</TabsTrigger>
          <TabsTrigger value="settings">设置</TabsTrigger>
        </TabsList>
        <TabsContent value="account">账户信息内容</TabsContent>
        <TabsContent value="password">密码设置内容</TabsContent>
        <TabsContent value="settings">系统设置内容</TabsContent>
      </Tabs>
    </div>
  )
}

// 响应式布局
export const Responsive: Story = {
  render: () => (
    <Tabs defaultValue="account" className="w-full max-w-md">
      <TabsList className="w-full">
        <TabsTrigger value="account" className="flex-1">
          账户
        </TabsTrigger>
        <TabsTrigger value="password" className="flex-1">
          密码
        </TabsTrigger>
        <TabsTrigger value="settings" className="flex-1">
          设置
        </TabsTrigger>
      </TabsList>
      <TabsContent value="account">账户信息内容</TabsContent>
      <TabsContent value="password">密码设置内容</TabsContent>
      <TabsContent value="settings">系统设置内容</TabsContent>
    </Tabs>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}
