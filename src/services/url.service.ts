'server-only'

/**
 * URL生成服务
 * 统一管理应用中所有URL的生成逻辑
 */

import {
  generateBookDetailUrl,
  generateBookSlug,
  parseBookSlug,
  isValidBookId,
  type BookUrlData as BookUrlDataType
} from '@/utils/book.utils';
import { convertCategoryNameToSlug } from '@/utils/category.utils';
import {
  generateAuthorDetailUrl,
  generateAuthorSlug,
  parseAuthorSlug,
  isValidAuthorId,
  type AuthorUrlData
} from '@/utils/author.utils';

/**
 * 书籍相关URL生成
 */
export class BookUrlService {
  /**
   * 生成书籍详情页面URL
   */
  static generateDetailUrl(book: BookUrlDataType): string {
    return generateBookDetailUrl(book);
  }

  /**
   * 生成书籍slug
   */
  static generateSlug(title: string, id: number | string): string {
    return generateBookSlug(title, id);
  }

  /**
   * 解析书籍URL获取ID和标题
   */
  static parseUrl(url: string): { id: number; title?: string } | null {
    try {
      // 提取slug部分
      const match = url.match(/\/book-summary\/(.+)$/);
      if (!match) {
        return null;
      }

      const slug = match[1];
      const parsed = parseBookSlug(slug);

      if (!isValidBookId(parsed.id)) {
        return null;
      }

      return {
        id: parsed.id,
        title: parsed.title
      };
    } catch (error) {
      console.error('Error parsing book URL:', url, error);
      return null;
    }
  }

  /**
   * 验证书籍URL的有效性
   */
  static validateUrl(url: string): boolean {
    return this.parseUrl(url) !== null;
  }

  /**
   * 批量生成书籍URL
   */
  static generateBatchUrls(books: BookUrlDataType[]): Array<{ book: BookUrlDataType; url: string }> {
    return books.map(book => ({
      book,
      url: this.generateDetailUrl(book)
    }));
  }
}

/**
 * 作者相关URL生成
 */
export class AuthorUrlService {
  /**
   * 生成作者详情页面URL
   */
  static generateDetailUrl(author: AuthorUrlData): string {
    return generateAuthorDetailUrl(author);
  }

  /**
   * 生成作者slug
   */
  static generateSlug(name: string, id: number | string): string {
    return generateAuthorSlug(name, id);
  }

  /**
   * 解析作者URL获取ID和名称
   */
  static parseUrl(url: string): { id: number; name?: string } | null {
    try {
      // 提取slug部分
      const match = url.match(/\/author\/(.+)$/);
      if (!match) {
        return null;
      }

      const slug = match[1];
      const parsed = parseAuthorSlug(slug);

      if (!isValidAuthorId(parsed.id)) {
        return null;
      }

      return {
        id: parsed.id,
        name: parsed.name
      };
    } catch (error) {
      console.error('Error parsing author URL:', url, error);
      return null;
    }
  }

  /**
   * 验证作者URL的有效性
   */
  static validateUrl(url: string): boolean {
    return AuthorUrlService.parseUrl(url) !== null;
  }

  /**
   * 批量生成作者URL
   */
  static generateBatchUrls(authors: AuthorUrlData[]): Array<{ author: AuthorUrlData; url: string }> {
    return authors.map(author => ({
      author,
      url: AuthorUrlService.generateDetailUrl(author)
    }));
  }
}

/**
 * 分类相关URL生成
 */
export class CategoryUrlService {
  /**
   * 生成分类页面URL
   */
  static generateDetailUrl(categoryName: string): string {
    const slug = convertCategoryNameToSlug(categoryName);
    return `/categories/${slug}`;
  }

  /**
   * 生成分类slug
   */
  static generateSlug(categoryName: string): string {
    return convertCategoryNameToSlug(categoryName);
  }

  /**
   * 解析分类URL获取分类名
   */
  static parseUrl(url: string): string | null {
    try {
      const match = url.match(/\/categories\/(.+)$/);
      if (!match) {
        return null;
      }

      return match[1]; // 返回slug
    } catch (error) {
      console.error('Error parsing category URL:', url, error);
      return null;
    }
  }

  /**
   * 验证分类URL的有效性
   */
  static validateUrl(url: string): boolean {
    return this.parseUrl(url) !== null;
  }
}

/**
 * 通用URL工具
 */
export class UrlService {
  /**
   * 构建完整的绝对URL
   */
  static buildAbsoluteUrl(path: string, baseUrl?: string): string {
    const base = baseUrl || process.env.NEXT_PUBLIC_BASE_URL || 'https://15minutes.ai';
    return new URL(path, base).toString();
  }

  /**
   * 构建多语言URL
   */
  static buildLocalizedUrl(path: string, locale: string): string {
    // 如果是默认语言（英语），不添加语言前缀
    if (locale === 'en') {
      return path;
    }

    // 确保路径以/开头
    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
    return `/${locale}${normalizedPath}`;
  }

  /**
   * 生成规范URL（用于SEO）
   */
  static generateCanonicalUrl(path: string, locale: string = 'en'): string {
    const localizedPath = this.buildLocalizedUrl(path, locale);
    return this.buildAbsoluteUrl(localizedPath);
  }

  /**
   * 生成多语言替代URL（用于SEO）
   */
  static generateAlternateUrls(path: string, supportedLocales: string[] = ['en', 'zh']): Record<string, string> {
    const alternates: Record<string, string> = {};

    supportedLocales.forEach(locale => {
      const localizedPath = this.buildLocalizedUrl(path, locale);
      alternates[locale] = this.buildAbsoluteUrl(localizedPath);
    });

    return alternates;
  }

  /**
   * 解析URL获取路径和语言
   */
  static parseLocalizedUrl(url: string): { path: string; locale: string } {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;

      // 检查是否有语言前缀
      const match = pathname.match(/^\/([a-z]{2})(\/.*)?$/);

      if (match) {
        const [, locale, path] = match;
        return {
          locale,
          path: path || '/'
        };
      }

      // 没有语言前缀，默认为英语
      return {
        locale: 'en',
        path: pathname
      };
    } catch (error) {
      console.error('Error parsing localized URL:', url, error);
      return {
        locale: 'en',
        path: '/'
      };
    }
  }
}

/**
 * URL生成的统一入口
 * 提供简化的API供组件使用
 */
export const urlGenerator = {
  // 书籍相关
  book: {
    detail: BookUrlService.generateDetailUrl,
    slug: BookUrlService.generateSlug,
    parse: BookUrlService.parseUrl,
    validate: BookUrlService.validateUrl,
    batch: BookUrlService.generateBatchUrls
  },

  // 作者相关
  author: {
    detail: AuthorUrlService.generateDetailUrl,
    slug: AuthorUrlService.generateSlug,
    parse: AuthorUrlService.parseUrl,
    validate: AuthorUrlService.validateUrl,
    batch: AuthorUrlService.generateBatchUrls
  },

  // 分类相关
  category: {
    detail: CategoryUrlService.generateDetailUrl,
    slug: CategoryUrlService.generateSlug,
    parse: CategoryUrlService.parseUrl,
    validate: CategoryUrlService.validateUrl
  },

  // 通用工具
  utils: {
    absolute: UrlService.buildAbsoluteUrl,
    localized: UrlService.buildLocalizedUrl,
    canonical: UrlService.generateCanonicalUrl,
    alternates: UrlService.generateAlternateUrls,
    parse: UrlService.parseLocalizedUrl
  }
};

// 导出类型定义
export type BookUrlData = BookUrlDataType;
export type { AuthorUrlData };
export type UrlGeneratorType = typeof urlGenerator;
