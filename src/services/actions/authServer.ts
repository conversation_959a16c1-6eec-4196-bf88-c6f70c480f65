'use server'
import 'server-only'
import { getRedisClient } from '@/lib/redis'
import { getRedisTokenKey } from '@/lib/auth/config'

interface VerifyResult {
  isValid: boolean
  uid?: number
}

/**
 * 验证JWT token
 * 这里不是标准的JWT鉴权方式
 * 这里保持和PHP的逻辑一致:
 * 获取JWT的头部和载荷
 * 检查载荷上的uid
 * 去Redis上检查是否有这个token的值
 * 如果有则认为token有效
 * 否则认为token无效
 */
export async function verifyJwtToken(token: string): Promise<VerifyResult> {
  try {
    // 分割JWT token
    const [, payloadBase64] = token.split('.')

    // 解码payload
    const payload = JSON.parse(Buffer.from(payloadBase64, 'base64').toString())

    // 检查payload中是否有uid
    if (!payload.uid) {
      return { isValid: false }
    }

    // 获取Redis客户端
    // PHP后端使用的是 'frontend' Redis池，对应数据库1
    // 而不是默认的数据库0，这是关键问题！
    const redis = getRedisClient(1) // 使用数据库1，与PHP后端的frontend池保持一致

    // 使用统一配置生成 Redis key
    const key = getRedisTokenKey(payload.uid)

    // 检查Redis中是否存在该token（使用zScore而不是exists，与PHP后端逻辑一致）
    const score = await redis.zscore(key, token)
    const exists = score !== null

    console.log('verifyJwtToken:', {
      key,
      token: token.substring(0, 20) + '...',
      score,
      exists,
      uid: payload.uid,
      redis_db: 1
    })

    return {
      isValid: exists,
      uid: payload.uid
    }
  } catch (error) {
    console.error('JWT verification failed:', error)
    return { isValid: false }
  }
}
