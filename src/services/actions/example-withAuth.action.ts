'use server'

/**
 * withAuth 装饰器使用示例
 * 展示如何在不修改现有函数签名的情况下添加认证
 */

import { 
  withAuth, 
  withOptionalAuth, 
  getCurrentAuthUser, 
  getCurrentUserId, 
  isAuthenticated,
  requireAuth,
  getAuthUser,
  checkAuth
} from '@/lib/auth/withAuth'

// ============================================================================
// 方式一：使用装饰器 + 内部获取用户信息
// ============================================================================

/**
 * 示例1：需要认证的函数，在内部获取用户ID
 */
export const getUserFavorites = withAuth(async (page: number = 1) => {
  // 在函数内部获取当前认证用户
  const { userId } = getCurrentAuthUser()
  
  console.log(`Getting favorites for user ${userId}, page ${page}`)
  
  // 执行业务逻辑
  // return await favoriteService.getUserFavorites(userId, page)
  return { userId, page, favorites: [] }
})

/**
 * 示例2：添加收藏功能
 */
export const addToFavorites = withAuth(async (bookId: number) => {
  const { userId } = getCurrentAuthUser()
  
  console.log(`User ${userId} adding book ${bookId} to favorites`)
  
  // return await favoriteService.addFavorite(userId, bookId)
  return { success: true, userId, bookId }
})

/**
 * 示例3：可选认证的函数
 */
export const getBookDetail = withOptionalAuth(async (bookId: number) => {
  const userId = getCurrentUserId() // 可能为 null
  
  if (userId) {
    console.log(`Getting personalized book detail for user ${userId}`)
    // return await bookService.getBookWithUserData(userId, bookId)
    return { bookId, personalized: true, userId }
  } else {
    console.log('Getting public book detail for anonymous user')
    // return await bookService.getPublicBook(bookId)
    return { bookId, personalized: false }
  }
})

/**
 * 示例4：复杂的业务逻辑，多次使用认证信息
 */
export const complexOperation = withAuth(async (data: any) => {
  const { userId } = getCurrentAuthUser()
  
  // 第一步：检查用户权限
  console.log(`Checking permissions for user ${userId}`)
  
  // 第二步：执行操作
  console.log(`User ${userId} performing operation with data:`, data)
  
  // 第三步：记录日志
  console.log(`Operation completed for user ${userId}`)
  
  return { success: true, userId, result: 'completed' }
})

// ============================================================================
// 方式二：使用 requireAuth 辅助函数
// ============================================================================

/**
 * 示例5：使用 requireAuth 辅助函数
 */
export const updateUserProfile = withAuth(async (profileData: any) => {
  // 使用 requireAuth 确保已认证（会抛出错误如果未认证）
  const { userId } = requireAuth()
  
  console.log(`Updating profile for user ${userId}`)
  
  // return await userService.updateProfile(userId, profileData)
  return { success: true, userId, profileData }
})

/**
 * 示例6：条件性认证检查
 */
export const conditionalAuth = withOptionalAuth(async (action: string) => {
  if (action === 'public') {
    // 公开操作，不需要认证
    return { action, result: 'public data' }
  }
  
  if (action === 'private') {
    // 私有操作，需要认证
    if (!isAuthenticated()) {
      return { success: false, error: 'Authentication required for private action' }
    }
    
    const { userId } = getCurrentAuthUser()
    return { action, result: 'private data', userId }
  }
  
  return { action, result: 'unknown action' }
})

// ============================================================================
// 方式三：不使用装饰器，直接调用认证函数
// ============================================================================

/**
 * 示例7：不使用装饰器，手动检查认证
 */
export async function manualAuthCheck(bookId: number) {
  // 手动检查认证状态
  const isLoggedIn = await checkAuth()
  
  if (!isLoggedIn) {
    return { success: false, error: 'Authentication required' }
  }
  
  // 获取用户信息
  const authUser = await getAuthUser()
  if (!authUser) {
    return { success: false, error: 'Failed to get user info' }
  }
  
  console.log(`Manual auth check passed for user ${authUser.userId}`)
  return { success: true, userId: authUser.userId, bookId }
}

/**
 * 示例8：可选认证的手动处理
 */
export async function optionalManualAuth(bookId: number) {
  // 尝试获取用户信息，但不强制要求
  const authUser = await getAuthUser()
  
  if (authUser) {
    console.log(`Authenticated user ${authUser.userId} accessing book ${bookId}`)
    // return await bookService.getBookWithUserData(authUser.userId, bookId)
    return { bookId, personalized: true, userId: authUser.userId }
  } else {
    console.log(`Anonymous user accessing book ${bookId}`)
    // return await bookService.getPublicBook(bookId)
    return { bookId, personalized: false }
  }
}

// ============================================================================
// 迁移现有代码示例
// ============================================================================

/**
 * 示例9：迁移现有的 my-library 代码
 */
export const getLibraryData = withAuth(async (tab: string, page: number) => {
  // 原来需要手动获取和验证 token 的代码，现在只需要一行
  const { userId } = getCurrentAuthUser()
  
  // 原来的业务逻辑保持不变
  try {
    switch (tab) {
      case 'listened':
        // return await myLibraryService.getUserListenedBooks(userId, page);
        return { tab, page, userId, data: [] }
      case 'favorites':
        // return await myLibraryService.getUserFavoriteBooks(userId, page);
        return { tab, page, userId, data: [] }
      case 'rating':
        // return await myLibraryService.getUserRatedBooks(userId, page);
        return { tab, page, userId, data: [] }
      case 'history':
        // return await myLibraryService.getUserReadingHistory(userId, page);
        return { tab, page, userId, data: [] }
      default:
        // return await myLibraryService.getUserListenedBooks(userId, page);
        return { tab, page, userId, data: [] }
    }
  } catch (error) {
    console.error('获取图书馆数据失败:', error);
    return {
      data: [],
      meta: {
        total: 0,
        page,
        limit: 12,
        totalPages: 0
      }
    };
  }
})

// ============================================================================
// 使用总结
// ============================================================================

/*
使用方式总结：

1. **简单认证**：
   export const myFunction = withAuth(async (param1, param2) => {
     const { userId } = getCurrentAuthUser()
     // 业务逻辑...
   })

2. **可选认证**：
   export const myFunction = withOptionalAuth(async (param1, param2) => {
     const userId = getCurrentUserId() // 可能为 null
     // 业务逻辑...
   })

3. **手动认证**：
   export async function myFunction(param1, param2) {
     const authUser = await getAuthUser()
     if (!authUser) return { error: 'Auth required' }
     // 业务逻辑...
   }

4. **迁移现有代码**：
   - 用 withAuth 包装函数
   - 删除手动的 token 获取和验证代码
   - 用 getCurrentAuthUser() 替换手动获取的 userId
   - 业务逻辑保持不变

优点：
- 不需要修改现有函数的参数签名
- 认证逻辑统一管理
- 类型安全
- 易于测试
- 支持多种使用场景
*/
