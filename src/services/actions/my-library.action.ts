'use server'

/**
 * My Library 页面 Server Actions
 * 负责处理客户端组件的数据请求
 */

import * as myLibraryService from '@/services/my-library.service';
import { withAuth, getCurrentAuthUser } from '@/lib/auth/withAuth';

/**
 * 获取用户图书馆数据
 * 使用 withAuth 装饰器简化认证逻辑
 * @param tab 标签页（listened, favorites, rating, history）
 * @param page 页码
 * @param locale 语言代码（如 'en', 'zh'）
 * @returns 分页数据
 */
export const getLibraryData = withAuth(async (tab: string, page: number, locale?: string) => {
  // 获取当前认证用户ID（由装饰器自动验证）
  const { userId } = getCurrentAuthUser();

  // 处理语言参数，将 locale 转换为 language 格式
  const language = locale === 'zh' ? 'zh-CN' : 'en';

  try {
    switch (tab) {
      case 'listened':
        return await myLibraryService.getUserListenedBooks(userId, page, 12, language);
      case 'favorites':
        return await myLibraryService.getUserFavoriteBooks(userId, page, 12, language);
      case 'rating':
        return await myLibraryService.getUserRatedBooks(userId, page, 12, language);
      case 'history':
        return await myLibraryService.getUserReadingHistory(userId, page, 12, language);
      default:
        return await myLibraryService.getUserListenedBooks(userId, page, 12, language);
    }
  } catch (error) {
    console.error('获取图书馆数据失败:', error);
    // 返回空数据
    return {
      data: [],
      meta: {
        total: 0,
        page,
        limit: 12,
        totalPages: 0
      }
    };
  }
})
