'use server'

/**
 * 分类页面 Server Actions
 * 负责处理客户端组件的数据请求
 */

import * as CategoryService from '@/services/category.service';
import { CategoryListItem } from '@/models/category.model';
import { BookListItem, PaginatedResult } from '@/types/book.types';

/**
 * 分类页面数据类型
 */
export interface CategoryPageData {
  category: CategoryListItem | null;
  books: PaginatedResult<BookListItem>;
  allCategories: CategoryListItem[];
}

/**
 * 获取分类页面数据
 * @param slug 分类别名
 * @param page 页码
 * @param limit 每页数量
 * @param language 语言
 * @param sortBy 排序方式
 */
export async function getCategoryPageData(
  slug: string,
  page: number = 1,
  limit: number = 12,
  language: string = 'zh-CN',
  sortBy: 'newest' | 'popular' | 'rating' = 'popular'
) {
  try {
    // 1. 根据 slug 获取分类 ID
    const category = await CategoryService.getCategoryBySlug(slug, language);

    if (!category) {
      return {
        success: false,
        error: 'Category not found'
      };
    }

    // 2. 获取分类下的书籍
    const books = await CategoryService.getCategoryBooks(
      category.id,
      page,
      limit,
      language,
      sortBy
    );

    // 3. 获取所有分类（用于侧边栏）- 使用与Header Browse相同的逻辑
    const allCategories = await CategoryService.getCategoriesForBrowse(language);

    return {
      success: true,
      data: {
        category,
        books,
        allCategories
      } as CategoryPageData
    };
  } catch (error) {
    console.error('获取分类页面数据失败:', error);
    return {
      success: false,
      error: 'Failed to load category data'
    };
  }
}
