'use server'

/**
 * 作者详情页 Server Actions
 * 负责处理客户端组件的数据请求
 */

import * as authorService from '@/services/author.service';
import { getLocale } from 'next-intl/server';
import { unstable_cache } from 'next/cache';

/**
 * 作者详情数据类型
 */
export interface AuthorData {
  author: {
    id: number;
    name: string;
    avatar: string | null;
    bio: string;
    twitter?: string;
    website?: string;
    viewCount: number;
  };
  books: {
    id: number;
    title: string;
    coverUrl: string;
  }[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * 热门作者数据类型
 */
export interface PopularAuthorData {
  id: number;
  name: string;
  avatar: string | null;
  bookCount: number;
}

/**
 * 获取作者详情数据
 * @param authorId 作者ID
 * @param page 页码
 * @param limit 每页数量
 * @returns 作者详情数据
 */
export async function getAuthorData(authorId: string, page: number = 1, limit: number = 12) {
  const locale = await getLocale();
  const language = locale || 'en';

  return unstable_cache(
    async () => {
      try {
        // 获取作者详情（使用纯数据获取函数，不包含副作用）
        const authorDetail = await authorService.getAuthorById(Number(authorId), language);

        if (!authorDetail) {
          return {
            success: false,
            error: 'Author not found'
          };
        }

        // 计算分页数据
        const totalBooks = authorDetail.books?.length || 0;
        const totalPages = Math.ceil(totalBooks / limit);

        // 获取当前页的书籍
        const paginatedBooks = authorDetail.books?.slice(
          (page - 1) * limit,
          page * limit
        ) || [];

        // 格式化返回数据
        return {
          success: true,
          data: {
            author: {
              id: authorDetail.id,
              name: authorDetail.translations.find(t => t.languageId === language)?.name ||
                authorDetail.translations.find(t => t.isDefault === 1)?.name ||
                `Author ${authorDetail.id}`,
              avatar: authorDetail.avatar_url || null,
              bio: authorDetail.translations.find(t => t.languageId === language)?.biography ||
                authorDetail.translations.find(t => t.isDefault === 1)?.biography || '',
              twitter: authorDetail.twitter_account || '',
              website: authorDetail.website || '',
              viewCount: Number(authorDetail.viewCount) || 0
            },
            books: paginatedBooks.map(book => ({
              id: book.id,
              title: book.title,
              coverUrl: book.coverUrl || 'https://placehold.co/176x256'
            })),
            pagination: {
              total: totalBooks,
              page,
              limit,
              totalPages
            }
          }
        };
      } catch (error) {
        console.error('Get author data failed:', error);
        return {
          success: false,
          error: 'Get author data failed'
        };
      }
    },
    [`author-data-${authorId}-${page}-${limit}-${language}`],
    {
      revalidate: 1800, // 30分钟
      tags: ['author-detail', `author-${authorId}`]
    }
  )();
}

/**
 * 获取热门作者数据
 * @param limit 数量限制
 * @returns 热门作者数据
 */
export async function getPopularAuthorsData(limit: number = 12) {
  const locale = await getLocale();
  const language = locale || 'en';

  return unstable_cache(
    async () => {
      try {
        // 获取热门作者
        const popularAuthors = await authorService.getPopularAuthors(limit, language);

        return {
          success: true,
          data: popularAuthors.map(author => ({
            id: author.id,
            name: author.name,
            avatar: author.avatar_url || null,
            bookCount: author.bookCount
          }))
        };
      } catch (error) {
        console.error('获取热门作者数据失败:', error);
        return {
          success: false,
          error: '获取热门作者数据失败',
          data: [] // 返回空数组，确保UI不会崩溃
        };
      }
    },
    [`popular-authors-${limit}-${language}`],
    {
      revalidate: 1800, // 30分钟
      tags: ['popular-authors']
    }
  )();
}
