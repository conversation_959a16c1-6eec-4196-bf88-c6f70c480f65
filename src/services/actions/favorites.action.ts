'use server'

/**
 * 用户收藏功能 Server Actions
 * 使用 withAuth 装饰器处理认证
 */

import { revalidateTag } from 'next/cache'
import { withAuth, withOptionalAuth, getCurrentAuthUser, getCurrentUserId } from '@/lib/auth/withAuth'
import * as userFavoritesService from '@/services/user-favorites.service'

/**
 * 获取用户收藏状态
 * 使用可选认证，未登录用户返回未收藏状态
 */
export const getFavoriteStatus = withOptionalAuth(async (bookId: number) => {
  try {
    const userId = getCurrentUserId()

    if (!userId) {
      return {
        success: true,
        isFavorited: false,
        isAuthenticated: false
      }
    }

    // 使用真实的数据库查询替换模拟数据
    const isFavorited = await userFavoritesService.getUserFavoriteStatus(userId, bookId)

    return {
      success: true,
      isFavorited,
      isAuthenticated: true
    }
  } catch (error) {
    console.error('获取收藏状态失败:', error)
    return {
      success: false,
      isFavorited: false,
      isAuthenticated: !!getCurrentUserId(),
      error: 'Server error'
    }
  }
})

/**
 * 切换收藏状态
 * 需要用户认证
 */
export const toggleFavorite = withAuth(async (bookId: number) => {
  try {
    const { userId } = getCurrentAuthUser()

    // 使用真实的数据库操作替换模拟数据
    const result = await userFavoritesService.toggleUserFavorite(userId, bookId)

    if (result.success) {
      // 清除相关缓存
      revalidateTag(`user-favorite-${userId}-${bookId}`)
      revalidateTag(`user-favorites`)
      revalidateTag(`user-${userId}`)
      revalidateTag(`book-${bookId}`)
      revalidateTag('user-library')
    }

    return result
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    return {
      success: false,
      isFavorited: false,
      error: 'Server error'
    }
  }
})

/**
 * 添加收藏
 * 需要用户认证
 */
export const addToFavorites = withAuth(async (bookId: number) => {
  try {
    const { userId } = getCurrentAuthUser()

    // 使用真实的数据库操作
    const result = await userFavoritesService.addUserFavorite(userId, bookId)

    if (result.success) {
      // 清除相关缓存
      revalidateTag(`user-favorite-${userId}-${bookId}`)
      revalidateTag(`user-favorites`)
      revalidateTag(`user-${userId}`)
      revalidateTag(`book-${bookId}`)
      revalidateTag('user-library')
    }

    return result
  } catch (error) {
    console.error('Add to favorites failed:', error)
    return {
      success: false,
      isFavorited: false,
      error: 'Server error'
    }
  }
})

/**
 * 移除收藏
 * 需要用户认证
 */
export const removeFromFavorites = withAuth(async (bookId: number) => {
  try {
    const { userId } = getCurrentAuthUser()

    // 使用真实的数据库操作
    const result = await userFavoritesService.removeUserFavorite(userId, bookId)

    if (result.success) {
      // 清除相关缓存
      revalidateTag(`user-favorite-${userId}-${bookId}`)
      revalidateTag(`user-favorites`)
      revalidateTag(`user-${userId}`)
      revalidateTag(`book-${bookId}`)
      revalidateTag('user-library')
    }

    return result
  } catch (error) {
    console.error('Remove from favorites failed:', error)
    return {
      success: false,
      isFavorited: true,
      error: 'Server error'
    }
  }
})

/**
 * 批量获取用户收藏状态
 * 用于需要检查多本书收藏状态的场景
 */
export const getBatchFavoriteStatus = withOptionalAuth(async (bookIds: number[]) => {
  try {
    const userId = getCurrentUserId()

    if (!userId || bookIds.length === 0) {
      return {
        success: true,
        favorites: bookIds.reduce((acc, id) => ({ ...acc, [id]: false }), {}),
        isAuthenticated: !!userId
      }
    }

    // 使用真实的数据库批量查询
    const favoritesMap = await userFavoritesService.getBatchUserFavoriteStatus(userId, bookIds)

    // 转换为对象格式
    const favorites: Record<number, boolean> = {}
    bookIds.forEach(bookId => {
      favorites[bookId] = favoritesMap.get(bookId) || false
    })

    return {
      success: true,
      favorites,
      isAuthenticated: true
    }
  } catch (error) {
    console.error('批量获取收藏状态失败:', error)
    return {
      success: false,
      favorites: {},
      isAuthenticated: !!getCurrentUserId(),
      error: 'Server error'
    }
  }
})

/**
 * 获取用户收藏统计
 * 需要用户认证
 */
export const getFavoriteStats = withAuth(async () => {
  try {
    const { userId } = getCurrentAuthUser()

    // 使用真实的数据库查询
    const stats = await userFavoritesService.getUserFavoriteStats(userId)

    return {
      success: true,
      stats
    }
  } catch (error) {
    console.error('获取收藏统计失败:', error)
    return {
      success: false,
      error: 'Server error'
    }
  }
})
