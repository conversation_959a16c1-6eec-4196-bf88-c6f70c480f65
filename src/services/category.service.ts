/**
 * 分类服务层
 * 负责业务逻辑处理，调用模型层获取数据
 */

import * as CategoryModel from '@/models/category.model';
import * as BookModel from '@/models/book.model';
import { CategoryDetail, CategoryListItem } from '@/models/category.model';
import { BookListItem, PaginatedResult } from '@/types/book.types';
import { unstable_cache } from 'next/cache';

/**
 * 获取分类列表
 * 使用unstable_cache缓存结果，30分钟更新一次
 */
export async function getCategories(language: string = 'en'): Promise<CategoryListItem[]> {
  return unstable_cache(
    async () => {
      return CategoryModel.getCategories(language);
    },
    [`categories-${language}`],
    {
      revalidate: 1800, // 30分钟
      tags: ['categories', `categories-${language}`]
    }
  )();
}

/**
 * 获取分类详情
 */
export async function getCategoryById(id: number): Promise<CategoryDetail | null> {
  return CategoryModel.getCategoryById(id);
}

/**
 * 获取Browse下拉菜单的分类列表
 * 使用unstable_cache缓存结果，30分钟更新一次
 */
export async function getCategoriesForBrowse(language: string = 'en'): Promise<CategoryListItem[]> {
  return unstable_cache(
    async () => {
      return CategoryModel.getCategoriesForBrowse(language);
    },
    [`categories-for-browse-${language}`],
    {
      revalidate: 1800, // 30分钟
      tags: ['categories', `categories-browse-${language}`]
    }
  )();
}

/**
 * 获取热门分类
 */
export async function getPopularCategories(language: string = 'en'): Promise<CategoryListItem[]> {
  return CategoryModel.getPopularCategories(language);
}

/**
 * 获取分类下的书籍
 */
export async function getCategoryBooks(
  categoryId: number,
  page: number = 1,
  limit: number = 10,
  language: string = 'en',
  sortBy: 'newest' | 'popular' | 'rating' = 'popular'
): Promise<PaginatedResult<BookListItem>> {
  return BookModel.getBooks({
    categoryId,
    page,
    limit,
    language,
    sortBy
  });
}

/**
 * 根据 slug 获取分类
 * 使用 name 字段匹配 slug（将 name 转换为小写并替换空格为连字符）
 */
export async function getCategoryBySlug(slug: string, language: string = 'en'): Promise<CategoryListItem | null> {
  return unstable_cache(
    async () => {
      return CategoryModel.getCategoryBySlug(slug, language);
    },
    [`category-by-slug-${slug}-${language}`],
    {
      revalidate: 1800, // 30分钟
      tags: ['category', `category-${slug}`]
    }
  )();
}
