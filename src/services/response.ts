import { ResponseCode } from '@/utils/constants'

export interface ApiResponse<T = SafeAny> {
  code: number
  message: string
  data: T
}

export interface PageResponse<T = SafeAny> extends ApiResponse<T[]> {
  meta: {
    pagination: {
      page: number
      pageSize: number
      total: number
      totalPages: number
    }
  }
}

export const successResponse = <T>(data: T): ApiResponse<T> => ({
  code: ResponseCode.Success,
  message: 'success',
  data
})

export const successPageResponse = <T>(
  data: T[],
  meta: PageResponse<T>['meta']
): PageResponse<T> => ({
  code: ResponseCode.Success,
  message: 'success',
  data,
  meta
})

/**
 * 错误响应
 * @param data 响应数据
 * @param code 响应码
 * @param message 响应消息
 * @returns 响应对象
 */
export const errorResponse = <T>(
  code: number = 500,
  message: string = 'error',
  data?: T
): ApiResponse<T> => ({
  code,
  message,
  data: data || ({} as T)
})
