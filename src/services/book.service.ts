'server-only'

/**
 * 书籍服务层
 * 负责业务逻辑处理，调用模型层获取数据
 */

import * as BookModel from '@/models/book.model';
import * as CategoryModel from '@/models/category.model';
import { BookQueryParams, BookListItem, PaginatedResult } from '@/types/book.types';
import { unstable_cache } from 'next/cache';
import BookDetailData from '@/app/[locale]/(book)/book-summary/[slug]/types';
import { parseBookSlug, isValidBookId, type BookUrlData } from '@/utils/book.utils';
import { BookUrlService } from '@/services/url.service';
import { CacheKeyGenerator, CacheTagGenerator, CacheConfig } from '@/services/cache.service';

/**
 * 获取书籍列表（分页）
 */
export async function getBooks(params: BookQueryParams): Promise<PaginatedResult<BookListItem>> {
  return BookModel.getBooks(params);
}

/**
 * 获取书籍详情（通过ID）
 */
export async function getBookById(id: number, language: string = 'en'): Promise<BookDetailData | null> {
  return unstable_cache(
    async () => {
      const book = await BookModel.getBookById(id, language);
      console.log('service getBookById ', id, book);
      return book;
    },
    [CacheKeyGenerator.bookDetail(id, language)], // 使用统一的缓存键生成器
    {
      revalidate: CacheConfig.BOOK_DETAIL_REVALIDATE, // 使用配置的缓存时间
      tags: CacheTagGenerator.book(id, language) // 使用统一的标签生成器
    }
  )();
}

/**
 * 获取书籍详情（通过slug）
 * 支持新格式 'title-id' 和旧格式 'id'
 */
export async function getBookBySlug(slug: string, language: string = 'en'): Promise<BookDetailData | null> {
  try {
    // 解析slug获取书籍ID
    const parsed = parseBookSlug(slug);

    if (!isValidBookId(parsed.id)) {
      console.error('Invalid book ID from slug:', slug, parsed.id);
      return null;
    }

    // 使用现有的getBookById函数，复用缓存逻辑
    return await getBookById(parsed.id, language);
  } catch (error) {
    console.error('Error parsing book slug:', slug, error);
    return null;
  }
}

/**
 * 获取热门书籍
 * 使用统一的缓存服务
 */
export async function getPopularBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return unstable_cache(
    async () => {
      return BookModel.getPopularBooks(limit, language);
    },
    [CacheKeyGenerator.popularBooks(limit, language)], // 使用统一的缓存键生成器
    {
      revalidate: CacheConfig.LIST_DATA_REVALIDATE, // 使用配置的缓存时间
      tags: CacheTagGenerator.popularBooks(language) // 使用统一的标签生成器
    }
  )();
}

/**
 * 获取最新书籍
 * 使用统一的缓存服务
 */
export async function getNewestBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return unstable_cache(
    async () => {
      return BookModel.getNewestBooks(limit, language);
    },
    [CacheKeyGenerator.newestBooks(limit, language)], // 使用统一的缓存键生成器
    {
      revalidate: CacheConfig.LIST_DATA_REVALIDATE, // 使用配置的缓存时间
      tags: CacheTagGenerator.newestBooks(language) // 使用统一的标签生成器
    }
  )();
}

/**
 * 获取月度精选书籍
 * 使用统一的缓存服务
 */
export async function getMonthlyPickedBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return unstable_cache(
    async () => {
      return BookModel.getMonthlyPickedBooks(limit, language);
    },
    [CacheKeyGenerator.monthlyPickedBooks(limit, language)], // 使用统一的缓存键生成器
    {
      revalidate: CacheConfig.LIST_DATA_REVALIDATE, // 使用配置的缓存时间
      tags: CacheTagGenerator.monthlyPickedBooks(language) // 使用统一的标签生成器
    }
  )();
}

/**
 * 获取相关书籍（同一作者或同一分类的其他书籍）
 */
export async function getRelatedBooks(bookId: number, limit: number = 6, language: string = 'en'): Promise<BookListItem[]> {
  const book = await BookModel.getBookById(bookId, language);

  if (!book) {
    return [];
  }

  // 获取同一作者的书籍
  const authorIds = book.authors.map((a: { id: string }) => parseInt(a.id));
  const categoryIds = book.categories.map((c: { id: string }) => parseInt(c.id));

  // 构建查询参数
  const params: BookQueryParams = {
    limit,
    language,
    sortBy: 'popular'
  };

  // 先尝试获取同一作者的其他书籍
  let relatedBooks: BookListItem[] = [];

  for (const authorId of authorIds) {
    if (relatedBooks.length >= limit) break;

    const authorParams = { ...params, authorId };
    const result = await BookModel.getBooks(authorParams);

    // 过滤掉当前书籍
    const otherBooks = result.data.filter(b => b.id !== bookId);

    relatedBooks = [...relatedBooks, ...otherBooks];
  }

  // 如果同一作者的书籍不足，再获取同一分类的书籍
  if (relatedBooks.length < limit) {
    for (const categoryId of categoryIds) {
      if (relatedBooks.length >= limit) break;

      const categoryParams = { ...params, categoryId };
      const result = await BookModel.getBooks(categoryParams);

      // 过滤掉当前书籍和已经添加的书籍
      const otherBooks = result.data.filter(
        b => b.id !== bookId && !relatedBooks.some(rb => rb.id === b.id)
      );

      relatedBooks = [...relatedBooks, ...otherBooks];
    }
  }

  // 如果还不足，获取热门书籍补充
  if (relatedBooks.length < limit) {
    const popularBooks = await BookModel.getPopularBooks(limit * 2, language);

    // 过滤掉当前书籍和已经添加的书籍
    const otherBooks = popularBooks.filter(
      b => b.id !== bookId && !relatedBooks.some(rb => rb.id === b.id)
    );

    relatedBooks = [...relatedBooks, ...otherBooks].slice(0, limit);
  }

  return relatedBooks;
}

/**
 * 获取首页数据
 * 使用unstable_cache缓存结果，30分钟更新一次
 */
export async function getHomePageData(language: string = 'en'): Promise<{
  popularBooks: BookListItem[];
  newestBooks: BookListItem[];
  monthlyPickedBooks: BookListItem[];
  // popularAuthors: <AUTHORS>
  popularCategories: CategoryModel.CategoryListItem[];
}> {
  // 使用unstable_cache包装原始函数，缓存30分钟
  return unstable_cache(
    async () => {
      // 并行获取数据
      const [popularBooks, newestBooks, monthlyPickedBooks, popularCategories] = await Promise.all([
        BookModel.getPopularBooks(10, language),
        BookModel.getNewestBooks(10, language),
        BookModel.getMonthlyPickedBooks(10, language),
        // AuthorModel.getPopularAuthors(6, language),
        CategoryModel.getPopularCategories(language)
      ]);

      return {
        popularBooks,
        newestBooks,
        monthlyPickedBooks,
        // popularAuthors,
        popularCategories
      };
    },
    [`home-page-data-${language}`], // 缓存键，基于语言参数
    {
      revalidate: 1800, // 30分钟 = 1800秒
      tags: ['homepage', `homepage-${language}`] // 添加标签以支持手动重新验证
    }
  )();
}

// 测试缓存函数
export async function getCacheData() {
  return unstable_cache(
    async () => {
      const date = new Date();

      return {
        time: date
      };
    },
    [`test-cache-data}`], // 缓存键，基于语言参数
    {
      revalidate: 10, // 30分钟 = 1800秒
      tags: ['test-cache'] // 添加标签以支持手动重新验证
    }
  )();
}

/**
 * 搜索书籍
 */
export async function searchBooks(query: string, page: number = 1, limit: number = 10, language: string = 'en'): Promise<PaginatedResult<BookListItem>> {
  return BookModel.getBooks({
    search: query,
    page,
    limit,
    language
  });
}

/**
 * 为书籍列表生成详情页面URL
 * 统一的链接生成服务，确保所有地方使用一致的URL格式
 */
export function generateBookUrls(books: BookListItem[]): Array<BookListItem & { detailUrl: string }> {
  return books.map(book => ({
    ...book,
    detailUrl: BookUrlService.generateDetailUrl({
      id: book.id,
      title: book.title
    })
  }));
}

/**
 * 为单个书籍生成详情页面URL
 */
export function generateBookUrl(book: BookUrlData): string {
  return BookUrlService.generateDetailUrl(book);
}

/**
 * 批量为书籍数据添加URL字段
 * 用于在服务层统一处理URL生成，避免在组件中重复逻辑
 */
export function enrichBooksWithUrls<T extends BookUrlData>(books: T[]): Array<T & { detailUrl: string }> {
  return books.map(book => ({
    ...book,
    detailUrl: BookUrlService.generateDetailUrl(book)
  }));
}

/**
 * 验证书籍slug的有效性
 * 用于API路由和中间件中的验证
 */
export function validateBookSlug(slug: string): { isValid: boolean; bookId?: number; error?: string } {
  try {
    const parsed = parseBookSlug(slug);

    if (!isValidBookId(parsed.id)) {
      return {
        isValid: false,
        error: `Invalid book ID: ${parsed.id}`
      };
    }

    return {
      isValid: true,
      bookId: parsed.id
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown parsing error'
    };
  }
}
