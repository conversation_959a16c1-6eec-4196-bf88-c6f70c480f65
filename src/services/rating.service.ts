'server-only'

/**
 * 评分服务层
 * 负责评分相关的业务逻辑处理，调用模型层获取数据
 * 包含缓存、事务处理和业务规则验证
 */

import * as RatingModel from '@/models/rating.model'
import { unstable_cache } from 'next/cache'

/**
 * 服务层返回的用户评分类型
 */
export interface UserRating {
  id: number
  score: number
  review_text?: string | null
  created_at: Date
  updated_at: Date
}

/**
 * 服务层返回的书籍评分统计类型
 */
export interface BookRatingStatistics {
  average_score: number
  total_ratings: number
  rating_distribution: {
    [key: number]: number // 1-5星的分布数量
  }
}

/**
 * 评分操作结果类型
 */
export interface RatingOperationResult {
  success: boolean
  data?: UserRating
  error?: string
  stats?: BookRatingStatistics
}

/**
 * 获取用户对特定书籍的评分（带缓存）
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns 用户评分数据或null
 */
export async function getUserBookRating(
  userId: number,
  bookId: number
): Promise<UserRating | null> {
  return unstable_cache(
    async () => {
      try {
        const rating = await RatingModel.getUserRating(userId, bookId)
        return rating
      } catch (error) {
        console.error('Service: Error fetching user rating:', error)
        return null
      }
    },
    [`user-rating-${userId}-${bookId}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['user-rating', `user-${userId}`, `book-${bookId}`, `rating-${userId}-${bookId}`]
    }
  )()
}

/**
 * 获取书籍评分统计信息（带缓存）
 * @param bookId 书籍ID
 * @returns 书籍评分统计
 */
export async function getBookRatingStatistics(
  bookId: number
): Promise<BookRatingStatistics> {
  return unstable_cache(
    async () => {
      try {
        const stats = await RatingModel.getBookRatingStats(bookId)
        return stats
      } catch (error) {
        console.error('Service: Error fetching book rating stats:', error)
        // 返回默认统计信息
        return {
          average_score: 0,
          total_ratings: 0,
          rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        }
      }
    },
    [`book-rating-stats-${bookId}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['book-rating-stats', `book-${bookId}`, 'rating-stats']
    }
  )()
}

/**
 * 提交用户评分
 * 包含完整的业务逻辑：验证、创建/更新评分、更新统计、缓存失效
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @param score 评分（1-5）
 * @param reviewText 评论内容（可选）
 * @returns 操作结果
 */
export async function submitRating(
  userId: number,
  bookId: number,
  score: number,
  reviewText?: string
): Promise<RatingOperationResult> {
  try {
    // 1. 业务规则验证
    if (!userId || userId <= 0) {
      return {
        success: false,
        error: 'Invalid user ID'
      }
    }

    if (!bookId || bookId <= 0) {
      return {
        success: false,
        error: 'Invalid book ID'
      }
    }

    if (score < 1 || score > 5) {
      return {
        success: false,
        error: 'Rating score must be between 1 and 5'
      }
    }

    // 2. 创建或更新评分
    const { rating, isNewRating } = await RatingModel.createOrUpdateRating(
      userId,
      bookId,
      score,
      reviewText
    )

    // 3. 更新书籍评分统计（只在新评分时增加计数）
    const stats = await RatingModel.updateBookRatingStats(bookId, isNewRating)

    // 4. 返回成功结果
    return {
      success: true,
      data: rating,
      stats: stats
    }
  } catch (error) {
    console.error('Service: Error submitting rating:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to submit rating'
    }
  }
}



/**
 * 批量获取用户对多本书籍的评分（带缓存）
 * @param userId 用户ID
 * @param bookIds 书籍ID数组
 * @returns 用户评分映射
 */
export async function getUserRatingsForBooks(
  userId: number,
  bookIds: number[]
): Promise<Record<number, UserRating>> {
  return unstable_cache(
    async () => {
      try {
        const ratings = await RatingModel.getUserRatingsForBooks(userId, bookIds)
        return ratings
      } catch (error) {
        console.error('Service: Error fetching user ratings for books:', error)
        return {}
      }
    },
    [`user-ratings-${userId}-${bookIds.sort().join('-')}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['user-ratings', `user-${userId}`, ...bookIds.map(id => `book-${id}`)]
    }
  )()
}

/**
 * 检查用户是否已对书籍评分
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns 是否已评分
 */
export async function hasUserRatedBook(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    const rating = await getUserBookRating(userId, bookId)
    return rating !== null
  } catch (error) {
    console.error('Service: Error checking if user rated book:', error)
    return false
  }
}

/**
 * 获取用户的平均评分
 * @param userId 用户ID
 * @returns 用户平均评分
 */
export async function getUserAverageRating(userId: number): Promise<number> {
  return unstable_cache(
    async () => {
      try {
        // 这里可以添加获取用户所有评分的逻辑
        // 暂时返回0，后续可以扩展
        return 0
      } catch (error) {
        console.error('Service: Error fetching user average rating:', error)
        return 0
      }
    },
    [`user-avg-rating-${userId}`],
    {
      revalidate: 1800, // 30分钟缓存
      tags: ['user-avg-rating', `user-${userId}`]
    }
  )()
}

/**
 * 获取书籍的评分趋势（最近的评分变化）
 * @param bookId 书籍ID
 * @param days 天数（默认30天）
 * @returns 评分趋势数据
 */
export async function getBookRatingTrend(
  bookId: number,
  days: number = 30
): Promise<{ date: string; average_score: number; count: number }[]> {
  return unstable_cache(
    async () => {
      try {
        // 这里可以添加获取评分趋势的逻辑
        // 暂时返回空数组，后续可以扩展
        return []
      } catch (error) {
        console.error('Service: Error fetching book rating trend:', error)
        return []
      }
    },
    [`book-rating-trend-${bookId}-${days}`],
    {
      revalidate: 3600, // 1小时缓存
      tags: ['book-rating-trend', `book-${bookId}`]
    }
  )()
}
