'server-only'

/**
 * My Library 页面服务层
 * 负责业务逻辑处理，调用模型层获取数据
 */

import { PaginatedResult } from '@/types/book.types';
import { unstable_cache } from 'next/cache';
import {
  MyLibraryBookItem
} from '@/types/my-library.types';
import * as myLibraryModel from '@/models/my-library.model';

/**
 * 处理书籍数据的通用函数
 * 使用 Promise.all 并行处理异步操作，提高性能
 *
 * @param sourceData 源数据（音频进度、收藏、评分或阅读历史）
 * @param bookIds 书籍ID列表
 * @param language 语言
 * @param formatItem 格式化单个项目的函数
 * @returns 格式化后的书籍数据
 */
async function processBookData<T>(
  sourceData: any[],
  bookIds: number[],
  language: string,
  formatItem: (item: any, book: any, bookRating: number) => T | null
): Promise<T[]> {
  // 如果没有书籍ID，返回空数组
  if (bookIds.length === 0) {
    return [];
  }

  // 获取书籍详情（包含 rate_score）
  const booksData = await myLibraryModel.getBooksByIds(bookIds, language);

  // 创建书籍ID到书籍数据的映射
  const booksMap = new Map();
  booksData.forEach((book: any) => {
    booksMap.set(book.id, book);
  });

  // 格式化数据
  return sourceData
    .map((item: any) => {
      const bookId = item.book_id;
      if (!bookId) return null;

      const book = booksMap.get(bookId);
      if (!book) return null;

      // 使用书籍表的 rate_score 字段
      const bookRating = Number(book.rate_score) || 0;

      // 使用传入的格式化函数处理每个项目
      return formatItem(item, book, bookRating);
    })
    .filter((item: any): item is NonNullable<typeof item> => item !== null);
}


/**
 * 获取用户已收听的书籍
 */
export async function getUserListenedBooks(
  userId: number,
  page: number = 1,
  limit: number = 12,
  language: string = 'zh-CN'
): Promise<PaginatedResult<MyLibraryBookItem>> {
  return unstable_cache(
    async () => {
      try {
        // 从 Model 层获取用户音频进度数据
        const { audioProgress, total } = await myLibraryModel.getUserAudioProgress(userId, page, limit);

        // 获取相关的书籍ID
        const bookIds = audioProgress.map((item: any) => item.book_id).filter(Boolean) as number[];

        // 处理书籍数据
        const books = await processBookData<MyLibraryBookItem>(
          audioProgress,
          bookIds,
          language,
          (item, book, bookRating) => {
            // 由于我们使用了 select 而不是 include，数据结构已经改变
            const translation = book.book_translations[0] || {};
            const cover = book.book_covers[0] || {};
            const authorData = book.book_authors[0]?.author?.author_translations[0] || {};
            const audioFile = book.audio_files[0] || {};

            // 安全地转换 Decimal 类型
            const totalSeconds = audioFile?.duration_seconds || 0;
            const positionSeconds = item.position_seconds ? Number(item.position_seconds) : 0;
            const remainingSeconds = totalSeconds - positionSeconds;

            // 格式化剩余时间
            const hours = Math.floor(remainingSeconds / 3600);
            const minutes = Math.floor((remainingSeconds % 3600) / 60);
            const seconds = remainingSeconds % 60;
            const timeLeft = hours > 0
              ? `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
              : `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // 直接使用数据库中已计算的进度百分比，确保与书籍详情页一致
            // 修复：之前重新计算进度导致显示错误，现在直接使用数据库存储的正确进度
            const progress = item.progress_percentage
              ? Math.round(Number(item.progress_percentage))
              : 0;

            return {
              id: book?.id || 0,
              title: translation?.title || '',
              author: authorData?.name || '',
              coverUrl: cover?.image_url || 'https://placehold.co/176x256',
              description: translation?.subtitle || '',
              rating: bookRating,
              progress,
              timeLeft
            };
          }
        );

        return {
          data: books,
          meta: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        };
      } catch (error) {
        console.error('获取用户已收听书籍失败:', error);
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        };
      }
    },
    [`user-listened-books-${userId}-${page}-${limit}-${language}`],
    {
      revalidate: 60, // 30分钟
      tags: ['user-library', `user-${userId}`]
    }
  )();
}

/**
 * 获取用户收藏的书籍
 */
export async function getUserFavoriteBooks(
  userId: number,
  page: number = 1,
  limit: number = 12,
  language: string = 'zh-CN'
): Promise<PaginatedResult<MyLibraryBookItem>> {
  return unstable_cache(
    async () => {
      try {
        // 从 Model 层获取用户收藏数据
        const { favorites, total } = await myLibraryModel.getUserFavorites(userId, page, limit);

        // 获取相关的书籍ID
        const bookIds = favorites.map((item: any) => item.book_id).filter(Boolean) as number[];

        // 处理书籍数据
        const books = await processBookData<MyLibraryBookItem>(
          favorites,
          bookIds,
          language,
          (_item, book, bookRating) => {
            // 由于我们使用了 select 而不是 include，数据结构已经改变
            const translation = book.book_translations[0] || {};
            const cover = book.book_covers[0] || {};
            const authorData = book.book_authors[0]?.author?.author_translations[0] || {};

            return {
              id: book?.id || 0,
              title: translation?.title || '',
              author: authorData?.name || '',
              coverUrl: cover?.image_url || 'https://placehold.co/176x256',
              description: translation?.subtitle || '',
              rating: bookRating
            };
          }
        );

        return {
          data: books,
          meta: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        };
      } catch (error) {
        console.error('获取用户收藏书籍失败:', error);
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        };
      }
    },
    [`user-favorite-books-${userId}-${page}-${limit}-${language}`],
    {
      revalidate: 60,
      tags: ['user-library', `user-${userId}`]
    }
  )();
}

/**
 * 获取用户评分的书籍
 */
export async function getUserRatedBooks(
  userId: number,
  page: number = 1,
  limit: number = 12,
  language: string = 'zh-CN'
): Promise<PaginatedResult<MyLibraryBookItem>> {
  return unstable_cache(
    async () => {
      try {
        // 从 Model 层获取用户评分数据
        const { userRatings, total } = await myLibraryModel.getUserRatings(userId, page, limit);

        // 获取相关的书籍ID
        const bookIds = userRatings.map((item: any) => item.book_id).filter(Boolean) as number[];

        // 处理书籍数据
        const books = await processBookData<MyLibraryBookItem>(
          userRatings,
          bookIds,
          language,
          (_item, book, bookRating) => {
            // 由于我们使用了 select 而不是 include，数据结构已经改变
            const translation = book.book_translations[0] || {};
            const cover = book.book_covers[0] || {};
            const authorData = book.book_authors[0]?.author?.author_translations[0] || {};

            return {
              id: book?.id || 0,
              title: translation?.title || '',
              author: authorData?.name || '',
              coverUrl: cover?.image_url || 'https://placehold.co/176x256',
              description: translation?.subtitle || '',
              rating: bookRating  // ✅ 修改：使用书籍的 rate_score 评分，而不是用户评分
            };
          }
        );

        return {
          data: books,
          meta: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        };
      } catch (error) {
        console.error('获取用户评分书籍失败:', error);
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        };
      }
    },
    [`user-rated-books-${userId}-${page}-${limit}-${language}`],
    {
      revalidate: 60,
      tags: ['user-library', `user-${userId}`]
    }
  )();
}

/**
 * 获取用户阅读历史
 */
export async function getUserReadingHistory(
  userId: number,
  page: number = 1,
  limit: number = 12,
  language: string = 'zh-CN'
): Promise<PaginatedResult<MyLibraryBookItem>> {
  return unstable_cache(
    async () => {
      try {
        // 从 Model 层获取用户阅读历史数据
        const { history, total } = await myLibraryModel.getUserReadingHistory(userId, page, limit);

        // 获取相关的书籍ID
        const bookIds = history.map((item: any) => item.book_id).filter(Boolean) as number[];

        // 处理书籍数据
        const books = await processBookData<MyLibraryBookItem>(
          history,
          bookIds,
          language,
          (item, book, bookRating) => {
            // 由于我们使用了 select 而不是 include，数据结构已经改变
            const translation = book.book_translations[0] || {};
            const cover = book.book_covers[0] || {};
            const authorData = book.book_authors[0]?.author?.author_translations[0] || {};

            return {
              id: book?.id || 0,
              title: translation?.title || '',
              author: authorData?.name || '',
              coverUrl: cover?.image_url || 'https://placehold.co/176x256',
              description: translation?.subtitle || '',
              rating: bookRating,
              lastReadAt: item.last_read_at ? new Date(item.last_read_at).toLocaleDateString() : ''
            };
          }
        );

        return {
          data: books,
          meta: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        };
      } catch (error) {
        console.error('获取用户阅读历史失败:', error);
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        };
      }
    },
    [`user-reading-history-${userId}-${page}-${limit}-${language}`],
    {
      revalidate: 60,
      tags: ['user-library', `user-${userId}`]
    }
  )();
}
