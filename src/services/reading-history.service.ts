'server-only'

/**
 * 阅读历史服务层
 * 负责业务逻辑处理，调用模型层获取数据
 * 添加缓存失效逻辑和业务规则验证
 */

import * as ReadingHistoryModel from '@/models/reading-history.model';
import { unstable_cache } from 'next/cache';

/**
 * 记录阅读历史服务
 * 添加缓存失效逻辑和业务验证
 * 
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否记录成功
 */
export async function recordReadingHistory(
  userId: number, 
  bookId: number
): Promise<boolean> {
  try {
    // 业务验证
    if (!userId || userId <= 0) {
      console.error('Invalid user ID for reading history:', userId);
      return false;
    }

    if (!bookId || bookId <= 0) {
      console.error('Invalid book ID for reading history:', bookId);
      return false;
    }

    // 记录阅读历史
    await ReadingHistoryModel.addOrUpdateReadingHistory(userId, bookId);
    
    // 可选：重新验证用户图书馆缓存
    // 这里可以添加缓存失效逻辑
    // revalidateTag(`user-library-${userId}`);
    // revalidateTag(`user-reading-history-${userId}`);
    
    return true;
  } catch (error) {
    console.error('Service: Failed to record reading history:', error);
    return false;
  }
}

/**
 * 删除阅读历史服务
 * 
 * @param userId 用户ID
 * @param bookIds 书籍ID数组
 * @returns Promise<boolean> 是否删除成功
 */
export async function deleteReadingHistoryService(
  userId: number, 
  bookIds: number[]
): Promise<boolean> {
  try {
    // 业务验证
    if (!userId || userId <= 0) {
      console.error('Invalid user ID for deleting reading history:', userId);
      return false;
    }

    if (!bookIds || !Array.isArray(bookIds) || bookIds.length === 0) {
      console.error('Invalid book IDs for deleting reading history:', bookIds);
      return false;
    }

    // 验证所有 bookId 都是有效的正整数
    const validBookIds = bookIds.filter(id => Number.isInteger(id) && id > 0);
    if (validBookIds.length === 0) {
      console.error('No valid book IDs provided for deletion');
      return false;
    }

    // 删除阅读历史
    const success = await ReadingHistoryModel.deleteReadingHistory(userId, validBookIds);
    
    if (success) {
      // 可选：重新验证用户图书馆缓存
      // revalidateTag(`user-library-${userId}`);
      // revalidateTag(`user-reading-history-${userId}`);
    }
    
    return success;
  } catch (error) {
    console.error('Service: Failed to delete reading history:', error);
    return false;
  }
}

/**
 * 检查阅读历史服务
 * 
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否存在阅读历史
 */
export async function checkReadingHistoryService(
  userId: number, 
  bookId: number
): Promise<boolean> {
  try {
    // 业务验证
    if (!userId || userId <= 0 || !bookId || bookId <= 0) {
      return false;
    }

    return await ReadingHistoryModel.hasReadingHistory(userId, bookId);
  } catch (error) {
    console.error('Service: Failed to check reading history:', error);
    return false;
  }
}

/**
 * 获取用户阅读历史统计服务
 * 带缓存的版本
 * 
 * @param userId 用户ID
 * @returns Promise<number> 阅读历史总数
 */
export async function getReadingHistoryStatsService(userId: number): Promise<number> {
  return unstable_cache(
    async () => {
      try {
        if (!userId || userId <= 0) {
          return 0;
        }

        return await ReadingHistoryModel.getReadingHistoryCount(userId);
      } catch (error) {
        console.error('Service: Failed to get reading history stats:', error);
        return 0;
      }
    },
    [`reading-history-stats-${userId}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['reading-history', `user-${userId}`]
    }
  )();
}

/**
 * 批量记录阅读历史服务
 * 用于批量操作场景
 * 
 * @param userId 用户ID
 * @param bookIds 书籍ID数组
 * @returns Promise<{ success: number; failed: number }> 成功和失败的数量
 */
export async function batchRecordReadingHistory(
  userId: number, 
  bookIds: number[]
): Promise<{ success: number; failed: number }> {
  let success = 0;
  let failed = 0;

  try {
    // 业务验证
    if (!userId || userId <= 0) {
      console.error('Invalid user ID for batch recording:', userId);
      return { success: 0, failed: bookIds.length };
    }

    if (!bookIds || !Array.isArray(bookIds) || bookIds.length === 0) {
      return { success: 0, failed: 0 };
    }

    // 验证并过滤有效的 bookId
    const validBookIds = bookIds.filter(id => Number.isInteger(id) && id > 0);
    
    // 并行处理所有记录操作
    const results = await Promise.allSettled(
      validBookIds.map(bookId => 
        ReadingHistoryModel.addOrUpdateReadingHistory(userId, bookId)
      )
    );

    // 统计结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        success++;
      } else {
        failed++;
        console.error(`Failed to record reading history for book ${validBookIds[index]}:`, result.reason);
      }
    });

    // 添加无效 bookId 到失败计数
    failed += bookIds.length - validBookIds.length;

    console.log(`Batch reading history recording completed: ${success} success, ${failed} failed`);
    
    return { success, failed };
  } catch (error) {
    console.error('Service: Failed to batch record reading history:', error);
    return { success: 0, failed: bookIds.length };
  }
}
