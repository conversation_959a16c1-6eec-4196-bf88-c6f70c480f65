/**
 * 作者服务层
 * 负责业务逻辑处理，调用模型层获取数据
 */

import * as AuthorModel from '@/models/author.model';
import { AuthorQueryParams, AuthorDetail, AuthorListItem } from '@/models/author.model';
import { generateAuthorUrls as generateAuthorUrlsUtil } from '@/utils/author.utils';

/**
 * 获取作者列表
 */
export async function getAuthors(params: AuthorQueryParams): Promise<{
  data: AuthorListItem[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> {
  return AuthorModel.getAuthors(params);
}

/**
 * 获取作者详情（纯数据获取，用于缓存）
 * 这个函数只负责获取数据，不处理浏览量统计和缓存失效
 */
export async function getAuthorById(id: number, language: string = 'en'): Promise<AuthorDetail | null> {
  return AuthorModel.getAuthorById(id, language);
}

/**
 * 获取热门作者
 */
export async function getPopularAuthors(limit: number = 10, language: string = 'en'): Promise<AuthorListItem[]> {
  return AuthorModel.getPopularAuthors(limit, language);
}

/**
 * 搜索作者
 */
export async function searchAuthors(query: string, page: number = 1, limit: number = 10, language: string = 'en'): Promise<{
  data: AuthorListItem[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> {
  return AuthorModel.getAuthors({
    search: query,
    page,
    limit,
    language
  });
}

/**
 * 为作者列表生成详情页面URL
 * 统一的链接生成服务，确保所有地方使用一致的URL格式
 */
export function generateAuthorUrls(authors: AuthorListItem[]): Array<AuthorListItem & { detailUrl: string }> {
  return generateAuthorUrlsUtil(authors.map(author => ({
    id: author.id,
    name: author.name
  }))).map((authorWithUrl, index) => ({
    ...authors[index],
    detailUrl: authorWithUrl.detailUrl
  }));
}
