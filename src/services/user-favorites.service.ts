'server-only'

/**
 * 用户收藏服务层
 * 负责业务逻辑处理，调用模型层获取数据
 */

import { unstable_cache } from 'next/cache'
import * as userFavoritesModel from '@/models/user-favorites.model'

/**
 * 服务层返回结果类型
 */
export interface FavoriteResult {
  success: boolean
  isFavorited: boolean
  error?: string
}

/**
 * 获取用户收藏状态（带缓存）
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否已收藏
 */
export async function getUserFavoriteStatus(
  userId: number, 
  bookId: number
): Promise<boolean> {
  return unstable_cache(
    async () => {
      return await userFavoritesModel.checkUserFavorite(userId, bookId)
    },
    [`user-favorite-${userId}-${bookId}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['user-favorites', `user-${userId}`, `book-${bookId}`]
    }
  )()
}

/**
 * 添加用户收藏
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<FavoriteResult> 操作结果
 */
export async function addUserFavorite(
  userId: number, 
  bookId: number
): Promise<FavoriteResult> {
  try {
    const success = await userFavoritesModel.addUserFavorite(userId, bookId)
    
    if (success) {
      return { success: true, isFavorited: true }
    } else {
      return { 
        success: false, 
        isFavorited: false, 
        error: 'Failed to add favorite' 
      }
    }
  } catch (error) {
    console.error('添加收藏失败:', error)
    return { 
      success: false, 
      isFavorited: false, 
      error: 'Server error' 
    }
  }
}

/**
 * 移除用户收藏
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<FavoriteResult> 操作结果
 */
export async function removeUserFavorite(
  userId: number, 
  bookId: number
): Promise<FavoriteResult> {
  try {
    const success = await userFavoritesModel.removeUserFavorite(userId, bookId)
    
    if (success) {
      return { success: true, isFavorited: false }
    } else {
      return { 
        success: false, 
        isFavorited: true, 
        error: 'Failed to remove favorite' 
      }
    }
  } catch (error) {
    console.error('移除收藏失败:', error)
    return { 
      success: false, 
      isFavorited: true, 
      error: 'Server error' 
    }
  }
}

/**
 * 切换用户收藏状态
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<FavoriteResult> 操作结果
 */
export async function toggleUserFavorite(
  userId: number, 
  bookId: number
): Promise<FavoriteResult> {
  try {
    const currentStatus = await userFavoritesModel.checkUserFavorite(userId, bookId)
    
    if (currentStatus) {
      // 当前已收藏，移除收藏
      return await removeUserFavorite(userId, bookId)
    } else {
      // 当前未收藏，添加收藏
      return await addUserFavorite(userId, bookId)
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    return { 
      success: false, 
      isFavorited: false, 
      error: 'Server error' 
    }
  }
}

/**
 * 批量获取用户收藏状态（带缓存）
 * @param userId 用户ID
 * @param bookIds 书籍ID列表
 * @returns Promise<Map<number, boolean>> 书籍ID到收藏状态的映射
 */
export async function getBatchUserFavoriteStatus(
  userId: number,
  bookIds: number[]
): Promise<Map<number, boolean>> {
  if (bookIds.length === 0) {
    return new Map()
  }

  return unstable_cache(
    async () => {
      return await userFavoritesModel.batchCheckUserFavorites(userId, bookIds)
    },
    [`user-favorites-batch-${userId}-${bookIds.sort().join('-')}`],
    {
      revalidate: 300, // 5分钟缓存
      tags: ['user-favorites', `user-${userId}`, ...bookIds.map(id => `book-${id}`)]
    }
  )()
}

/**
 * 获取用户收藏统计信息（带缓存）
 * @param userId 用户ID
 * @returns Promise<{count: number, recentBookIds: number[]}> 收藏统计信息
 */
export async function getUserFavoriteStats(
  userId: number
): Promise<{ count: number; recentBookIds: number[] }> {
  return unstable_cache(
    async () => {
      const [count, recentFavorites] = await Promise.all([
        userFavoritesModel.getUserFavoriteCount(userId),
        userFavoritesModel.getRecentFavorites(userId, 5)
      ])
      
      return {
        count,
        recentBookIds: recentFavorites.map(f => f.book_id)
      }
    },
    [`user-favorite-stats-${userId}`],
    {
      revalidate: 600, // 10分钟缓存
      tags: ['user-favorites', `user-${userId}`]
    }
  )()
}

/**
 * 获取书籍收藏统计信息（带缓存）
 * @param bookId 书籍ID
 * @returns Promise<number> 收藏该书籍的用户数量
 */
export async function getBookFavoriteStats(bookId: number): Promise<number> {
  return unstable_cache(
    async () => {
      return await userFavoritesModel.getBookFavoriteCount(bookId)
    },
    [`book-favorite-stats-${bookId}`],
    {
      revalidate: 1800, // 30分钟缓存
      tags: ['user-favorites', `book-${bookId}`]
    }
  )()
}

/**
 * 验证用户是否可以收藏指定书籍
 * 这里可以添加业务规则，比如用户等级限制、书籍状态检查等
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<{canFavorite: boolean, reason?: string}> 验证结果
 */
export async function validateUserCanFavorite(
  userId: number,
  bookId: number
): Promise<{ canFavorite: boolean; reason?: string }> {
  try {
    // 这里可以添加各种业务规则检查
    // 例如：
    // 1. 检查书籍是否存在且已发布
    // 2. 检查用户是否有权限收藏
    // 3. 检查用户收藏数量是否达到上限
    
    // 目前简单返回允许收藏
    return { canFavorite: true }
  } catch (error) {
    console.error('验证用户收藏权限失败:', error)
    return { 
      canFavorite: false, 
      reason: 'Validation failed' 
    }
  }
}
