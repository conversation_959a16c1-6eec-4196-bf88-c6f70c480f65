'server-only'

/**
 * 缓存服务
 * 统一管理应用中的缓存策略和键生成
 */

import { unstable_cache } from 'next/cache';

/**
 * 缓存键生成器
 */
export class CacheKeyGenerator {
  /**
   * 生成书籍详情缓存键
   */
  static bookDetail(id: number, language: string): string {
    return `book-${id}-${language}`;
  }

  /**
   * 生成书籍详情缓存键（通过slug）
   */
  static bookDetailBySlug(slug: string, language: string): string {
    return `book-slug-${slug}-${language}`;
  }

  /**
   * 生成热门书籍缓存键
   */
  static popularBooks(limit: number, language: string): string {
    return `popular-books-${limit}-${language}`;
  }

  /**
   * 生成最新书籍缓存键
   */
  static newestBooks(limit: number, language: string): string {
    return `newest-books-${limit}-${language}`;
  }

  /**
   * 生成月度精选书籍缓存键
   */
  static monthlyPickedBooks(limit: number, language: string): string {
    return `monthly-picked-books-${limit}-${language}`;
  }

  /**
   * 生成相关书籍缓存键
   */
  static relatedBooks(bookId: number, limit: number, language: string): string {
    return `related-books-${bookId}-${limit}-${language}`;
  }

  /**
   * 生成首页数据缓存键
   */
  static homePageData(language: string): string {
    return `home-page-data-${language}`;
  }

  /**
   * 生成搜索结果缓存键
   */
  static searchResults(query: string, page: number, limit: number, language: string): string {
    // 对查询字符串进行编码以避免特殊字符问题
    const encodedQuery = encodeURIComponent(query.toLowerCase().trim());
    return `search-${encodedQuery}-${page}-${limit}-${language}`;
  }

  /**
   * 生成分类页面数据缓存键
   */
  static categoryPageData(categorySlug: string, page: number, limit: number, language: string): string {
    return `category-${categorySlug}-${page}-${limit}-${language}`;
  }

  /**
   * 生成书籍列表缓存键
   */
  static bookList(params: Record<string, any>): string {
    // 将参数排序并序列化，确保相同参数生成相同的键
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as Record<string, any>);

    const paramString = JSON.stringify(sortedParams);
    const hash = this.simpleHash(paramString);
    return `book-list-${hash}`;
  }

  /**
   * 简单哈希函数，用于生成短的缓存键
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * 缓存标签生成器
 */
export class CacheTagGenerator {
  /**
   * 生成书籍相关标签
   */
  static book(id: number, language?: string): string[] {
    const tags = ['book-detail', `book-${id}`];
    if (language) {
      tags.push(`book-${language}`);
    }
    return tags;
  }

  /**
   * 生成热门书籍标签
   */
  static popularBooks(language: string): string[] {
    return ['popular-books', `popular-books-${language}`];
  }

  /**
   * 生成最新书籍标签
   */
  static newestBooks(language: string): string[] {
    return ['newest-books', `newest-books-${language}`];
  }

  /**
   * 生成月度精选书籍标签
   */
  static monthlyPickedBooks(language: string): string[] {
    return ['monthly-picked-books', `monthly-picked-books-${language}`];
  }

  /**
   * 生成首页数据标签
   */
  static homePageData(language: string): string[] {
    return ['homepage', `homepage-${language}`];
  }

  /**
   * 生成搜索结果标签
   */
  static searchResults(language: string): string[] {
    return ['search-results', `search-${language}`];
  }

  /**
   * 生成分类页面标签
   */
  static categoryPage(categorySlug: string, language: string): string[] {
    return ['category-page', `category-${categorySlug}`, `category-${language}`];
  }
}

/**
 * 缓存配置
 */
export const CacheConfig = {
  /**
   * 默认缓存时间（秒）
   */
  DEFAULT_REVALIDATE: 1800, // 30分钟

  /**
   * 书籍详情缓存时间
   */
  BOOK_DETAIL_REVALIDATE: 3600, // 1小时

  /**
   * 列表数据缓存时间
   */
  LIST_DATA_REVALIDATE: 1800, // 30分钟

  /**
   * 搜索结果缓存时间
   */
  SEARCH_RESULTS_REVALIDATE: 900, // 15分钟

  /**
   * 首页数据缓存时间
   */
  HOMEPAGE_REVALIDATE: 1800, // 30分钟
} as const;

/**
 * 缓存工具类
 */
export class CacheService {
  /**
   * 创建带缓存的函数
   */
  static cached<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator: (...args: Parameters<T>) => string,
    tagGenerator: (...args: Parameters<T>) => string[],
    revalidate: number = CacheConfig.DEFAULT_REVALIDATE
  ): T {
    return ((...args: Parameters<T>) => {
      const key = keyGenerator(...args);
      const tags = tagGenerator(...args);

      return unstable_cache(
        async () => fn(...args),
        [key],
        {
          revalidate,
          tags
        }
      )();
    }) as T;
  }

  /**
   * 为书籍详情创建缓存函数
   */
  static cachedBookDetail<T extends (id: number, language: string) => Promise<any>>(
    fn: T
  ): T {
    return this.cached(
      fn,
      (id: number, language: string) => CacheKeyGenerator.bookDetail(id, language),
      (id: number, language: string) => CacheTagGenerator.book(id, language),
      CacheConfig.BOOK_DETAIL_REVALIDATE
    );
  }

  /**
   * 为列表数据创建缓存函数
   */
  static cachedListData<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator: (...args: Parameters<T>) => string,
    tagGenerator: (...args: Parameters<T>) => string[]
  ): T {
    return this.cached(
      fn,
      keyGenerator,
      tagGenerator,
      CacheConfig.LIST_DATA_REVALIDATE
    );
  }

  /**
   * 为搜索结果创建缓存函数
   */
  static cachedSearchResults<T extends (query: string, page: number, limit: number, language: string) => Promise<any>>(
    fn: T
  ): T {
    return this.cached(
      fn,
      (query: string, page: number, limit: number, language: string) =>
        CacheKeyGenerator.searchResults(query, page, limit, language),
      (_query: string, _page: number, _limit: number, language: string) =>
        CacheTagGenerator.searchResults(language),
      CacheConfig.SEARCH_RESULTS_REVALIDATE
    );
  }
}

/**
 * 缓存失效工具
 */
export class CacheInvalidation {
  /**
   * 失效书籍相关缓存
   */
  static async invalidateBook(id: number): Promise<void> {
    // 这里可以添加缓存失效逻辑
    // Next.js 的 revalidateTag 可以在这里使用
    console.log(`Invalidating cache for book ${id}`);
  }

  /**
   * 失效列表缓存
   */
  static async invalidateListCaches(language: string): Promise<void> {
    console.log(`Invalidating list caches for language ${language}`);
  }

  /**
   * 失效搜索缓存
   */
  static async invalidateSearchCaches(language: string): Promise<void> {
    console.log(`Invalidating search caches for language ${language}`);
  }
}
