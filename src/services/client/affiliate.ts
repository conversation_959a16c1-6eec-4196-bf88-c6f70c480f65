'use client'
import apiClient from '@/utils/apiClient'

interface DistributionClickProps {
  queryRef: string
}

interface DistributionClickRes {
  data: {
    is_logged_in: boolean
  }
}

export async function distributionClick(data: DistributionClickProps) {
  try {
    const response = await apiClient
      .post(`distribution/click`, {
        json: {
          distribution_code: data.queryRef
        }
      })
      .json<DistributionClickRes>()
    // if (response.code !== ResponseCode.Success) return null;
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}
