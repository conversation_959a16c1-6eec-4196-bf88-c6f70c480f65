'use client'
import apiClient from '@/utils/apiClient'
import md5 from 'crypto-js/md5'
import { ApiResponse as Response } from '@/services/response'
import Cookies from 'js-cookie'

type LoginResponse = Response<User>

export const login = async (email: string, password: string): Promise<LoginResponse> => {
  const utm = Cookies.get('utm') || null
  return await apiClient
    .post('login', {
      json: {
        email,
        password: md5(password).toString(),
        utm,
        source: utm
      }
    })
    .json<LoginResponse>()
}

type RegisterResponse = Response<{ user: User }>

export const signup = async (email: string, password: string): Promise<RegisterResponse> => {
  const PROJECT_NAME = process.env.NEXT_PUBLIC_PROJECT
  const source = Cookies.get('utm') || null
  const refCode = Cookies.get(`${PROJECT_NAME}_REF_CODE`) || null
  const hashPassword = md5(password).toString()
  return await apiClient
    .post('signup', {
      json: {
        email,
        password: hashPassword,
        /** FIXME:
         * 后端文档错误先不修改(毕竟项目保持和coloringbook一致)
         * 并且UI上没有校验密码是否一致
         */
        comfirm_password: hashPassword,
        source,
        distribution_code: refCode
      }
    })
    .json<RegisterResponse>()
}

type CompleteSignupResponse = Response<SafeAny>

export const completeSignup = async (
  email: string,
  code: string
): Promise<CompleteSignupResponse> => {
  return await apiClient
    .post('complete-signup', {
      json: {
        email,
        activate_code: code
      }
    })
    .json<CompleteSignupResponse>()
}

export const resendActivationCode = async (email: string): Promise<CompleteSignupResponse> => {
  return await apiClient
    .post('resend-activate-email', {
      json: {
        email
      }
    })
    .json<CompleteSignupResponse>()
}

type ForgotPasswordResponse = Response<SafeAny>

/** just send reset-password url */
export const forgotPassword = async (email: string): Promise<ForgotPasswordResponse> => {
  return await apiClient
    .post('forgot-password', {
      json: {
        email
      }
    })
    .json<ForgotPasswordResponse>()
}

export interface ResetPasswordData {
  email: string
  password: string
  confirmPassword: string
  code: string
}

export const resetPassword = async (data: ResetPasswordData): Promise<ForgotPasswordResponse> => {
  const { email, password, confirmPassword, code } = data
  return await apiClient
    .post('reset-password', {
      json: {
        email,
        password: md5(password).toString(),
        comfirm_password: md5(confirmPassword).toString(),
        code
      }
    })
    .json<ForgotPasswordResponse>()
}

export const logout = async () => {
  return await apiClient.delete('logout')
}

type GoogleAuthResponse = Response<User>

export const googleAuth = async (token: string): Promise<GoogleAuthResponse> => {
  const source = Cookies.get('utm') || null
  return await apiClient
    .post('auth-google/login', {
      json: {
        code: token,
        source,
        redirect_uri: window.location.origin
      }
    })
    .json<GoogleAuthResponse>()
}

export const googleAuthCredential = async (token: string): Promise<GoogleAuthResponse> => {
  const source = Cookies.get('utm') || null
  return await apiClient
    .post('auth-google/login', {
      json: {
        credential: token,
        source,
        redirect_uri: window.location.origin
      }
    })
    .json<GoogleAuthResponse>()
}
