import apiClient from '@/utils/apiClient'
import { ApiResponse as Response } from '../response'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type SafeAny = any

/** client */
interface SubscribeData {
  planId: number
  successUrl: string
  cancelUrl: string
}

export const subscribe = async (data: SubscribeData): Promise<Response<SafeAny>> => {
  const response = await apiClient
    .post(`subscription`, {
      json: {
        plan_id: data.planId,
        success_url: data.successUrl,
        cancel_url: data.cancelUrl
      }
    })
    .json<SafeAny>()
  return response
}

export const session = async (sessionId: string) => {
  const response = await apiClient
    .get(`subscription/check-session`, {
      searchParams: {
        session_id: sessionId
      }
    })
    .json<SafeAny>()
  return response
}

/**
 * 打开Stripe订阅管理门户
 * 用于管理用户的订阅（如取消订阅）
 */
export const openStripePortal = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('stripe/portal')
    if (response.ok) {
      const data = await response.json<Response<{ url: string }>>()
      if (data?.data?.url) {
        window.open(data.data.url, '_blank')
        return true
      }
    }
    return false
  } catch (error) {
    console.error('Failed to open Stripe portal', error)
    return false
  }
}
