'server-only'
import apiServer from '@/utils/apiServer'
import { ResponseCode } from '@/utils/constants'
import { ApiResponse as Response } from '../response'

export type RankName = 'Free' | 'Starter' | 'Standard' | 'Premium'

interface Permission {
  image: {
    priority_prompts: number
    private: boolean
    remove_background: boolean
    remove_watermark: boolean
    upscale: boolean
  }
}

export interface PricePlan {
  duration: string
  first_price: number
  id: number
  is_actived: boolean
  max_online_users: number
  max_team_members: number
  original_price: number
  price: number
  rank_name: RankName
  trial_days: number
  permission: Permission
}

interface GroupPrice {
  '1 months': PricePlan
}

export type PriceGroup = {
  free: PricePlan
  groups: {
    [P in RankName]: P extends 'Free' ? PricePlan : GroupPrice
  }
  plans: PricePlan[]
}

export const getPrice = async (): Promise<PricePlan[] | null> => {
  try {
    const response = await apiServer
      .get(`price`, {
        searchParams: {
          group: 1
        }
      })
      .json<Response<PriceGroup>>()
    if (response.code !== ResponseCode.Success) return null
    const plans = [response.data.free, ...response.data.plans]
    return plans
  } catch (error) {
    console.warn(error)
    return null
  }
}
