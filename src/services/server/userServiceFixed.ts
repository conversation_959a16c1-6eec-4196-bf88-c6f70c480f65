'server-only'
import { cookies } from 'next/headers'
import { ResponseCode } from '@/utils/constants'
import { ApiResponse as Response } from '../response'

export type UserInfoResponse = Response<{
  user: User
  rank: Rank
  permissions_quota: PermissionQuota[]
}>

/**
 * 修复版本的 getUserInfo - 使用直接fetch而不是apiServer
 * 这个版本绕过了apiServer的Cookie传递问题
 */
export const getUserInfoFixed = async (): Promise<User | null> => {
  try {
    console.log('getUserInfoFixed: Starting request to user API')

    // 直接获取Cookie并构建请求
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')

    console.log('getUserInfoFixed: Cookie info:', {
      totalCookies: allCookies.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.value.length || 0,
      authTokenPreview: authToken?.value.substring(0, 50) + '...'
    })

    // 尝试多种Token传递方式
    const cookieString = allCookies
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ')

    // 方式1：通过Cookie传递（添加正确的域名头信息）
    console.log('getUserInfoFixed: Trying method 1 - Cookie header with domain info')
    let response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/common-api/v1/user`, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (compatible; Next.js Server)',
        'Referer': process.env.NEXT_PUBLIC_BASE_URL || '',
        'Origin': process.env.NEXT_PUBLIC_BASE_URL || '',
        'Host': '15minutes.ai',
        'X-Forwarded-Host': '15minutes.ai',
        'X-Forwarded-Proto': 'https'
      },
      credentials: 'include'
    })

    console.log('getUserInfoFixed: Method 1 response:', {
      status: response.status,
      statusText: response.statusText
    })

    // 如果方式1失败，尝试方式2：通过Authorization头传递
    if (!response.ok && authToken) {
      console.log('getUserInfoFixed: Trying method 2 - Authorization header')
      response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/common-api/v1/user`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken.value}`,
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Next.js Server)'
        }
      })

      console.log('getUserInfoFixed: Method 2 response:', {
        status: response.status,
        statusText: response.statusText
      })
    }

    // 如果方式2也失败，尝试方式3：通过X-Access-Token头传递
    if (!response.ok && authToken) {
      console.log('getUserInfoFixed: Trying method 3 - X-Access-Token header')
      response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/common-api/v1/user`, {
        method: 'GET',
        headers: {
          'X-Access-Token': authToken.value,
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Next.js Server)'
        }
      })

      console.log('getUserInfoFixed: Method 3 response:', {
        status: response.status,
        statusText: response.statusText
      })
    }

    console.log('getUserInfoFixed: Final HTTP response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    })

    if (!response.ok) {
      console.error('getUserInfoFixed: HTTP error:', response.status, response.statusText)
      return null
    }

    const data = await response.json() as UserInfoResponse

    console.log('getUserInfoFixed: API response received:', {
      code: data.code,
      message: data.message,
      hasUserData: !!data.data?.user,
      userEmail: data.data?.user?.email
    })

    if (data.code !== ResponseCode.Success) {
      console.warn('getUserInfoFixed: API returned error code:', data.code, data.message)
      return null
    }

    console.log('getUserInfoFixed: Successfully retrieved user data for:', data.data.user.email)
    return data.data.user

  } catch (error) {
    console.error('getUserInfoFixed: Request failed with error:', error)
    if (error instanceof Error) {
      console.error('getUserInfoFixed error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500)
      })
    }
    return null
  }
}

/**
 * 修复版本的 getUserQuota
 */
export const getUserQuotaFixed = async (): Promise<PermissionQuota | null> => {
  try {
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const cookieString = allCookies
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ')

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/common-api/v1/user`, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json() as UserInfoResponse
    if (data.code !== ResponseCode.Success) return null
    return data.data.permissions_quota?.[0] || null
  } catch (error) {
    console.warn(error)
    return null
  }
}

/**
 * 对比测试函数 - 同时测试原版和修复版
 */
export const compareUserInfoMethods = async () => {
  console.log('🔍 ===== Comparing getUserInfo methods =====')

  // 导入原版函数
  const { getUserInfo: originalGetUserInfo } = await import('./userService')

  // 测试原版
  console.log('📊 Testing original getUserInfo...')
  const originalResult = await originalGetUserInfo()

  // 测试修复版
  console.log('📊 Testing fixed getUserInfo...')
  const fixedResult = await getUserInfoFixed()

  const comparison = {
    original: {
      success: !!originalResult,
      email: originalResult?.email || null
    },
    fixed: {
      success: !!fixedResult,
      email: fixedResult?.email || null
    },
    bothWork: !!originalResult && !!fixedResult,
    onlyFixedWorks: !originalResult && !!fixedResult,
    onlyOriginalWorks: !!originalResult && !fixedResult,
    neitherWorks: !originalResult && !fixedResult
  }

  console.log('📊 Comparison result:', comparison)

  return {
    originalResult,
    fixedResult,
    comparison
  }
}
