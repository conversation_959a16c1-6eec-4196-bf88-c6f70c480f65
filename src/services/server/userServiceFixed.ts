'server-only'
import { cookies } from 'next/headers'
import { ResponseCode } from '@/utils/constants'
import { ApiResponse as Response } from '../response'

export type UserInfoResponse = Response<{
  user: User
  rank: Rank
  permissions_quota: PermissionQuota[]
}>

/**
 * 修复版本的 getUserInfo - 使用直接fetch而不是apiServer
 * 这个版本绕过了apiServer的Cookie传递问题
 */
export const getUserInfoFixed = async (): Promise<User | null> => {
  try {
    console.log('getUserInfoFixed: Starting request to user API')

    // 直接获取Cookie并构建请求
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')

    console.log('getUserInfoFixed: Cookie info:', {
      totalCookies: allCookies.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.value.length || 0,
      authTokenPreview: authToken?.value.substring(0, 50) + '...'
    })

    // 尝试多种Token传递方式
    const cookieString = allCookies
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ')

    // 方式1：通过Cookie传递（添加完整的浏览器环境头信息）
    console.log('getUserInfoFixed: Trying method 1 - Complete browser headers')

    // 从环境变量中提取域名
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.15minutes.ai'
    const urlObj = new URL(baseUrl)
    const domain = urlObj.hostname

    console.log('getUserInfoFixed: Request details:', {
      baseUrl,
      domain,
      cookieStringLength: cookieString.length,
      authTokenExists: !!authToken
    })

    let response = await fetch(`${baseUrl}/common-api/v1/user`, {
      method: 'GET',
      headers: {
        // Cookie信息
        'Cookie': cookieString,

        // 浏览器标识
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',

        // 请求来源信息（关键！）
        'Host': domain,
        'Origin': baseUrl,
        'Referer': `${baseUrl}/`,

        // 代理和转发信息（后端Cookie设置逻辑需要）
        'X-Forwarded-Host': domain,
        'X-Forwarded-Proto': 'https',
        'X-Forwarded-For': '127.0.0.1',

        // 其他可能需要的头
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
      },
      credentials: 'include'
    })

    console.log('getUserInfoFixed: Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: {
        contentType: response.headers.get('content-type'),
        setCookie: response.headers.get('set-cookie') ? '[PRESENT]' : '[NOT SET]'
      }
    })

    // 无论成功还是失败，都读取响应内容进行调试
    let responseText = ''
    let responseData = null

    try {
      responseText = await response.clone().text()
      console.log('getUserInfoFixed: Raw response text:', responseText)

      if (responseText) {
        responseData = JSON.parse(responseText)
        console.log('getUserInfoFixed: Parsed response data:', {
          code: responseData.code,
          message: responseData.message,
          hasData: !!responseData.data,
          dataType: typeof responseData.data
        })
      }
    } catch (parseError) {
      console.error('getUserInfoFixed: Failed to parse response:', parseError)
      console.log('getUserInfoFixed: Response text length:', responseText.length)
    }

    // 使用已经解析的响应数据
    if (!response.ok) {
      console.error('getUserInfoFixed: HTTP error:', {
        status: response.status,
        statusText: response.statusText,
        responseData: responseData
      })
      return null
    }

    // 如果没有解析到数据，尝试重新解析
    if (!responseData) {
      try {
        responseData = await response.json() as UserInfoResponse
      } catch (error) {
        console.error('getUserInfoFixed: Failed to parse JSON response:', error)
        return null
      }
    }

    console.log('getUserInfoFixed: Final API response analysis:', {
      code: responseData.code,
      message: responseData.message,
      hasUserData: !!responseData.data?.user,
      userEmail: responseData.data?.user?.email,
      dataStructure: responseData.data ? Object.keys(responseData.data) : 'no data'
    })

    if (responseData.code !== ResponseCode.Success) {
      console.warn('getUserInfoFixed: API returned error code:', {
        code: responseData.code,
        message: responseData.message,
        expectedCode: ResponseCode.Success
      })
      return null
    }

    if (!responseData.data?.user) {
      console.warn('getUserInfoFixed: No user data in successful response:', responseData)
      return null
    }

    console.log('getUserInfoFixed: Successfully retrieved user data for:', responseData.data.user.email)
    return responseData.data.user

  } catch (error) {
    console.error('getUserInfoFixed: Request failed with error:', error)
    if (error instanceof Error) {
      console.error('getUserInfoFixed error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500)
      })
    }
    return null
  }
}

/**
 * 修复版本的 getUserQuota
 */
export const getUserQuotaFixed = async (): Promise<PermissionQuota | null> => {
  try {
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const cookieString = allCookies
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ')

    // 使用与getUserInfoFixed相同的请求头配置
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.15minutes.ai'
    const urlObj = new URL(baseUrl)
    const domain = urlObj.hostname

    const response = await fetch(`${baseUrl}/common-api/v1/user`, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Host': domain,
        'Origin': baseUrl,
        'Referer': `${baseUrl}/`,
        'X-Forwarded-Host': domain,
        'X-Forwarded-Proto': 'https'
      },
      credentials: 'include'
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json() as UserInfoResponse
    if (data.code !== ResponseCode.Success) return null
    return data.data.permissions_quota?.[0] || null
  } catch (error) {
    console.warn(error)
    return null
  }
}

/**
 * 对比测试函数 - 同时测试原版和修复版
 */
export const compareUserInfoMethods = async () => {
  console.log('🔍 ===== Comparing getUserInfo methods =====')

  // 导入原版函数
  const { getUserInfo: originalGetUserInfo } = await import('./userService')

  // 测试原版
  console.log('📊 Testing original getUserInfo...')
  const originalResult = await originalGetUserInfo()

  // 测试修复版
  console.log('📊 Testing fixed getUserInfo...')
  const fixedResult = await getUserInfoFixed()

  const comparison = {
    original: {
      success: !!originalResult,
      email: originalResult?.email || null
    },
    fixed: {
      success: !!fixedResult,
      email: fixedResult?.email || null
    },
    bothWork: !!originalResult && !!fixedResult,
    onlyFixedWorks: !originalResult && !!fixedResult,
    onlyOriginalWorks: !!originalResult && !fixedResult,
    neitherWorks: !originalResult && !fixedResult
  }

  console.log('📊 Comparison result:', comparison)

  return {
    originalResult,
    fixedResult,
    comparison
  }
}
