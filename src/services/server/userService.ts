'server-only'
import apiServer from '@/utils/apiServer'
import { ResponseCode } from '@/utils/constants'
import { ApiResponse as Response } from '../response'
import { InvoiceRecord } from '@/app/[locale]/(home)/profile/components/PaymentRecords'

export type UserInfoResponse = Response<{
  user: User
  rank: Rank
  permissions_quota: PermissionQuota[]
}>
/** server */
export const getUserInfo = async (): Promise<User | null> => {
  try {
    console.log('getUserInfo: Starting request to user API')
    const response = await apiServer.get(`user`).json<UserInfoResponse>()

    console.log('getUserInfo: API response received:', {
      code: response.code,
      message: response.message,
      hasUserData: !!response.data?.user,
      userEmail: response.data?.user?.email
    })

    if (response.code !== ResponseCode.Success) {
      console.warn('getUserInfo: API returned error code:', response.code, response.message)
      return null
    }

    console.log('getUserInfo: Successfully retrieved user data for:', response.data.user.email)
    return response.data.user
  } catch (error) {
    console.error('getUserInfo: Request failed with error:', error)
    // 检查是否是网络错误、认证错误等
    if (error instanceof Error) {
      console.error('getUserInfo error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500) // 截断堆栈信息
      })
    }
    return null
  }
}

export const getUserQuota = async (): Promise<PermissionQuota | null> => {
  try {
    const response = await apiServer.get(`user`).json<UserInfoResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response.data.permissions_quota?.[0] || null
  } catch (error) {
    console.warn(error)
    return null
  }
}

export interface InvoiceData {
  data: InvoiceRecord[]
  from: number
  to: number
  total: number
}

interface InvoiceParams {
  per_page: number
  page: number
}

export const invoices = async (data: InvoiceParams) => {
  try {
    const response = await apiServer
      .get('invoices', {
        searchParams: {
          per_page: 10,
          page: data.page
        }
      })
      .json<Response<InvoiceData>>()
    if (response.code !== ResponseCode.Success) return null
    return response.data
  } catch (error) {
    console.warn(error)
    return null
  }
}
