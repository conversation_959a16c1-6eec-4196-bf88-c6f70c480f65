'server-only'
/** 消息队列服务封装 */
import { publishMessage, publishToExchange, PublishOptions } from '../../lib/rabbitmq/producer'
import { Consumer, ProcessStatus, ConsumeOptions } from '../../lib/rabbitmq/consumer'
import { ConsumeMessage } from 'amqplib'

// 特定消息类型的队列服务
export class QueueService<T> {
  private queueName: string

  constructor(queueName: string) {
    this.queueName = queueName
  }

  // 发送消息到队列
  async sendMessage(message: T, options?: PublishOptions): Promise<boolean> {
    return publishMessage<T>(this.queueName, message, options)
  }

  // 发送消息到交换机
  async sendToExchange(
    exchange: string,
    routingKey: string,
    message: T,
    options?: PublishOptions
  ): Promise<boolean> {
    return publishToExchange<T>(exchange, routingKey, message, options)
  }

  // 处理队列中的消息
  async processMessages(
    handler: (message: T) => Promise<ProcessStatus>,
    options?: ConsumeOptions
  ): Promise<void> {
    const consumer = new Consumer<T>(this.queueName)
    await consumer.consume(async (message: T, _originalMsg: ConsumeMessage) => {
      return handler(message)
    }, options)
  }

  // 处理来自交换机的消息
  async processFromExchange(
    exchange: string,
    patterns: string[],
    handler: (message: T) => Promise<ProcessStatus>,
    options?: ConsumeOptions
  ): Promise<void> {
    const consumer = new Consumer<T>(this.queueName)
    await consumer.consumeFromExchange(
      exchange,
      patterns,
      async (message: T, _originalMsg: ConsumeMessage) => {
        return handler(message)
      },
      options
    )
  }
}

// 创建类型化队列服务的工厂函数
export function createQueueService<T>(queueName: string): QueueService<T> {
  return new QueueService<T>(queueName)
}
