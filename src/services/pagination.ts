import { PrismaClient } from '@prisma/client'

// 定义分页参数接口
export interface PaginationParams {
  page?: number
  pageSize?: number
}

/** Prisma查询选项接口 */
export type PrismaQueryOptions<T> = {
  where?: T
  orderBy?: Record<string, 'asc' | 'desc'> | Array<Record<string, 'asc' | 'desc'>>
  select?: Record<string, boolean | Record<string, unknown>>
  include?: Record<string, boolean | Record<string, unknown>>
}

/**
 * 创建通用分页查询函数
 * @param prisma Prisma客户端实例
 * @returns 分页查询对象
 */
export function createPaginationHelper(prisma: PrismaClient) {
  return {
    /**
     * 执行分页查询
     * @param model Prisma模型名称
     * @param params 分页参数
     * @param options 查询选项
     * @returns 分页结果
     */
    async paginate<T extends keyof typeof prisma & string, U>(
      model: T,
      searchParams: URLSearchParams,
      options: PrismaQueryOptions<unknown> = {}
    ): Promise<{
      data: U[]
      meta: {
        pagination: {
          page: number
          pageSize: number
          total: number
          totalPages: number
        }
      }
    }> {
      // 获取Prisma模型
      const prismaModel = prisma[model]

      if (!prismaModel || typeof prismaModel !== 'object') {
        throw new Error(`Invalid Prisma model: ${model}`)
      }

      if (!('findMany' in prismaModel) || !('count' in prismaModel)) {
        throw new Error(`Model ${model} does not have required methods`)
      }
      // 默认分页参数
      const page = Math.max(Number(searchParams.get('page')) || 1, 1)
      const pageSize = Math.max(Number(searchParams.get('pageSize')) || 10, 1)

      // 计算跳过的记录数
      const skip = (page - 1) * pageSize

      // 查询数据和总数
      const [data, total] = await Promise.all([
        // @ts-expect-error 动态调用Prisma模型方法
        prismaModel.findMany({
          ...options,
          skip,
          take: pageSize
        }) as Promise<U[]>,
        // @ts-expect-error 动态调用Prisma模型方法
        prismaModel.count({
          where: options.where
        }) as Promise<number>
      ])

      // 返回分页响应
      return {
        data,
        meta: {
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }
    }
  }
}
