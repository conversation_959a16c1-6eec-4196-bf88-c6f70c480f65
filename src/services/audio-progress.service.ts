/**
 * 音频播放进度服务层
 */

import { prisma } from '@prisma/prisma';
import { PlaybackProgressData, PlaybackProgressQuery } from '@/types/audio-progress.types';
import { adaptProgressFromDatabase } from '@/lib/audio/progress-validation';

/**
 * 保存或更新播放进度
 */
export async function savePlaybackProgress(
  userId: number,
  data: PlaybackProgressData
): Promise<any> {
  const {
    bookId,
    currentTime,
    duration,
    progressPercentage,
    playbackRate = 1.0,
    volume = 1.0
  } = data;

  // 计算是否完成（进度超过95%认为完成）
  const isCompleted = progressPercentage >= 95;

  const result = await prisma.user_audio_progress.upsert({
    where: {
      user_id_book_id: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    },
    update: {
      position_seconds: currentTime,
      duration_seconds: duration,
      progress_percentage: progressPercentage,
      playback_rate: playbackRate,
      volume: volume,
      is_completed: isCompleted,
      last_listened_at: new Date(),
      updated_at: new Date()
    },
    create: {
      user_id: BigInt(userId),
      book_id: bookId,
      position_seconds: currentTime,
      duration_seconds: duration,
      progress_percentage: progressPercentage,
      playback_rate: playbackRate,
      volume: volume,
      is_completed: isCompleted,
      last_listened_at: new Date()
    }
  });

  return adaptProgressFromDatabase(result);
}

/**
 * 获取特定书籍的播放进度
 */
export async function getBookPlaybackProgress(
  userId: number,
  bookId: number
): Promise<any | null> {
  const result = await prisma.user_audio_progress.findUnique({
    where: {
      user_id_book_id: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    }
  });

  return result ? adaptProgressFromDatabase(result) : null;
}

/**
 * 获取用户所有播放进度
 */
export async function getUserPlaybackProgress(
  userId: number,
  query: PlaybackProgressQuery = {}
): Promise<any[]> {
  const { limit = 10, offset = 0, includeCompleted = true } = query;

  const results = await prisma.user_audio_progress.findMany({
    where: {
      user_id: BigInt(userId),
      ...(includeCompleted ? {} : { is_completed: false })
    },
    orderBy: {
      last_listened_at: 'desc'
    },
    take: limit,
    skip: offset,
    include: {
      book: {
        select: {
          id: true,
          book_translations: {
            select: {
              title: true
            },
            where: {
              is_default: 1
            },
            take: 1
          },
          book_covers: {
            select: {
              image_url: true
            },
            where: {
              is_primary: 1
            },
            take: 1
          }
        }
      }
    }
  });

  return results.map(result => adaptProgressFromDatabase(result));
}

/**
 * 标记为已完成
 */
export async function markProgressAsCompleted(
  userId: number,
  bookId: number
): Promise<any> {
  const result = await prisma.user_audio_progress.update({
    where: {
      user_id_book_id: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    },
    data: {
      is_completed: true,
      progress_percentage: 100,
      last_listened_at: new Date(),
      updated_at: new Date()
    }
  });

  return adaptProgressFromDatabase(result);
}

/**
 * 删除播放进度
 */
export async function deletePlaybackProgress(
  userId: number,
  bookId: number
): Promise<void> {
  await prisma.user_audio_progress.delete({
    where: {
      user_id_book_id: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    }
  });
}
