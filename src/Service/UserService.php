<?php

declare(strict_types=1);

namespace Website\Common\Service;

use Carbon\Carbon;
use GuzzleHttp\Psr7\Query;
use Hyperf\Cache\Annotation\Cacheable;
use Lete\Pay\Cashier;
use Lete\Pay\Customer;
use Website\Common\Amqp\Producer\SyncUserQuotaProducer;
use Website\Common\Constants\ErrorCode;
use Website\Common\Model\DistributionCode;
use Website\Common\Model\DistributionInfo;
use Website\Common\Model\FrontendApiSecretKey;
use Website\Common\Model\Order;
use Website\Common\Model\Subscription;
use Website\Common\Model\TeamInvite;
use Website\Common\Model\TeamMember;
use Website\Common\Model\TeamRole;
use Website\Common\Utils\Alarm;
use Website\Common\Utils\Event;
use Website\Common\Utils\Website;
use Website\Common\Exception\UserException;
use Website\Common\Model\User;
use Website\Common\Model\UserQuota;
use Website\Common\Model\UserSocialite;
use Website\Common\Amqp\Producer\EMailProducer;
use Hyperf\Amqp\Producer;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Parallel;
use Hyperf\Utils\Str;
use Lete\Base\Utils\IP;
use Lete\Base\Utils\Timestamp;
use Lete\MongoDB\MongoClient\MongoDb;

class UserService
{
    /**
     * 登录类型-手动登录
     * @var string
     */
    const LOGIN_MODE_MANUAL = '手动登录';

    /**
     * 登录类型-自动登录
     * @var string
     */
    const LOGIN_MODE_AUTOMATIC = '自动登录';

    /**
     * 通过验证码重置密码
     * @param string $account 账号
     * @param string $password 密码
     * @param string $code 验证码
     * @param int $account_type 账号类型
     * @throws UserException
     */
    public static function resetPasswordByCode(string $account, string $password, string $code, int $account_type = UserSocialite::TYPE_EMAIL)
    {
        switch ($account_type) {
            case UserSocialite::TYPE_EMAIL:
                $UserSocialite = UserSocialite::query()
                    ->where('type', UserSocialite::TYPE_EMAIL)
                    ->where('socialite_id', $account)
                    ->first();
                if (!$UserSocialite) {
                    throw new UserException(ErrorCode::USER_EMAIL_NOT_REGISTERED);
                }

                $FrontendRedis = Website::FrontendRedis();
                $redis_key = self::forgotPasswordEmailRedisKey($UserSocialite->user_id);
                if ($FrontendRedis->get($redis_key) !== $code) {
                    throw new UserException(ErrorCode::RESET_PASSWORD_INVALID_CODE);
                }
                $User = User::query()->find($UserSocialite->user_id);
                $User->update([
                    'password' => $password,
                ]);
                $FrontendRedis->del($redis_key);
                break;
            default:
                break;
        }
    }

    /**
     * 发送忘记密码邮件
     * @param string $email 邮箱
     * @throws UserException
     */
    public static function sendForgotPasswordEmail(string $email)
    {
        $User = User::query()
            ->where('account', $email)
            ->first();
        if (!$User) {
            throw new UserException(ErrorCode::USER_EMAIL_NOT_REGISTERED);
        }
        $UserSocialite = UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->first();
        if (!$UserSocialite) {
            // 通过快速登录的，账号没有设置过密码
            if (UserSocialite::query()
                ->where('user_id', $User->id)
                ->where('type', '<>', UserSocialite::TYPE_EMAIL)
                ->exists()) {
                throw new UserException(ErrorCode::RESET_PASSWORD_EMPTY_PASSWORD);
            }

            throw new UserException(ErrorCode::USER_EMAIL_IS_NOT_ACTIVATED);
        }

        self::precheckSendEmail((int) $UserSocialite->user_id);

        // 有效时间3小时
        $FrontendRedis = Website::FrontendRedis();
        $redis_key = self::forgotPasswordEmailRedisKey($UserSocialite->user_id);
        $ttl = 3 * 3600;
        $code = Str::random(32);
        $FrontendRedis->setex($redis_key, $ttl, $code);

        $Producer = ApplicationContext::getContainer()->get(Producer::class);
        $Producer->produce(new EMailProducer([
            'email' => $email,
            'subject' => 'Reset Password',
            'template' => 'mail/forgot-password',
            'view_data' => [
                'email' => $email,
                'code' => $code,
                'reset_url' => env('WEBSITE_FRONTEND_BASE_URI') . "/resetPassword?". Query::build([
                        'type' => 'forget',
                        'email' => $email,
                        'code' => $code,
                    ]),
            ],
        ]));
    }

    /**
     * @param int $user_id 用户id
     * @return string
     */
    public static function forgotPasswordEmailRedisKey($user_id)
    {
        return env('APP_NAME') . ":email:forgot-password:{$user_id}";
    }

    /**
     * 通过谷歌账号注册登录
     * @param array $payload 谷歌账号信息
     * @param array $others 其它属性
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function loginByGoogle(array $payload, array $others = [])
    {
        $socialite_id = $payload['sub'] ?? null;
        $UserSocialite = UserSocialite::query()
            ->where('type', UserSocialite::TYPE_GOOGLE)
            ->where('socialite_id', $socialite_id)
            ->first();
        if ($UserSocialite) {
            return User::query()->find($UserSocialite->user_id);
        }

        // 注册
        try {
            Db::beginTransaction();

            $User = User::query()
                ->where('account', $payload['email'])
                ->first();
            $new_registered_user = false;
            if (!$User) {
                $new_registered_user = true;

                // 校验分销码是否存在
                if (!empty($others['distribution_code'])) {
                    $distribution_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('id');
                    if (empty($distribution_id)) {
                        unset($others['distribution_code']);
                    }
                }

                $User = User::create(array_merge([
                    'account' => $payload['email'],
                    'username' => $payload['name'],
                    'email' => $payload['email'],
                    'account_status' => User::ACCOUNT_STATUS_ACTIVATED,
                    'avatar' => $payload['picture'] ?? null,
                ], $others))->refresh();

                // 通过分销码的注册数加一
                if (!empty($others['distribution_code'])) {
                    $code_user_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('user_id');
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('register_num');
                }
            } else {
                if ($User->account_status === User::ACCOUNT_STATUS_DISABLED) {
                    throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
                }

                if (!$User->avatar && !empty($payload['picture'])) {
                    $User->update([
                        'avatar' => $payload['picture'],
                    ]);
                }
            }

            UserSocialite::updateOrCreate([
                'type' => UserSocialite::TYPE_GOOGLE,
                'socialite_id' => $socialite_id,
            ], [
                'socialite_raw_data' => $payload,
                'user_id' => $User->id,
            ]);

            if ($new_registered_user && Website::config()->get('website.user_account.register.send_registration_successful_email', false)) {
                self::sendRegistrationSuccessfulEmail($payload['email']);
            }

            Db::commit();
            return $User;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }

    /**
     * 通过邮箱注册登录（无需密码）
     * @param string $email 邮箱
     * @param array $others 其它属性
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function loginByEmailWithoutPassword(string $email, array $others = [])
    {
        $UserSocialite = UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->first();
        if ($UserSocialite) {
            return User::query()->find($UserSocialite->user_id);
        }

        // 注册
        try {
            Db::beginTransaction();

            $User = User::query()
                ->where('account', $email)
                ->first();
            if (!$User) {
                // 校验分销码是否存在
                if (!empty($others['distribution_code'])) {
                    $distribution_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('id');
                    if (empty($distribution_id)) {
                        unset($others['distribution_code']);
                    }
                }

                $User = User::create(array_merge([
                    'account' => $email,
                    'username' => $email,
                    'email' => $email,
                    'account_status' => User::ACCOUNT_STATUS_ACTIVATED,
                ], $others))->refresh();

                // 通过分销码的注册数加一
                if (!empty($others['distribution_code'])) {
                    $code_user_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('user_id');
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('register_num');
                }
            } else {
                if ($User->account_status === User::ACCOUNT_STATUS_DISABLED) {
                    throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
                }
            }

            UserSocialite::firstOrCreate([
                'type' => UserSocialite::TYPE_EMAIL,
                'socialite_id' => $email,
            ], [
                'user_id' => $User->id,
            ]);

            Db::commit();

            if (Website::config()->get('website.user_account.register.send_registration_successful_email', false)) {
                self::sendRegistrationSuccessfulEmail($email);
            }

            return $User;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }

    /**
     * 通过登录
     * @param string $email 邮箱
     * @param string $password 密码
     * @return User
     * @throws UserException
     */
    public static function loginByEmail(string $email, string $password)
    {
        $User = User::query()
            ->where('account', $email)
            ->first();
        if (!$User) {
            throw new UserException(ErrorCode::USER_EMAIL_NOT_REGISTERED);
        }
        if ($User->account_status === User::ACCOUNT_STATUS_DISABLED) {
            throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
        }

        // 未激活的邮箱账号，需要更新密码，发送激活邮件
        if (!UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->count()) {
            $User->update([
                'password' => $password,
            ]);
            // 是否需要邮箱验证
            if (Website::config()->get('website.user_account.register.validate_email', true)) {
                try {
                    self::sendActivateEmail((int) $User->id, $email);
                } catch (UserException $e) {
                    if ($e->getCode() !== ErrorCode::SEND_EMAIL_FREQUENT) {
                        throw $e;
                    }
                }
            }
            throw new UserException(ErrorCode::USER_EMAIL_IS_NOT_ACTIVATED);
        }

        if ($User && (is_null($User->password) || $User->password !== md5("{$User->salt}-{$password}"))) {
            throw new UserException(ErrorCode::USER_PASSWORD_NOT_MATCH);
        }
        return $User;
    }

    /**
     * 通过邮箱注册
     * @param string $email 邮箱
     * @param string $password 密码
     * @param array $others 其它属性
     * @param bool|null $validate_email 是否需要邮箱验证
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function signupByEmail(string $email, string $password, array $others = [], $validate_email = null)
    {
        if (UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->count()) {
            throw new UserException(ErrorCode::USER_EMAIL_EXISTS);
        }

        // 是否需要邮箱验证
        if (is_null($validate_email)) {
            $validate_email = Website::config()->get('website.user_account.register.validate_email', true);
        }

        try {
            Db::beginTransaction();

            $User = User::query()
                ->where('account', $email)
                ->first();
            if ($User) {
                if ($User->account_status === User::ACCOUNT_STATUS_DISABLED) {
                    throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
                }
                $User->update([
                    'password' => $password,
                ]);
            } else {
                // 校验分销码是否存在
                if (!empty($others['distribution_code'])) {
                    $distribution_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('id');
                    if (empty($distribution_id)) {
                        unset($others['distribution_code']);
                    }
                }

                $User = User::create(array_merge([
                    'account' => $email,
                    'username' => $others['username'] ?? $email,
                    'email' => $email,
                    'password' => $password,
                    'account_status' => $validate_email ? User::ACCOUNT_STATUS_UNACTIVATED : User::ACCOUNT_STATUS_ACTIVATED,
                ], $others))->refresh();

                // 通过分销码的注册数加一
                if (!empty($others['distribution_code'])) {
                    $code_user_id = DistributionCode::query()->where('distribution_code', '=', $others['distribution_code'])->value('user_id');
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('register_num');
                }
            }

            // 不验证邮箱
            if (!$validate_email) {
                UserSocialite::create([
                    'type' => UserSocialite::TYPE_EMAIL,
                    'socialite_id' => $email,
                    'user_id' => $User->id,
                ]);
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }

        if ($validate_email) {
            try {
                self::sendActivateEmail($User->id, $email);
            } catch (UserException $e) {
                if ($e->getCode() !== ErrorCode::SEND_EMAIL_FREQUENT) {
                    throw $e;
                }
            }
        } else {
            if (Website::config()->get('website.user_account.register.send_registration_successful_email', false)) {
                self::sendRegistrationSuccessfulEmail($email);
            }
        }
        return $User;
    }

    /**
     * @param $user_id
     * @return string
     */
    public static function emailActivateCodeRedisKey($user_id)
    {
        return env('APP_NAME') . ":email:activate-code:{$user_id}";
    }

    /**
     * 发送邮件前检查，是否触发频率限制
     * @param int $user_id 用户id
     * @throws UserException
     */
    public static function precheckSendEmail(int $user_id)
    {
        // 发送频率限制，1分钟1次
        $frequency_limitation_key = env('APP_NAME') . ":email-frequency-limitation:{$user_id}";
        $FrontendRedis = Website::FrontendRedis();
        if ($FrontendRedis->incr($frequency_limitation_key) > 1) {
            $FrontendRedis->decr($frequency_limitation_key);
            $remaining_time = $FrontendRedis->ttl($frequency_limitation_key);
            throw new UserException(ErrorCode::SEND_EMAIL_FREQUENT, "Frequent operations, please try again in {$remaining_time} seconds.");
        }
        if ($FrontendRedis->ttl($frequency_limitation_key) === -1) {
            $FrontendRedis->expire($frequency_limitation_key, 60);
        }
    }

    /**
     * 发送注册激活邮件
     * @param int $user_id 用户id
     * @param string $email 邮箱
     * @throws UserException
     */
    public static function sendActivateEmail(int $user_id, string $email)
    {
        self::precheckSendEmail($user_id);

        $UserSocialite = UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->first();
        if ($UserSocialite) {
            throw new UserException(ErrorCode::USER_EMAIL_IS_ACTIVATED);
        }

        // 验证码4位数字，有效时间48小时
        $FrontendRedis = Website::FrontendRedis();
        $activate_code = rand(1000, 9999);
        $redis_key = self::emailActivateCodeRedisKey($user_id);
        $ttl = 48 * 3600;
        $FrontendRedis->setex($redis_key, $ttl, $activate_code);

        $Producer = ApplicationContext::getContainer()->get(Producer::class);
        $Producer->produce(new EMailProducer([
            'email' => $email,
            'subject' => 'Complete your registration',
            'template' => 'mail/activate-code',
            'view_data' => [
                'activate_code' => $activate_code
            ],
        ]));
    }

    /**
     * 完成注册
     * @param string $email 邮箱
     * @param string $activate_code 激活码
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function completeSignupByEmail(string $email, string $activate_code)
    {
        $UserSocialite = UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->first();
        if ($UserSocialite) {
            throw new UserException(ErrorCode::USER_EMAIL_IS_ACTIVATED);
        }

        $User = User::query()
            ->where('account', $email)
            ->first();
        $FrontendRedis = Website::FrontendRedis();
        $redis_key = self::emailActivateCodeRedisKey($User->id);
        if ($FrontendRedis->get($redis_key) !== $activate_code) {
            throw new UserException(ErrorCode::ACCOUNT_ACTIVATE_CODE_INVALID);
        }

        try {
            Db::beginTransaction();

            UserSocialite::create([
                'type' => UserSocialite::TYPE_EMAIL,
                'socialite_id' => $email,
                'user_id' => $User->id,
            ]);

            $User->update([
                'email_verified_at' => time(),
                'account_status' => User::ACCOUNT_STATUS_ACTIVATED,
            ]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
        $FrontendRedis->del($redis_key);
        self::sendRegistrationSuccessfulEmail($email);
        return $User;
    }

    /**
     * 发送注册成功的邮件
     * @param $email
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function sendRegistrationSuccessfulEmail($email)
    {
        $Producer = ApplicationContext::getContainer()->get(Producer::class);
        $Producer->produce(new EMailProducer([
            'email' => $email,
            'subject' => 'Welcome to ' . Website::config()->get('website.name', ''),
            'template' => 'mail/account-activation-successful',
            'view_data' => [],
        ]));
    }

    /**
     * 登录日志
     * @param int $user_id
     * @param string|null $ip
     * @param array $others
     */
    public static function markLogin(int $user_id, string $ip = null, array $others = [])
    {
        $data = array_merge([
            'user_id' => $user_id,
            'ip' => $ip,
            'ip_location' => IP::location($ip),
            'created_at' => time(),
        ], $others);
        MongoDb::collection('user_login_logs')
            ->insertOne($data);

        if ($data['login_status']) {
            if ($User = User::query()->find($user_id)) {
                $user_values = [
                    'last_ip' => $data['ip'],
                    'last_location' => $data['ip_location'],
                    'last_at' => $data['created_at'],
                ];
                isset($data['device_language']) && $user_values['device_language'] = $data['device_language'];
                isset($data['device_timezone']) && $user_values['device_timezone'] = $data['device_timezone'];
                $User->update($user_values);
            }
        }
    }

    /**
     * 用户每日数据统计
     * @param $date
     * @return array
     */
    public static function dailyStatistics(string $date)
    {
        $date_start = Timestamp::dateStart($date);
        $date_end = Timestamp::dateEnd($date);
        $values= [
            'total' => 0,
            'new_users' => 0,
            'active_user_ids' => [],
        ];

        $Parallel = new Parallel(3);

        $Parallel->add(function () use (&$values) {
            $values['total'] = User::query()
                ->count();
        });

        $Parallel->add(function () use (&$values, $date_start, $date_end) {
            $values['new_users'] = User::query()
                ->where('created_at', '>=', $date_start)
                ->where('created_at', '<=', $date_end)
                ->count();
        });

        $Parallel->add(function () use (&$values, $date_start, $date_end) {
            $values['active_user_ids'] = User::query()
                ->where('last_at', '>=', $date_start)
                ->where('last_at', '<=', $date_end)
                ->get(['id'])
                ->groupBy('id')
                ->keys()
                ->values()
                ->toArray();
        });

        $Parallel->wait();

        $values['active_users'] = count($values['active_user_ids']);

        $values['inactive_users'] = $values['total'] - $values['active_users'];

        $values['active_seniors'] = $values['active_users'] - $values['new_users'];

        $values['active_seniors_rate'] = $values['active_users'] > 0 ? round($values['active_seniors'] / $values['active_users'], 2) : 0;

        return $values;
    }

    /**
     * 用户指定权限在redis中key名称
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @return string
     */
    public static function getPermissionRedisKey($user_id, $permission_name)
    {
        return env('APP_NAME') . ":quota_used:{$user_id}:{$permission_name}";
    }

    /**
     * 获取用户所有权限的已使用次数
     * @param int $user_id 用户id
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getAllPermissionsUsed($user_id)
    {
        // 从redis中获取用户所有权限的已使用次数
        $keys = [];
        foreach (array_keys(RankService::getPermissions()) as $key) {
            $keys[] = self::getPermissionRedisKey($user_id, $key);
        }
        $result = Website::FrontendRedis()->mget($keys);

        $permissions = [];
        foreach (array_keys(RankService::getPermissions()) as $index => $key) {
            $permissions[$key] = intval($result[$index] ?: 0);
        }

        return $permissions;
    }

    /**
     * 获取用户指定权限的已使用次数
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionUsed($user_id, $permission_name)
    {
        return intval(Website::FrontendRedis()->get(self::getPermissionRedisKey($user_id, $permission_name)) ?: 0);
    }

    /**
     * 增加用户指定权限的已使用次数
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @param int $used 使用次数
     * @param string|null $timezone 时区
     * @return bool true为成功，false为失败
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function incrPermissionUsed($user_id, $permission_name, $used, $timezone = null)
    {
        $User = User::query()
            ->with('rank:id,rank_name,permission')
            ->find($user_id, ['id', 'rank_id', 'created_at', 'vip_started_at']);
        if (!$User) {
            return false;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($User->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if (!$limit) {
            return false;
        }

        // 判断是否超出使用次数限制
        if ($limit !== -1 && $limit < (self::getPermissionUsed($user_id, $permission_name) + $used)) {
            return false;
        }

        $key = self::getPermissionRedisKey($user_id, $permission_name);
        $result = Website::FrontendRedis()->incrBy($key, $used);

        // 设置权限重置时间
        if (Website::FrontendRedis()->ttl($key) === -1) {
            $CarbonCreatedAt = Carbon::createFromTimeString($User->created_at);
            $timezone && $CarbonCreatedAt->setTimezone($timezone);
            Website::FrontendRedis()->expire($key, self::calcPermissionResetAt($user_id, $permission_name, $CarbonCreatedAt->timestamp, $User->vip_started_at, $timezone) - time());
        }

        // 如果超额的，需要恢复
        if ($limit !== -1 && $limit < $result) {
            Website::FrontendRedis()->decrBy($key, $used);
            return false;
        }

        // 同步数据到用户权限配额表
        Website::producer()->produce(new SyncUserQuotaProducer([
            'user_id' => $user_id,
            'permission_name' => $permission_name,
            'used' => $result,
            'reset_at' => time() + Website::FrontendRedis()->ttl($key),
        ]));

        return true;
    }

    /**
     * 恢复用户指定权限的已使用次数
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @param int $used 恢复次数
     * @return bool true为成功，false为失败
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function decrPermissionUsed($user_id, $permission_name, $used)
    {
        $User = User::query(false, true)
            ->with('rank:id,rank_name,permission')
            ->find($user_id, ['id', 'rank_id', 'created_at', 'vip_started_at']);
        if (!$User) {
            return false;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($User->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if (!$limit) {
            return false;
        }

        $key = self::getPermissionRedisKey($user_id, $permission_name);
        if (!Website::FrontendRedis()->exists($key)) {
            return false;
        }
        if (!self::getPermissionUsed($user_id, $permission_name)) {
            return false;
        }
        $result = Website::FrontendRedis()->decrBy($key, $used);

        // 避免出现少于0的情况
        if ($result < 0) {
            Website::FrontendRedis()->incrBy($key, $used);
            return false;
        }

        // 同步数据到用户权限配额表
        Website::producer()->produce(new SyncUserQuotaProducer([
            'user_id' => $user_id,
            'permission_name' => $permission_name,
            'used' => $result,
            'reset_at' => time() + Website::FrontendRedis()->ttl($key),
        ]));

        return true;
    }

    /**
     * 获取用户指定权限的剩余次数
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionRemaining($user_id, $permission_name)
    {
        $User = User::query()
            ->with('rank:id,rank_name,permission')
            ->find($user_id, ['id', 'rank_id']);
        if (!$User) {
            return 0;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($User->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if ($limit === -1) {
            return $limit;
        }

        // 剩余次数
        $remainning = intval($limit - self::getPermissionUsed($user_id, $permission_name));
        $remainning < 0 && $remainning = 0;
        return $remainning;
    }

    /**
     * 获取次数权限的重置时间
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @param int|null $created_at 用户注册时间
     * @param int|null $vip_started_at 会员到期时间
     * @param string|null $timezone 时区
     * @return float|int|string|null 重置时间
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionResetAt($user_id, $permission_name, $created_at = null, $vip_started_at = null, $timezone = null)
    {
        // redis上存在该key，则直接返回
        $redis_key = self::getPermissionRedisKey($user_id, $permission_name);
        if (($ttl = Website::FrontendRedis()->ttl($redis_key)) && $ttl > 0) {
            return time() + $ttl;
        }

        return self::calcPermissionResetAt($user_id, $permission_name, $created_at, $vip_started_at, $timezone);
    }

    /**
     * 计算次数权限的重置时间
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @param int|null $created_at 用户注册时间
     * @param int|null $vip_started_at 会员到期时间
     * @param string|null $timezone 时区
     * @return float|int|string|null 重置时间
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function calcPermissionResetAt($user_id, $permission_name, $created_at = null, $vip_started_at = null, $timezone = null)
    {
        if (!$created_at) {
            $User = User::query()
                ->find($user_id, ['id', 'created_at', 'vip_started_at']);
            if (!$User) {
                return null;
            }
            $created_at = Carbon::createFromTimeString($User->created_at)->timestamp;
            $vip_started_at = $User->vip_started_at;
        }

        $reset_days = RankService::getPermissions()[$permission_name]['reset_days'] ?? 1;
        if ($reset_days === 30) {
            // 如果是30天重置的，默认按自然月
            $NextCycle = Carbon::createFromTimestamp($vip_started_at ?: $created_at, $timezone);
            $Now = Carbon::now($timezone);
            $diff_in_months = $Now->diffInMonths($NextCycle);
            $diff_in_months && $NextCycle->addMonths($diff_in_months);
            $NextCycle->timestamp < $Now->timestamp && $NextCycle->addMonth();
            return $NextCycle->timestamp;
        } else {
            return Carbon::today($timezone)->addDays($reset_days)->timestamp;
        }
    }

    /**
     * 重置用户权限的已使用次数
     * @param int $user_id 用户id
     * @param string|null $permission_name 权限名称，不填就是重置用户所有权限
     */
    public static function resetPermissionUsed($user_id, $permission_name = null)
    {
        $keys = [];
        if ($permission_name) {
            $keys[] = self::getPermissionRedisKey($user_id, $permission_name);
        } else {
            foreach (array_keys(RankService::getPermissions()) as $key) {
                $keys[] = self::getPermissionRedisKey($user_id, $key);
            }
        }
        Website::FrontendRedis()->del($keys);

        // 同步到用户权限次数表
        retry(99, function () use ($user_id, $permission_name) {
            $UserQuotaBuilder = UserQuota::query()
                ->where('user_id', $user_id);
            if ($permission_name) {
                $UserQuotaBuilder->where('permission_name', $permission_name);
            }
            $UserQuotaBuilder->update([
                'remaining' => Db::raw('`limit`'),
                'used' => 0,
                'reset_at' => 0,
            ]);
        }, 1000);
    }

    /**
     * 更新用户权限次数
     * @param int $user_id 用户id
     * @param string $permission_name 权限名称
     * @param int $used 已用次数
     * @param int $reset_at 重置时间
     */
    public static function updatePermissionQuota($user_id, $permission_name, $used, $reset_at)
    {
        $User = User::with('rank:id,rank_name,permission')
            ->find($user_id, ['id', 'rank_id']);
        if (!$User) {
            return;
        }

        $permission = RankService::flattenPermission($User->rank->permission, '');
        $values = [
            'limit' => $permission[$permission_name] ?? null,
            'remaining' => isset($permission[$permission_name]) ? ($permission[$permission_name] - $used) : null,
            'used' => $used,
            'reset_at' => $reset_at,
        ];
        if ($values['remaining'] < 0) {
            $values['remaining'] = 0;
        }
        UserQuota::updateOrCreate([
            'user_id' => $user_id,
            'permission_name' => $permission_name,
        ], $values);
    }

    /**
     * 获取用户最新的订阅的信息
     * @param int $user_id 用户id
     * @return array|null
     */
    public static function getLastSubcription($user_id)
    {
        $Subscription = Subscription::query()
            ->where('user_id',  $user_id)
            ->whereNotNull('subscription_status')
            ->orderByDesc('id')
            ->first();
        if (!$Subscription) {
            return null;
        }

        $Order = Db::table('orders')
            ->join('stripe_payment_intents', 'orders.transaction_number', '=', 'stripe_payment_intents.payment_intent')
            ->where([
                ['orders.user_id', $Subscription->user_id],
                ['orders.subscription_sn', $Subscription->subscription_sn],
            ])
            ->orderByDesc('orders.id')
            ->first(['payment_method', 'payment_method_details']);
        if ($Order) {
            $payment_method_details = $Order->payment_method_details ? json_decode($Order->payment_method_details, true) : null;
        }
        return array_merge($Subscription->toArray(), [
            'subscription_status_text' => SubscriptionService::statusText((int) $Subscription->subscription_status),
            'payment_platform_text' => OrderService::paymentPlatformText((int) $Subscription->payment_platform),
            'payment_method' => $Order->payment_method ?? null,
            'payment_method_details' => $payment_method_details ?? null,
        ]);
    }

    /**
     * 查找客户的stripe信息，没有则添加
     * @param int $user_id 用户id
     * @return Customer
     * @throws \Lete\Pay\Exceptions\CustomerAlreadyCreated
     */
    public static function stripeCustomer($user_id)
    {
        $StripeCustomer = (new Cashier::$customerModel)->where('user_id', $user_id)->first();
        if (!$StripeCustomer) {
            $User = User::query()
                ->find($user_id, ['account', 'username', 'email']);
            $StripeCustomer = new Customer();
            $StripeCustomer->user_id = $user_id;
            $StripeCustomer->createAsStripeCustomer([
                'name' => $User->username,
                'email' => $User->email ?: $User->account,
            ]);
        }
        return $StripeCustomer;
    }

    /**
     * 后台通过邮箱注册
     * @param string $email 邮箱
     * @param string $password 密码
     * @param array $others 其它属性
     * @throws UserException
     * @throws \Throwable
     */
    public static function backendSignupByEmail($email, $password, array $others = [])
    {
        if (UserSocialite::query()
            ->where('type', UserSocialite::TYPE_EMAIL)
            ->where('socialite_id', $email)
            ->count()) {
            return ['type' => false, 'msg' => '账号已存在', 'data' => []];
        }

        $user_id = User::query()
            ->where('account', $email)
            ->value('id');
        if ($user_id) {
            return ['type' => false, 'msg' => '用户已存在', 'data' => []];
        }
        try {
            Db::beginTransaction();

            $User = User::create(array_merge([
                'account' => $email,
                'username' => $others['username'] ?? $email,
                'email' => $email,
                'password' => $password,
                'account_status' => User::ACCOUNT_STATUS_ACTIVATED,
            ], $others))->refresh();

            UserSocialite::create([
                'type' => UserSocialite::TYPE_EMAIL,
                'socialite_id' => $email,
                'user_id' => $User->id,
            ]);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            return ['type' => false, 'msg' => $e->getMessage(), 'data' => []];
        }

        return ['type' => true, 'msg' => '', 'data' => []];
    }

    /**
     * 获取用户上一个的套餐名称
     * @param int $user_id 用户id
     * @return null|string
     */
    public static function getLastRankName($user_id)
    {
        $LastOrder = Order::query()
            ->join('ranks', 'orders.rank_id', '=', 'ranks.id')
            ->where('user_id', $user_id)
            ->where('order_status', '<>', OrderService::STATUS_UNPAID)
            ->orderByDesc('orders.id')
            ->first(['ranks.rank_name']);
        return $LastOrder ? $LastOrder->rank_name : null;
    }

    /**
     * 从缓存中获取用户上一个的套餐名称
     * @Cacheable(prefix="user-last-rank-name", ttl=60)
     * @param int $user_id 用户id
     * @return null|string
     */
    public static function getLastRankNameFromCache($user_id)
    {
        return self::getLastRankName($user_id);
    }

    /**
     * 删除账号-存储验证码的redis键名
     * @param int $user_id 用户id
     * @return string
     */
    public static function deleteAccountVerificationCodeRedisKey($user_id)
    {
        return env('APP_NAME') . ":email:delete-account-verification-code:{$user_id}";
    }

    /**
     * 删除账号-发送验证码邮件
     * @param int $user_id 用户id
     * @throws UserException
     */
    public static function deleteAccountSendEmail($user_id)
    {
        $User = User::query()
            ->find($user_id);
        if ($User->account_status !== User::ACCOUNT_STATUS_ACTIVATED) {
            throw new UserException(ErrorCode::USER_EMAIL_IS_NOT_ACTIVATED);
        }

        self::precheckSendEmail($user_id);

        // 验证码4位数字，有效时间48小时
        $verification_code = rand(1000, 9999);
        $redis_key = self::deleteAccountVerificationCodeRedisKey($user_id);
        $ttl = 48 * 3600;
        Website::FrontendRedis()->setex($redis_key, $ttl, $verification_code);

        $Producer = ApplicationContext::getContainer()->get(Producer::class);
        $Producer->produce(new EMailProducer([
            'email' => $User->email,
            'subject' => 'Account deletion confirmation',
            'template' => 'mail/account-deletion-confirmation',
            'view_data' => [
                'verification_code' => $verification_code,
            ],
        ]));
    }

    /**
     * 删除账号-加入删除队列
     * @param int $user_id 用户id
     * @param string|null $verification_code 验证码
     * @param array $log_data 日志
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function deleteAccountJoinDeletionQueue($user_id, $verification_code = null, $log_data = [])
    {
        if ($verification_code) {
            $FrontendRedis = Website::FrontendRedis();
            $redis_key = self::deleteAccountVerificationCodeRedisKey($user_id);
            if ($FrontendRedis->get($redis_key) !== $verification_code) {
                throw new UserException(ErrorCode::DELETE_ACCOUNT_VERIFICATION_CODE_INVALID);
            }
        }

        try {
            Db::beginTransaction();

            $User = User::lockForUpdate()
                ->find($user_id);
            if ($User && $User->account_status === User::ACCOUNT_STATUS_ACTIVATED) {
                $original_user = $User->toArray();
                $User->update([
                    'account_status' => User::ACCOUNT_STATUS_IN_DELETION_QUEUE,
                    'delete_task_created_at' => time(),
                    'delete_task_plan_executed_at' => Carbon::now()->addDays(30)->timestamp,
                ]);

                self::deleteAccountLog(array_merge([
                    'user_id' => $User->id,
                    'event' => 'join-deletion-queue',
                    'description' => '加入删除队列',
                    'original_user' => $original_user,
                    'user' => $User->toArray(),
                ], $log_data));
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
        if ($verification_code) {
            $FrontendRedis->del($redis_key);
        }
        return $User;
    }

    /**
     * 删除账号-移出删除队列
     * @param int $user_id 用户id
     * @param array $log_data 日志
     * @return User
     * @throws UserException
     * @throws \Throwable
     */
    public static function deleteAccountRemoveDeletionQueue($user_id, $log_data = [])
    {
        try {
            Db::beginTransaction();

            $User = User::lockForUpdate()
                ->find($user_id);
            if ($User && $User->account_status === User::ACCOUNT_STATUS_IN_DELETION_QUEUE) {
                $original_user = $User->toArray();
                $User->update([
                    'account_status' => User::ACCOUNT_STATUS_ACTIVATED,
                    'delete_task_created_at' => null,
                    'delete_task_plan_executed_at' => null,
                ]);

                self::deleteAccountLog(array_merge([
                    'user_id' => $User->id,
                    'event' => 'remove-deletion-queue',
                    'description' => '移出删除队列',
                    'original_user' => $original_user,
                    'user' => $User->toArray(),
                ], $log_data));
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
        return $User;
    }

    /**
     * 删除账号
     * @param int $user_id 用户id
     * @param array $log_data 日志
     * @return User
     * @throws \Throwable
     */
    public static function deleteAccount($user_id, $log_data = ['handler_type' => 'system', 'handler' => 'system'])
    {
        try {
            Db::beginTransaction();

            $User = User::lockForUpdate()
                ->find($user_id);
            if ($User && $User->account_status !== User::ACCOUNT_STATUS_DELETED) {
                $original_deleted_data = [];
                $original_updated_data = [];

                // 删除登录方式
                $UserSocialites = UserSocialite::query()
                    ->where('user_id', $User->id)
                    ->get()
                    ->each(function (UserSocialite $UserSocialite) use (&$original_deleted_data) {
                        $original_deleted_data['user_socialites'][] = $UserSocialite->toArray();
                        $UserSocialite->delete();
                    });

                // 删除我的团队
                try {
                    $MyTeam = TeamService::myTeam($User->id);
                } catch (\Throwable $e) {
                }
                if (!empty($MyTeam)) {
                    TeamInvite::query()
                        ->where('team_id', $MyTeam->id)
                        ->get()
                        ->each(function (TeamInvite $TeamInvite) use (&$original_deleted_data) {
                            $original_deleted_data['team_invites'][] = $TeamInvite->toArray();
                            $TeamInvite->delete();
                        });

                    TeamMember::query()
                        ->where('team_id', $MyTeam->id)
                        ->get()
                        ->each(function (TeamMember $TeamMember) use (&$original_deleted_data) {
                            $original_deleted_data['team_members'][] = $TeamMember->toArray();
                            $TeamMember->delete();
                        });

                    TeamRole::query()
                        ->where('team_id', $MyTeam->id)
                        ->get()
                        ->each(function (TeamRole $TeamRole) use (&$original_deleted_data) {
                            $original_deleted_data['team_roles'][] = $TeamRole->toArray();
                            $TeamRole->delete();
                        });

                    $original_deleted_data['teams'][] = $MyTeam->toArray();
                    $MyTeam->delete();
                }

                // 退出团队
                $teams = TeamService::getTeams($User->id);
                foreach ($teams as $team) {
                    $original_deleted_data['team_members'][] = TeamMember::query()
                        ->where('team_id', $team['id'])
                        ->where('user_id', $User->id)
                        ->first()
                        ->toArray();
                    TeamService::removeMember($team['id'], null, $User->id);
                }

                // 停用分销
                DistributionInfo::query()
                    ->where('user_id', $User->id)
                    ->get()
                    ->each(function (DistributionInfo $DistributionInfo) use (&$original_updated_data) {
                        $original_updated_data['distribution_info'][] = $DistributionInfo->toArray();
                        $DistributionInfo->update([
                            'status' => DistributionInfo::STATUS_NOT_DISTRIBUTION,
                        ]);;
                    });

                // 删除前台接口令牌
                FrontendApiSecretKey::query()
                    ->where('user_id', $User->id)
                    ->get()
                    ->each(function (FrontendApiSecretKey $FrontendApiSecretKey) use (&$original_deleted_data) {
                        $original_deleted_data['frontend_api_secret_keys'][] = $FrontendApiSecretKey->toArray();
                        $FrontendApiSecretKey->delete();
                    });

                // 存在订阅的，需要取消
                Subscription::query()
                    ->where('user_id',  $User->id)
                    ->where('subscription_status', SubscriptionService::STATUS_ACTIVE)
                    ->get(['id'])
                    ->each(function (Subscription $Subscription) {
                        (new SubscriptionService($Subscription->id))->cancel(SubscriptionService::CANCELED_HANDLER_APPLICATION);
                    });

                // 账号状态改为已删除，更改账号
                $original_user = $User->toArray();
                $User->update([
                    'account' => "[Del-" .time(). "]{$User->account}",
                    'account_status' => User::ACCOUNT_STATUS_DELETED,
                    'delete_task_executed_at' => time(),
                ]);

                Event::produce('user.deleted', [
                    'user_id' => $User->id,
                ]);

                self::deleteAccountLog(array_merge([
                    'user_id' => $User->id,
                    'event' => 'delete-account',
                    'description' => '删除账号',
                    'original_user' => $original_user,
                    'user' => $User->toArray(),
                    'original_deleted_data' => $original_deleted_data,
                    'original_updated_data' => $original_updated_data,
                ], $log_data));
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();

            Alarm::feishu("【删除账号失败】\n用户ID：{$User->id}\n" . $e->getMessage());

            throw $e;
        }
    }

    /**
     * 删除账号-记录日志
     * @param array $log_data
     */
    public static function deleteAccountLog($log_data)
    {
        /*$log_data = [
            // 操作者类型：member（会员）、admin（管理员）、system（系统）
            'handler_type' => '',
            // 操作者名称
            'handler' => '',
            // 用户ID
            'user_id' => 0,
            // 日志类型：join-deletion-queue（加入删除队列）、remove-deletion-queue（移出删除队列）、delete-account（删除账号）
            'type' => '',
            // 日志描述
            'description' => '',
            // 变动前的用户数据
            'original_user' => [],
            // 变动后的用户数据
            'user' => [],
            // 日志时间
            'created_at' => 0,
        ];*/
        $log_data['created_at'] = time();
        MongoDb::collection('delete_account_logs')
            ->insertOne($log_data);
    }
}
