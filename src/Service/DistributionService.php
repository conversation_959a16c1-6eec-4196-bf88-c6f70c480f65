<?php

declare(strict_types=1);
namespace Website\Common\Service;

use Website\Common\Model\DistributionCode;
use Website\Common\Model\DistributionCommission;
use Website\Common\Model\DistributionInfo;
use Website\Common\Model\DistributionRecord;
use Website\Common\Model\DistributionSettlement;
use Website\Common\Model\Rank;
use Hyperf\DbConnection\Db;
use Website\Common\Utils\Website;

/**
 * Class UserService
 */
class DistributionService extends ModelServiceAbstract
{
    protected $modelProvider = DistributionInfo::class;


    /**
     * 检查分销码状态
     * @param $distribution_code
     * @param int $user_id
     * @return bool
     */
    public function check($distribution_code, int $user_id = 0)
    {
        $DistributionInfo = DistributionInfo::query();
        if ($user_id) {
            $DistributionInfo->where('user_id', '<>', $user_id);
        }

        $distribution_info_id = $DistributionInfo->where('distribution_code', '=', strtoupper($distribution_code))
            ->where('status', '=', DistributionInfo::STATUS_DISTRIBUTION)
            ->value('id');
        if ($distribution_info_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 结算佣金
     * @param $money
     * @param $user_id
     * @param $validator_data
     * @return array
     */
    public static function settleCommission($money, $user_id, $validator_data): array
    {
        try {
            if ($money < 0) {
                return ['type' => false, 'msg' => '金额异常'];
            }
            Db::beginTransaction();
            $distribution_info = DistributionInfo::query()->where('user_id', '=', $user_id)->lockForUpdate()->first();

            if (empty($distribution_info->distribution_code)) {
                Db::rollBack();
                return ['type' => false, 'msg' => '分销码为空'];
            }

            // 当前资产
            $current_assets = bcsub((string)$distribution_info->assets, (string)$money, 2);
            if ($current_assets < 0) {
                Db::rollBack();
                return ['type' => false, 'msg' => '分销者的当前资产必须大于或等于付款金额, 请刷新'];
            }

            // 记录佣金记录
            $insert = [
                'type' => DistributionCommission::DEL_TYPE,
                'user_id' => $user_id,
                'money' => $money,
                'current_assets' => $current_assets,
            ];
            DistributionCommission::create($insert);

            // 已结算佣金
            $settlement_commission = bcadd((string)$distribution_info->settlement_commission, (string)$money, 2);
            // 更新当前资产 已结算佣金
            DistributionInfo::query()->where('user_id', '=', $user_id)->update(['assets' => $current_assets, 'settlement_commission' => $settlement_commission]);

            /* 分销结算记录 */
            $insert = [
                'user_id' => $user_id,
                'pay_money' => $money,
                'pay_time' => strtotime($validator_data['pay_time']),
                'status' => DistributionSettlement::PAY_STATUS,
                'remark' => $validator_data['remark'],
                'pay_account' => $validator_data['pay_account'],
                'created_at' => time(),
                'updated_at' => time(),
            ];
            DistributionSettlement::create($insert);

            Db::commit();
            return ['type' => true, 'msg' => ''];
        } catch (\Throwable $e) {
            Db::rollBack();
            Website::logger(class_basename(self::class))->error($e->getMessage());
            return ['type' => false, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 确认佣金
     * @param $distribution_id
     * @param int $user_id
     * @return array
     */
    public static function confirmCommission($distribution_id, int $user_id = 0): array
    {
        try {
            Db::beginTransaction();
            if (!empty($distribution_id)) {
                $distribution_info = DistributionInfo::query()->where('id', '=', $distribution_id)->lockForUpdate()->first();
            } else {
                $distribution_info = DistributionInfo::query()->where('user_id', '=', $user_id)->lockForUpdate()->first();
            }

            $distribution_code_arr = DistributionCode::query()->where('user_id', '=', $distribution_info->user_id)->pluck('distribution_code')->toArray();
            if (empty($distribution_code_arr)) {
                Db::rollBack();
                return ['type' => false, 'msg' => '分销码为空'];
            }

            // 查出来用id作为条件
            $distribution_ids = DistributionRecord::query()
                ->whereIn('distribution_code', $distribution_code_arr)
                ->where('sure_status', '=', 1)
                ->where('sure_time', '<', time())
                ->where('pay_status', '=', 1)
                ->pluck('id');
            if (empty($distribution_ids)) {
                Db::rollBack();
                return ['type' => true, 'msg' => '数据为空'];
            }

            // 分销记录
            $distribution_record = DistributionRecord::query()
                ->whereIn('id', $distribution_ids)
                ->whereIn('distribution_code', $distribution_code_arr)
                ->where('sure_status', '=', 1)
                ->where('sure_time', '<', time())
                ->where('pay_status', '=', 1)
                ->lockForUpdate()
                ->get()->toArray();

            if (empty($distribution_record)) {
                Db::rollBack();
                return ['type' => true, 'msg' => '数据为空'];
            }

            // 计算总佣金
            $money = '0';
            foreach ($distribution_record as $value) {
                $money = bcadd((string)($value['commission']), $money, 2);
                // 当前资产
                $current_assets = bcadd((string)$distribution_info->assets, $money, 2);

                // 记录佣金记录
                $insert = [
                    'type' => DistributionCommission::ADD_TYPE,
                    'user_id' => $distribution_info->user_id,
                    'money' => $value['commission'],
                    'current_assets' => $current_assets,
                ];
                DistributionCommission::create($insert);
            }
            // 未确认佣金
            $not_confirmed_commission = bcsub((string)$distribution_info->not_confirmed_commission, $money, 2);
            if ($not_confirmed_commission < 0) {
                Db::rollBack();
                return ['type' => false, 'msg' => '未确认佣金异常, 不足以扣减, 请刷新'];
            }
            // 已确认佣金
            $confirmed_commission = bcadd((string)$distribution_info->confirmed_commission, $money, 2);

            // 更新当前资产+已结算佣金
            DistributionInfo::query()->where('id', '=', $distribution_info->id)->update(['assets' => $current_assets, 'not_confirmed_commission' => $not_confirmed_commission, 'confirmed_commission' => $confirmed_commission]);

            // 分销记录表更新
            foreach ($distribution_record as $value) {
                DistributionRecord::query()->where('id', '=', $value['id'])->update(['sure_status' => DistributionRecord::SURE_STATUS]);
            }

            Db::commit();
            return ['type' => true, 'msg' => ''];
        } catch (\Throwable $e) {
            Db::rollBack();
            Website::logger(class_basename(self::class))->error($e->getMessage());
            return ['type' => false, 'msg' => $e->getMessage()];
        }
    }


    /**
     * 取消结算记录
     * @return array
     */
    public static function cancelCommission($settlement_id)
    {
        try {
            Db::beginTransaction();
            // 结算记录状态
            $settlement_data = DistributionSettlement::query()->where('id', $settlement_id)->lockForUpdate()->first();
            if ($settlement_data->status == 2) {
                Db::rollBack();
                return ['type' => false, 'msg' => '已作废的数据不可在作废'];
            }

            $money = $settlement_data->pay_money;
            $user_id = $settlement_data->user_id;

            $distribution_info = DistributionInfo::query()->where('user_id', '=', $user_id)->lockForUpdate()->first();
            if (empty($distribution_info->distribution_code)) {
                Db::rollBack();
                return ['type' => false, 'msg' => '分销码为空'];
            }

            // 当前资产
            $current_assets = bcadd((string)$distribution_info->assets, (string)$money, 2);
            // 记录佣金记录
            $insert = [
                'type' => DistributionCommission::ADD_SETTLEMENT_TYPE,
                'user_id' => $user_id,
                'money' => $money,
                'current_assets' => $current_assets,
            ];
            DistributionCommission::create($insert);

            // 退回已结算佣金
            $settlement_commission = bcsub((string)$distribution_info->settlement_commission, (string)$money, 2);
            // 更新当前资产 已结算佣金
            DistributionInfo::query()->where('user_id', '=', $user_id)->update(['assets' => $current_assets, 'settlement_commission' => $settlement_commission]);

            // 更改作废状态
            DistributionSettlement::query()->where('id', $settlement_id)->update(['status' => 2]);

            Db::commit();
            return ['type' => true, 'msg' => ''];

        } catch (\Throwable $e) {
            Db::rollBack();
            Website::logger(class_basename(self::class))->error($e->getMessage());
            return ['type' => false, 'msg' => $e->getMessage()];
        }

    }


    /**
     * 获取默认分销套餐数据
     * @param string $check
     * @return array
     */
    public static function defaultRankData(string $check = ''): array
    {
        $rank = Rank::query()
            ->where('allowed_buy', 1)
            ->where('price', '>', 0)
            ->where('is_visibled', 1)
            ->select('id as rank_id', 'rank_name', 'duration')
            ->get()
            ->toArray();

        $return = [];
        foreach ($rank as $value) {
            $return[] = [
                'rank_id' => $value['rank_id'],
                'rank_name' => $value['rank_name'],
                'duration' => $value['duration'],
                'check' => $check,
                'commission_rate' => DistributionCode::COMMISSION_RATE,
            ];
        }
        return $return;
    }


}
