<?php

declare(strict_types=1);
namespace Website\Common\Service;

use Carbon\Carbon;
use Hyperf\Redis\Redis;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Str;

class WebsiteSettingService
{
    const HASH_KEYS_DEFAULT = [
        'static_version',
    ];

    /**
     * 获取redis键名
     * @return string
     */
    public static function getRedisKey()
    {
        return env('APP_NAME') . ':website-setting';
    }

    /**
     * 获取静态资源版本号
     * @return bool|mixed|string
     */
    public static function getStaticVersion()
    {
        return self::get('static_version', Carbon::now()->format('YmdH'));
    }

    /**
     * 设置静态资源版本号
     * @param string $value
     * @return bool|int
     */
    public static function setStaticVersion(string $value)
    {
        return self::set('static_version', $value);
    }

    /**
     * @return array
     */
    public static function getAll()
    {
        $all = [];
        foreach (self::HASH_KEYS_DEFAULT as $hash_key) {
            $method = 'get' . strtoupper(Str::camel($hash_key));
            $all[$hash_key] = self::$method();
        }
        return $all;
    }

    /**
     * @param $key
     * @param $value
     * @return bool|int
     */
    public static function set($hash_key, $value)
    {
        try {
            return self::redis()->hSet(self::getRedisKey(), $hash_key, $value);
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * @param $key
     * @param $default
     * @return bool|mixed|string
     */
    public static function get($hash_key, $default = null)
    {
        try {
            $redis = self::redis();
            return $redis->hExists(self::getRedisKey(), $hash_key) ? $redis->hGet(self::getRedisKey(), $hash_key) : $default;
        } catch (\Throwable $e) {
            return $default;
        }

    }

    /**
     * @return Redis|mixed
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private static function redis()
    {
        return ApplicationContext::getContainer()->get(Redis::class);
    }
}
