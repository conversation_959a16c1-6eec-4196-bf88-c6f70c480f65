<?php

declare(strict_types=1);
namespace Website\Common\Service;

use Hyperf\Apidog\Validation\Validation;
use Hyperf\DbConnection\Model\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;

abstract class ModelServiceAbstract
{
    /**
     * 模型的类
     * @var string
     */
    protected $modelProvider;

    /**
     * 模型
     * @var Model
     */
    protected $model;

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 属性
     * @var array
     */
    protected $attributes = [];

    /**
     * @Inject()
     * @var Validation
     */
    protected $validation;

    /**
     * ModelServiceAbstract constructor.
     * @param null $id
     */
    public function __construct($id = null)
    {
        $this->model = make($this->modelProvider);
        if ($id) {
            $this->model = $this->model->where($this->primaryKey, $id)->first();
        }
    }

    /**
     * @param $name
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void
     */
    public function __get($name)
    {
        return $this->model->getAttribute($name);
    }

    /**
     * @param $name
     * @param $value
     * @return mixed
     */
    public function __set($name, $value)
    {
        if ($this->hasSetMutator($name)) {
            $this->setMutatedAttributeValue($name, $value);
        } else {
            $this->attributes[$name] = $value;
        }

        $this->model->setAttribute($name, $this->attributes[$name]);
    }

    /**
     * @param $name
     * @param $arguments
     * @return mixed|null
     */
    public function __call($name, $arguments)
    {
        return call([$this->model, $name], $arguments);
    }

    /**
     * @param $name
     * @param $arguments
     * @return mixed
     */
    public static function __callStatic($name, $arguments)
    {
        return make((new static())->modelProvider)->{$name}(...$arguments);
    }

    /**
     * 检查该属性是否含有修改器
     * @param string $name
     * @return bool
     */
    public function hasSetMutator($name)
    {
        return method_exists($this, 'set' . Str::studly($name) . 'Attribute');
    }

    /**
     * 通过修改器设置属性值
     * @param $name
     * @param $value
     * @return mixed
     */
    protected function setMutatedAttributeValue($name, $value)
    {
        return $this->{'set' . Str::studly($name) . 'Attribute'}($value);
    }

    /**
     * 验证json的key是否合法
     * @param array $value
     * @param array $guard_keys
     * @param string $attribute_name
     * @return bool|string
     */
    protected function validJsonKeys(array $value, array $guard_keys, $attribute_name = '')
    {
        $keys = array_keys($value);
        if (count($keys) !== count($guard_keys) || count(Arr::merge($guard_keys, $keys, true)) !== count($guard_keys)) {
            return $attribute_name . '必需包含且只允许这些属性：' . implode('、', $guard_keys);
        }
        return true;
    }
}
