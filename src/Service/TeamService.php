<?php

declare(strict_types=1);

namespace Website\Common\Service;

use Carbon\Carbon;
use Hyperf\Amqp\Producer;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Exception\TimeoutException;
use Hyperf\Utils\Str;
use Website\Common\Amqp\Producer\EMailProducer;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\TeamException;
use Website\Common\Model\Team;
use Website\Common\Model\TeamInvite;
use Website\Common\Model\TeamMember;
use Website\Common\Model\TeamRole;
use Website\Common\Model\User;
use Website\Common\Utils\Website;

class TeamService
{
    /**
     * 生成邀请码
     * @param int $team_id 团队id
     * @param int $role_id 角色id
     * @param string|null $email 被邀请人的邮箱
     * @param int $valid_hour 有效小时
     * @return string
     * @throws \Throwable
     */
    public static function generateInviteCode($team_id, $role_id = 0, $email = null, $valid_hour = 24)
    {
        if (!$role_id) {
            $role_id = TeamRole::query()
                ->where('team_id', $team_id)
                ->orderByDesc('is_default')
                ->value('id') ?: 0;
        }

        // 先查找有效期内的邀请码
        $code = TeamInvite::query()
            ->where('team_id', $team_id)
            ->where('role_id', $role_id)
            ->where('email', $email)
            ->where('expired_at', '>', time())
            ->orderByDesc('id')
            ->value('code');
        if ($code) {
            return $code;
        }

        retry(3, function () use ($team_id, $role_id, $email, $valid_hour, &$code) {
            $code = Str::random(9);
             $attributes = [
                 'team_id' => $team_id,
                 'role_id' => $role_id,
                 'email' => $email,
                 'code' => $code,
                 'expired_at' => $valid_hour ? time() + $valid_hour * 3600 : null,
             ];
             TeamInvite::create($attributes);
        }, 300);
        return $code;
    }

    /**
     * 生成团队名称
     * @param string $username 用户名称
     * @return string
     */
    public static function generateTeamName($username)
    {
        return Str::replaceLast("'s", '', $username) . "'s Team";
    }

    /**
     * 创建团队
     * @param int $owner_user_id 所有者的用户id
     * @param string|null $team_name 团队名称
     * @return \Hyperf\Database\Model\Model|Team
     * @throws \Throwable
     */
    public static function create($owner_user_id, $team_name = null)
    {
        $User = User::query()->find($owner_user_id);
        if (!$User->rank->max_team_members) {
            throw new TeamException(ErrorCode::TEAM_NO_PERMISSION_TO_CREATE);
        }
        if (!$team_name) {
            $team_name = self::generateTeamName($User->username);
        }
        try {
            Db::beginTransaction();

            $Team = Team::firstOrCreate(['user_id' => $owner_user_id], [
                'name' => $team_name,
            ]);

            Db::commit();
            return $Team;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }

    /**
     * 我的团队
     * @param int $owner_user_id 所有者的用户id
     * @param boolean $auto_create 当没有团队时，自动创建
     * @return \Hyperf\Database\Model\Model|Team
     * @throws \Throwable
     */
    public static function myTeam($owner_user_id, $auto_create = false)
    {
        $Team = Team::query()
            ->where('user_id', $owner_user_id)
            ->first();
        if (empty($Team)) {
            if (!$auto_create) {
                throw new TeamException(ErrorCode::TEAM_NOT_CREATE);
            }
            $Team = self::create($owner_user_id);
        }
        return $Team;
    }

    /**
     * 加入团队
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param int $role_id 角色id
     * @param int|null $invite_id 邀请id
     * @throws \Throwable
     */
    public static function join($team_id, $member_user_id, $role_id, $invite_id = null)
    {
        try {
            $redis_key = env('APP_NAME') . ":join-team:{$team_id}";
            $Redis = ApplicationContext::getContainer()->get(Redis::class);
            retry(5, function () use ($Redis, $redis_key) {
                if (!$Redis->set($redis_key, 1, ['NX', 'EX' => 30])) {
                    throw new TimeoutException();
                }
            }, 300);

            $Team = Team::query()->find($team_id);
            if ($Team->user_id === $member_user_id) {
                throw new TeamException(ErrorCode::TEAM_YOUR_ARE_OWNER);
            }

            if (TeamMember::query()
                ->where('team_id', $team_id)
                ->where('user_id', $member_user_id)
                ->count()) {
                throw new TeamException(ErrorCode::TEAM_ALREADY_A_MEMBER);
            }

            if ($Team->member_count >= $Team->owner->rank->max_team_members ?? 0) {
                throw new TeamException(ErrorCode::TEAM_MEMBERS_REACHED_MAXIMUM_LIMIT);
            }

            try {
                Db::beginTransaction();

                TeamMember::firstOrCreate([
                    'team_id' => $team_id,
                    'user_id' => $member_user_id,
                ], [
                    'role_id' => $role_id,
                    'invite_id' => $invite_id,
                ]);

                Db::commit();
            } catch (\Throwable $e) {
                Db::rollBack();
                throw $e;
            }
        } catch (\Throwable $e) {
            throw $e;
        } finally {
            $Redis->del($redis_key);
        }
    }

    /**
     * 通过邀请码加入团队
     * @param string $code 邀请码
     * @param int $member_user_id 团队成员的用户id
     * @param int|null $role_id 角色id
     * @throws \Throwable
     */
    public static function joinByCode($code, $member_user_id, $role_id = null)
    {
        $TeamInvite = TeamInvite::query()
            ->where('code', $code)
            ->first();
        if (empty($TeamInvite) || ($TeamInvite->expired_at && time() > $TeamInvite->expired_at)) {
            throw new TeamException(ErrorCode::TEAM_INVITE_CODE_INVALID);
        }

        if ($TeamInvite->email) {
            $email = User::query()
                ->where('id', $member_user_id)
                ->value('account');
            if ($email !== $TeamInvite->email) {
                throw new TeamException(ErrorCode::TEAM_INVITE_CODE_AND_EMAIL_NOT_MATCH);
            }
        }

        self::join($TeamInvite->team_id, $member_user_id, $role_id ?: $TeamInvite->role_id, $TeamInvite->id);
    }


    /**
     * 添加团队成员
     * @param int $team_id 团队id
     * @param string $email 被邀请人的邮箱
     * @throws TeamException
     * @throws \Throwable
     */
    public static function addMember($team_id, $email)
    {
        $member_user_id = User::query()
            ->where('account', $email)
            ->value('id');

        if (empty($member_user_id)) {
            throw new TeamException(ErrorCode::TEAM_EMAIL_NOT_REGISTERED);
        }

        $role_id = TeamRole::query()
            ->where('team_id', $team_id)
            ->orderByDesc('is_default')
            ->value('id') ?: 0;
        self::join($team_id, $member_user_id, $role_id);
    }

    /**
     * 通过邀请码获取团队信息
     * @param $code
     * @return \Hyperf\Database\Model\Model|Team
     */
    public static function getTeamByCode($code)
    {
        $TeamInvite = TeamInvite::query()
            ->where('code', $code)
            ->first();
        if (empty($TeamInvite) || ($TeamInvite->expired_at && time() > $TeamInvite->expired_at)) {
            throw new TeamException(ErrorCode::TEAM_INVITE_CODE_INVALID);
        }
        return $TeamInvite->team;
    }

    /**
     * 团队成员
     * @param int $team_id 团队id
     * @return array
     */
    public static function getMembers($team_id)
    {
        $members = [];
        $Team = Team::query()->find($team_id);
        if ($Team) {
            // 团队所有者信息
            $members[] = [
                'id' => 0,
                'user' => [
                    'account' => $Team->owner->account,
                    'username' => $Team->owner->username,
                ],
                'role' => [
                    'id' => 0,
                    'name' => 'Owner',
                ],
                'status' => 1,
                'created_at' => $Team->created_at->timestamp,
            ];

            // 团队其它成员
            TeamMember::query()
                ->with('user:id,account,username')
                ->with('role:id,name')
                ->where('team_id', $Team->id)
                ->get(['id', 'user_id', 'role_id', 'status', 'created_at'])
                ->each(function (TeamMember $TeamMember) use (&$members) {
                    $member = $TeamMember->toArray();
                    $member['created_at'] = Carbon::createFromTimeString($member['created_at'])->timestamp;
                    $member['user'] = Arr::only($member['user'], ['account', 'username']);
                    $members[] = Arr::only($member, ['id', 'user', 'role', 'status', 'created_at']);
                });
        }
        return $members;
    }

    /**
     * 我加入的团队
     * @param int $member_user_id 团队成员的用户id
     * @param int|null $status 团队成员的状态
     * @return array
     */
    public static function getTeams($member_user_id, $status = null)
    {
        $teams = [];
        $Builder = TeamMember::query()
            ->with('team:id,name,user_id')
            ->with('role:id,name')
            ->where('user_id', $member_user_id);
        $status && $Builder->where('status', $status);
        $Builder->orderByDesc('id')
            ->get(['id', 'team_id', 'user_id', 'role_id', 'status'])
            ->each(function (TeamMember $TeamMember) use (&$teams) {
                // 团队所有者
                $owner = User::query()
                    ->with('rank:id,rank_name,permission')
                    ->find($TeamMember->team->user_id, ['id', 'account', 'username', 'rank_id'])
                    ->toArray();
                Arr::forget($owner, ['id', 'rank_id', 'rank.id']);

                $team = [
                    'id' => $TeamMember->team->id,
                    'name' => $TeamMember->team->name,
                    'owner' => $owner,
                    'role' => [
                        'id' => $TeamMember->role->id,
                        'name' => $TeamMember->role->name,
                    ],
                    'member' => [
                        'status' => $TeamMember->status,
                    ]
                ];
                $teams[] = $team;
            });
        return $teams;
    }

    /**
     * 是否管理员
     * @param $team_id
     * @param $member_user_id
     * @return bool
     */
    public static function isAdmin($team_id, $member_user_id)
    {
        $Team = Team::query()->find($team_id);
        if (!$Team) {
            return false;
        }
        if ($Team->user_id !== $member_user_id) {
            $is_admin = TeamMember::query()
                ->where('team_id', $team_id)
                ->where('user_id', $member_user_id)
                ->where('is_admin', 1)
                ->count();
            if (!$is_admin) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param int $team_id 团队id
     * @param int $role_id 角色id
     * @param string $email 被邀请人的邮箱
     * @param int $sender_user_id 发送者的用户id
     * @throws \Throwable
     */
    public static function sendInviteEmail($team_id, $role_id, $email, $sender_user_id = null)
    {
        $Team = Team::query()->find($team_id);

        $User = User::query()
            ->where('account', $email)
            ->first(['id']);
        if ($User) {
            if ($Team->user_id === $User->id ||
                TeamMember::query()
                    ->where('team_id', $team_id)
                    ->where('user_id', $User->id)
                    ->count()) {
                throw new TeamException(ErrorCode::TEAM_ALREADY_A_MEMBER);
            }
        }

        if ($Team->member_count >= $Team->owner->rank->max_team_members ?? 0) {
            throw new TeamException(ErrorCode::TEAM_MEMBERS_REACHED_MAXIMUM_LIMIT);
        }

        if (!TeamRole::query()
            ->where('team_id', $team_id)
            ->where('id', $role_id)
            ->count()) {
            throw new TeamException(ErrorCode::TEAM_ROLE_ID_INVALID);
        }

        UserService::precheckSendEmail($sender_user_id ?: $Team->user_id);

        $Producer = ApplicationContext::getContainer()->get(Producer::class);
        $Producer->produce(new EMailProducer([
            'email' => $email,
            'subject' => "Invitation to join {$Team->name}",
            'template' => 'mail/team-invite',
            'view_data' => [
                'team_name' => $Team->name,
                'invite_code' => self::generateInviteCode($team_id, $role_id, $email),
            ],
        ]));
    }

    /**
     * 删除成员
     * @param int $team_id 团队id
     * @param int|null $member_id 团队成员id
     * @param int|null $member_user_id 团队成员的用户id
     * @throws \Throwable
     */
    public static function removeMember($team_id, $member_id = null, $member_user_id = null)
    {
        $Builder = TeamMember::query()
            ->where('team_id', $team_id);
        $member_id && $Builder->where('id', $member_id);
        $member_user_id && $Builder->where('user_id', $member_user_id);
        $TeamMember = $Builder->first();
        if (!$TeamMember) {
            throw new TeamException(ErrorCode::TEAM_NOT_MEMBER);
        }

        // 未满x天不能删除成员
        if ($member_id && ($days_limit = config('website.team.owner_setting.remove_member.days_limit', 0))) {
            if (time() - $TeamMember->created_at->timestamp <= $days_limit * 24 * 3600) {
                throw new TeamException(ErrorCode::TEAM_REMOVE_MEMBER_DAYS_LIMIT);
            }
        }

        $TeamMember->delete();
    }

    /**
     * 更改成员的角色
     * @param int $team_id 团队id
     * @param int $member_id 团队成员id
     * @param int $role_id 角色id
     * @throws \Throwable
     */
    public static function changeMemberRole($team_id, $member_id, $role_id)
    {
        $TeamMember = TeamMember::query()
            ->where('team_id', $team_id)
            ->where('id', $member_id)
            ->first();
        if (!$TeamMember) {
            throw new TeamException(ErrorCode::TEAM_NOT_MEMBER);
        }

        if (!TeamRole::query()
            ->where('team_id', $team_id)
            ->where('id', $role_id)
            ->count()) {
            throw new TeamException(ErrorCode::TEAM_ROLE_ID_INVALID);
        }

        $TeamMember->update([
            'role_id' => $role_id,
        ]);
    }

    /**
     * 更新角色默认的权限（应当新增权限的情况）
     */
    public static function updateRoleDefaultPermission()
    {
        foreach (config('website.team.role.default_data', []) as $role) {
            TeamRole::query()
                ->where('name', $role['name'])
                ->update([
                    'permission' => json_encode($role['permission']),
                ]);
        }
    }

    /**
     * 团队成员指定权限在redis中key名称
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param string $permission_name 权限名称
     * @return string
     */
    public static function getPermissionRedisKey($team_id, $member_user_id, $permission_name)
    {
        return env('APP_NAME') . ":team_quota_used:{$team_id}_{$member_user_id}:{$permission_name}";
    }

    /**
     * 获取团队成员指定权限的已使用次数
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param string $permission_name 权限名称
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionUsed($team_id, $member_user_id, $permission_name)
    {
        return intval(Website::FrontendRedis()->get(self::getPermissionRedisKey($team_id, $member_user_id, $permission_name)) ?: 0);
    }

    /**
     * 增加团队成员指定权限的已使用次数
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param string $permission_name 权限名称
     * @param int $used 使用次数
     * @param string|null $timezone 时区
     * @return bool true为成功，false为失败
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function incrPermissionUsed($team_id, $member_user_id, $permission_name, $used, $timezone = null)
    {
        $TeamMember = TeamMember::query()
            ->with('team:id,user_id')
            ->where('team_id', $team_id)
            ->where('user_id', $member_user_id)
            ->first();
        if (!$TeamMember) {
            return false;
        }

        $TeamOwner = User::query()
            ->with('rank:id,rank_name,permission')
            ->find($TeamMember->team->user_id, ['id', 'rank_id', 'created_at', 'vip_started_at']);
        if (!$TeamOwner) {
            return false;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($TeamOwner->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if (!$limit) {
            return false;
        }

        // 判断是否超出使用次数限制
        if ($limit !== -1 && $limit < (self::getPermissionUsed($team_id, $member_user_id, $permission_name) + $used)) {
            return false;
        }

        $key = self::getPermissionRedisKey($team_id, $member_user_id, $permission_name);
        $result = Website::FrontendRedis()->incrBy($key, $used);

        // 设置权限重置时间
        if (Website::FrontendRedis()->ttl($key) === -1) {
            $CarbonCreatedAt = Carbon::createFromTimeString($TeamOwner->created_at);
            $timezone && $CarbonCreatedAt->setTimezone($timezone);
            Website::FrontendRedis()->expire($key, UserService::calcPermissionResetAt($TeamOwner->id, $permission_name, $CarbonCreatedAt->timestamp, $TeamOwner->vip_started_at, $timezone) - time());
        }

        // 如果超额的，需要恢复
        if ($limit !== -1 && $limit < $result) {
            Website::FrontendRedis()->decrBy($key, $used);
            return false;
        }

        return true;
    }

    /**
     * 获取团队成员指定权限的剩余次数
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param string $permission_name 权限名称
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionRemaining($team_id, $member_user_id, $permission_name)
    {
        $TeamMember = TeamMember::query()
            ->with('team:id,user_id')
            ->where('team_id', $team_id)
            ->where('user_id', $member_user_id)
            ->first();
        if (!$TeamMember) {
            return 0;
        }

        $TeamOwner = User::query()
            ->with('rank:id,rank_name,permission')
            ->find($TeamMember->team->user_id, ['id', 'rank_id']);
        if (!$TeamOwner) {
            return 0;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($TeamOwner->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if ($limit === -1) {
            return $limit;
        }

        // 剩余次数
        $remainning = intval($limit - self::getPermissionUsed($team_id, $member_user_id, $permission_name));
        $remainning < 0 && $remainning = 0;
        return $remainning;
    }

    /**
     * 恢复团队成员指定权限的已使用次数
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @param string $permission_name 权限名称
     * @param int $used 恢复次数
     * @return bool true为成功，false为失败
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function decrPermissionUsed($team_id, $member_user_id, $permission_name, $used)
    {
        $TeamMember = TeamMember::query()
            ->with('team:id,user_id')
            ->where('team_id', $team_id)
            ->where('user_id', $member_user_id)
            ->first();
        if (!$TeamMember) {
            return false;
        }

        $TeamOwner = User::query(false, true)
            ->with('rank:id,rank_name,permission')
            ->find($TeamMember->team->user_id, ['id', 'rank_id', 'created_at', 'vip_started_at']);
        if (!$TeamOwner) {
            return false;
        }

        // 获取权限次数限制
        $permissions = RankService::flattenPermission($TeamOwner->rank->permission, '');
        $limit = $permissions[$permission_name] ?? 0;
        if (!$limit) {
            return false;
        }

        $key = self::getPermissionRedisKey($team_id, $member_user_id, $permission_name);
        if (!Website::FrontendRedis()->exists($key)) {
            return false;
        }
        if (!self::getPermissionUsed($team_id, $member_user_id, $permission_name)) {
            return false;
        }
        $result = Website::FrontendRedis()->decrBy($key, $used);

        // 避免出现少于0的情况
        if ($result < 0) {
            Website::FrontendRedis()->incrBy($key, $used);
            return false;
        }

        return true;
    }

    /**
     * 重置团队权限的已使用次数
     * @param int $team_id 团队id
     * @param string|null $permission_name 权限名称，不填就是重置用户所有权限
     */
    public static function resetPermissionUsed($team_id, $permission_name = null)
    {
        $keys = [];
        $team_members = TeamMember::query()
            ->where('team_id', $team_id)
            ->get(['id', 'user_id'])
            ->toArray();
        foreach ($team_members as $team_member) {
            if ($permission_name) {
                $keys[] = self::getPermissionRedisKey($team_id, $team_member['user_id'], $permission_name);
            } else {
                foreach (array_keys(RankService::getPermissions()) as $key) {
                    $keys[] = self::getPermissionRedisKey($team_id, $team_member['user_id'], $key);
                }
            }
        }
        if ($keys) {
            Website::FrontendRedis()->del($keys);
        }
    }

    /**
     * 获取团队成员所有权限的已使用次数
     * @param int $team_id 团队id
     * @param int $member_user_id 团队成员的用户id
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getAllPermissionsUsed($team_id, $member_user_id)
    {
        // 从redis中获取用户所有权限的已使用次数
        $keys = [];
        foreach (array_keys(RankService::getPermissions()) as $key) {
            $keys[] = self::getPermissionRedisKey($team_id, $member_user_id, $key);
        }
        $result = Website::FrontendRedis()->mget($keys);

        $permissions = [];
        foreach (array_keys(RankService::getPermissions()) as $index => $key) {
            $permissions[$key] = intval($result[$index] ?: 0);
        }

        return $permissions;
    }

    /**
     * 团队主的等级套餐变化事件
     * @param int $team_id 团队id
     */
    public static function ownerRankChangeEvent($team_id)
    {
        $Team = Team::query()->find($team_id);
        if ($Team) {
            $max_team_members = $Team->owner->rank->max_team_members ?? 0;

            // 降套餐，早添加的禁用
            $ActivatedTeamMembers = TeamMember::query()
                ->where('team_id', $Team->id)
                ->where('status', TeamMember::STATUS_ACTIVATED)
                ->orderBy('id')
                ->get();
            if ($max_team_members !== -1 && $ActivatedTeamMembers->count() && $ActivatedTeamMembers->count() > ($max_team_members - 1)) {
                for ($i = 0; $i < ($ActivatedTeamMembers->count() - ($max_team_members - 1)); $i++) {
                    $TeamMember = $ActivatedTeamMembers->offsetGet($i);
                    $TeamMember->update([
                        'status' => TeamMember::STATUS_DISABLED_DUE_REACHED_MAXIMUM_LIMIT,
                    ]);
                }
            } else {
                // 升套餐，被禁用的需要恢复
                $DisabledTeamMembers = TeamMember::query()
                    ->where('team_id', $Team->id)
                    ->where('status', TeamMember::STATUS_DISABLED_DUE_REACHED_MAXIMUM_LIMIT)
                    ->orderByDesc('id')
                    ->get();
                if ($DisabledTeamMembers->count()) {
                    $max = $max_team_members === -1 ? $DisabledTeamMembers->count() : ($max_team_members - 1 - $ActivatedTeamMembers->count());
                    $max = min($max, $DisabledTeamMembers->count());
                    for ($i = 0; $i < $max; $i++) {
                        $TeamMember = $DisabledTeamMembers->offsetGet($i);
                        $TeamMember->update([
                            'status' => TeamMember::STATUS_ACTIVATED,
                        ]);
                    }
                }
            }
        }
    }
}
