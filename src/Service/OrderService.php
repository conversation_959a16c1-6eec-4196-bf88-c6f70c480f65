<?php

declare(strict_types=1);
namespace Website\Common\Service;

use Website\Common\Model\Subscription;
use Website\Common\Model\Team;
use Website\Common\Utils\Tool;
use Hyperf\Utils\Exception\TimeoutException;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\OrderException;
use Website\Common\Model\DistributionCode;
use Website\Common\Model\DistributionInfo;
use Website\Common\Model\DistributionRecord;
use Website\Common\Model\Order;
use Website\Common\Model\OrderRefund;
use Website\Common\Model\Rank;
use Website\Common\Model\User;
use Website\Common\Redis\FrontendRedis;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Lete\MongoDB\MongoClient\MongoDb;
use Lete\Pay\Cashier;
use Stripe\Checkout\Session;

class OrderService extends ModelServiceAbstract
{
    protected $modelProvider = Order::class;

    /**
     * 订单类型-订阅订单
     * @var int
     */
    const TYPE_SUBSCRIPTION = 1;

    /**
     * 订单状态-未支付
     * @var int
     */
    const STATUS_UNPAID = 1;

    /**
     * 订单状态-已支付
     * @var int
     */
    const STATUS_PAID = 2;

    /**
     * 订单状态-已退款
     * @var int
     */
    const STATUS_REFUNDED = 3;

    /**
     * 支付平台-线下支付
     * @var int
     */
    const PAYMENT_PLATFORM_OFFLINE = 100;

    /**
     * 支付平台-Stripe
     * @var int
     */
    const PAYMENT_PLATFORM_STRIPE = 1;

    /**
     * @Inject
     * @var FrontendRedis
     */
    public $FrontendRedis;

    /**
     * @param $order_sn
     * @return OrderService|null
     */
    public static function make($order_sn)
    {
        $Order = Db::table('orders')->where('order_sn', $order_sn)->first(['id']);
        return $Order ? make(self::class, [$Order->id]) : null;
    }

    /**
     * 结算
     * @param int $payment_platform 支付平台
     * @param string $success_url 支付成功回调链接
     * @param string|null $cancel_url 取消支付回调链接
     * @return \Stripe\Checkout\Session
     * @throws \Lete\Pay\Exceptions\CustomerAlreadyCreated
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function checkout($payment_platform, $success_url, $cancel_url = null)
    {
        switch ($payment_platform) {
            case self::PAYMENT_PLATFORM_STRIPE:
                $StripeCustomer = UserService::stripeCustomer($this->user_id);

                $params = [
                    'success_url' => $success_url . (Str::contains($success_url, '?') ? '&' : '?') . 'session_id={CHECKOUT_SESSION_ID}',
                    'customer' => $StripeCustomer->stripeId(),
                    'client_reference_id' => $this->order_sn,
                ];
                $cancel_url && $params['cancel_url'] = $cancel_url;

                if ($this->type === self::TYPE_SUBSCRIPTION) {
                    $Rank = Rank::find($this->rank_id);

                    $params['mode'] = $Rank->duration === 'forever' ? 'payment' : 'subscription';

                    $params['line_items'] = [[
                        'price' => $Rank->stripe_price_id,
                        'quantity' => 1,
                    ]];

                    // 试用天数
                    if ($Rank->duration !== 'forever' && $this->trial_days) {
                        $params['subscription_data'] = [
                            'trial_settings' => ['end_behavior' => ['missing_payment_method' => 'cancel']],
                            'trial_period_days' => $this->trial_days,
                        ];
                    }

                    // 首次购买优惠
                    $this->first_time && $Rank->stripe_coupon_id && $params['discounts'][] = ['coupon' => $Rank->stripe_coupon_id];
                } else {
                    $params['mode'] = 'payment';
                    $params['line_items'] = [
                        [
                            'price_data' => [
                                'currency' => 'usd',
                                'product_data' => [
                                    'name' => config('website.name') . "' Product {$this->order_amount}USD",
                                ],
                                'unit_amount' => $this->order_amount * 100,
                            ],
                            'quantity' => 1,
                        ]
                    ];
                }

                $StripeCheckoutSession = Cashier::stripe()->checkout->sessions->create($params);

                // 防重复支付：使上一个stripe结算会话立刻过期
                $checkout_session_id = $this->FrontendRedis->get($this->stripeCheckoutSessionKey());
                if ($checkout_session_id !== false && Cashier::stripe()->checkout->sessions->retrieve($checkout_session_id)->status === Session::STATUS_OPEN) {
                    Cashier::stripe()->checkout->sessions->expire($checkout_session_id);
                }
                $this->FrontendRedis->setex($this->stripeCheckoutSessionKey(), $StripeCheckoutSession->expires_at - time(), $StripeCheckoutSession->id);

                return $StripeCheckoutSession;
                break;
            default:
                throw new OrderException(400, 'payment is invalid');
                break;
        }
    }

    /**
     * stripe结算会话在redis中的key
     * @return string
     */
    public function stripeCheckoutSessionKey()
    {
        return env('APP_NAME'). ":stripe:checkout:session:{$this->user_id}";
    }

    /**
     * 退款
     * @param $amount
     * @param $method
     * @param $cancel_vip
     * @param $cancel_subscription
     * @param $admin_id
     * @param array $others 其它参数
     * @throws \Throwable
     */
    public function refund($amount, $method, $cancel_vip, $cancel_subscription, $admin_id, $others = [])
    {
        // 通过api直接退款的，只能全额退
        if (config('website.order.refund.retracement', false) && $this->transaction_number && $method != self::PAYMENT_PLATFORM_OFFLINE) {
            if ($amount != $this->paid_amount) {
                throw new OrderException(400, '该退款方式不支持部分退款');
            }
            if ($method == self::PAYMENT_PLATFORM_STRIPE && $this->payment_platform === self::PAYMENT_PLATFORM_STRIPE) {
                $refund_amount = Cashier::stripe()->paymentIntents->retrieve($this->transaction_number)->amount;
            }
        }

        try {
            Db::beginTransaction();

            if (!in_array($this->order_status, [self::STATUS_PAID, self::STATUS_REFUNDED])) {
                throw new OrderException(400, 'The order was not paid.');
            }

            if (!($amount >= 0 && $this->profit_amount >= $amount)) {
                throw new OrderException(400, 'Lack of profit amount.');
            }

            OrderRefund::create([
                'order_id' => $this->id,
                'amount' => $amount,
                'method' => $method,
                'cancel_vip' => $cancel_vip,
                'cancel_subscription' => $cancel_subscription,
                'admin_id' => $admin_id
            ]);

            $this->save([
                'order_status' => self::STATUS_REFUNDED,
                'refunded_amount' => round($this->refunded_amount + $amount, 2),
                'refunded_at' => time(),
            ]);

            switch ($this->model->type) {
                case self::TYPE_SUBSCRIPTION:
                    // 修改会员等级为free，清空会员时间（开通+结束）
                    if ($cancel_vip) {
                        $User = User::find($this->user_id);
                        $User->update([
                            'rank_id' => RankService::FREE,
                            'vip_started_at' => null,
                            'vip_expired_at' => null,
                        ]);
                    }

                    // 如果有生效的订阅，需要取消掉订阅
                    if ($cancel_subscription && $this->payment_platform > 0 && $this->subscription_sn) {
                        SubscriptionService::make($this->payment_platform, $this->subscription_sn)->cancel(SubscriptionService::CANCELED_HANDLER_ADMIN);
                    }
                    break;
                default:
                    $order_type_class = self::getOrderTypeClass($this->model->type);
                    if ($order_type_class && method_exists($order_type_class, 'refund')) {
                        $order_type_class::refund($this->model->id, $others);
                    }
                    break;
            }

            // 更新会员价值
            $this->updateUserValue();

            /* 分销码处理 */
            $distribution_code = $this->distribution_code;
            if (!empty($distribution_code)) {
                $distribution_record = DistributionRecord::query(false, true)
                    ->where('order_id', '=', $this->id)
                    ->where('pay_status', '=', DistributionRecord::PAY_STATUS)
                    ->select("id", "commission", 'sure_time')
                    ->first();
                // 未到达确认时间
                if (!empty($distribution_record->id)) {
                    $code_user_id = DistributionCode::query()->where('distribution_code', '=', $distribution_code)->value('user_id');
                    /* 确认时间内的佣金退款逻辑 */
                    if ($distribution_record->sure_time >= time()) {
                        // 分销记录表为已退款
                        DistributionRecord::query()->where('id', '=', $distribution_record->id)->update(['pay_status' => DistributionRecord::REFUND_STATUS]);
                        // 未确认佣金
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->decrement('not_confirmed_commission', $distribution_record->commission);
                        // 已取消佣金
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('cancel_commission', $distribution_record->commission);
                    }
                    /* 是否已经退款过逻辑 */
                    $order_refund_count = OrderRefund::query()->where('order_id', '=', $this->id)->count('id');
                    if ($order_refund_count == 1) {
                        // 收入
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->decrement('income_num', $this->paid_amount);
                        // 订单无效数加1
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('order_invalid_num');
                        // 订单有效数减1
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->decrement('order_effect_num');
                        // 判断用户是否要付费客户数减一
                        $tmp_order_id = Order::query()->where('user_id', '=', $this->user_id)->where('distribution_code', '=', $distribution_code)->where('order_status', '=', self::STATUS_PAID)->where('id', '<>', $this->id)->value('id');
                        if (!$tmp_order_id) {
                            DistributionInfo::query()->where('user_id', '=', $code_user_id)->decrement('pay_customer');
                        }
                    }
                }
            }

            // 通过api直接退款
            if (!empty($refund_amount)) {
                if ($method == self::PAYMENT_PLATFORM_STRIPE && $this->payment_platform === self::PAYMENT_PLATFORM_STRIPE) {
                    Cashier::stripe()->refunds->create([
                        'payment_intent' => $this->transaction_number,
                        'amount' => $refund_amount,
                    ]);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }

    /**
     * 已支付
     * @param $paid_amount
     * @param int|null $paid_at
     * @param int|null $payment_platform
     * @param string|null $subscription_sn
     * @param array $others 其它参数
     * @throws \Throwable
     */
    public function paid($paid_amount, ?int $paid_at = null, ?int $payment_platform = null, ?string $subscription_sn = null, $others = [])
    {
        // 避免并发
        $lock_key = env('APP_NAME') . ":order-paid-{$this->model->id}";
        retry(3, function () use ($lock_key) {
            if (!Tool::lock($lock_key, 60)) {
                throw new TimeoutException();
            }
        }, 500);

        try {
            // 订单不是待支付状态，就退出
            $this->model->refresh();
            if ($this->model->order_status !== self::STATUS_UNPAID) {
                return;
            }

            Db::beginTransaction();

            $data = [
                'order_status' => self::STATUS_PAID,
                'paid_amount' => $paid_amount,
                'paid_at' => $paid_at ?: time(),
            ];
            !is_null($payment_platform) && $data['payment_platform'] = $payment_platform;
            !is_null($subscription_sn) && $data['subscription_sn'] = $subscription_sn;

            if ($this->save($data)) {
                switch ($this->model->type) {
                    case self::TYPE_SUBSCRIPTION:
                        // 更新会员到期时间
                        $User = User::find($this->model->user_id);
                        $rank_change = $User->rank_id !== $this->model->rank_id;
                        $rank_change && $User->vip_started_at = time();
                        $User->rank_id = $this->model->rank_id;
                        if ($this->trial_days) {
                            // 试用天数
                            $User->vip_expired_at = Carbon::createFromTimestamp($User->vip_started_at)->addDays((int) $this->trial_days)->timestamp;
                        } else {
                            if ($this->model->rank_duration === 'forever') {
                                $Carbon = Carbon::createFromTimeString('9999-01-01 00:00:00');
                            } else  {
                                $arr = explode(' ', $this->model->rank_duration);
                                $function = 'add' . Str::title($arr[1]);
                                $Carbon = Carbon::createFromTimestamp(
                                    !$rank_change && $User->vip_expired_at && $User->vip_expired_at > $User->vip_started_at ?
                                        $User->vip_expired_at :
                                        $User->vip_started_at, 'PRC')->{$function}((int) $arr[0]);
                            }
                            $User->vip_expired_at = $Carbon->timestamp;
                        }
                        $User->save();

                        // 清除权限
                        UserService::resetPermissionUsed($this->model->user_id);
                        if (config('website.team.permission_isolation', false)) {
                            $Team = Team::query()
                                ->where('user_id', $User->id)
                                ->first();
                            if ($Team) {
                                TeamService::resetPermissionUsed($Team->id);
                            }
                        }

                        // 如果套餐时长是永久，则会走一次付款模式（虽然是订单类型是订阅），如果存在其他正在订阅的，需要取消
                        if ($this->model->rank_duration === 'forever') {
                            Subscription::query()
                                ->where('user_id',  $this->model->user_id)
                                ->where('subscription_status', SubscriptionService::STATUS_ACTIVE)
                                ->get(['id'])
                                ->each(function (Subscription $Subscription) {
                                    (new SubscriptionService($Subscription->id))->cancel(SubscriptionService::CANCELED_HANDLER_APPLICATION);
                                });
                        }
                        break;
                    default:
                        $order_type_class = self::getOrderTypeClass($this->model->type);
                        if ($order_type_class && method_exists($order_type_class, 'paid')) {
                            $order_type_class::paid($this->model->id, $others);
                        }
                        break;
                }

                // 更新会员价值
                $this->updateUserValue();

                /* 分销码处理 */
                $distribution_code = $this->model->distribution_code;
                if (!empty($distribution_code)) {
                    $distribution_code_data = DistributionCode::query()->where('distribution_code', '=', $distribution_code)->first();
                    $code_user_id = $distribution_code_data->user_id;
                    $rank_data = $distribution_code_data->rank_data;
                    if (!empty($rank_data)) {
                        $commission_rate = 0;
                        foreach ($rank_data as $rank_datum) {
                            if ($rank_datum['rank_id'] == $this->model->rank_id) {
                                $commission_rate = $rank_datum['commission_rate'] ?: 0;
                                break;
                            }
                        }
                    } else {
                        // 佣金比例
                        $commission_rate = $distribution_code_data->commission_rate;
                    }

                    // 佣金
                    $commission = bcdiv(bcmul((string)$this->model->paid_amount, (string)$commission_rate), '100', 2);
                    // 订单有效数加1
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->decrement('order_invalid_num');
                    // 订单无效数减1
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('order_effect_num');
                    // 收入数
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('income_num', $this->model->paid_amount);
                    // 未确认佣金
                    DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('not_confirmed_commission', $commission);
                    // 付费客户数
                    $tmp_order_id = Order::query()->where('user_id', '=', $this->model->user_id)->where('distribution_code', '=', $distribution_code)->where('order_status', '=', self::STATUS_PAID)->where('id', '<>', $this->model->id)->value('id');
                    if (!$tmp_order_id) {
                        DistributionInfo::query()->where('user_id', '=', $code_user_id)->increment('pay_customer');
                    }

                    // 分销记录表
                    $insert = [
                        'distribution_code' => $distribution_code,
                        'sure_status' => DistributionRecord::NO_SURE_STATUS,
                        'order_id' => $this->model->id,
                        'pay_status' => DistributionRecord::PAY_STATUS,
                        'sure_time' => $data['paid_at'] + DistributionInfo::SURE_TIME,
                        'commission_rate' => $commission_rate,
                        'commission' => $commission,
                    ];
                    DistributionRecord::create($insert);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        } finally {
            Tool::unlock($lock_key);
        }
    }

    /**
     * 后台手动创建订单
     * @param string $account 用户账号
     * @param int $type 订单类型
     * @param int $payment_platform 支付方式
     * @param float $paid_amount 支付金额
     * @param int $paid_at 支付时间
     * @param string $remark 备注
     * @param array $others 其它参数
     * @return OrderService
     * @throws \Throwable
     */
    public static function manualCreate($account, $type, $payment_platform, $paid_amount, $paid_at, $remark, $others = [])
    {
        $User = User::query()
            ->where('account', $account)
            ->first(['id', 'rank_id']);
        if (!$User) {
            throw new OrderException(400, '用户不存在');
        }

        $data = [
            'type' => $type,
            'order_sn' => self::generateOrderSN($User->id),
            'user_id' => $User->id,
            'first_time' => false,
            'order_status' => self::STATUS_UNPAID,
            'order_amount' => $paid_amount,
            'remark' => $remark,
            'payment_platform' => $payment_platform,
        ];

        switch ($type) {
            case self::TYPE_SUBSCRIPTION:
                if ($others['rank_id'] === RankService::FREE) {
                    throw new OrderException(400, 'Then plan id is invalid.');
                }

                $Rank = Rank::query()->find($others['rank_id']);
                $data['rank_id'] = $Rank->id;
                $data['rank_duration'] = $others['rank_duration'] ?: $Rank->duration;
                break;
            default:
                break;
        }

        $Service = make(self::class);
        try {
            Db::beginTransaction();

            if ($Service->save($data)) {
                if ($others['auto_paid'] ?? true) {
                    $Service->paid($paid_amount, $paid_at);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
        return $Service;
    }

    /**
     * 创建订阅订单
     * @param int $user_id 用户id
     * @param int $rank_id 套餐id
     * @param bool $first_time 首次购买
     * @return OrderService
     * @throws \Throwable
     */
    public static function createSubscriptionOrder($user_id, $rank_id, $first_time = false)
    {
        // 只允许购买价格大于0的套餐
        $rank_id = (int) $rank_id;
        if (!($rank_id !== RankService::FREE && ($Rank = Rank::find($rank_id)) && $Rank->allowed_buy && $Rank->price > 0)) {
            throw new OrderException(400, 'The plan id is invalid.');
        }

        // 如果购买的套餐和现在订阅的套餐相同，则不能购买
        $User = User::with('rank')->find($user_id, ['id', 'rank_id', 'vip_started_at', 'vip_expired_at']);
        if ($User->rank->allowed_buy && strtolower($User->rank->rank_name) === strtolower($Rank->rank_name) &&
            ($last_subcription = UserService::getLastSubcription($user_id)) && $last_subcription['subscription_status'] === SubscriptionService::STATUS_ACTIVE) {
            throw new OrderException(ErrorCode::SUBSCRIPTION_SAME_PLAN);
        }

        // 升级套餐
        if ($User->rank->allowed_buy && RankService::compareGrade($User->rank, $Rank) > 0) {
            // 会员原套餐的订单信息
            $Order = Order::query()
                ->where('user_id', $user_id)
                ->where('rank_id', $User->rank_id)
                ->whereIn('order_status', [OrderService::STATUS_PAID, OrderService::STATUS_REFUNDED])
                ->orderByDesc('id')
                ->first(['id', 'profit_amount']);
            if ($Order && $Order->profit_amount > 0) {
                $VipStartedAtCarbon = Carbon::createFromTimestamp($User->vip_started_at);
                $VipExpiredAtCarbon = Carbon::createFromTimestamp($User->vip_expired_at);
                $NowCarbon = Carbon::now();
                // 原套餐剩余金额 = 利润额 / 套餐天数 * 套餐剩余天数（向下取整）
                $prev_plan_remain_amount = $Order->profit_amount / floor(($VipExpiredAtCarbon->timestamp - $VipStartedAtCarbon->timestamp) / 3600 / 24) * floor(($VipExpiredAtCarbon->timestamp - $NowCarbon->timestamp) / 3600 / 24);

                // 套餐每天价格 = 套餐总价 / 套餐天数
                if ($Rank->duration !== 'forever') {
                    $duration = explode(' ', $Rank->duration);
                    $ExpiredCarbon = Carbon::createFromTimestamp($NowCarbon->timestamp)->{'add' . Str::title($duration[1])}(intval($duration[0]));
                    $price_of_day = $Rank->price / floor(($ExpiredCarbon->timestamp - $NowCarbon->timestamp) / 3600 / 24);

                    // 折算剩余天数
                    $prev_plan_reamin_days = floor($prev_plan_remain_amount / $price_of_day);
                }
            }
        }

        // 首次购买
        if ($first_time) {
            $first_time = Db::table('orders')
                    ->where('user_id', $user_id)
                    ->whereIn('order_status', [self::STATUS_PAID, self::STATUS_REFUNDED])
                    ->count() === 0;
        } else {
            $first_time = false;
        }

        $order_amount = $Rank->price;
        $others = [
            'rank_id' => $Rank->id,
            'rank_duration' => $Rank->duration,
            'first_time' => $first_time,
        ];
        if ($Rank->duration !== 'forever' && $first_time && $Rank->trial_days) {
            $others['trial_days'] = $Rank->trial_days;
        }
        if (isset($prev_plan_reamin_days)) {
            $order_amount = 0;
            $others['trial_days'] = $others['prev_plan_reamin_days'] = $prev_plan_reamin_days;
        }

        return self::create($user_id, self::TYPE_SUBSCRIPTION, $order_amount, $others);
    }

    /**
     * 创建订单
     * @param int $user_id 用户id
     * @param int $type 订单类型
     * @param float $order_amount 订单金额
     * @param array $others 其它参数
     * @return OrderService
     * @throws \Throwable
     */
    public static function create($user_id, $type, $order_amount, $others = [])
    {
        $data = array_merge([
            'order_sn' => self::generateOrderSN($user_id),
            'user_id' => $user_id,
            'type' => $type,
            'order_amount' => $order_amount,
        ], $others);
        try {
            Db::beginTransaction();

            $Service = make(self::class);
            $Service->save($data);

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
        return $Service;
    }

    /**
     * 生成订单号
     * @param $user_id
     * @return string
     * @throws \Throwable
     */
    public static function generateOrderSN($user_id): string
    {
        $order_sn = '';
        retry(2, function () use ($user_id, &$order_sn) {
            $rand_num = rand(111, 9999);
            $order_sn = time() . substr((string) ($user_id + $rand_num), -3) . substr((string) $rand_num, -3);
            if (Db::table('orders')->where('order_sn', $order_sn)->count() > 0) {
                $order_sn = '';
                throw new OrderException(400, 'failed to generate');
            }
        }, 500);

        return $order_sn;
    }

    /**
     * @param $attributes
     * @return bool
     * @throws \Exception
     */
    public function save($attributes)
    {
        [$data, $error] = $this->validation->check($this->rules, $attributes, $this);
        if ($data === null) {
            throw new OrderException(400, implode(PHP_EOL, $error));
        }

        return $this->model->forceFill($data)->save();
    }

    /**
     * 验证规则
     * @var array
     */
    protected $rules = [
        'order_sn|order sn' => '',
        'user_id|user id' => 'integer',
        'rank_id|rank id' => 'integer',
        'rank_duration|rank duration' => 'cb_ruleCheckDuration',
        'first_time|first time' => 'boolean',
        'order_status|order status' => 'cb_ruleCheckOrderStatus',
        'payment_platform|payment platform' => 'integer',
        'subscription_sn|subscription sn' => '',
        'order_amount|order amount' => 'gte:0',
        'paid_amount|pay amount' => 'numeric',
        'refunded_amount|refunded amount' => 'numeric',
        'profit_amount|profit amount' => 'numeric',
        'remark|remark' => '',
        'paid_at|paid at' => '',
        'refunded_at|refunded at' => '',
        'stripe_invoice|stripe invoice' => '',
        'hosted_invoice_url|hosted invoice url' => '',
        'invoice_pdf|invoice pdf' => '',
        'transaction_number|transaction number' => '',
        'prev_plan_reamin_days|prev plan reamin days' => '',
        'distribution_code|distribution code' => '',
        'trial_days|trial days' => '',
        'type|type' => 'cb_ruleCheckType',
    ];

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckDuration($attribute, $value)
    {
        return RankService::ruleCheckDuration($attribute, $value);
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckOrderStatus($attribute, $value)
    {
        if (is_null($this->statusText($value))) {
            return 'is invalid';
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckType($attribute, $value)
    {
        if (!(array_column(self::typeList(), null, 'value')[$value] ?? null)) {
            return 'is invalid';
        }

        return true;
    }

    /**
     * 订单类型
     * @return array
     */
    public static function typeList()
    {
        return array_map(function ($item) {
            return Arr::only($item, ['text', 'value']);
        }, array_merge([
            [
                'text' => '订阅订单',
                'value' => self::TYPE_SUBSCRIPTION,
            ]
        ], config('website.order.types', [])));
    }

    /**
     * 所有状态
     * @return array
     */
    public static function statusList()
    {
        return [
            [
                'text' => '未支付',
                'value' => self::STATUS_UNPAID,
            ],
            [
                'text' => '已支付',
                'value' => self::STATUS_PAID,
            ],
            [
                'text' => '已退款',
                'value' => self::STATUS_REFUNDED,
            ],
        ];
    }

    /**
     * 状态名字
     * @param $status
     * @return string|null
     */
    public static function statusText($status)
    {
        $result = Arr::where(self::statusList(), function ($v) use ($status) {
            return $v['value'] == $status;
        });
        return $result ? array_values($result)[0]['text'] : null;
    }

    /**
     * 所有支付平台
     * @return array
     */
    public static function paymentPlatformList()
    {
        return [
            [
                'text' => '线下支付',
                'value' => self::PAYMENT_PLATFORM_OFFLINE,
            ],
            [
                'text' => 'stripe',
                'value' => self::PAYMENT_PLATFORM_STRIPE,
            ],
        ];
    }

    /**
     * 支付平台名字
     * @param int $status
     * @return string|null
     */
    public static function paymentPlatformText(?int $status = null)
    {
        $result = Arr::where(self::paymentPlatformList(), function ($v) use ($status) {
            return $v['value'] == $status;
        });
        return $result ? array_values($result)[0]['text'] : null;
    }

    /**
     * 获取账单模板的数据
     * @param int $order_id 订单id
     * @param bool $logs_enable 是否记录日志
     * @return array|null
     */
    public static function getInvoiceTemplateData(int $order_id, bool $logs_enable = true)
    {
        $Order = Order::with('rank:id,rank_name')
            ->find($order_id, ['user_id', 'order_sn', 'rank_id', 'rank_duration', 'paid_at', 'paid_amount', 'stripe_invoice', 'type']);
        if (!$Order) {
            return null;
        }

        $user = User::query()
            ->find($Order->user_id, ['account', 'username', 'email', 'company', 'country', 'province', 'city', 'postal', 'address', 'phone', 'vat'])
            ->toArray();

        try {
            // 下载日志数据
            $log_data = [
                'user_id' => $Order->user_id,
                'email' => $user['email'] ?: $user['account'],
                'order_id' => $order_id,
                'order_sn' => $Order->order_sn,
                'created_at' => time(),
                'status' => 1,
                'error_message' => null,
                'result' => null,
            ];

            $order = $Order->toArray();
            if ($order['paid_at'] && $order['type'] === OrderService::TYPE_SUBSCRIPTION) {
                if ($order['rank_duration'] === 'forever') {
                    $Carbon = Carbon::createFromTimeString('9999-01-01 00:00:00');
                } else  {
                    $arr = explode(' ', $order['rank_duration']);
                    $function = 'add' . Str::title($arr[1]);
                    $Carbon = Carbon::createFromTimestamp($order['paid_at'])->{$function}((int) $arr[0]);
                }
                $order['next_period_start'] = $Carbon->timestamp;
            }
            // 获取stripe的账单链接
            $invoice_url = null;
            if ($order['stripe_invoice']) {
                try {
                    retry(3, function () use ($order, &$invoice_url) {
                        $StripeInvoice = Cashier::stripe()->invoices->retrieve($order['stripe_invoice']);
                        $invoice_url = $StripeInvoice->hosted_invoice_url;
                    }, 300);
                } catch (\Throwable $e) {
                    $stripe_error_logs = array_merge($log_data, [
                        'error_message' => $e->getMessage(),
                        'status' => 0,
                    ]);
                    go(function () use ($stripe_error_logs) {
                        MongoDb::collection('invoice_template_stripe_errors')->insertOne($stripe_error_logs);
                    });
                }
            }
            $order['invoice_url'] = $invoice_url;
            unset($order['user_id'], $order['rank_id'], $order['rank_duration'], $order['rank']['id'], $order['rank']['discounts'], $order['stripe_invoice']);
            if ($order['type'] !== OrderService::TYPE_SUBSCRIPTION) {
                unset($order['rank']);
            }

            $result = [
                'order' => $order,
                'user' => $user,
            ];
            $log_data['result'] = $result;
            return $result;
        } catch (\Throwable $e) {
            $log_data['status'] = 0;
            $log_data['error_message'] = $e->getMessage();
        } finally {
            if ($logs_enable) {
                go(function () use ($log_data) {
                    MongoDb::collection('invoice_template_data_logs')->insertOne($log_data);
                });
            }
        }
    }

    /**
     * 更新会员价值
     */
    protected function updateUserValue()
    {
        $query = 'update users
set user_value=ifnull((select sum(orders.profit_amount) from orders where orders.user_id=users.id and orders.order_status in(2,3)), 0)
where users.id=?';
        Db::update($query, [$this->user_id]);
    }

    /**
     * 获取订单类型对应的class
     * @param int $type 订单类型
     * @param $type
     * @return mixed|null
     */
    public static function getOrderTypeClass($type)
    {
        $order_types = array_column(config('website.order.types', []), null, 'value');
        return $order_types[$type]['class'] ?? null;
    }
}
