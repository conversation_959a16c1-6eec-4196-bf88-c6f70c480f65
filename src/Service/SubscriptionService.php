<?php

declare(strict_types=1);
namespace Website\Common\Service;

use Website\Common\Model\Subscription;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\Utils\Arr;
use Lete\Pay\Cashier;
use Stripe\Subscription as StripeSubscription;
use Website\Common\Model\User;

/**
 * Class SubscriptionService
 * @package App\Service
 */
class SubscriptionService extends ModelServiceAbstract
{
    protected $modelProvider = Subscription::class;

    /**
     * 订阅状态-已生效
     * @var int
     */
    const STATUS_ACTIVE = 1;

    /**
     * 订阅状态-已取消
     * @var int
     */
    const STATUS_CANCELED = 2;

    /**
     * 取消来源-会员
     * @var string
     */
    const CANCELED_HANDLER_MEMBER = 'member';

    /**
     * 取消来源-网站后台
     * @var string
     */
    const CANCELED_HANDLER_ADMIN = 'admin';

    /**
     * 取消来源-Stripe平台
     * @var string
     */
    const CANCELED_HANDLER_STRIPE = 'stripe';

    /**
     * 取消来源-程序
     */
    const CANCELED_HANDLER_APPLICATION = 'application';

    /**
     * @Inject
     * @var Redis
     */
    protected $redis;

    /**
     * 取消订阅
     * @param $canceled_handler
     * @param int|null $canceled_at
     * @param array $others
     * @throws \Throwable
     */
    public function cancel($canceled_handler, ?int $canceled_at = null, $others = [])
    {
        try {
            Db::beginTransaction();

            $data = array_merge([
                'subscription_status' => self::STATUS_CANCELED,
                'canceled_handler' => $canceled_handler,
                'canceled_at' => $canceled_at ?: time(),
            ], $others);
            if ($this->canceled_handler) {
                unset($data['canceled_handler']);
            }
            $this->save($data);

            // 该账号下，没有任务订阅，取消用户的订阅状态
            if (!Subscription::query()
                ->where('user_id',  $this->user_id)
                ->where('subscription_status', self::STATUS_ACTIVE)
                ->exists()) {
                User::query()
                    ->where('id', $this->user_id)
                    ->update([
                        'subscription_status' => false,
                        'subscription_platform' => null,
                        'subscription_started_at' => null,
                        'subscription_next_deduct_at' => null,
                    ]);
            }

            // 通过api取消订阅
            if ($this->payment_platform === OrderService::PAYMENT_PLATFORM_STRIPE && $this->platform_status !== StripeSubscription::STATUS_CANCELED) {
                $StripeSubscription = Cashier::stripe()->subscriptions->retrieve($this->subscription_sn);
                if ($StripeSubscription->status === StripeSubscription::STATUS_CANCELED) {
                    $this->save([
                        'platform_status' => StripeSubscription::STATUS_CANCELED,
                        'canceled_at' => $StripeSubscription->canceled_at,
                    ]);
                } else {
                    Cashier::stripe()->subscriptions->cancel($this->subscription_sn);
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }
    }

    /**
     * @param $payment_platform
     * @param $subscription_sn
     * @param array $others
     * @return SubscriptionService
     * @throws \Exception
     */
    public static function make($payment_platform, $subscription_sn)
    {
        $Subscriptions = Db::table('subscriptions')->where([
            ['payment_platform', $payment_platform],
            ['subscription_sn', $subscription_sn],
        ])->first();
        return $Subscriptions ? make(self::class, [$Subscriptions->id]) : null;
    }

    /**
     * 查找或者创建
     * @param $payment_platform
     * @param $subscription_sn
     * @param array $others
     * @return SubscriptionService
     * @throws \Throwable
     */
    public static function firstOrCreate($payment_platform, $subscription_sn, $others = [])
    {
        $Subscriptions = Db::table('subscriptions')->where([
            ['payment_platform', $payment_platform],
            ['subscription_sn', $subscription_sn],
        ])->first();
        $Service = make(self::class, [$Subscriptions ? $Subscriptions->id : null]);
        if (!$Subscriptions && !$Service->redis->set(env('APP_NAME') . ":subscription:{$payment_platform}:{$subscription_sn}", 1, ['NX', 'EX' => 1])) {
            return $Service;
        }

        try {
            Db::beginTransaction();

            $data = array_merge([
                'payment_platform' => $payment_platform,
                'subscription_sn' => $subscription_sn,
            ], $others);
            if (isset($data['platform_status']) && in_array($data['platform_status'], [StripeSubscription::STATUS_ACTIVE, StripeSubscription::STATUS_TRIALING])) {
                $data['subscription_status'] = self::STATUS_ACTIVE;
            }
            $Service->save($data);

            // 更新会员订阅信息
            if ($Service->subscription_status === self::STATUS_ACTIVE) {
                User::query()
                    ->where('id', $Service->user_id)
                    ->update([
                    'subscription_status' => true,
                    'subscription_platform' => $Service->payment_platform,
                    'subscription_started_at' => $Service->start_date,
                    'subscription_next_deduct_at' => $Service->next_period_start,
                ]);
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw $e;
        }

        // 存在其他正在订阅的，需要取消
        if (isset($data['platform_status']) && in_array($data['platform_status'], [StripeSubscription::STATUS_ACTIVE, StripeSubscription::STATUS_TRIALING])) {
            Subscription::query()
                ->where('user_id',  $Service->user_id)
                ->where('subscription_status', self::STATUS_ACTIVE)
                ->where('id', '<>', $Service->id)
                ->get(['id'])
                ->each(function ($item) {
                    (new SubscriptionService($item->id))->cancel(self::CANCELED_HANDLER_APPLICATION);
                });
        }

        return $Service;
    }

    /**
     * @param $attributes
     * @return bool
     * @throws \Exception
     */
    public function save($attributes)
    {
        [$data, $error] = $this->validation->check($this->rules, $attributes, $this);
        if ($data === null) {
            throw new \Exception(implode(PHP_EOL, $error));
        }

        return $this->model->forceFill($data)->save();
    }

    /**
     * 验证规则
     * @var array
     */
    protected $rules = [
        'user_id|user id' => 'integer',
        'payment_platform|payment platform' => 'integer',
        'subscription_sn|subscription sn' => '',
        'subscription_status|subscription status' => '',
        'platform_status|platform status' => '',
        'product_name|product name' => '',
        'start_date|start date' => 'int',
        'next_period_start|next period start' => 'int',
        'next_period_amount|next period amount' => 'numeric',
        'canceled_handler|canceled handler' => '',
        'canceled_at|canceled at' => '',
    ];

    /**
     * 所有状态
     * @return array
     */
    public static function statusList()
    {
        return [
            [
                'text' => '已生效',
                'value' => self::STATUS_ACTIVE,
            ],
            [
                'text' => '已取消',
                'value' => self::STATUS_CANCELED,
            ],
        ];
    }

    /**
     * 状态名字
     * @param $status
     * @return string|null
     */
    public static function statusText($status)
    {
        $result = Arr::where(self::statusList(), function ($v) use ($status) {
            return $v['value'] == $status;
        });
        return $result ? array_values($result)[0]['text'] : null;
    }

    /**
     * 所有取消来源
     * @return array
     */
    public static function canceledHandlerList()
    {
        return [
            [
                'text' => self::CANCELED_HANDLER_ADMIN,
                'value' => self::CANCELED_HANDLER_ADMIN,
            ],
            [
                'text' => self::CANCELED_HANDLER_STRIPE,
                'value' => self::CANCELED_HANDLER_STRIPE,
            ],
            [
                'text' => self::CANCELED_HANDLER_MEMBER,
                'value' => self::CANCELED_HANDLER_MEMBER,
            ],
            [
                'text' => self::CANCELED_HANDLER_APPLICATION,
                'value' => self::CANCELED_HANDLER_APPLICATION,
            ],
        ];
    }
}
