<?php

declare(strict_types=1);
namespace Website\Common\Service;

use GuzzleHttp\Client;

class FeiShuBotService
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var string|null
     */
    protected $secret;

    /**
     * @var string|null
     */
    protected $base_uri;

    /**
     * FeiShuBotService constructor.
     * @param string|null $webhook
     * @param string|null $secret
     */
    public function __construct(string $webhook = null, string $secret = null)
    {
        $this->secret = $secret ?: env('FEI_SHU_BOT_SECRET');
        $this->webhook = $webhook ?: env('FEI_SHU_BOT_WEBHOOK');
        $this->client = new Client([
            'base_uri' => $this->webhook,
            'verify' => false,
            'timeout' => 30,
            'headers' => [
                'content-type' => 'application/json',
            ],
        ]);
    }

    /**
     * @param int $timestamp
     * @return string
     */
    protected function genSign(int $timestamp)
    {
        return base64_encode(hash_hmac('sha256', "", "{$timestamp}\n{$this->secret}", true));
    }

    /**
     * 发送文本消息
     * @param string $text
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendTextMessage(string $text)
    {
        return $this->sendMessage([
            'msg_type' => 'text',
            'content' => [
                'text' => $text
            ],
        ]);
    }

    /**
     * 发送消息卡片
     * @param array $card
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendInteractiveMessage(array $card)
    {
        return $this->sendMessage([
            'msg_type' => 'interactive',
            'card' => $card,
        ]);
    }

    /**
     * @param array $json
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendMessage(array $json)
    {
        if (!$this->webhook) {
            return null;
        }

        if ($this->secret) {
            $json['timestamp'] = time();
            $json['sign'] = $this->genSign($json['timestamp']);
        }
        $response = $this->client->post('', ['json' => $json]);
        $result = json_decode($response->getBody()->getContents(), true);
        return $result;
    }
}
