<?php

declare(strict_types=1);

namespace Website\Common\Service;

use Website\Common\Model\Rank;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Cache\Listener\DeleteListenerEvent;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Lete\Pay\Cashier;
use Psr\EventDispatcher\EventDispatcherInterface;
use Website\Common\Utils\Website;

class RankService
{
    /**
     * 等级套餐-Free
     * @var int
     */
    const FREE = 1;

    /**
     * 所有次数权限
     * @return array|mixed
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissions()
    {
        return Website::config()->get('website.rank.quota_permissions', []);
    }

    /**
     * 获取权限标题
     * @param string $permission_name 权限名称
     * @return mixed|null
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getPermissionTitle($permission_name)
    {
        return static::getPermissions()[$permission_name]['title'] ?? null;
    }

    /**
     * 权限管理的基础数据结构
     * @return array|mixed
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function structurePermission()
    {
        return Website::config()->get('website.rank.structure_permissions', []);
    }

    /**
     * 把权限转为一维数组
     * @param array $permission 权限
     * @param string $prefix 前缀
     * @return array
     */
    public static function flattenPermission($permission = [], $prefix = '')
    {
        $result = array();
        foreach ($permission as $key => $value) {
            if (is_array($value)) {
                $result = array_merge($result, static::flattenPermission($value, $prefix . $key . '_'));
            } else {
                $result[$prefix . $key] = $value;
            }
        }
        return $result;
    }

    /**
     * 按套餐名称重新整合
     * @return array
     */
    public static function integration()
    {
        $data = [];
        Rank::query()
            ->get()
            ->groupBy('rank_name')
            ->each(function (Collection $item, $key) use (&$data) {
                $data[] = [
                    'rank_name' => $key,
                    'id' => implode(',', $item->groupBy('id')->keys()->values()->toArray()),
                    'details' => $item->toArray(),
                ];
            });
        return $data;
    }

    /**
     * 同步产品和价格到stripe
     * @throws \Stripe\Exception\ApiErrorException
     */
    public static function syncStripeProduct($rank_id)
    {
        if (!env('STRIPE_KEY')) {
            return;
        }

        $Rank = Rank::query()->find($rank_id);
        if (!$Rank || !$Rank->price) {
            return;
        }

        // 不存在产品id则创建
        if (!$Rank->stripe_product_id) {
            $StripeProduct = Cashier::stripe()->products->create([
                'name' => $Rank->product_name,
                'active' => true,
                'description' => $Rank->rank_name,
            ]);
            $Rank->stripe_product_id = $StripeProduct->id;
        }

        // 获取产品所有价格后，判断当前价格id和价钱是否一致，不一致需要替换或者创建
        if ($Rank->stripe_price_id) {
            $StripeCollection = Cashier::stripe()->prices->all(['product' => $Rank->stripe_product_id, 'expand' => ['data.tiers']]);
            $StripePrices = array_values(array_filter($StripeCollection->data, function ($StripePrice) use ($Rank) {
                return $StripePrice->id === $Rank->stripe_price_id && $StripePrice->unit_amount === intval($Rank->price * 100);
            }));

            // 没有匹配到，则检查是否有价格相同的，有则替换
            if (!$StripePrices) {
                $StripePrices = array_values(array_filter($StripeCollection->data, function ($StripePrice) use ($Rank) {
                    return $StripePrice->unit_amount === intval($Rank->price * 100);
                }));
                $Rank->stripe_price_id = $StripePrices ? $StripePrices[0]->id : '';
            }
        }

        // 不存在价格id则创建
        if (!$Rank->stripe_price_id) {
            $params = [
                'product' => $Rank->stripe_product_id,
                'unit_amount' => $Rank->price * 100,
                'currency' => 'usd',
            ];
            if ($Rank->duration !== 'forever') {
                $duration_exploder = explode(' ', $Rank->duration);
                $params['recurring'] = [
                    'interval' => Str::singular($duration_exploder[1]),
                    'interval_count' => intval($duration_exploder[0]),
                ];
            }
            $StripePrice = Cashier::stripe()->prices->create($params);
            $Rank->stripe_price_id = $StripePrice->id;
        }

        // 有优惠的情况，需要替换或者创建优惠券
        if ($Rank->first_price > 0 && $Rank->price > $Rank->first_price) {
            $amount_off = ($Rank->price - $Rank->first_price) * 100;
            try {
                $Rank->stripe_coupon_id && $StripeCoupon = Cashier::stripe()->coupons->retrieve($Rank->stripe_coupon_id);
                isset($StripeCoupon) && $StripeCoupon->amount_off != $amount_off && $Rank->stripe_coupon_id = null;
            } catch (\Throwable $e) {
                // 处理：不存在优惠券id，会抛出错误
                $Rank->stripe_coupon_id = null;
            }
            // 不存在又优惠券id则创建
            if (!$Rank->stripe_coupon_id) {
                $StripeCoupon = Cashier::stripe()->coupons->create([
                    'name' => 'US$' . $amount_off / 100 . ' OFF',
                    'amount_off' => $amount_off,
                    'currency' => 'usd',
                    'duration' => 'once',
                    'applies_to' => [
                        'products' => [$Rank->stripe_product_id],
                    ]
                ]);
                $Rank->stripe_coupon_id = $StripeCoupon->id;
            }
        } else {
            $Rank->stripe_coupon_id = null;
        }

        if ($Rank->isDirty()) {
            $Rank->save();
        }
    }

    /**
     * 生成产品名称
     * @param string $rank_name 套餐名称
     * @param string $duration 时长
     * @return string
     */
    public static function generateProductName($rank_name, $duration)
    {
        $arr = explode(' ', $duration);
        $duration = count($arr) === 1 ? $duration : "{$arr[0]} " . ($arr[0] > 1 ? $arr[1] : Str::singular($arr[1]));
        return Str::title($rank_name . ' subscribe / ' . $duration);
    }

    /**
     * @Cacheable(prefix="planIds", ttl=-1, listener="planIdsDelete")
     * @return array
     */
    public static function getPlanIds()
    {
        $ids = Rank::query()
            ->where('allowed_buy', 1)
            ->where('price', '>', 0)
            ->distinct()
            ->get('id')
            ->groupBy('id')
            ->keys()
            ->values()
            ->toArray();
        return $ids;
    }

    /**
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function flushPlanIds()
    {
        ApplicationContext::getContainer()
            ->get(EventDispatcherInterface::class)
            ->dispatch(new DeleteListenerEvent('planIdsDelete', []));
    }

    /**
     * 比较两个套餐的等级
     * @param Rank $current_plan_name 当前套餐名称
     * @param Rank $target_plan_name 目标套餐名称
     * @return bool|int
     * 如果结果为false，则表示无法比较
     * 如果结果是0，则表示两个套餐等级相同
     * 如果结果大于0，则表示需要升级
     * 如果结果小于0，则表示需要降级
     */
    public static function compareGrade(Rank $current_plan, Rank $target_plan)
    {
        // 如果非标准套餐，则无法比较
        if (!$current_plan->allowed_buy || !$target_plan->allowed_buy) {
            return false;
        }

        if ($current_plan->rank_name === $target_plan->rank_name) {
            return 0;
        }
        return $target_plan->price - $current_plan->price;
    }

    /**
     * 验证规则-时长
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public static function ruleCheckDuration($attribute, $value)
    {
        $error = '必须是：x days、x weeks、x months、x years、forever，其中 x 必须是大于0的整数';
        $arr = explode(' ', $value);
        if (!in_array(count($arr), [1, 2])) {
            return $error;
        }
        if (!(count($arr) === 1 && $arr[0] === 'forever') &&
            !(count($arr) === 2 && is_int((int) $arr[0]) && $arr[0] > 0 && in_array($arr[1], ['days', 'weeks', 'months', 'years']))) {
            return $error;
        }
        return true;
    }
}
