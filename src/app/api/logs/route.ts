import Log from '@/models/Log'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const { level, message, metadata, service, userId } = await request.json()

    const log = new Log({
      level,
      message,
      metadata,
      service,
      userId,
      timestamp: new Date()
    })
    await log.save()

    return NextResponse.json({
      status: 200,
      message: 'Log saved successfully',
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        Pragma: 'no-cache',
        Expires: '0'
      }
    })
  } catch (error) {
    return NextResponse.json({
      status: 500,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
