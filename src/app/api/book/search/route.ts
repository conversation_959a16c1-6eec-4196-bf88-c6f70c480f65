import { NextRequest, NextResponse } from 'next/server';
import * as BookService from '@/services/book.service';

/**
 * 书籍搜索API
 * GET /api/book/search
 *
 * 查询参数:
 * - query: 搜索关键词
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 12)
 * - language: 语言代码 (默认: en)
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const language = searchParams.get('language') || 'en';

    // 验证搜索关键词
    if (!query.trim()) {
      return NextResponse.json({
        success: false,
        error: 'Search keyword is required'
      }, { status: 400 });
    }

    // 调用服务层搜索书籍
    const results = await BookService.searchBooks(query, page, limit, language);

    // 返回成功结果
    return NextResponse.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Search API error:', error);

    // 返回错误信息
    return NextResponse.json({
      success: false,
      error: 'Failed to search books'
    }, { status: 500 });
  }
}
