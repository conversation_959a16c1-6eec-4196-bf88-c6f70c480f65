import { NextRequest, NextResponse } from 'next/server'
import { getUserInfo } from '@/services/server/userService'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 ===== Test API Route Started =====')
    console.log('📍 Test Info:', {
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent')
    })

    // 检查当前Cookie状态
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')

    console.log('🍪 Test Route Cookie Check:', {
      totalCookies: allCookies.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.value.length || 0,
      cookieNames: allCookies.map(c => c.name)
    })

    // 测试直接fetch调用（模拟浏览器直接访问）
    console.log('🌐 Testing direct fetch to /common-api/v1/user...')
    let directFetchResult = null
    try {
      // 构建Cookie字符串
      const cookieString = allCookies
        .map((cookie) => `${cookie.name}=${cookie.value}`)
        .join('; ')

      console.log('🍪 Cookie string for direct fetch:', {
        length: cookieString.length,
        preview: cookieString.substring(0, 200) + (cookieString.length > 200 ? '...' : '')
      })

      const directResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/common-api/v1/user`, {
        method: 'GET',
        headers: {
          'Cookie': cookieString,
          'User-Agent': request.headers.get('user-agent') || 'Next.js Server',
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      console.log('📥 Direct fetch response:', {
        status: directResponse.status,
        statusText: directResponse.statusText,
        headers: Object.fromEntries(directResponse.headers.entries())
      })

      if (directResponse.ok) {
        directFetchResult = await directResponse.json()
        console.log('✅ Direct fetch success:', {
          hasData: !!directFetchResult.data,
          code: directFetchResult.code,
          message: directFetchResult.message
        })
      } else {
        const errorText = await directResponse.text()
        console.log('❌ Direct fetch failed:', {
          status: directResponse.status,
          errorText: errorText.substring(0, 500)
        })
        directFetchResult = { error: `HTTP ${directResponse.status}: ${errorText}` }
      }
    } catch (directError) {
      console.error('❌ Direct fetch error:', directError)
      directFetchResult = { error: directError instanceof Error ? directError.message : 'Unknown error' }
    }

    // 调用 getUserInfo，这会触发 apiServer 的调试日志
    console.log('🚀 Calling getUserInfo via apiServer...')
    const userInfo = await getUserInfo()
    console.log('📊 getUserInfo Result:', {
      success: !!userInfo,
      userEmail: userInfo?.email || 'N/A',
      hasUserData: !!userInfo
    })

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      cookieInfo: {
        totalCookies: allCookies.length,
        hasAuthToken: !!authToken,
        authTokenLength: authToken?.value.length || 0
      },
      // apiServer调用结果
      apiServerResult: {
        userInfo: userInfo ? {
          email: userInfo.email,
          username: userInfo.username,
          hasAvatar: !!userInfo.avatar
        } : null,
        success: !!userInfo,
        message: userInfo ? 'User info retrieved successfully via apiServer' : 'No user info available via apiServer'
      },
      // 直接fetch调用结果
      directFetchResult: {
        success: directFetchResult && !directFetchResult.error,
        data: directFetchResult,
        message: directFetchResult && !directFetchResult.error ? 'Direct fetch successful' : 'Direct fetch failed'
      },
      // 对比分析
      comparison: {
        bothSuccessful: !!userInfo && directFetchResult && !directFetchResult.error,
        apiServerOnly: !!userInfo && (!directFetchResult || directFetchResult.error),
        directFetchOnly: !userInfo && directFetchResult && !directFetchResult.error,
        bothFailed: !userInfo && (!directFetchResult || directFetchResult.error),
        recommendation: getRecommendation(userInfo, directFetchResult)
      }
    })

  } catch (error) {
    console.error('❌ Test API Route Error:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 生成调试建议
 */
function getRecommendation(userInfo: any, directFetchResult: any): string {
  if (userInfo && directFetchResult && !directFetchResult.error) {
    return '✅ 两种方式都成功，认证系统工作正常'
  }

  if (!userInfo && directFetchResult && !directFetchResult.error) {
    return '🔧 直接访问成功但apiServer失败，检查apiServer的Cookie传递逻辑'
  }

  if (userInfo && (!directFetchResult || directFetchResult.error)) {
    return '🔧 apiServer成功但直接访问失败，检查网络配置或URL重写规则'
  }

  return '❌ 两种方式都失败，检查认证Token和Redis配置'
}
