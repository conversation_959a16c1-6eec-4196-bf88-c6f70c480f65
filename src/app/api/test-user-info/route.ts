import { NextRequest, NextResponse } from 'next/server'
import { getUserInfo } from '@/services/server/userService'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 ===== Test API Route Started =====')
    console.log('📍 Test Info:', {
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent')
    })

    // 检查当前Cookie状态
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')
    
    console.log('🍪 Test Route Cookie Check:', {
      totalCookies: allCookies.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.value.length || 0,
      cookieNames: allCookies.map(c => c.name)
    })

    // 调用 getUserInfo，这会触发 apiServer 的调试日志
    console.log('🚀 Calling getUserInfo...')
    const userInfo = await getUserInfo()
    console.log('📊 getUserInfo Result:', {
      success: !!userInfo,
      userEmail: userInfo?.email || 'N/A',
      hasUserData: !!userInfo
    })

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      cookieInfo: {
        totalCookies: allCookies.length,
        hasAuthToken: !!authToken,
        authTokenLength: authToken?.value.length || 0
      },
      userInfo: userInfo ? {
        email: userInfo.email,
        username: userInfo.username,
        hasAvatar: !!userInfo.avatar
      } : null,
      message: userInfo ? 'User info retrieved successfully' : 'No user info available'
    })

  } catch (error) {
    console.error('❌ Test API Route Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
