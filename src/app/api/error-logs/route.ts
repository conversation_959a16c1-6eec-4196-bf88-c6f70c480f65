import { NextRequest, NextResponse } from 'next/server'
import dbConnect from '@/lib/mongodb'
import reportError from '@/services/server/errorService'

export async function POST(request: NextRequest) {
  try {
    // 连接数据库
    await dbConnect()

    // 解析请求体
    const data = await request.json()

    // 创建错误日志
    await reportError({
      ...data,
      userAgent: request.headers.get('user-agent') || undefined
    })

    return NextResponse.json({ success: true }, { status: 201 })
  } catch (error) {
    console.error('Error log API error:', error)
    return NextResponse.json({ success: false, message: 'Error log API error' }, { status: 500 })
  }
}
