/**
 * 音频生成API路由 - 简化版S3 + TTS方案 + 用户认证
 * 负责处理书籍音频生成请求，使用简化的单次TTS生成流程
 * 集成ElevenLabs TTS、S3存储、并发控制和用户认证
 * 要求用户登录才能使用音频功能
 *
 * 简化说明：
 * - 移除了复杂的文本分段处理逻辑
 * - 移除了批量处理和音频合并功能
 * - 使用直接的单次TTS生成方式
 * - 保留S3存储、缓存、并发控制等核心功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { adaptDynamicRoute, auth } from '@/lib/error/withRouteErrorHandling';
import { getBookById } from '@/services/book.service';
import { AudioApiResponse } from '@/types/audio.types';
import {
  validateBookId,
  validateLocale,
  validateTextLength,
  calculateCharacterCount,
  checkEnvironmentConfig,
  generateRequestId
} from '@/lib/audio/file-utils';
import { generateTTSAudio, estimateAudioDuration } from '@/lib/audio/elevenlabs';
// 注释掉复杂的分段处理逻辑，改为简单的一次性生成
// import { analyzeSegmentationNeeds } from '@/lib/audio/segmentation';
// import { processBatchSegments } from '@/lib/audio/batch-processor';
// import { mergeAudioSegments, cleanupTempFiles } from '@/lib/audio/merger';
import { createS3Manager } from '@/lib/audio/s3';
import { setCachedAudioMetadata } from '@/lib/audio/cache';
import { checkAudioFileWithCache } from '@/lib/audio/file-checker';
import { createConcurrencyManager } from '@/lib/audio/concurrency';
import {
  logAudioRequestStart,
  logAudioGenerationComplete,
  logValidationError,
  logError,
  logChapterProcessing
} from '@/lib/audio/logger';
import {
  assembleChapterTextFromObjects,
  getChapterStatsFromObjects,
  hasValidChapterObjects
} from '@/lib/audio/chapter-utils';

/**
 * 音频生成处理函数 - 使用withRoute + auth中间件
 * 要求用户登录，自动处理认证、错误和国际化
 */
const audioHandler = async (
  request: NextRequest,
  context: RequestContext,
  routeParams: { bookId: string }
): Promise<NextResponse<AudioApiResponse>> => {
  const startTime = Date.now();
  const requestId = generateRequestId();

  // 1. 环境配置检查
  const envCheck = checkEnvironmentConfig();
  if (!envCheck.valid) {
    logError('Environment configuration invalid', {
      missing: envCheck.missing,
      requestId
    });
    return NextResponse.json({
      success: false,
      error: 'Audio service configuration error'
    }, { status: 500 });
  }

  // 2. 获取用户ID（认证中间件自动注入）
  const userId = parseInt(context.params.userId as string);

  // 3. 参数验证
  const { bookId } = routeParams;
  const { searchParams } = new URL(request.url);
  const rawLocale = searchParams.get('locale');

  const bookIdNum = validateBookId(bookId);
  if (bookIdNum === null) {
    logValidationError('bookId', bookId, 'Must be a positive integer');
    return NextResponse.json({
      success: false,
      error: 'Invalid book ID. Must be a positive integer.'
    }, { status: 400 });
  }

  const locale = validateLocale(rawLocale);

  logAudioRequestStart(bookIdNum, locale);

  // 5. 初始化管理器
  const s3Manager = createS3Manager();
  const concurrencyManager = createConcurrencyManager();

  // 6. 缓存优先检查文件是否已存在
  const existingFile = await checkAudioFileWithCache(s3Manager, bookIdNum, locale);
  if (existingFile.exists && existingFile.url) {
    const responseTime = Date.now() - startTime;

    logAudioGenerationComplete({
      bookId: bookIdNum,
      locale,
      responseTime,
      fileSize: existingFile.fileSize,
      cached: true,
      fromCache: existingFile.fromCache,
      cacheAge: existingFile.cacheAge,
      requestId,
      userId
    });

    return NextResponse.json({
      success: true,
      data: {
        audioUrl: existingFile.url,
        fileSize: existingFile.fileSize,
        cached: true,
        fromCache: existingFile.fromCache,
        cacheAge: existingFile.cacheAge,
        generatedAt: existingFile.lastModified || new Date().toISOString()
      }
    });
  }

  // 5. 尝试获取分布式锁
  const lockResult = await concurrencyManager.acquireLock(bookIdNum, locale);

  if (lockResult.acquired) {
    try {
      // 6. 获取锁成功，开始生成音频

      // 再次检查文件（防止竞态条件）- 使用缓存优先检查
      const doubleCheck = await checkAudioFileWithCache(s3Manager, bookIdNum, locale);
      if (doubleCheck.exists && doubleCheck.url) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);

        return NextResponse.json({
          success: true,
          data: {
            audioUrl: doubleCheck.url,
            fileSize: doubleCheck.fileSize,
            cached: true,
            fromCache: doubleCheck.fromCache,
            cacheAge: doubleCheck.cacheAge,
            generatedAt: doubleCheck.lastModified || new Date().toISOString()
          }
        });
      }

      // 7. 获取书籍数据（保持原有逻辑不变）
      const bookData = await getBookById(bookIdNum, locale);
      if (!bookData) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: 'Book not found.'
        }, { status: 404 });
      }

      // 8. 使用已有的章节数据（避免重复查询）
      const chapters = bookData.chapters || [];

      // 9. 验证章节内容
      if (!chapters || chapters.length === 0) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: 'No chapters available for this book.'
        }, { status: 404 });
      }

      if (!hasValidChapterObjects(chapters)) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: 'No valid chapter content found for this book.'
        }, { status: 404 });
      }

      // 10. 组装章节文本
      let audioText: string;
      try {
        audioText = assembleChapterTextFromObjects(chapters);
      } catch (error) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to process chapter content'
        }, { status: 400 });
      }

      // 11. 记录章节处理统计
      const chapterStats = getChapterStatsFromObjects(chapters);
      logChapterProcessing({
        bookId: bookIdNum,
        locale,
        totalChapters: chapterStats.totalChapters,
        validChapters: chapterStats.validChapters,
        finalTextLength: audioText.length,
        averageChapterLength: chapterStats.averageChapterLength
      });

      // 12. 验证最终文本长度
      if (!validateTextLength(audioText)) {
        const charCount = calculateCharacterCount(audioText);
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: `Processed text length invalid. Current: ${charCount} characters. Must be between 10 and 50,000 characters.`
        }, { status: 400 });
      }

      // 13. 简化音频生成逻辑 - 使用单次TTS生成
      // 注释掉复杂的分段处理逻辑，改为直接生成
      // const segmentationAnalysis = analyzeSegmentationNeeds(chapters, locale);

      let audioBuffer: Buffer;
      // 移除临时文件管理，简化流程
      // let tempFiles: string[] = [];

      try {
        // 简化逻辑：直接使用单次TTS生成，不进行分段处理
        console.log(`[AUDIO] Using simplified single generation for ${calculateCharacterCount(audioText)} characters`);
        audioBuffer = await generateTTSAudio(audioText, locale);
        console.log(`[AUDIO] Single generation completed successfully`);

        /* 注释掉复杂的分段处理逻辑
        if (segmentationAnalysis.needsSegmentation) {
          // 分段处理
          console.log(`[AUDIO] Using segmentation: ${segmentationAnalysis.reason}`);

          // 批量生成音频分段
          const batchResult = await processBatchSegments(
            segmentationAnalysis.segments!,
            locale
          );

          if (!batchResult.success) {
            throw new Error(`Batch processing failed: ${batchResult.errors.map(e => e.error).join(', ')}`);
          }

          // 合并音频分段
          const mergeResult = await mergeAudioSegments(batchResult.audioSegments);

          if (!mergeResult.success || !mergeResult.finalBuffer) {
            throw new Error(`Audio merge failed: ${mergeResult.error}`);
          }

          audioBuffer = mergeResult.finalBuffer;
          tempFiles = mergeResult.tempFiles || [];

          console.log(`[AUDIO] Segmented generation completed: ${batchResult.audioSegments.length} segments merged`);
        } else {
          // 单次处理
          console.log(`[AUDIO] Using single generation: ${segmentationAnalysis.reason}`);
          audioBuffer = await generateTTSAudio(audioText, locale);
        }
        */
      } finally {
        // 简化后不需要清理临时文件
        // 注释掉临时文件清理逻辑
        /*
        if (tempFiles.length > 0) {
          await cleanupTempFiles(tempFiles).catch(error =>
            console.warn('[AUDIO] Failed to cleanup temp files:', error)
          );
        }
        */
      }

      // 14. 上传到S3
      const uploadResult = await s3Manager.uploadAudioFile(
        bookIdNum,
        locale,
        audioBuffer,
        {
          bookTitle: bookData.book.title,
          author: bookData.authors[0]?.name || 'Unknown',
          textLength: calculateCharacterCount(audioText).toString(),
          requestId,
          chaptersCount: chapterStats.validChapters.toString()
        }
      );

      if (!uploadResult.success || !uploadResult.url) {
        await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
        return NextResponse.json({
          success: false,
          error: uploadResult.error || 'Failed to upload audio file'
        }, { status: 500 });
      }

      // 15. 缓存元数据
      const estimatedDuration = estimateAudioDuration(audioText, locale);
      await setCachedAudioMetadata(bookIdNum, locale, {
        audioUrl: uploadResult.url,
        duration: estimatedDuration,
        fileSize: uploadResult.fileSize || audioBuffer.length,
        generatedAt: new Date().toISOString(),
        lastChecked: Date.now()
      });

      // 16. 释放锁
      await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);

      const responseTime = Date.now() - startTime;

      logAudioGenerationComplete({
        bookId: bookIdNum,
        locale,
        responseTime,
        fileSize: uploadResult.fileSize || audioBuffer.length,
        cached: false,
        requestId,
        userId
      });

      // 17. 返回成功响应
      return NextResponse.json({
        success: true,
        data: {
          audioUrl: uploadResult.url,
          duration: estimatedDuration,
          fileSize: uploadResult.fileSize || audioBuffer.length,
          cached: false,
          generatedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      // 确保释放锁
      await concurrencyManager.releaseLock(bookIdNum, locale, lockResult.lockValue);
      throw error;
    }
  } else {
    // 7. 获取锁失败，等待其他进程完成
    const waitResult = await concurrencyManager.waitForCompletion(bookIdNum, locale);

    if (waitResult.success && waitResult.audioUrl) {
      const responseTime = Date.now() - startTime;

      logAudioGenerationComplete({
        bookId: bookIdNum,
        locale,
        responseTime,
        cached: true,
        waitedForGeneration: true,
        requestId
      });

      return NextResponse.json({
        success: true,
        data: {
          audioUrl: waitResult.audioUrl,
          cached: true,
          generatedAt: new Date().toISOString(),
          waitedForGeneration: true
        }
      });
    } else {
      // 等待超时
      return NextResponse.json({
        success: false,
        error: 'Audio generation timeout. Please try again later.'
      }, { status: 408 });
    }
  }

  // 记录用户使用情况（可选）
  // await recordAudioUsage(userId, bookIdNum, locale);
};

// 导出使用withRoute + auth中间件的API
export const GET = adaptDynamicRoute<{ bookId: string }>(auth)(audioHandler);

/**
 * 处理不支持的HTTP方法 - 使用withRoute系统
 */
const methodNotAllowedHandler = async (
  _request: NextRequest,
  _context: RequestContext,
  _routeParams: { bookId: string }
): Promise<NextResponse> => {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use GET to retrieve audio.'
  }, { status: 405 });
};

export const POST = adaptDynamicRoute<{ bookId: string }>()(methodNotAllowedHandler);
export const PUT = adaptDynamicRoute<{ bookId: string }>()(methodNotAllowedHandler);
export const DELETE = adaptDynamicRoute<{ bookId: string }>()(methodNotAllowedHandler);
export const PATCH = adaptDynamicRoute<{ bookId: string }>()(methodNotAllowedHandler);
