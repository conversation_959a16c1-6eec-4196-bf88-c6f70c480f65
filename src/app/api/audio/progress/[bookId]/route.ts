/**
 * 单本书播放进度API
 * GET /api/audio/progress/[bookId] - 获取特定书籍播放进度
 * DELETE /api/audio/progress/[bookId] - 删除播放进度
 */

import { NextRequest, NextResponse } from 'next/server';
import { adaptDynamicRoute, auth } from '@/lib/error/withRouteErrorHandling';
import {
  getBookPlaybackProgress,
  deletePlaybackProgress
} from '@/services/audio-progress.service';

const getBookProgressHandler = async (
  _request: NextRequest,
  context: RequestContext,
  routeParams: { bookId: string }
): Promise<NextResponse> => {
  const userId = parseInt(context.params.userId as string);
  const bookId = parseInt(routeParams.bookId);

  if (isNaN(bookId)) {
    return NextResponse.json({
      success: false,
      error: 'Invalid book ID'
    }, { status: 400 });
  }

  try {
    const progress = await getBookPlaybackProgress(userId, bookId);

    return NextResponse.json({
      success: true,
      data: progress
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
};

const deleteProgressHandler = async (
  _request: NextRequest,
  context: RequestContext,
  routeParams: { bookId: string }
): Promise<NextResponse> => {
  const userId = parseInt(context.params.userId as string);
  const bookId = parseInt(routeParams.bookId);

  try {
    await deletePlaybackProgress(userId, bookId);

    return NextResponse.json({
      success: true,
      message: 'Progress deleted successfully'
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
};

export const GET = adaptDynamicRoute<{ bookId: string }>(auth)(getBookProgressHandler);
export const DELETE = adaptDynamicRoute<{ bookId: string }>(auth)(deleteProgressHandler);
