/**
 * 标记播放完成API
 * POST /api/audio/progress/[bookId]/complete - 标记为已完成
 */

import { NextRequest, NextResponse } from 'next/server';
import { adaptDynamicRoute, auth } from '@/lib/error/withRouteErrorHandling';
import { markProgressAsCompleted } from '@/services/audio-progress.service';

const markCompleteHandler = async (
  _request: NextRequest,
  context: RequestContext,
  routeParams: { bookId: string }
): Promise<NextResponse> => {
  const userId = parseInt(context.params.userId as string);
  const bookId = parseInt(routeParams.bookId);

  if (isNaN(bookId)) {
    return NextResponse.json({
      success: false,
      error: 'Invalid book ID'
    }, { status: 400 });
  }

  try {
    const progress = await markProgressAsCompleted(userId, bookId);

    return NextResponse.json({
      success: true,
      data: progress
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
};

export const POST = adaptDynamicRoute<{ bookId: string }>(auth)(markCompleteHandler);
