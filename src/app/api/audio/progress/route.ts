/**
 * 播放进度管理API
 * POST /api/audio/progress - 保存播放进度
 * GET /api/audio/progress - 获取用户所有播放进度
 */

import { NextRequest, NextResponse } from 'next/server';
import withRoute, { auth } from '@/lib/error/withRouteErrorHandling';
import { savePlaybackProgress, getUserPlaybackProgress } from '@/services/audio-progress.service';
import { validateProgressData } from '@/lib/audio/progress-validation';

// 保存播放进度
const saveProgressHandler = async (
  request: NextRequest,
  context: RequestContext
): Promise<NextResponse> => {
  const userId = parseInt(context.params.userId as string);

  try {
    const body = await request.json();
    const validatedData = validateProgressData(body);

    const progress = await savePlaybackProgress(userId, validatedData);

    return NextResponse.json({
      success: true,
      data: progress
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 400 });
  }
};

// 获取用户播放进度列表
const getProgressHandler = async (
  request: NextRequest,
  context: RequestContext
): Promise<NextResponse> => {
  const userId = parseInt(context.params.userId as string);
  const { searchParams } = new URL(request.url);

  const limit = parseInt(searchParams.get('limit') || '10');
  const offset = parseInt(searchParams.get('offset') || '0');
  const includeCompleted = searchParams.get('includeCompleted') !== 'false';

  try {
    const progressList = await getUserPlaybackProgress(userId, {
      limit,
      offset,
      includeCompleted
    });

    return NextResponse.json({
      success: true,
      data: progressList
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
};

export const POST = withRoute(auth)(saveProgressHandler);
export const GET = withRoute(auth)(getProgressHandler);
