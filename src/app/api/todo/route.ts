import { prisma } from '@prisma/prisma'
// 使用 any 类型处理 Prisma 客户端
const prismaAny = prisma as any
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建待办事项的验证模式
const createTodoSchema = z.object({
  text: z.string().min(1, '内容不能为空')
})

// 更新待办事项的验证模式
const updateTodoSchema = z.object({
  text: z.string().min(1, '内容不能为空').optional(),
  completed: z.boolean().optional()
})

// 获取所有待办事项
export async function GET() {
  try {
    const todos = await prismaAny.todo.findMany({
      orderBy: { createdAt: 'desc' }
    })
    return NextResponse.json(todos)
  } catch (error) {
    console.error('获取待办事项失败:', error)
    return NextResponse.json({ error: '获取待办事项失败' }, { status: 500 })
  }
}

// 创建新待办事项
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createTodoSchema.parse(body)

    const newTodo = await prismaAny.todo.create({
      data: {
        text: validatedData.text
      }
    })

    return NextResponse.json(newTodo, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }

    console.error('创建待办事项失败:', error)
    return NextResponse.json({ error: '创建待办事项失败' }, { status: 500 })
  }
}

// 更新待办事项
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({ error: '缺少待办事项ID' }, { status: 400 })
    }

    // 验证更新数据
    updateTodoSchema.parse(updateData)

    const updatedTodo = await prismaAny.todo.update({
      where: { id },
      data: updateData
    })

    return NextResponse.json(updatedTodo)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }

    console.error('更新待办事项失败:', error)
    return NextResponse.json({ error: '更新待办事项失败' }, { status: 500 })
  }
}

// 删除待办事项
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: '缺少待办事项ID' }, { status: 400 })
    }

    await prismaAny.todo.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('删除待办事项失败:', error)
    return NextResponse.json({ error: '删除待办事项失败' }, { status: 500 })
  }
}
