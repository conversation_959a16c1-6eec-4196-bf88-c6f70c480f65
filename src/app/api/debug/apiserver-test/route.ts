import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import apiServer from '@/utils/apiServer'

/**
 * apiServer调试测试 API 端点
 * POST /api/debug/apiserver-test
 * 
 * 这个端点专门用于调试apiServer的Cookie传递问题
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 ===== apiServer Debug Test Started =====')
    
    // 获取Cookie信息
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')
    
    console.log('🍪 Current Cookies:', {
      totalCookies: allCookies.length,
      hasAuthToken: !!authToken,
      authTokenLength: authToken?.value.length || 0,
      cookieNames: allCookies.map(c => c.name)
    })

    // 测试不同的apiServer调用方式
    const testResults = {
      userEndpoint: null as any,
      rawResponse: null as any,
      errorDetails: null as any
    }

    // 1. 测试用户端点
    console.log('🚀 Testing apiServer.get("user")...')
    try {
      const userResponse = await apiServer.get('user').json()
      testResults.userEndpoint = {
        success: true,
        data: userResponse,
        hasUserData: !!(userResponse as any)?.data?.user
      }
      console.log('✅ User endpoint success:', testResults.userEndpoint)
    } catch (userError) {
      console.error('❌ User endpoint failed:', userError)
      testResults.userEndpoint = {
        success: false,
        error: userError instanceof Error ? {
          name: userError.name,
          message: userError.message,
          stack: userError.stack?.substring(0, 500)
        } : 'Unknown error'
      }
    }

    // 2. 测试原始响应（不解析JSON）
    console.log('🚀 Testing raw response...')
    try {
      const rawResponse = await apiServer.get('user')
      const responseText = await rawResponse.text()
      
      testResults.rawResponse = {
        success: true,
        status: rawResponse.status,
        statusText: rawResponse.statusText,
        headers: Object.fromEntries(rawResponse.headers.entries()),
        bodyPreview: responseText.substring(0, 1000),
        bodyLength: responseText.length
      }
      console.log('✅ Raw response success:', {
        status: rawResponse.status,
        bodyLength: responseText.length
      })
    } catch (rawError) {
      console.error('❌ Raw response failed:', rawError)
      testResults.rawResponse = {
        success: false,
        error: rawError instanceof Error ? {
          name: rawError.name,
          message: rawError.message
        } : 'Unknown error'
      }
    }

    // 3. 分析错误详情
    if (!testResults.userEndpoint.success || !testResults.rawResponse.success) {
      testResults.errorDetails = analyzeApiServerError(testResults)
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      cookieInfo: {
        totalCookies: allCookies.length,
        hasAuthToken: !!authToken,
        authTokenLength: authToken?.value.length || 0,
        cookieNames: allCookies.map(c => c.name)
      },
      testResults,
      analysis: generateAnalysis(testResults),
      recommendations: generateRecommendations(testResults)
    })

  } catch (error) {
    console.error('❌ apiServer Debug Test Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 分析apiServer错误
 */
function analyzeApiServerError(testResults: any) {
  const analysis = {
    errorType: 'unknown',
    possibleCauses: [] as string[],
    debugSteps: [] as string[]
  }

  // 检查用户端点错误
  if (!testResults.userEndpoint.success) {
    const error = testResults.userEndpoint.error
    
    if (error.name === 'HTTPError') {
      analysis.errorType = 'http_error'
      analysis.possibleCauses.push('HTTP请求失败，可能是认证问题')
      analysis.debugSteps.push('检查Cookie是否正确传递')
      analysis.debugSteps.push('验证Token是否有效')
    } else if (error.name === 'TimeoutError') {
      analysis.errorType = 'timeout'
      analysis.possibleCauses.push('请求超时')
      analysis.debugSteps.push('检查网络连接')
      analysis.debugSteps.push('检查后端服务状态')
    } else if (error.message.includes('fetch')) {
      analysis.errorType = 'network_error'
      analysis.possibleCauses.push('网络连接问题')
      analysis.debugSteps.push('检查destinationUrl配置')
      analysis.debugSteps.push('验证后端服务可访问性')
    }
  }

  // 检查原始响应错误
  if (!testResults.rawResponse.success) {
    analysis.possibleCauses.push('底层HTTP请求失败')
    analysis.debugSteps.push('检查apiServer配置')
    analysis.debugSteps.push('验证ky库配置')
  }

  return analysis
}

/**
 * 生成分析结果
 */
function generateAnalysis(testResults: any) {
  const analysis = {
    overallStatus: 'unknown',
    summary: '',
    details: {} as any
  }

  if (testResults.userEndpoint.success && testResults.rawResponse.success) {
    analysis.overallStatus = 'success'
    analysis.summary = 'apiServer工作正常，能够成功获取用户数据'
  } else if (!testResults.userEndpoint.success && !testResults.rawResponse.success) {
    analysis.overallStatus = 'complete_failure'
    analysis.summary = 'apiServer完全无法工作，需要检查基础配置'
  } else if (!testResults.userEndpoint.success && testResults.rawResponse.success) {
    analysis.overallStatus = 'json_parse_error'
    analysis.summary = 'HTTP请求成功但JSON解析失败，可能是响应格式问题'
  } else {
    analysis.overallStatus = 'partial_failure'
    analysis.summary = '部分功能正常，需要进一步调试'
  }

  // 详细分析
  analysis.details = {
    httpRequestWorking: testResults.rawResponse.success,
    jsonParsingWorking: testResults.userEndpoint.success,
    authenticationWorking: testResults.userEndpoint.success && testResults.userEndpoint.hasUserData,
    responseStatus: testResults.rawResponse.success ? testResults.rawResponse.status : 'unknown'
  }

  return analysis
}

/**
 * 生成修复建议
 */
function generateRecommendations(testResults: any) {
  const recommendations = []

  if (!testResults.rawResponse.success) {
    recommendations.push({
      priority: 'high',
      category: 'network',
      title: '检查网络配置',
      description: '基础HTTP请求失败，检查destinationUrl和网络连接',
      actions: [
        '验证NEXT_PUBLIC_BASE_URL环境变量',
        '检查后端服务是否运行',
        '测试直接访问后端API'
      ]
    })
  }

  if (testResults.rawResponse.success && !testResults.userEndpoint.success) {
    recommendations.push({
      priority: 'medium',
      category: 'parsing',
      title: '检查响应格式',
      description: 'HTTP请求成功但JSON解析失败',
      actions: [
        '检查响应内容格式',
        '验证Content-Type头',
        '查看原始响应内容'
      ]
    })
  }

  if (testResults.userEndpoint.success && !testResults.userEndpoint.hasUserData) {
    recommendations.push({
      priority: 'high',
      category: 'authentication',
      title: '检查认证配置',
      description: '请求成功但没有用户数据，可能是认证问题',
      actions: [
        '验证Cookie传递',
        '检查Token有效性',
        '确认Redis配置'
      ]
    })
  }

  return recommendations
}
