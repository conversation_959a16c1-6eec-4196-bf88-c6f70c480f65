import { NextRequest, NextResponse } from 'next/server'
import { getTokenKey, getAppName } from '@/lib/auth/config'

/**
 * 环境配置对比调试 API 端点
 * GET /api/debug/env-config
 * 
 * 这个端点用于调试环境配置问题，显示：
 * 1. 当前环境变量配置
 * 2. 前后端配置对比
 * 3. 配置一致性检查
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 ===== Environment Config Debug Started =====')

    // 收集前端环境配置
    const frontendConfig = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
      PROJECT_NAME: process.env.PROJECT_NAME,
      TOKEN_KEY: process.env.TOKEN_KEY,
      REDIS_HOST: process.env.REDIS_HOST,
      REDIS_PORT: process.env.REDIS_PORT,
      REDIS_DB: process.env.REDIS_DB,
      hasRedisPassword: !!process.env.REDIS_PASSWORD
    }

    // 生成的配置
    const generatedConfig = {
      appName: getAppName(),
      tokenKey: getTokenKey(),
      expectedTokenKey: generateExpectedTokenKey()
    }

    // 配置一致性检查
    const consistencyCheck = {
      tokenKeyMatch: frontendConfig.TOKEN_KEY === generatedConfig.tokenKey,
      appNameConsistent: frontendConfig.PROJECT_NAME === generatedConfig.appName,
      environmentDetection: detectEnvironmentType(),
      redisConfigComplete: checkRedisConfigCompleteness(frontendConfig)
    }

    // 生成配置建议
    const suggestions = generateConfigSuggestions(frontendConfig, generatedConfig, consistencyCheck)

    console.log('📊 Frontend Config:', frontendConfig)
    console.log('🔧 Generated Config:', generatedConfig)
    console.log('✅ Consistency Check:', consistencyCheck)

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      frontendConfig,
      generatedConfig,
      consistencyCheck,
      suggestions,
      expectedBackendConfig: generateExpectedBackendConfig(frontendConfig),
      troubleshooting: generateTroubleshootingSteps(consistencyCheck)
    })

  } catch (error) {
    console.error('❌ Environment Config Debug Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 生成期望的Token Key
 */
function generateExpectedTokenKey() {
  const appName = getAppName()
  const nodeEnv = process.env.NODE_ENV
  
  return nodeEnv === 'production'
    ? `${appName.toUpperCase()}_ACCESS_TOKEN`
    : `${appName.toUpperCase()}_ACCESS_TOKEN-dev`
}

/**
 * 检测环境类型
 */
function detectEnvironmentType() {
  const nodeEnv = process.env.NODE_ENV
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL
  
  let environmentType = 'unknown'
  
  if (nodeEnv === 'production') {
    if (baseUrl?.includes('15minutes.ai')) {
      environmentType = 'production'
    } else if (baseUrl?.includes('tangshu.com')) {
      environmentType = 'staging'
    } else {
      environmentType = 'production-other'
    }
  } else if (nodeEnv === 'development') {
    environmentType = 'development'
  }
  
  return {
    nodeEnv,
    baseUrl,
    detectedType: environmentType,
    isProduction: environmentType === 'production',
    isStaging: environmentType === 'staging',
    isDevelopment: environmentType === 'development'
  }
}

/**
 * 检查Redis配置完整性
 */
function checkRedisConfigCompleteness(config: any) {
  return {
    hasHost: !!config.REDIS_HOST,
    hasPort: !!config.REDIS_PORT,
    hasPassword: config.hasRedisPassword,
    hasDatabase: !!config.REDIS_DB,
    isComplete: !!(config.REDIS_HOST && config.REDIS_PORT && config.REDIS_DB)
  }
}

/**
 * 生成期望的后端配置
 */
function generateExpectedBackendConfig(frontendConfig: any) {
  return {
    APP_NAME: frontendConfig.PROJECT_NAME,
    APP_ENV: frontendConfig.NODE_ENV === 'production' ? 'prod' : 'dev',
    REDIS_HOST: frontendConfig.REDIS_HOST,
    REDIS_PORT: frontendConfig.REDIS_PORT,
    REDIS_DB: frontendConfig.REDIS_DB,
    WEBSITE_FRONTEND_BASE_URI: frontendConfig.NEXT_PUBLIC_BASE_URL,
    expectedCookieName: frontendConfig.TOKEN_KEY
  }
}

/**
 * 生成配置建议
 */
function generateConfigSuggestions(frontendConfig: any, generatedConfig: any, consistencyCheck: any) {
  const suggestions = []

  if (!consistencyCheck.tokenKeyMatch) {
    suggestions.push({
      type: 'error',
      message: `Token Key不匹配: 环境变量=${frontendConfig.TOKEN_KEY}, 生成的=${generatedConfig.tokenKey}`,
      action: '检查TOKEN_KEY环境变量设置'
    })
  }

  if (!consistencyCheck.appNameConsistent) {
    suggestions.push({
      type: 'warning',
      message: `应用名称不一致: PROJECT_NAME=${frontendConfig.PROJECT_NAME}, 生成的=${generatedConfig.appName}`,
      action: '确保PROJECT_NAME环境变量正确设置'
    })
  }

  if (!consistencyCheck.redisConfigComplete.isComplete) {
    suggestions.push({
      type: 'error',
      message: 'Redis配置不完整',
      action: '检查REDIS_HOST, REDIS_PORT, REDIS_DB环境变量'
    })
  }

  if (frontendConfig.NODE_ENV === 'production' && !frontendConfig.hasRedisPassword) {
    suggestions.push({
      type: 'warning',
      message: '生产环境缺少Redis密码',
      action: '设置REDIS_PASSWORD环境变量'
    })
  }

  return suggestions
}

/**
 * 生成故障排除步骤
 */
function generateTroubleshootingSteps(consistencyCheck: any) {
  const steps = []

  steps.push({
    step: 1,
    title: '检查环境变量',
    description: '确认所有必需的环境变量都已正确设置',
    status: consistencyCheck.redisConfigComplete.isComplete ? 'success' : 'error'
  })

  steps.push({
    step: 2,
    title: '验证Token Key一致性',
    description: '确保前端和后端使用相同的Token Key',
    status: consistencyCheck.tokenKeyMatch ? 'success' : 'error'
  })

  steps.push({
    step: 3,
    title: '检查Redis数据库配置',
    description: '确认前后端使用相同的Redis数据库',
    status: 'pending'
  })

  steps.push({
    step: 4,
    title: '验证Cookie域名设置',
    description: '确保Cookie域名配置正确',
    status: 'pending'
  })

  return steps
}
