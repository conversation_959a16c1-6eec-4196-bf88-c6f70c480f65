import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJwtToken } from '@/services/actions/authServer'
import { getRedisClient } from '@/lib/redis'
import { getTokenKey, getAppName, getRedisConfig, getRedisTokenKey } from '@/lib/auth/config'

/**
 * Redis配置调试 API 端点
 * GET /api/debug/redis-config
 * 
 * 这个端点用于调试Redis配置问题，显示：
 * 1. Redis连接配置
 * 2. Token在不同数据库中的存储情况
 * 3. 配置对比分析
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const currentTokenKey = getTokenKey()
    const token = cookieStore.get(currentTokenKey)?.value

    console.log('🔍 ===== Redis Config Debug Started =====')
    console.log('📊 Basic Info:', {
      currentTokenKey,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      appName: getAppName(),
      nodeEnv: process.env.NODE_ENV
    })

    // 获取Redis配置
    const redisConfig = getRedisConfig()
    console.log('⚙️ Redis Config:', redisConfig)

    let tokenVerification = null
    let redisAnalysis = null

    if (token) {
      // 验证Token
      try {
        const { isValid, uid } = await verifyJwtToken(token)
        tokenVerification = {
          isValid,
          uid,
          tokenKey: currentTokenKey
        }
        console.log('🔐 Token Verification:', tokenVerification)

        if (isValid && uid) {
          // 检查不同Redis数据库中的Token存储
          const redisTokenKey = getRedisTokenKey(uid)
          console.log('🔑 Redis Token Key:', redisTokenKey)

          // 检查数据库0和数据库1
          const db0Analysis = await checkRedisDatabase(0, redisTokenKey, token)
          const db1Analysis = await checkRedisDatabase(1, redisTokenKey, token)

          redisAnalysis = {
            tokenKey: redisTokenKey,
            database0: db0Analysis,
            database1: db1Analysis,
            configuredDb: redisConfig.db,
            recommendation: db0Analysis.tokenExists ? 'Use database 0' : 
                          db1Analysis.tokenExists ? 'Use database 1' : 
                          'Token not found in either database'
          }
          console.log('📊 Redis Analysis:', redisAnalysis)
        }
      } catch (error) {
        console.error('❌ Token verification failed:', error)
        tokenVerification = {
          isValid: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      environment: {
        nodeEnv: process.env.NODE_ENV,
        appName: getAppName(),
        tokenKey: currentTokenKey,
        redisConfig: {
          host: redisConfig.host,
          port: redisConfig.port,
          db: redisConfig.db,
          hasPassword: !!redisConfig.password
        }
      },
      cookieInfo: {
        hasToken: !!token,
        tokenLength: token?.length || 0,
        tokenKey: currentTokenKey
      },
      tokenVerification,
      redisAnalysis,
      suggestions: generateSuggestions(redisAnalysis, redisConfig)
    })

  } catch (error) {
    console.error('❌ Redis Config Debug Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 检查指定Redis数据库中的Token存储情况
 */
async function checkRedisDatabase(db: number, tokenKey: string, expectedToken: string) {
  try {
    // 创建指定数据库的Redis客户端
    const redis = getRedisClient({ db })
    
    // 检查Token是否存在
    const storedToken = await redis.get(tokenKey)
    const tokenExists = !!storedToken
    const tokenMatches = storedToken === expectedToken

    // 获取TTL
    const ttl = await redis.ttl(tokenKey)

    // 关闭连接
    await redis.quit()

    return {
      database: db,
      tokenExists,
      tokenMatches,
      ttl: ttl > 0 ? ttl : null,
      storedTokenLength: storedToken?.length || 0
    }
  } catch (error) {
    console.error(`❌ Error checking database ${db}:`, error)
    return {
      database: db,
      error: error instanceof Error ? error.message : 'Unknown error',
      tokenExists: false,
      tokenMatches: false
    }
  }
}

/**
 * 生成配置建议
 */
function generateSuggestions(redisAnalysis: any, redisConfig: any) {
  const suggestions = []

  if (!redisAnalysis) {
    suggestions.push('无法分析Redis配置 - Token验证失败')
    return suggestions
  }

  const { database0, database1, configuredDb } = redisAnalysis

  if (database0.tokenExists && database1.tokenExists) {
    suggestions.push('⚠️ Token同时存在于数据库0和1中，可能存在配置冲突')
  }

  if (database0.tokenExists && configuredDb !== 0) {
    suggestions.push('🔧 Token存在于数据库0，但配置使用数据库' + configuredDb + '，建议修改配置为数据库0')
  }

  if (database1.tokenExists && configuredDb !== 1) {
    suggestions.push('🔧 Token存在于数据库1，但配置使用数据库' + configuredDb + '，建议修改配置为数据库1')
  }

  if (!database0.tokenExists && !database1.tokenExists) {
    suggestions.push('❌ Token在数据库0和1中都不存在，可能已过期或配置错误')
  }

  if (database0.tokenMatches === false || database1.tokenMatches === false) {
    suggestions.push('⚠️ 存储的Token与当前Token不匹配，可能存在同步问题')
  }

  return suggestions
}
