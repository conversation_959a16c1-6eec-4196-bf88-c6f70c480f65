import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJwtToken } from '@/services/actions/authServer'
import { getRedisClient } from '@/lib/redis'
import { getTokenKey, getRedisTokenKey } from '@/lib/auth/config'

/**
 * Token验证调试 API 端点
 * GET /api/debug/token-validation
 * 
 * 详细验证Token的有效性和Redis存储状态
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 ===== Token Validation Debug Started =====')

    // 获取Cookie信息
    const cookieStore = await cookies()
    const currentTokenKey = getTokenKey()
    const token = cookieStore.get(currentTokenKey)?.value

    console.log('🍪 Token Info:', {
      tokenKey: currentTokenKey,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenPreview: token ? token.substring(0, 50) + '...' : 'N/A'
    })

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'No token found',
        timestamp: new Date().toISOString()
      })
    }

    // 1. 验证JWT Token格式和签名
    console.log('🔐 Verifying JWT token...')
    let jwtVerification: any = null
    let tokenPayload: any = null

    try {
      // 先手动解析JWT payload来获取详细信息
      try {
        const [, payloadBase64] = token.split('.')
        tokenPayload = JSON.parse(Buffer.from(payloadBase64, 'base64').toString())
        console.log('🔍 Parsed JWT payload:', {
          uid: tokenPayload.uid,
          exp: tokenPayload.exp,
          iat: tokenPayload.iat,
          iss: tokenPayload.iss,
          aud: tokenPayload.aud
        })
      } catch (parseError) {
        console.error('❌ Failed to parse JWT payload:', parseError)
      }

      // 然后使用verifyJwtToken验证
      const { isValid, uid } = await verifyJwtToken(token)
      jwtVerification = {
        isValid,
        uid,
        payload: tokenPayload ? {
          iss: tokenPayload.iss,
          aud: tokenPayload.aud,
          exp: tokenPayload.exp,
          iat: tokenPayload.iat,
          nbf: tokenPayload.nbf,
          uid: tokenPayload.uid
        } : null,
        isExpired: tokenPayload ? Date.now() / 1000 > tokenPayload.exp : null,
        expiresAt: tokenPayload?.exp ? new Date(tokenPayload.exp * 1000).toISOString() : null,
        issuedAt: tokenPayload?.iat ? new Date(tokenPayload.iat * 1000).toISOString() : null
      }
      console.log('✅ JWT verification result:', jwtVerification)
    } catch (jwtError) {
      console.error('❌ JWT verification failed:', jwtError)
      jwtVerification = {
        isValid: false,
        error: jwtError instanceof Error ? jwtError.message : 'Unknown JWT error'
      }
    }

    // 2. 检查Redis中的Token存储
    console.log('📊 Checking Redis token storage...')
    let redisVerification: any = null
    if (jwtVerification.isValid && jwtVerification.uid) {
      try {
        const redis = getRedisClient(1) // 使用数据库1
        const redisTokenKey = getRedisTokenKey(jwtVerification.uid)

        console.log('🔑 Redis key:', redisTokenKey)

        // 使用zScore检查Token（与PHP后端逻辑一致）
        const score = await redis.zscore(redisTokenKey, token)
        const tokenExists = score !== null

        // 也检查TTL
        const ttl = await redis.ttl(redisTokenKey)

        // 检查key是否存在
        const keyExists = await redis.exists(redisTokenKey)

        // 获取sorted set的所有成员数量
        const memberCount = await redis.zcard(redisTokenKey)

        await redis.quit()

        redisVerification = {
          redisKey: redisTokenKey,
          keyExists: keyExists > 0,
          tokenExists,
          tokenScore: score,
          ttl: ttl > 0 ? ttl : null,
          memberCount,
          redisMethod: 'zscore', // 说明使用的Redis方法
          explanation: 'PHP后端使用sorted set存储token，通过zscore检查token是否存在'
        }

        console.log('✅ Redis verification result:', redisVerification)
      } catch (redisError) {
        console.error('❌ Redis verification failed:', redisError)
        redisVerification = {
          error: redisError instanceof Error ? redisError.message : 'Unknown Redis error'
        }
      }
    }

    // 3. 测试后端Token验证
    console.log('🌐 Testing backend token validation...')
    let backendVerification: any = null
    try {
      // 构建Cookie字符串
      const allCookies = cookieStore.getAll()
      const cookieString = allCookies
        .map((cookie) => `${cookie.name}=${cookie.value}`)
        .join('; ')

      // 测试后端验证
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.15minutes.ai'
      const urlObj = new URL(baseUrl)
      const domain = urlObj.hostname

      const response = await fetch(`${baseUrl}/common-api/v1/user`, {
        method: 'GET',
        headers: {
          'Cookie': cookieString,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'application/json',
          'Host': domain,
          'Origin': baseUrl,
          'Referer': `${baseUrl}/`,
          'X-Forwarded-Host': domain,
          'X-Forwarded-Proto': 'https'
        },
        credentials: 'include'
      })

      const responseText = await response.text()
      let responseData = null

      try {
        responseData = JSON.parse(responseText)
      } catch (parseError) {
        console.error('Failed to parse backend response:', parseError)
      }

      backendVerification = {
        httpStatus: response.status,
        httpStatusText: response.statusText,
        responseData,
        isSuccess: response.ok && responseData?.code === 200,
        errorMessage: responseData?.message || null
      }

      console.log('✅ Backend verification result:', backendVerification)
    } catch (backendError) {
      console.error('❌ Backend verification failed:', backendError)
      backendVerification = {
        error: backendError instanceof Error ? backendError.message : 'Unknown backend error'
      }
    }

    // 4. 生成诊断结果
    const diagnosis = generateDiagnosis(jwtVerification, redisVerification, backendVerification)

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      tokenInfo: {
        tokenKey: currentTokenKey,
        hasToken: !!token,
        tokenLength: token?.length || 0
      },
      jwtVerification,
      redisVerification,
      backendVerification,
      diagnosis
    })

  } catch (error) {
    console.error('❌ Token Validation Debug Error:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 生成诊断结果
 */
function generateDiagnosis(jwt: any, redis: any, backend: any) {
  const issues = []
  const recommendations = []

  // JWT问题
  if (!jwt.isValid) {
    issues.push('JWT Token无效或格式错误')
    recommendations.push('检查Token生成逻辑和签名密钥')
  } else if (jwt.isExpired) {
    issues.push('JWT Token已过期')
    recommendations.push('用户需要重新登录')
  }

  // Redis问题
  if (redis && !redis.tokenExists) {
    issues.push('Token在Redis中不存在')
    recommendations.push('检查Redis连接和Token存储逻辑')
  } else if (redis && !redis.tokenMatches) {
    issues.push('Redis中存储的Token与当前Token不匹配')
    recommendations.push('检查Token同步机制')
  }

  // 后端问题
  if (backend && !backend.isSuccess) {
    issues.push(`后端验证失败: ${backend.errorMessage || '未知错误'}`)
    recommendations.push('检查后端认证中间件和Cookie处理逻辑')
  }

  return {
    overallStatus: issues.length === 0 ? 'healthy' : 'issues_found',
    issuesCount: issues.length,
    issues,
    recommendations,
    summary: issues.length === 0
      ? '所有验证都通过，Token状态正常'
      : `发现 ${issues.length} 个问题需要解决`
  }
}
