import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

/**
 * Cookie检查调试 API 端点
 * GET /api/debug/cookie-check
 * 
 * 详细检查当前的Cookie状态
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🍪 ===== Cookie Check Debug Started =====')

    // 获取所有Cookie
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()

    // 检查特定的认证相关Cookie
    const authTokens = [
      'MINUTES_ACCESS_TOKEN',
      'MINUTES_ACCESS_TOKEN-dev',
      'MINUTES_ACCESS_TOKEN-prod',
      'MINUTES_SESSION_ID'
    ]

    const tokenAnalysis: Record<string, {
      exists: boolean
      hasValue: boolean
      valueLength: number
      valuePreview: string
    }> = {}

    authTokens.forEach(tokenName => {
      const token = cookieStore.get(tokenName)
      tokenAnalysis[tokenName] = {
        exists: !!token,
        hasValue: !!token?.value,
        valueLength: token?.value?.length || 0,
        valuePreview: token?.value ? token.value.substring(0, 30) + '...' : 'N/A'
      }
    })

    // 分析Cookie结构
    const cookieAnalysis = {
      totalCount: allCookies.length,
      cookieNames: allCookies.map(c => c.name),
      authRelatedCookies: allCookies.filter(c =>
        c.name.includes('TOKEN') ||
        c.name.includes('SESSION') ||
        c.name.includes('AUTH')
      ).map(c => ({
        name: c.name,
        hasValue: !!c.value,
        valueLength: c.value?.length || 0
      })),
      allCookiesDetail: allCookies.map(c => ({
        name: c.name,
        hasValue: !!c.value,
        valueLength: c.value?.length || 0,
        valuePreview: c.name.includes('TOKEN')
          ? (c.value ? c.value.substring(0, 30) + '...' : 'EMPTY')
          : (c.value?.length > 50 ? c.value.substring(0, 50) + '...' : c.value)
      }))
    }

    // 环境信息
    const environmentInfo = {
      nodeEnv: process.env.NODE_ENV,
      baseUrl: process.env.NEXT_PUBLIC_BASE_URL,
      projectName: process.env.PROJECT_NAME,
      tokenKey: process.env.TOKEN_KEY,
      userAgent: request.headers.get('user-agent'),
      host: request.headers.get('host'),
      origin: request.headers.get('origin'),
      referer: request.headers.get('referer')
    }

    // 生成诊断
    const diagnosis = {
      hasAuthToken: !!cookieStore.get('MINUTES_ACCESS_TOKEN'),
      cookieCount: allCookies.length,
      authTokenCount: allCookies.filter(c => c.name.includes('TOKEN')).length,
      possibleIssues: [] as string[]
    }

    if (!diagnosis.hasAuthToken) {
      diagnosis.possibleIssues.push('主要认证Token (MINUTES_ACCESS_TOKEN) 不存在')
    }

    if (diagnosis.cookieCount === 0) {
      diagnosis.possibleIssues.push('没有任何Cookie，可能是Cookie设置或传递问题')
    }

    if (diagnosis.authTokenCount === 0) {
      diagnosis.possibleIssues.push('没有任何认证相关的Cookie')
    }

    console.log('🍪 Cookie analysis completed:', {
      totalCookies: allCookies.length,
      hasAuthToken: diagnosis.hasAuthToken,
      authTokenCount: diagnosis.authTokenCount
    })

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      cookieAnalysis,
      tokenAnalysis,
      environmentInfo,
      diagnosis,
      recommendations: generateRecommendations(diagnosis, tokenAnalysis)
    })

  } catch (error) {
    console.error('❌ Cookie Check Debug Error:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 生成修复建议
 */
function generateRecommendations(diagnosis: any, tokenAnalysis: any) {
  const recommendations = []

  if (!diagnosis.hasAuthToken) {
    recommendations.push({
      priority: 'critical',
      issue: '缺少认证Token',
      action: '检查用户是否已登录，或者Cookie是否正确设置',
      details: '主要认证Token MINUTES_ACCESS_TOKEN 不存在'
    })
  }

  if (diagnosis.cookieCount === 0) {
    recommendations.push({
      priority: 'critical',
      issue: '没有任何Cookie',
      action: '检查Cookie域名设置和传递机制',
      details: '可能是域名配置问题或Cookie被阻止'
    })
  }

  if (tokenAnalysis['MINUTES_ACCESS_TOKEN-dev']?.exists && !tokenAnalysis['MINUTES_ACCESS_TOKEN']?.exists) {
    recommendations.push({
      priority: 'high',
      issue: '使用了开发环境Token',
      action: '检查环境配置，确保生产环境使用正确的Token名称',
      details: '发现开发环境Token但没有生产环境Token'
    })
  }

  return recommendations
}
