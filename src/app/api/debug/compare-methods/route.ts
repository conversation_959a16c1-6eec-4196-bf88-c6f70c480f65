import { NextRequest, NextResponse } from 'next/server'
import { compareUserInfoMethods } from '@/services/server/userServiceFixed'

/**
 * 对比测试 API 端点
 * POST /api/debug/compare-methods
 * 
 * 对比原版getUserInfo和修复版getUserInfoFixed的结果
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 ===== Compare Methods Test Started =====')
    
    const comparisonResult = await compareUserInfoMethods()
    
    // 生成建议
    const recommendations = generateRecommendations(comparisonResult.comparison)
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      comparisonResult,
      recommendations,
      nextSteps: generateNextSteps(comparisonResult.comparison)
    })

  } catch (error) {
    console.error('❌ Compare Methods Test Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 生成建议
 */
function generateRecommendations(comparison: any) {
  const recommendations = []

  if (comparison.onlyFixedWorks) {
    recommendations.push({
      priority: 'high',
      title: '使用修复版本',
      description: '修复版getUserInfoFixed工作正常，原版存在问题',
      action: '临时使用getUserInfoFixed替换原版getUserInfo'
    })
  }

  if (comparison.bothWork) {
    recommendations.push({
      priority: 'low',
      title: '两种方法都正常',
      description: '原版和修复版都能正常工作',
      action: '继续使用原版，但保留修复版作为备用'
    })
  }

  if (comparison.neitherWorks) {
    recommendations.push({
      priority: 'critical',
      title: '两种方法都失败',
      description: '原版和修复版都无法工作，可能是认证或网络问题',
      action: '检查Token有效性和网络配置'
    })
  }

  if (comparison.onlyOriginalWorks) {
    recommendations.push({
      priority: 'medium',
      title: '原版正常工作',
      description: '原版getUserInfo工作正常，修复版存在问题',
      action: '继续使用原版，调试修复版的问题'
    })
  }

  return recommendations
}

/**
 * 生成下一步行动
 */
function generateNextSteps(comparison: any) {
  const steps = []

  if (comparison.onlyFixedWorks) {
    steps.push({
      step: 1,
      title: '立即应用修复',
      description: '在Header组件中使用getUserInfoFixed',
      urgent: true
    })
    steps.push({
      step: 2,
      title: '调试原版问题',
      description: '分析apiServer的Cookie传递问题',
      urgent: false
    })
    steps.push({
      step: 3,
      title: '长期修复',
      description: '修复apiServer后恢复使用原版',
      urgent: false
    })
  }

  if (comparison.bothWork) {
    steps.push({
      step: 1,
      title: '保持现状',
      description: '继续使用原版getUserInfo',
      urgent: false
    })
    steps.push({
      step: 2,
      title: '性能对比',
      description: '对比两种方法的性能差异',
      urgent: false
    })
  }

  if (comparison.neitherWorks) {
    steps.push({
      step: 1,
      title: '检查认证状态',
      description: '验证Token是否有效',
      urgent: true
    })
    steps.push({
      step: 2,
      title: '检查网络配置',
      description: '验证NEXT_PUBLIC_BASE_URL和网络连接',
      urgent: true
    })
    steps.push({
      step: 3,
      title: '检查后端服务',
      description: '确认后端API服务正常运行',
      urgent: true
    })
  }

  return steps
}
