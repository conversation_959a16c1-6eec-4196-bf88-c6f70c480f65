import { NextResponse } from 'next/server'
import { getPrice } from '@/services/server/payService'
import { getUserInfo } from '@/services/server/userService'

/**
 * 获取价格数据和用户信息的 API 路由
 * GET /api/pricing-data
 * 
 * 返回格式:
 * {
 *   success: boolean;
 *   plans: PricePlan[];
 *   user: User | null;
 * }
 */
export async function GET() {
  try {
    // 并行获取价格数据和用户信息
    const [plans, user] = await Promise.all([
      getPrice(),
      getUserInfo()
    ])

    // 检查是否获取到有效的价格数据
    if (!plans || plans.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No pricing plans available',
          plans: [],
          user: null
        },
        { status: 404 }
      )
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      plans,
      user
    })
  } catch (error) {
    console.error('Failed to fetch pricing data:', error)
    
    // 返回错误响应
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch pricing data',
        plans: [],
        user: null
      },
      { status: 500 }
    )
  }
}
