import { NextRequest, NextResponse } from 'next/server';
import * as BookModel from '@/models/book.model';
import { CacheManager } from '@/lib/cache-manager';

/**
 * 处理书籍访问量更新的 API 路由
 * POST /api/book-view
 *
 * 请求体格式:
 * {
 *   id: number;      // 书籍ID
 *   language: string; // 语言代码，默认为 'en'
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 从请求体中获取书籍ID和语言
    const { id, language = 'en' } = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Book ID is required' },
        { status: 400 }
      );
    }

    // 更新访问量
    await BookModel.incrementBookViewCount(Number(id));

    // 重新验证相关缓存
    CacheManager.book.revalidatePopular(language);

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: `updated view count for book (ID: ${id}) successfully`
    });
  } catch (error) {
    console.error('Error updating book view count:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update book view count' },
      { status: 500 }
    );
  }
}
