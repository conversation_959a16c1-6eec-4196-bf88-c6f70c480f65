import { NextRequest, NextResponse } from 'next/server';
import * as ReadingHistoryModel from '@/models/reading-history.model';
import { cookies } from 'next/headers';
import { verifyJwtToken } from '@/services/actions/authServer';
import { revalidateTag } from 'next/cache';

/**
 * 记录用户阅读历史的 API 路由
 * POST /api/reading-history
 *
 * 请求体格式:
 * {
 *   bookId: number;      // 书籍ID
 *   language?: string;   // 语言代码（用于缓存失效，可选）
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 验证用户认证
    const cookieStore = await cookies();
    const token = cookieStore.get(process.env.TOKEN_KEY!)?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { isValid, uid } = await verifyJwtToken(token);
    if (!isValid || !uid) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // 2. 解析请求体
    const body = await request.json();
    const { bookId } = body;

    if (!bookId) {
      return NextResponse.json(
        { success: false, error: 'Book ID is required' },
        { status: 400 }
      );
    }

    // 验证 bookId 是否为有效数字
    const bookIdNum = Number(bookId);
    if (isNaN(bookIdNum) || bookIdNum <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid book ID' },
        { status: 400 }
      );
    }

    // 3. 记录阅读历史
    await ReadingHistoryModel.addOrUpdateReadingHistory(Number(uid), bookIdNum);

    // 4. 重新验证相关缓存
    try {
      // 重新验证用户图书馆相关的缓存
      revalidateTag('user-library');
      revalidateTag(`user-${uid}`);
      console.log(`Cache revalidated for user ${uid} after recording reading history`);
    } catch (cacheError) {
      console.error('Failed to revalidate cache:', cacheError);
      // 缓存失效失败不应该影响主要功能
    }

    // 5. 返回成功响应
    return NextResponse.json({
      success: true,
      message: `Reading history recorded for user ${uid}, book ${bookIdNum}`
    });

  } catch (error) {
    console.error('Error recording reading history:', error);

    // 即使记录阅读历史失败，也返回成功响应，避免影响用户体验
    // 这是一个非关键功能，失败不应该影响页面正常显示
    return NextResponse.json({
      success: true,
      message: 'Request processed (reading history recording may have failed silently)'
    });
  }
}

/**
 * 删除阅读历史记录
 * DELETE /api/reading-history
 *
 * 请求体格式:
 * {
 *   bookIds: number[];   // 要删除的书籍ID数组
 * }
 *
 * 或者删除单个记录:
 * {
 *   bookId: number;      // 要删除的单个书籍ID
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    // 1. 验证用户认证
    const cookieStore = await cookies();
    const token = cookieStore.get(process.env.TOKEN_KEY!)?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { isValid, uid } = await verifyJwtToken(token);
    if (!isValid || !uid) {
      return NextResponse.json(
        { success: false, error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // 2. 解析请求体
    const body = await request.json();
    const { bookIds, bookId } = body;

    let success = false;

    if (bookIds && Array.isArray(bookIds)) {
      // 批量删除
      const validBookIds = bookIds.filter(id => Number.isInteger(Number(id)) && Number(id) > 0);
      if (validBookIds.length === 0) {
        return NextResponse.json(
          { success: false, error: 'No valid book IDs provided' },
          { status: 400 }
        );
      }

      success = await ReadingHistoryModel.deleteReadingHistory(Number(uid), validBookIds.map(Number));
    } else if (bookId) {
      // 删除单个记录
      const bookIdNum = Number(bookId);
      if (isNaN(bookIdNum) || bookIdNum <= 0) {
        return NextResponse.json(
          { success: false, error: 'Invalid book ID' },
          { status: 400 }
        );
      }

      success = await ReadingHistoryModel.deleteSingleReadingHistory(Number(uid), bookIdNum);
    } else {
      return NextResponse.json(
        { success: false, error: 'Either bookId or bookIds must be provided' },
        { status: 400 }
      );
    }

    // 3. 重新验证相关缓存
    if (success) {
      try {
        revalidateTag('user-library');
        revalidateTag(`user-${uid}`);
        console.log(`Cache revalidated for user ${uid} after deleting reading history`);
      } catch (cacheError) {
        console.error('Failed to revalidate cache:', cacheError);
        // 缓存失效失败不应该影响主要功能
      }
    }

    // 4. 返回响应
    return NextResponse.json({
      success,
      message: success
        ? 'Reading history deleted successfully'
        : 'Failed to delete reading history'
    });

  } catch (error) {
    console.error('Error deleting reading history:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete reading history' },
      { status: 500 }
    );
  }
}
