import { NextRequest, NextResponse } from 'next/server';
import * as AuthorModel from '@/models/author.model';
import { CacheManager } from '@/lib/cache-manager';

/**
 * 处理作者访问量更新的 API 路由
 * POST /api/author-view
 *
 * 请求体格式:
 * {
 *   id: number;      // 作者ID
 *   language: string; // 语言代码，默认为 'en'
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 从请求体中获取作者ID和语言
    const { id, language = 'en' } = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Author ID is required' },
        { status: 400 }
      );
    }

    // 更新访问量
    await AuthorModel.incrementAuthorViewCount(Number(id));

    // 重新验证相关缓存
    CacheManager.author.revalidate(Number(id), language);
    CacheManager.author.revalidatePopular(language);

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: `Updated view count for author (ID: ${id}) successfully`
    });
  } catch (error) {
    console.error('Error updating author view count:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update author view count' },
      { status: 500 }
    );
  }
}
