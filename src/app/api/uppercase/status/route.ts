import { NextRequest, NextResponse } from 'next/server'
import Task from '@/models/Task'

export async function GET(req: NextRequest) {
  try {
    // 获取所有任务
    const tasks = await Task.find(
      {},
      {
        taskId: 1,
        text: 1,
        result: 1,
        status: 1,
        submittedAt: 1,
        completedAt: 1,
        _id: 0
      }
    ).sort({ submittedAt: -1 })

    // 转换为前端需要的格式
    const mappedTasks = tasks.map((task) => ({
      id: task.taskId,
      text: task.text,
      result: task.result,
      status: task.status,
      submittedAt: task.submittedAt,
      completedAt: task.completedAt
    }))

    console.log(`查询到 ${mappedTasks.length} 个任务`)

    // 返回任务列表
    return NextResponse.json({
      success: true,
      tasks: mappedTasks
    })
  } catch (error) {
    console.error('查询任务状态失败:', error)

    return NextResponse.json({ error: '服务器错误，请稍后重试' }, { status: 500 })
  }
}
