import { NextRequest, NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'
import Task from '@/models/Task'
import { publishMessage } from '@/lib/rabbitmq/producer'

// 处理转大写请求
export async function POST(req: NextRequest) {
  try {
    // 解析请求体
    const body = await req.json()
    const { text } = body

    if (!text) {
      return NextResponse.json({ error: '请提供文本内容' }, { status: 400 })
    }

    if (text.length > 1000) {
      return NextResponse.json({ error: '文本长度不能超过1000个字符' }, { status: 400 })
    }

    // 生成唯一任务ID
    const taskId = uuidv4()

    // 创建新任务记录
    const task = new Task({
      taskId,
      text,
      status: 'pending',
      submittedAt: new Date()
    })

    // 保存到数据库
    await task.save()
    console.log(`创建了新的大写转换任务: ${taskId}`)

    // 发布消息到RabbitMQ队列
    const messageSuccess = await publishMessage('uppercase-tasks', { taskId, text })

    if (!messageSuccess) {
      throw new Error('无法发送任务到队列')
    }

    console.log(`任务 ${taskId} 已发送到队列`)

    // 返回任务ID
    return NextResponse.json({
      success: true,
      taskId
    })
  } catch (error) {
    console.error('处理大写转换请求失败:', error)

    return NextResponse.json({ error: '服务器错误，请稍后重试' }, { status: 500 })
  }
}
