// app/api/auth/verify/route.js
import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJwtToken } from '@/services/actions/authServer'

export async function GET() {
  const cookieStore = await cookies()
  const token = cookieStore.get('TAROT_ACCESS_TOKEN-dev')?.value

  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    // 验证并解码JWT
    const decodedToken = await verifyJwtToken(token)
    console.log('decodedToken:', decodedToken)
    // 返回用户信息
    // return NextResponse.json({
    //   id: decodedToken.uid,
    //   username: decodedToken.username,
    //   // 其他用户信息...
    // });
  } catch (error) {
    console.error('验证Token失败:', error)
    return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
  }
}
