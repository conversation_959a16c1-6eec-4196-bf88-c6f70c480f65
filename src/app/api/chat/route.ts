import { NextResponse } from 'next/server'
import { GoogleGenAI, Type } from '@google/genai'
import { Readable } from 'node:stream'
import { ReadableStream } from 'node:stream/web'
import { JSONParser } from '@streamparser/json'

export async function POST(req: Request) {
  try {
    const { apiKey } = await req.json()

    const ai = new GoogleGenAI({ apiKey })

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const originalStreamResponse = await ai.models.generateContentStream({
            model: 'gemini-2.0-flash',
            config: {
              responseMimeType: 'application/json',
              responseSchema: {
                type: Type.ARRAY,
                items: {
                  type: Type.OBJECT,
                  properties: {
                    recipeName: {
                      type: Type.STRING,
                      description: 'Name of the recipe',
                      nullable: false
                    }
                  },
                  required: ['recipeName']
                }
              }
            },
            contents: 'List a few popular cookie recipes using this JSON schema'
          })

          // 发送消息 ID
          controller.enqueue(JSON.stringify({ type: 'start', messageId: `msg-${Date.now()}` }))

          // 初始化 content 对象
          const content = { text: '' }

          for await (const chunk of originalStreamResponse) {
            const text = chunk.text
            if (text) {
              // 追加文本到 content
              content.text += text
              controller.enqueue(JSON.stringify({ type: 'data', content: text }))
            }
          }

          // 发送结束标记
          controller.enqueue(
            JSON.stringify({
              type: 'end',
              finishReason: 'stop',
              usage: {
                promptTokens: 913,
                completionTokens: 10
              }
            })
          )

          controller.close()
        } catch (error) {
          console.error('Error in ReadableStream start:', error)
          controller.error(error)
        }
      }
    })

    // 将 ReadableStream 转换为 Node.js 可读流
    const readable = new Readable({
      async read() {
        const reader = stream.getReader()
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            this.push(null)
            break
          }
          this.push(value)
        }
      }
    })

    // 使用 JSONParser 解析流式 JSON
    const jsonparser = new JSONParser({ stringBufferSize: undefined })

    jsonparser.onValue = (value) => {
      console.log('Parsed value:', value)
    }

    jsonparser.onToken = (token) => {
      console.log('Parsed token:', token)
    }

    jsonparser.onError = (err) => {
      console.error('JSONParser error:', err)
    }

    // 监听 Readable 流并解析 JSON
    readable.on('data', (chunk) => {
      console.log('chunk', chunk)
      try {
        jsonparser.write(chunk.toString())
      } catch (err) {
        console.error('Error writing chunk to JSONParser:', err)
      }
    })

    readable.on('end', () => {
      console.log('Readable stream ended.')
    })

    // @ts-expect-error
    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive'
      }
    })
  } catch (error) {
    console.error('Error calling Gemini API:', error)
    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 })
  }
}
