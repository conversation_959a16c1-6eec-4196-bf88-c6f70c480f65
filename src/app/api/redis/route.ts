import { NextRequest, NextResponse } from 'next/server'
import { getRedisClient } from '@/lib/redis'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const action = searchParams.get('action')
  const key = searchParams.get('key')

  if (!key) {
    return NextResponse.json({ success: false, error: '键名必须提供' }, { status: 400 })
  }

  try {
    const redis = getRedisClient()

    if (action === 'get') {
      const value = await redis.get(key)
      return NextResponse.json({
        success: true,
        value
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: '不支持的操作'
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Redis操作失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Redis操作失败'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, key, value } = body

    if (!key) {
      return NextResponse.json({ success: false, error: '键名必须提供' }, { status: 400 })
    }

    const redis = getRedisClient()

    if (action === 'set') {
      if (value === undefined) {
        return NextResponse.json({ success: false, error: '设置操作需要提供值' }, { status: 400 })
      }
      await redis.set(key, value)
      return NextResponse.json({ success: true })
    } else if (action === 'delete') {
      await redis.del(key)
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json({ success: false, error: '不支持的操作' }, { status: 400 })
    }
  } catch (error) {
    console.error('Redis操作失败:', error)
    return NextResponse.json({ success: false, error: 'Redis操作失败' }, { status: 500 })
  }
}
