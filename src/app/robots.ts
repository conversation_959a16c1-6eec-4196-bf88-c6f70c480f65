import type { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {

  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/private/',
        '/terms',
        '/privacy',
        '/_next',
        // 禁止搜索引擎收录其他语言路径
        '/zh/',
        '/ja/',
        '/ko/',
        '/de/',
        '/es/',
        '/fr/',
        '/pt/',
        '/tw/',
        '/vi/'
      ]
    },
    sitemap: `${process.env.NEXT_PUBLIC_BASE_URL}/sitemap.xml`
  }
}
