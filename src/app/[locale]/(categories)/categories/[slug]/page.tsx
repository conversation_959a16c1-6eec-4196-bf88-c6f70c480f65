import { Metadata } from 'next';
import BookGenreList from "./components/BookGenreList";
import { getCategoryPageData } from '@/services/actions/categories.action';

interface CategoryPageProps {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
  searchParams: Promise<{
    page?: string;
    sort?: 'newest' | 'popular' | 'rating';
  }>;
}

// 动态生成页面元数据
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug, locale } = await params;

  try {
    const result = await getCategoryPageData(slug, 1, 1, locale);

    if (result.success && result.data && result.data.category) {
      // 获取品牌名称
      const { getTranslations } = await import('next-intl/server');
      const t = await getTranslations({ locale, namespace: 'Metadata' });
      const brandName = t('brandName');

      // 构建分类名称
      const categoryName = result.data.category.name;

      // 构建SEO标题和描述
      const title = `Best ${categoryName} Book Summaries & Audio Analysis | ${brandName}`;
      const description = `Explore top ${categoryName} book summaries & analysis online. Download PDF or listen to audiobooks for concise chapter synopses, plot reviews & expert insights.`;

      // 构建规范URL
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      const canonicalUrl = `${baseUrl}/genres/${slug}`;

      return {
        title,
        description,
        alternates: {
          canonical: canonicalUrl,
          languages: {
            'en-US': `${baseUrl}/genres/${slug}`,
            'zh-CN': `${baseUrl}/zh/genres/${slug}`
          }
        },
        openGraph: {
          title,
          description,
          url: canonicalUrl,
          type: 'website'
        },
        twitter: {
          card: 'summary',
          title: `${categoryName} Book Summaries | ${brandName}`,
          description: `Explore top ${categoryName} book summaries & analysis online.`
        }
      };
    }
  } catch (error) {
    console.error('获取分类元数据失败:', error);
  }

  // 默认元数据
  return {
    title: 'Category Books | 15Minutes',
    description: 'Explore our collection of book summaries and analyses by category.'
  };
}

export default async function CategoriesPage({ params, searchParams }: CategoryPageProps) {
  const { slug, locale } = await params;
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1;
  const sort = resolvedSearchParams.sort || 'popular';

  // 获取分类数据
  const categoryData = await getCategoryPageData(
    slug,
    page,
    12,
    locale,
    sort as 'newest' | 'popular' | 'rating'
  );

  return (
    <BookGenreList
      genre={slug}
      initialData={categoryData.success && categoryData.data ? categoryData.data : null}
      error={categoryData.success ? null : (categoryData.error || "Failed to load category data")}
    />
  );
}
