"use client";

import React from 'react';
import GenreLink from './GenreLink';
import { CategoryListItem } from '@/models/category.model';

interface GenreSidebarProps {
  currentGenre: string;
  categories?: CategoryListItem[];
}

const GenreSidebar: React.FC<GenreSidebarProps> = ({ currentGenre, categories = [] }) => {
  // 将分类名称转换为 slug 格式
  function convertToSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 将空格替换为连字符
      .replace(/-+/g, '-'); // 移除连续的连字符
  }

  // 定义类型以确保一致性
  type GenreItem = {
    name: string;
    href: string;
    bookCount?: number;
  };

  // 如果有服务端数据，使用服务端数据
  const genres: GenreItem[] = categories.length > 0
    ? categories.map(category => ({
      name: category.name,
      href: `/categories/${convertToSlug(category.name)}`,
      bookCount: category.bookCount
    }))
    : [
      { name: 'Fiction', href: '/categories/fiction' },
      { name: 'Non-Fiction', href: '/categories/non-fiction' },
      { name: 'Mystery', href: '/categories/mystery' },
      { name: 'Science Fiction', href: '/categories/science-fiction' },
      { name: 'Fantasy', href: '/categories/fantasy' },
      { name: 'Romance', href: '/categories/romance' },
      { name: 'Thriller', href: '/categories/thriller' },
      { name: 'Horror', href: '/categories/horror' },
      { name: 'Historical Fiction', href: '/categories/historical-fiction' },
      { name: 'Biography', href: '/categories/biography' },
      { name: 'Self-Help', href: '/categories/self-help' },
      { name: 'Business', href: '/categories/business' },
      { name: 'Children\'s Books', href: '/categories/childrens-books' },
      { name: 'Young Adult', href: '/categories/young-adult' },
      { name: 'Poetry', href: '/categories/poetry' },
    ];

  return (
    <div className="w-full lg:w-1/4 mt-8 lg:mt-0">
      <div className="bg-gray-50 p-6 rounded-lg sticky top-24">
        <h3 className="font-bold text-xl mb-4">Browse by Genre</h3>
        <ul className="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
          {genres.map((genre) => (
            <GenreLink
              key={genre.name}
              name={genre.name}
              href={genre.href}
              isActive={genre.name.toLowerCase() === currentGenre.toLowerCase()}
              bookCount={genre.bookCount}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};

export default GenreSidebar;
