'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useLocale } from 'next-intl';
import { CategoryPageData, getCategoryPageData } from '@/services/actions/categories.action';
import BookList from './BookList';
import GenreSidebar from './GenreSidebar';

interface BookGenreListProps {
  genre?: string;
  initialData: CategoryPageData | null;
  error: string | null;
}

const BookGenreList: React.FC<BookGenreListProps> = ({ genre = 'Fiction', initialData, error }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const locale = useLocale(); // 获取当前语言
  const [isLoading, setIsLoading] = useState(false);
  const [currentData, setCurrentData] = useState<CategoryPageData | null>(initialData);
  const [currentError, setCurrentError] = useState<string | null>(error);

  // 获取当前页面参数
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const currentSort = searchParams.get('sort') || 'popular';

  // 数据获取函数
  const fetchData = async (page: number, sort: string) => {
    if (!genre) return;

    setIsLoading(true);
    setCurrentError(null);

    try {
      const result = await getCategoryPageData(
        genre,
        page,
        12,
        locale, // 使用当前语言
        sort as 'newest' | 'popular' | 'rating'
      );

      if (result.success && result.data) {
        setCurrentData(result.data);
      } else {
        setCurrentError(result.error || 'Failed to load category data');
        setCurrentData(null);
      }
    } catch (err) {
      console.error('获取分类数据失败:', err);
      setCurrentError('Failed to load category data');
      setCurrentData(null);
    } finally {
      setIsLoading(false);
    }
  };

  // 监听路由参数变化，重新获取数据
  useEffect(() => {
    // 如果页面参数变化且不是初始加载（第一页且默认排序），则重新获取数据
    // 但是如果当前数据的页码与URL页码不匹配，也需要重新获取
    const isInitialLoad = currentPage === 1 && currentSort === 'popular';
    const isDataPageMismatch = currentData?.books?.meta?.page !== currentPage;

    if (!isInitialLoad || isDataPageMismatch) {
      fetchData(currentPage, currentSort);
    }
  }, [currentPage, currentSort, genre, locale]);

  // 处理页面变化
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    router.push(`?${params.toString()}`);

    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  // 如果有错误，显示错误信息
  if (currentError) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{currentError}</p>
        </div>
      </div>
    );
  }

  // 获取分类信息
  const category = currentData?.category;
  const genreInfo = {
    title: category ? `${category.name} Books` : `${genre} Books`,
    description: category?.description || 'Discover captivating stories that transport you to different worlds, introduce you to unforgettable characters, and explore the depths of human imagination. Our curated selection of books spans multiple genres and styles.'
  };

  return (
    <div>
      {/* 标题区域 */}
      <section className="bg-green-50 py-12">
        <div className="container mx-auto px-4">
          <h1 className="font-heading font-bold text-5xl tracking-tight mb-4">{genreInfo.title}</h1>
          <p className="text-lg mb-2 text-gray-600">{genreInfo.description}</p>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row">
            {/* 书籍列表 */}
            <BookList
              initialBooks={currentData?.books || { data: [], meta: { total: 0, page: 1, limit: 12, totalPages: 0 } }}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              currentPage={currentPage}
            />

            {/* 侧边栏 */}
            <GenreSidebar
              currentGenre={category?.name || genre}
              categories={currentData?.allCategories || []}
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default BookGenreList;
