"use client";

import React from 'react';
import Link from 'next/link';

interface GenreLinkProps {
  name: string;
  href: string;
  isActive?: boolean;
  bookCount?: number;
}

const GenreLink: React.FC<GenreLinkProps> = ({ name, href, isActive = false, bookCount }) => {
  return (
    <li>
      <div className="flex justify-between items-center">
        <Link
          className={isActive
            ? "text-green-600 font-medium hover:underline"
            : "text-gray-600 hover:text-green-600 transition duration-200"}
          href={href}
        >
          {name}
        </Link>
        {/* {bookCount !== undefined && (
          <span className="text-xs text-gray-400">
            {bookCount} {bookCount === 1 ? 'book' : 'books'}
          </span>
        )} */}
      </div>
    </li>
  );
};

export default GenreLink;
