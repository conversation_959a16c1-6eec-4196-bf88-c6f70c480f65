"use client";

import Link from 'next/link';
import React from 'react';
import BookCover from '@/components/common/BookCover';
import { urlGenerator } from '@/services/url.service';

interface BookCardProps {
  id: number;
  coverImage: string | null | undefined;
  title: string;
  author: string;
  description: string;
  rating: number;
  titleLength?: number;
}

const BookCard: React.FC<BookCardProps> = ({
  id,
  coverImage,
  title,
  author,
  description,
  rating,
  titleLength = 3,
}) => {
  // 生成书籍详情页面URL
  const bookDetailUrl = urlGenerator.book.detail({
    id,
    title
  });

  // 渲染星级评分
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const stars = '★'.repeat(fullStars) + '☆'.repeat(5 - fullStars);
    return stars;
  };
  const TitleTagName = `h${titleLength}`;

  return (
    <Link href={bookDetailUrl} className="block">
      <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-200 cursor-pointer flex flex-col h-full">
        {/* 图片区域 - 自适应宽度 */}
        <div className="flex-shrink-0 w-full" style={{ aspectRatio: '176/256' }}>
          <BookCover
            coverUrl={coverImage || 'https://placehold.co/176x256'}
            title={title}
            className="w-full h-full object-cover"
            style={{ width: '100%', height: '100%' }}
            fallbackColor="e9e1cc"
            priority={false}
          />
        </div>

        {/* 文本区域 - 使用Grid布局精确控制 */}
        <div className="p-3 xs:p-4 grid grid-rows-[auto_auto_1fr_auto] gap-1 xs:gap-2 h-[140px] xs:h-[150px] sm:h-[160px] md:h-[145px]">
          {/* Title: 1行，超出省略 */}
          {/* <h3 className="font-bold text-sm xs:text-base sm:text-lg tracking-tight line-clamp-1 leading-tight">
            {title}
          </h3> */}
          {React.createElement(
            TitleTagName as keyof JSX.IntrinsicElements,
            {
              className: "font-bold text-sm xs:text-base sm:text-lg tracking-tight line-clamp-1 leading-tight"
            },
            title
          )}

          {/* Author: 1行，超出省略 */}
          <p className="text-gray-600 text-xs xs:text-sm line-clamp-1 leading-tight">
            {author}
          </p>

          {/* Description: 2行，超出省略 */}
          <p className="text-gray-500 text-xs italic line-clamp-2 leading-tight max-h-[30px]">
            {description}
          </p>

          {/* Rating: 固定在底部 */}
          <div className="flex items-center text-xs sm:text-sm">
            <span className="text-yellow-500">{renderStars(rating)}</span>
            <span className="text-gray-500 ml-1">{rating.toFixed(1)}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default BookCard;
