"use client";

import React from 'react';
import BookCard from './BookCard';
import Pagination from '@/components/common/Pagination';
import { BookListItem, PaginatedResult } from '@/types/book.types';

interface BookListProps {
  initialBooks: PaginatedResult<BookListItem>;
  onPageChange: (page: number) => void;
  isLoading: boolean;
  currentPage: number; // 添加当前页码 prop
}

const BookList: React.FC<BookListProps> = ({
  initialBooks,
  onPageChange,
  isLoading,
  currentPage,
}) => {

  // const initialBooks1 = initialBooks.data.concat(initialBooks.data, initialBooks.data)

  return (
    <div className="w-full lg:w-3/4 lg:pr-8">
      {/* 加载状态 */}
      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array(12).fill(0).map((_, index) => (
            <div key={index} className="bg-gray-100 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      ) : (
        <>
          {/* 书籍列表 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {initialBooks.data.length > 0 ? (
              initialBooks.data.map(book => (
                // initialBooks1.map(book => (
                <BookCard
                  key={book.id}
                  id={book.id}
                  coverImage={book.coverUrl || 'https://placehold.co/176x256'}
                  title={book.title}
                  author={book.authors.join(', ')}
                  description={book.subtitle || ''}
                  rating={book.rating || 0}
                  titleLength={2}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-10">
                <p className="text-gray-500">No books found in this category.</p>
              </div>
            )}
          </div>

          {/* 分页 */}
          {initialBooks.meta.totalPages > 1 && (
            <div className="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={initialBooks.meta.totalPages}
                onPageChange={onPageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default BookList;
