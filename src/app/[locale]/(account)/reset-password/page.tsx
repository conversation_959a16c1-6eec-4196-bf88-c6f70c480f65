'use client'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { AuthForm, FormData } from '@/components/AuthForm'
import { Link, useRouter } from '@/i18n/routing'
import { useToast } from '@/hooks/useToast'
import { forgotPassword } from '@/services/client/authService'
import { useTranslations } from 'next-intl'

export default function ResetPassword() {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>()
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const { showToast } = useToast()
  const t = useTranslations('resetPassword')

  const onSubmit = async (data: FormData): Promise<void> => {
    setLoading(true)
    try {
      const response = await forgotPassword(data.email)
      setLoading(false)
      if (response.code === 200) {
        /** TODO: email and code */
        router.push(`/reset-password/sent?code`)
      } else {
        showToast(response.message, 'error')
      }
    } catch {
      setLoading(false)
    }
  }

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <AuthForm
        type="reset-password"
        handleSubmit={handleSubmit}
        onSubmit={onSubmit}
        register={register}
        errors={errors}
        loading={loading}
        isResetPassword
      />
      <p className="mt-4 text-center">
        {t('rememberPassword')}
        <Link href="/login" className="text-primary ml-2">
          {t('signIn')}
        </Link>
      </p>
    </>
  )
}
