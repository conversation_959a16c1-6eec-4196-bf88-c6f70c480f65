'use client'
import { useState, Suspense } from 'react'
import { useForm } from 'react-hook-form'
import { AuthForm, FormData } from '@/components/AuthForm'
import { Link, useRouter } from '@/i18n/routing'
import { useToast } from '@/hooks/useToast'
import { resetPassword, ResetPasswordData } from '@/services/client/authService'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

function ResetPasswordContent() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<FormData>()
  const searchParams = useSearchParams()
  const code = searchParams.get('code') || ''
  const email = searchParams.get('email') || ''

  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const { showToast } = useToast()

  const t = useTranslations('resetPassword')

  const onSubmit = async (data: FormData): Promise<void> => {
    try {
      const response = await resetPassword({ ...data, code, email } as ResetPasswordData)
      setLoading(false)
      if (response.code === 200) {
        router.push(`/login`)
      } else {
        showToast(response.message, 'error')
      }
    } catch {
      setLoading(false)
    }
  }

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <AuthForm
        type="password-reset"
        handleSubmit={handleSubmit}
        onSubmit={onSubmit}
        register={register}
        errors={errors}
        loading={loading}
        watch={watch}
        isResetPassword
      />
      <p className="mt-4 text-center">
        {t('rememberPassword')}
        <Link href="/login" className="text-primary ml-2">
          {t('signIn')}
        </Link>
      </p>
    </>
  )
}

export default function ResetPassword() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordContent />
    </Suspense>
  )
}
