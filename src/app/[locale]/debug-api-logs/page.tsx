'use client'
import { useState, useEffect } from 'react'

export default function DebugApiLogsPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [isDebugEnabled, setIsDebugEnabled] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // 检查调试是否启用
    const debugEnabled = localStorage.getItem('enableApiDebug') === 'true'
    setIsDebugEnabled(debugEnabled)

    // 拦截console.log来捕获日志
    const originalLog = console.log
    const originalError = console.error

    const captureLog = (level: 'log' | 'error') => (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ')

      if (message.includes('apiServer') || message.includes('===')) {
        setLogs(prev => [...prev, `[${level.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`])
      }

      // 调用原始方法
      if (level === 'log') originalLog(...args)
      else originalError(...args)
    }

    console.log = captureLog('log')
    console.error = captureLog('error')

    return () => {
      console.log = originalLog
      console.error = originalError
    }
  }, [])

  const toggleDebug = () => {
    if (isDebugEnabled) {
      localStorage.removeItem('enableApiDebug')
      setIsDebugEnabled(false)
    } else {
      localStorage.setItem('enableApiDebug', 'true')
      setIsDebugEnabled(true)
    }
    window.location.reload() // 重新加载以应用设置
  }

  const testApiCall = async () => {
    setLoading(true)
    setLogs([]) // 清空之前的日志

    try {
      // 这会触发 apiServer 的调试日志
      const result = await fetch('/api/test-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      })

      const data = await result.json()
      setTestResult(data)
    } catch (error: any) {
      setTestResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">API调试日志监控</h1>

      {/* 控制面板 */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">调试控制</h2>

        <div className="flex gap-4 mb-4">
          <button
            onClick={toggleDebug}
            className={`px-4 py-2 rounded ${isDebugEnabled
              ? 'bg-red-500 text-white hover:bg-red-600'
              : 'bg-green-500 text-white hover:bg-green-600'
              }`}
          >
            {isDebugEnabled ? '关闭调试日志' : '启用调试日志'}
          </button>

          <button
            onClick={testApiCall}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试API调用'}
          </button>

          <button
            onClick={clearLogs}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空日志
          </button>
        </div>

        <div className="text-sm text-gray-600">
          <p><strong>调试状态:</strong> {isDebugEnabled ? '✅ 已启用' : '❌ 已禁用'}</p>
          <p><strong>说明:</strong> 启用调试后，所有 apiServer 的请求和响应都会被记录</p>
        </div>
      </div>

      {/* 测试结果 */}
      {testResult && (
        <div className="bg-blue-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-2">测试结果</h3>
          <pre className="bg-white p-3 rounded text-sm overflow-auto">
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}

      {/* 实时日志 */}
      <div className="bg-black text-green-400 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">实时API日志 ({logs.length}条)</h3>
          <div className="text-sm">
            {logs.length > 0 && `最新: ${logs[logs.length - 1]?.split(']:')[0]}]`}
          </div>
        </div>

        <div className="h-96 overflow-y-auto font-mono text-sm">
          {logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              暂无日志记录<br />
              {!isDebugEnabled && '请先启用调试日志，然后测试API调用'}
            </div>
          ) : (
            logs.map((log, index) => (
              <div
                key={index}
                className={`mb-2 p-2 rounded ${log.includes('[ERROR]') ? 'bg-red-900/30' :
                  log.includes('===') ? 'bg-blue-900/30' :
                    'bg-gray-900/30'
                  }`}
              >
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 bg-yellow-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">使用说明</h3>
        <div className="text-sm space-y-2">
          <p><strong>1. 启用调试:</strong> 点击"启用调试日志"按钮</p>
          <p><strong>2. 测试API:</strong> 点击"测试API调用"按钮触发 getUserInfo 调用</p>
          <p><strong>3. 查看日志:</strong> 在下方黑色区域查看详细的请求和响应日志</p>
          <p><strong>4. 生产环境:</strong> 可以通过设置环境变量 ENABLE_API_DEBUG=true 启用</p>
          <p><strong>5. 关闭调试:</strong> 点击"关闭调试日志"按钮或移除环境变量</p>
        </div>
      </div>
    </div>
  )
}
