'use client'
import { useState, useEffect } from 'react'

export default function DebugApiLogsPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [isDebugEnabled, setIsDebugEnabled] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // 检查调试是否启用
    const debugEnabled = localStorage.getItem('enableApiDebug') === 'true'
    setIsDebugEnabled(debugEnabled)

    // 拦截console.log来捕获日志
    const originalLog = console.log
    const originalError = console.error

    const captureLog = (level: 'log' | 'error') => (...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ')

      if (message.includes('apiServer') || message.includes('===')) {
        setLogs(prev => [...prev, `[${level.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`])
      }

      // 调用原始方法
      if (level === 'log') originalLog(...args)
      else originalError(...args)
    }

    console.log = captureLog('log')
    console.error = captureLog('error')

    return () => {
      console.log = originalLog
      console.error = originalError
    }
  }, [])

  const toggleDebug = () => {
    if (isDebugEnabled) {
      localStorage.removeItem('enableApiDebug')
      setIsDebugEnabled(false)
    } else {
      localStorage.setItem('enableApiDebug', 'true')
      setIsDebugEnabled(true)
    }
    window.location.reload() // 重新加载以应用设置
  }

  const testApiCall = async () => {
    setLoading(true)
    setLogs([]) // 清空之前的日志

    try {
      // 这会触发 apiServer 的调试日志
      const result = await fetch('/api/test-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      })

      const data = await result.json()
      setTestResult(data)
    } catch (error: any) {
      setTestResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testDirectApiCall = async () => {
    setLoading(true)
    try {
      // 直接测试外部API
      const result = await fetch('/common-api/v1/user', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        directApiResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        directApiError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testAuthDebug = async () => {
    setLoading(true)
    try {
      // 测试认证调试接口
      const result = await fetch('/api/debug/auth', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        authDebugResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        authDebugError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testRedisConfig = async () => {
    setLoading(true)
    try {
      // 测试Redis配置调试接口
      const result = await fetch('/api/debug/redis-config', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        redisConfigResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        redisConfigError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testEnvConfig = async () => {
    setLoading(true)
    try {
      // 测试环境配置调试接口
      const result = await fetch('/api/debug/env-config', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        envConfigResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        envConfigError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testApiServer = async () => {
    setLoading(true)
    try {
      // 测试apiServer调试接口
      const result = await fetch('/api/debug/apiserver-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        apiServerResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        apiServerError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testCompareMethods = async () => {
    setLoading(true)
    try {
      // 测试对比方法接口
      const result = await fetch('/api/debug/compare-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        compareMethodsResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        compareMethodsError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testTokenValidation = async () => {
    setLoading(true)
    try {
      // 测试Token验证接口
      const result = await fetch('/api/debug/token-validation', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        tokenValidationResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        tokenValidationError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testFixedVersion = async () => {
    setLoading(true)
    try {
      // 测试修复版本接口
      const result = await fetch('/api/debug/test-fixed-version', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        fixedVersionResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        fixedVersionError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testCookieCheck = async () => {
    setLoading(true)
    try {
      // 测试Cookie检查接口
      const result = await fetch('/api/debug/cookie-check', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await result.json()
      setTestResult({
        ...testResult,
        cookieCheckResult: {
          status: result.status,
          data: data
        }
      })
    } catch (error: any) {
      setTestResult({
        ...testResult,
        cookieCheckError: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">API调试日志监控</h1>

      {/* 控制面板 */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">调试控制</h2>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
          <button
            onClick={toggleDebug}
            className={`px-4 py-2 rounded ${isDebugEnabled
              ? 'bg-red-500 text-white hover:bg-red-600'
              : 'bg-green-500 text-white hover:bg-green-600'
              }`}
          >
            {isDebugEnabled ? '关闭调试日志' : '启用调试日志'}
          </button>

          <button
            onClick={testApiCall}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试内部API'}
          </button>

          <button
            onClick={testDirectApiCall}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试直接API'}
          </button>

          <button
            onClick={testAuthDebug}
            disabled={loading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试认证调试'}
          </button>

          <button
            onClick={testRedisConfig}
            disabled={loading}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试Redis配置'}
          </button>

          <button
            onClick={testEnvConfig}
            disabled={loading}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试环境配置'}
          </button>

          <button
            onClick={testApiServer}
            disabled={loading}
            className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试apiServer'}
          </button>

          <button
            onClick={testCompareMethods}
            disabled={loading}
            className="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '对比测试'}
          </button>

          <button
            onClick={testTokenValidation}
            disabled={loading}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : 'Token验证'}
          </button>

          <button
            onClick={testFixedVersion}
            disabled={loading}
            className="px-4 py-2 bg-emerald-500 text-white rounded hover:bg-emerald-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试修复版本'}
          </button>

          <button
            onClick={testCookieCheck}
            disabled={loading}
            className="px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 disabled:opacity-50"
          >
            {loading ? '测试中...' : 'Cookie检查'}
          </button>

          <button
            onClick={clearLogs}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空日志
          </button>
        </div>

        <div className="text-sm text-gray-600">
          <p><strong>调试状态:</strong> {isDebugEnabled ? '✅ 已启用' : '❌ 已禁用'}</p>
          <p><strong>说明:</strong> 启用调试后，所有 apiServer 的请求和响应都会被记录</p>
        </div>
      </div>

      {/* 测试结果 */}
      {testResult && (
        <div className="bg-blue-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-2">测试结果</h3>
          <pre className="bg-white p-3 rounded text-sm overflow-auto">
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}

      {/* 实时日志 */}
      <div className="bg-black text-green-400 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">实时API日志 ({logs.length}条)</h3>
          <div className="text-sm">
            {logs.length > 0 && `最新: ${logs[logs.length - 1]?.split(']:')[0]}]`}
          </div>
        </div>

        <div className="h-96 overflow-y-auto font-mono text-sm">
          {logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              暂无日志记录<br />
              {!isDebugEnabled && '请先启用调试日志，然后测试API调用'}
            </div>
          ) : (
            logs.map((log, index) => (
              <div
                key={index}
                className={`mb-2 p-2 rounded ${log.includes('[ERROR]') ? 'bg-red-900/30' :
                  log.includes('===') ? 'bg-blue-900/30' :
                    'bg-gray-900/30'
                  }`}
              >
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 bg-yellow-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">使用说明</h3>
        <div className="text-sm space-y-2">
          <p><strong>1. 启用调试:</strong> 点击"启用调试日志"按钮</p>
          <p><strong>2. 测试内部API:</strong> 点击"测试内部API"按钮触发 getUserInfo 调用（包含直接fetch对比）</p>
          <p><strong>3. 测试直接API:</strong> 点击"测试直接API"按钮直接访问 /common-api/v1/user</p>
          <p><strong>4. 测试认证调试:</strong> 点击"测试认证调试"按钮查看详细认证状态</p>
          <p><strong>5. 测试Redis配置:</strong> 点击"测试Redis配置"按钮检查Redis数据库配置</p>
          <p><strong>6. 测试环境配置:</strong> 点击"测试环境配置"按钮检查环境变量配置</p>
          <p><strong>7. 测试apiServer:</strong> 点击"测试apiServer"按钮专门调试apiServer的Cookie传递问题</p>
          <p><strong>8. 对比测试:</strong> 点击"对比测试"按钮对比原版和修复版getUserInfo的结果</p>
          <p><strong>9. Token验证:</strong> 点击"Token验证"按钮详细检查Token的有效性和Redis存储状态</p>
          <p><strong>10. 测试修复版本:</strong> 点击"测试修复版本"按钮专门验证getUserInfoFixed是否能解决问题</p>
          <p><strong>11. 查看日志:</strong> 在下方黑色区域查看详细的请求和响应日志</p>
          <p><strong>12. 生产环境:</strong> 可以通过设置环境变量 ENABLE_API_DEBUG=true 启用</p>
          <p><strong>13. 关闭调试:</strong> 点击"关闭调试日志"按钮或移除环境变量</p>
        </div>
      </div>
    </div>
  )
}
