import { GoogleOAuthProvider } from '@react-oauth/google'
import ToastProvider from '@/components/ToastProvider'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages, getTranslations, setRequestLocale } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { routing, SupportedLanguage } from '@/i18n/routing'
import { GoogleLoginClient } from '@/components/GoogleLoginClient'
import { headers } from 'next/headers'
import { Metadata } from 'next'
import { DM_Sans, Poppins } from 'next/font/google'
import { ViewTransitions } from 'next-view-transitions'

import { getUserInfo } from '@/services/server/userService'
import GoogleAnalytics from '@/components/GoogleAnalytics'
import { Toaster } from '@/components/ui/toaster'
import { TooltipProvider } from '@/components/ui/tooltip'
import { AuthProvider } from '@/contexts/AuthContext'
// import dbConnect from '@/lib/mongodb'
import { AuthModal } from '@/components/common/authModal'
import { PricingModal } from '@/components/common/pricingModal'
import '@/app/[locale]/globals.css'

// 配置 DM Sans 字体
const dmSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-dm-sans',
  display: 'swap',
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
})

// 配置 Poppins 字体
const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  display: 'swap',
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
})

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: SupportedLanguage }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'Metadata' })

  // 获取请求头
  const headersList = await headers()
  // 从请求头中获取当前路径，如果不存在则使用空字符串
  const currentPath = headersList.get('x-next-url') || ''

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || ''

  const partUrl = extractPathFromUrl(currentPath, locale)

  // 构建当前页面的完整URL
  const currentUrl = `${baseUrl}${partUrl}`

  // 构建各语言版本的URL
  const languageAlternates: Record<string, string> = {}

  // 只为英语创建URL（因为只支持英语）
  languageAlternates['en-US'] = `${baseUrl}${partUrl}`

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      url: currentUrl,
      siteName: t('brandName'),
      locale: 'en_US',
      type: 'website'
    },
    metadataBase: new URL(baseUrl || 'https://example.com'),
    alternates: {
      languages: languageAlternates,
      canonical: currentUrl
    },
    icons: {
      icon: [
        {
          url: '/favicon.ico'
        },
        {
          url: '/favicon-16x16.png',
          sizes: '16x16',
          type: 'image/png'
        },
        {
          url: '/favicon-32x32.png',
          sizes: '32x32',
          type: 'image/png'
        },
        {
          url: '/favicon-48x48.png',
          sizes: '48x48',
          type: 'image/png'
        },
        {
          url: '/favicon-128x128.png',
          sizes: '128x128',
          type: 'image/png'
        }
      ],
      apple: '/favicon.ico'
    }
  }
}

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode
  params: Promise<{ locale: SupportedLanguage }>
}>) {
  const { locale } = await params;

  // await dbConnect()

  const userInfo = await getUserInfo()

  if (!routing.locales.includes(locale)) {
    notFound()
  }

  setRequestLocale(locale)

  const message = await getMessages()
  const clientId = process.env.CLIENT_ID
  return (
    <ViewTransitions>

      <html lang={locale} className={`light ${dmSans.variable} ${poppins.variable}`}>
        <head>
          <GoogleAnalytics />
        </head>
        <body suppressHydrationWarning className={`antialiased bg-body text-body font-body`}>
          <NextIntlClientProvider messages={message}>
            <TooltipProvider>
              <GoogleOAuthProvider clientId={clientId!}>
                <AuthProvider>
                  <ToastProvider>
                    <div className="min-h-screen flex flex-col">{children}</div>
                    <GoogleLoginClient user={userInfo} />
                    <AuthModal />
                    <PricingModal />
                  </ToastProvider>
                </AuthProvider>
              </GoogleOAuthProvider>
              <Toaster />
            </TooltipProvider>
          </NextIntlClientProvider>
        </body>
      </html>
    </ViewTransitions>
  )
}

function extractPathFromUrl(url: string, locale: string): string {
  try {
    // 检查URL是否是完整URL（包含协议）
    if (url.startsWith('http://') || url.startsWith('https://')) {
      const parsedUrl = new URL(url)
      return parsedUrl.pathname.replace(`/${locale}`, '')
    } else {
      // 如果只是路径，直接处理
      return url.replace(`/${locale}`, '')
    }
  } catch (error) {
    console.error('URL处理错误:', error)
    // 出错时返回空字符串，避免整个应用崩溃
    return ''
  }
}
