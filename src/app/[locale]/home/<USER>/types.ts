export interface EarningsSectionProps {
  message: {
    earningsSectionTitle: string
    card1: string
    card2: string
    card3: string
    earningsData: {
      recommendations: Array<{ text: string; value: string; icon?: string }>
      others: Array<{ text: string; value: string; icon?: string }>
      earnings: Array<{ text: string; value: string; icon?: string }>
    }
  }
}

export interface HowItWorksProps {
  message: {
    howItWorkTitle: string
    steps: Array<{ content: string; icon: string }>
  }
}

export interface SectionProps {
  message: {
    text?: string
    features: Array<FeaturesProps>
  }
}

export interface FeaturesProps {
  icon: string
  title: string
  description: string
  details: string
}

export interface HeroProps {
  message: {
    heroTitle1: string
    heroTitle2: string
    heroMarketPlan: string
    heroGetBenefit: string
    heroStartEarning: string
  }
}

export interface BenefitsProps {
  message: {
    benefitsTitle: string
    benefitsData: Array<{ icon: string; title: string; description: string }>
  }
}
