function Quote({ color, className = '' }: { color: string; className?: string }) {
  return (
    <svg
      className={className}
      width="23"
      height="28"
      viewBox="0 0 23 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9889 27.6731C10.1419 27.1437 6.4775 26.6663 2.95553 25.7288C1.03424 25.2255 0.334135 23.4865 1.27633 21.6135C4.54638 15.1481 7.88024 8.7161 11.2032 2.27529C11.6374 1.43935 12.331 0.668294 13.2373 0.589001C15.9244 0.334451 18.5958 0.597019 20.9901 1.86669C22.214 2.51863 22.4397 3.70555 22.0997 5.03927C20.3958 11.7763 18.723 18.5204 17.0602 25.2636C16.6416 26.974 15.4822 27.6398 12.9796 27.6836L12.9889 27.6731Z"
        fill={color}
      />
    </svg>
  )
}

export function Quotes({ color }: { color: string }) {
  return (
    <div className="flex items-center">
      <Quote color={color} className="rotate-[10deg]" />
      <Quote color={color} className="rotate-[5deg]" />
    </div>
  )
}
