import Image from 'next/image'
import { StatisticsBanner } from './StatisticsBanner'

export function ClientSection() {
  return (
    <>
      {/** Clients */}
      <div className="mt-24 sm:mt-20 md:mt-16 relative max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-4 mx-auto">
        <div className="absolute z-10 inset-x-0 -top-16 mx-auto text-center mb-6 px-4">
          <StatisticsBanner />
        </div>

        {/* Mobile layout (金字塔形状) */}
        <div className="flex flex-wrap items-center justify-center gap-6 md:hidden mt-10">
          <div className="flex justify-center w-full gap-x-6">
            {/* 第一行两个 */}
            <Image
              src="/brands/Reddit.png"
              alt="Reddit logo"
              width={0}
              height={0}
              sizes="100%"
              className="w-20 h-auto object-contain"
            />
            <Image
              src="/brands/YouTube.png"
              alt="YouTube logo"
              width={0}
              height={0}
              sizes="100%"
              className="w-20 h-auto object-contain"
            />
          </div>
          <div className="flex justify-center w-full gap-x-6">
            {/* 第二行三个 */}
            <Image
              src="/brands/Discord.png"
              alt="Discord logo for community engagement"
              width={0}
              height={0}
              sizes="100%"
              className="w-24 h-auto object-contain"
            />
            <Image
              src="/brands/WhatsApp.png"
              alt="WhatsApp logo"
              width={0}
              height={0}
              sizes="100%"
              className="w-24 h-auto object-contain"
            />
            <Image
              src="/brands/Threads.png"
              alt="Threads logo"
              width={0}
              height={0}
              sizes="100%"
              className="w-24 h-auto object-contain"
            />
          </div>
        </div>

        {/* Desktop layout (水平排列) */}
        <div className="hidden md:flex items-center justify-center gap-x-6 sm:gap-x-12 lg:gap-x-24">
          <Image
            src="/brands/Reddit.png"
            alt="Reddit logo"
            width={0}
            height={0}
            sizes="100%"
            className="w-20 lg:w-24 h-auto object-contain"
          />
          <Image
            src="/brands/YouTube.png"
            alt="YouTube logo"
            width={0}
            height={0}
            sizes="100%"
            className="w-20 lg:w-24 h-auto object-contain"
          />
          <Image
            src="/brands/Discord.png"
            alt="Discord logo for community engagement"
            width={0}
            height={0}
            sizes="100%"
            className="w-20 lg:w-24 h-auto object-contain"
          />
          <Image
            src="/brands/WhatsApp.png"
            alt="WhatsApp logo"
            width={0}
            height={0}
            sizes="100%"
            className="w-20 lg:w-24 h-auto object-contain"
          />
          <Image
            src="/brands/Threads.png"
            alt="Threads logo"
            width={0}
            height={0}
            sizes="100%"
            className="w-20 lg:w-24 h-auto object-contain"
          />
        </div>
      </div>
      {/** End Clients */}
    </>
  )
}
