import { getPrice } from '@/services/server/payService'
import { PriceCards } from '../../../../components/Block/PriceCards'
import { getUserInfo } from '@/services/server/userService'
import Head from 'next/head'
import { getTranslations } from 'next-intl/server'
import { PriceFAQ } from './components/PriceFAQ'
import { SubscribeCompare } from '@/components/SubscribeCompare'
import { TopButton } from '../components/TopButton'

export default async function Pricing() {
  const plans = await getPrice()

  const userInfo = await getUserInfo()

  const t = await getTranslations('Metadata')
  const welcomeT = await getTranslations('FAQ.Welcome')

  return (
    <>
      <Head>
        <title>{t('title')}</title>
        <meta name="og:title" content={t('title')} />
        <meta name="description" content={t('description')} />
        <meta name="og:description" content={t('description')} />
        <meta name="keywords" content={t('keywords')} />
      </Head>
      <main id="content">
        <div className="overflow-hidden">
          <div className="relative">
            <div aria-hidden="true" className="flex -z-1 absolute -top-48 start-0">
              <div className="bg-primary opacity-30 blur-3xl w-[1036px] h-[600px] dark:bg-primary dark:opacity-20"></div>
              <div className="bg-gray-200 opacity-90 blur-3xl w-[577px] h-[300px] transform translate-y-32 dark:bg-neutral-800/60"></div>
            </div>

            <div className="max-w-[85rem] px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
              <PriceCards cards={plans || []} user={userInfo} />
              <SubscribeCompare />
            </div>

            <div className="absolute top-1/2 start-1/2 -z-1 transform -translate-y-1/2 -translate-x-1/2 w-[340px] h-[340px] border border-dashed border-violet-200 rounded-full dark:border-primary/60"></div>
            <div className="absolute top-1/2 start-1/2 -z-1 transform -translate-y-1/2 -translate-x-1/2 w-[575px] h-[575px] border border-dashed border-violet-200 rounded-full opacity-80 dark:border-primary/60"></div>
            <div className="absolute top-1/2 start-1/2 -z-1 transform -translate-y-1/2 -translate-x-1/2 w-[840px] h-[840px] border border-dashed border-violet-200 rounded-full opacity-60 dark:border-primary/60"></div>
            <div className="absolute top-1/2 start-1/2 -z-1 transform -translate-y-1/2 -translate-x-1/2 w-[1080px] h-[1080px] border border-dashed border-violet-200 rounded-full opacity-40 dark:border-primary/60"></div>
          </div>
        </div>
        <PriceFAQ />
        <div className="w-full my-10 flex justify-center">
          <TopButton>{welcomeT('upgradeNow')}</TopButton>
        </div>
      </main>
    </>
  )
}
