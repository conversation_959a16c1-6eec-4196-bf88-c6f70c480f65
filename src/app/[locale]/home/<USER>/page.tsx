import AffiliateHero from './components/affiliate-hero'
import BenefitsGrid from './components/benefits-grid'
import EarningsSection from './components/earnings-section'
import FeatureSection from './components/feature-section'
import HowItWorks from './components/how-it-works'
import { getTranslations } from 'next-intl/server'

export default async function AffiliationPage() {
  const t = await getTranslations('Affiliation.staticPage')

  const heroMessages = {
    heroTitle1: t('heroTitle1'),
    heroTitle2: t('heroTitle2'),
    heroMarketPlan: t('heroMarketPlan'),
    heroGetBenefit: t('heroGetBenefit'),
    heroStartEarning: t('heroStartEarning')
  }

  const featuresMessages = {
    features: [
      {
        icon: '/affiliation/1/join.png',
        title: t('featuresJoinTitle'),
        description: t('featuresJoinDescription'),
        details: t('featuresJoinDetails')
      },
      {
        icon: '/affiliation/1/promote.png',
        title: t('featuresPromoteTitle'),
        description: t('featuresPromoteDescription'),
        details: t('featuresPromoteDetails')
      },
      {
        icon: '/affiliation/1/earn.png',
        title: t('featuresEarnTitle'),
        description: t('featuresEarnDescription'),
        details: t('featuresEarnDetails')
      }
    ]
  }

  const howItWorksMessages = {
    howItWorkTitle: t('howItWorkTitle'),
    steps: [
      {
        icon: '/affiliation/2/project_affiliate_banner1.png',
        content: t('step1')
      },
      {
        icon: '/affiliation/2/project_affiliate_banner2.png',
        content: t('step2')
      },
      {
        icon: '/affiliation/2/project_affiliate_banner3.png',
        content: t('step3')
      }
    ]
  }

  const earningsSectionMessages = {
    earningsSectionTitle: t('earningsSectionTitle'),
    card1: t('card1'),
    card2: t('card2'),
    card3: t('card3'),
    earningsData: {
      recommendations: [
        { text: t('recommendationsText1'), value: '' },
        { text: t('recommendationsText2'), value: '' }
      ],
      others: [
        { text: t('othersText1'), value: '', icon: '/affiliation/3/meta.svg' },
        { text: t('othersText2'), value: '', icon: '/affiliation/3/meta.svg' },
        { text: t('othersText3'), value: '' }
      ],
      earnings: [
        { text: t('earningsText1'), value: '' },
        { text: t('earningsText2'), value: '' }
      ]
    }
  }

  const benefitMessages = {
    benefitsTitle: t('benefitsTitle'),
    benefitsData: [
      {
        icon: '/affiliation/3/24.svg',
        title: t('benefit1Title'),
        description: t('benefit1Description')
      },
      {
        icon: '/affiliation/3/adspytool.svg',
        title: t('benefit2Title'),
        description: t('benefit2Description')
      },
      {
        icon: '/affiliation/3/dropshipping.svg',
        title: t('benefit3Title'),
        description: t('benefit3Description')
      },
      {
        icon: '/affiliation/3/experience.svg',
        title: t('benefit4Title'),
        description: t('benefit4Description')
      },
      {
        icon: '/affiliation/3/interface.svg',
        title: t('benefit5Title'),
        description: t('benefit5Description')
      },
      {
        icon: '/affiliation/3/monthlyupdates.svg',
        title: t('benefit6Title'),
        description: t('benefit6Description')
      },
      {
        icon: '/affiliation/3/popularadspytool.svg',
        title: t('benefit7Title'),
        description: t('benefit7Description')
      },
      {
        icon: '/affiliation/3/tiktokadspy.svg',
        title: t('benefit8Title'),
        description: t('benefit8Description')
      },
      {
        icon: '/affiliation/3/winningproducts.svg',
        title: t('benefit9Title'),
        description: t('benefit9Description')
      }
    ]
  }

  return (
    <main className="min-h-screen bg-white">
      <AffiliateHero message={heroMessages} />
      <FeatureSection message={featuresMessages} />
      <HowItWorks message={howItWorksMessages} />
      <EarningsSection message={earningsSectionMessages} />
      <BenefitsGrid message={benefitMessages} />
    </main>
  )
}
