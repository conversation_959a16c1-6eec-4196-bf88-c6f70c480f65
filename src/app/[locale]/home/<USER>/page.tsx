'use client'
import { session } from '@/services/client/payService'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Loading } from '@/components/Loading'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/routing'

export default function PaySuccess() {
  const t = useTranslations('PaySuccess')
  const [url, setUrl] = useState()
  const [loading, setLoading] = useState(false)
  const searchParams = useSearchParams()

  useEffect(() => {
    const checkSession = async () => {
      setLoading(true)
      const sessionId = searchParams.get('session_id')
      if (!sessionId) return
      const response = await session(sessionId)
      const url = response.data?.order?.invoice_pdf
      if (response?.code === 200 && url) {
        setUrl(url)
      }
      setLoading(false)
    }

    checkSession()
  }, [searchParams])

  const handleDownload = () => {
    if (url && !loading) {
      const a = document.createElement('a')
      document.body.appendChild(a)
      a.style.display = 'none'
      // 使用获取到的blob对象创建的url
      a.href = `${url}?response-content-type=application%2Foctet-stream`
      a.rel = 'noopener noreferrer'
      a.click()
      document.body.removeChild(a)
      // 移除blob对象的url
      window.URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {/* 成功图标 */}
      <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mb-6">
        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>

      {/* 标题文本 */}
      <h1 className="text-2xl font-bold mb-8">{t('paymentSuccess')}</h1>

      {/* 开始按钮 */}
      <Link
        href="/"
        className="w-full max-w-xs bg-primary text-white py-3 rounded-lg mb-4 text-center"
      >
        {t('getStarted')}
      </Link>

      {/* 下载链接 */}
      <button
        onClick={handleDownload}
        className={`text-gray-700 underline flex items-center gap-2 ${
          loading ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-900'
        }`}
        disabled={loading}
      >
        {loading ? <Loading /> : null}
        {t('downloadVoucher')}
      </button>
    </div>
  )
}
