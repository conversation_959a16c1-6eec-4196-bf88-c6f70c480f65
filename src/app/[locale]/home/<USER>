import type { Metadata } from 'next'
import '@/app/[locale]/globals.css'
import { Header } from '@/components/Block/Header'
import { Footer } from '@/components/Block/Footer'
import { getTranslations } from 'next-intl/server'
import AffiliateTracker from '@/components/AffiliateTracker'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Metadata')

  return {
    title: t('title'),
    description: t('description')
  }
}

export default async function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  // 这里简单模拟一个 userInfo，实际项目中应该从 API 获取
  const userInfo = null;

  return (
    <div className="min-h-screen flex flex-col">
      <AffiliateTracker />
      <Header userInfo={userInfo} />
      <main>{children}</main>
      <Footer />
    </div>
  )
}
