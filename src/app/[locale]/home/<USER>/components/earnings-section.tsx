'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { EarningsSectionProps } from '../types'

export default function EarningsSection({ message }: EarningsSectionProps) {
  const { earningsSectionTitle, card1, card2, card3, earningsData } = message

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-12 text-center text-4xl font-bold"
        >
          {earningsSectionTitle}
        </motion.h2>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <Card className="h-full flex gap-4 px-4 py-8 justify-between">
            <CardContent className="space-y-4 w-full">
              <span className="text-lg font-medium">{card1}</span>
              {earningsData.recommendations.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-gray-700">{item.text}</span>
                  {/*<motion.span*/}
                  {/*  initial={{ scale: 0.8 }}*/}
                  {/*  whileInView={{ scale: 1 }}*/}
                  {/*  transition={{ type: "spring", stiffness: 300 }}*/}
                  {/*  viewport={{ once: true }}*/}
                  {/*  className="flex h-6 w-6 items-center justify-center rounded-full bg-orange-100 text-orange-500"*/}
                  {/*/>*/}
                </div>
              ))}
            </CardContent>
            <CardContent className="space-y-4 w-full">
              <span className="text-lg font-medium">{card2}</span>
              {earningsData.others.map((item, index) => (
                <div key={index} className="flex items-center justify-start">
                  {item.icon && <motion.img src={item.icon} className="mr-1" />}
                  <span className="text-gray-700">{item.text}</span>
                </div>
              ))}
            </CardContent>
            <CardContent className="flex flex-col space-y-4 w-full">
              <span className="text-lg font-medium">{card3}</span>
              {earningsData.earnings.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-gray-700">{item.text}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
