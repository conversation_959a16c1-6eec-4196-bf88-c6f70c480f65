'use client'

import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { BenefitsProps } from '../types'

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.4 } }
}

export default function BenefitsGrid({ message }: BenefitsProps) {
  const { benefitsTitle, benefitsData } = message

  return (
    <section className="py-16 bg-linear-to-b from-white to-orange-50">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-12 text-center text-3xl font-bold"
        >
          {benefitsTitle}
        </motion.h2>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: '-100px' }}
          className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          {benefitsData.map((benefit, index) => (
            <motion.div key={index} variants={item}>
              <Card className="h-full border-none shadow-xs hover:shadow-md transition-shadow duration-300">
                <CardHeader className="flex flex-row items-center gap-4 pb-2">
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <img
                      src={benefit.icon || '/placeholder.svg'}
                      alt={benefit.title}
                      className="h-10 w-10"
                    />
                  </motion.div>
                  <CardTitle className="text-base font-medium">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">{benefit.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
