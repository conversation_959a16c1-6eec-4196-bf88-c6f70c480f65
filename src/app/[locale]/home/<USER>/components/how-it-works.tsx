'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { HowItWorksProps } from '../types'

export default function HowItWorks({ message }: HowItWorksProps) {
  const { howItWorkTitle, steps } = message

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center text-4xl font-extrabold mb-16 text-gray-900"
        >
          {howItWorkTitle}
        </motion.h2>

        <div className="mx-auto max-w-5xl space-y-5">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true, margin: '-100px' }}
            >
              <Card className="overflow-hidden shadow-md border-0 rounded-lg">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    <div className="flex-1 p-6 md:p-8 flex flex-col justify-center relative">
                      <div className="text-4xl font-serif text-gray-300 absolute top-4 left-4">
                        &quot;
                      </div>
                      <p className="text-gray-800 text-lg leading-relaxed my-4 z-10">
                        {step.content}
                      </p>
                      <div className="text-4xl font-serif text-gray-300 absolute bottom-4 right-4">
                        &quot;
                      </div>
                    </div>

                    <motion.img
                      whileHover={{ scale: 1.03 }}
                      initial={{ scale: 1.02 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                      src={step.icon}
                      alt={`How it works - Step ${index + 1}`}
                      className="h-48 w-80"
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
