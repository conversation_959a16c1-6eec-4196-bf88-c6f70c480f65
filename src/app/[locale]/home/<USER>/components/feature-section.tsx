'use client'

import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SectionProps } from '../types'

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
}

export default function FeatureSection({ message }: SectionProps) {
  const { features } = message

  return (
    <section className="py-16">
      <motion.div
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, margin: '-100px' }}
        className="container mx-auto grid grid-cols-1 gap-8 px-4 md:grid-cols-3"
      >
        {features.map((feature, index) => (
          <motion.div key={index} variants={item}>
            <Card className="border-none text-center hover:shadow-lg transition-shadow duration-200 bg-orange-50">
              <CardHeader className="pb-2">
                <div className="mx-auto mb-4">
                  <motion.img
                    src={feature.icon}
                    alt={feature.title}
                    className="h-16 w-16"
                    whileHover={{ rotate: 5, scale: 1.1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  />
                </div>
                <CardTitle className="text-xl font-bold text-gray-800">{feature.title}</CardTitle>
                <CardDescription className="text-base text-gray-600">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500">{feature.details}</p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </section>
  )
}
