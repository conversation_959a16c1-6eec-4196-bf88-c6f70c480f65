'use client'

import CountUp from '@/components/bits/CountUp/CountUp'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { Link } from '@/i18n/routing'
import { HeroProps } from '../types'

export default function AffiliateHero({ message }: HeroProps) {
  const { heroTitle1, heroTitle2, heroMarketPlan, heroGetBenefit, heroStartEarning } = message

  return (
    <section className="relative w-full overflow-hidden py-20 text-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="container mx-auto mt-8 px-4 flex flex-col items-center justify-center gap-4"
      >
        <h1 className="mb-2 text-4xl font-bold md:text-8xl">
          {heroTitle1}
          <span className="bg-linear-to-r from-pink-500 to-yellow-500 bg-clip-text text-transparent">
            <CountUp
              from={0}
              to={30}
              separator=","
              direction="up"
              duration={0.6}
              className="count-up-text"
            />
            %{' '}
          </span>
          {heroTitle2}
        </h1>
        <h2 className="mb-8 text-3xl font-bold md:text-8xl">{heroMarketPlan}</h2>
        <p className="mx-auto mb-10 max-w-md text-gray-600">{heroGetBenefit}</p>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Link href="/affiliation/register">
            <Button className="bg-orange-500 px-10 py-6 rounded-full text-lg font-medium text-white hover:bg-orange-600">
              {heroStartEarning}
            </Button>
          </Link>
        </motion.div>
      </motion.div>
    </section>
  )
}
