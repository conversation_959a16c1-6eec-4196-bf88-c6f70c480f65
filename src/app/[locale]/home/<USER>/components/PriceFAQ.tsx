import { FAQ, FAQItem } from '@/components/Block/FAQ'
import { getTranslations } from 'next-intl/server'

export async function PriceFAQ() {
  const t = await getTranslations('FAQ.Price')

  const faqItems: FAQItem[] = [
    {
      question: t('q1'),
      answer: t('a1'),
      id: '1'
    },
    {
      question: t('q2'),
      answer: t('a2'),
      id: '2'
    },
    {
      question: t('q3'),
      answer: t('a3'),
      id: '3'
    },
    {
      question: t('q4'),
      answer: t.rich('a4', {
        emailLink: (chunks) => {
          return (
            <a href="mailto:<EMAIL>" className="text-orange-600 hover:underline">
              {chunks}
            </a>
          )
        }
      }),
      id: '4'
    }
  ]

  return (
    <div>
      <FAQ items={faqItems} title={t('title')} description={t('description')} />
    </div>
  )
}
