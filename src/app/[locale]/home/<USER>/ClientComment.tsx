'use client'
import { useTranslations } from 'next-intl'
import { motion } from 'framer-motion'
import { Start5 } from '@/components/svg/Start5'

export default function ClientComment() {
  const t = useTranslations('Home')

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3 // 子元素之间的延迟时间
      }
    }
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <motion.div
      variants={container}
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, amount: 1 }}
      className="grid grid-cols-1 gap-8 md:gap-0 md:grid-cols-4 mb-4 text-base text-gray-500 justify-center w-4/5 xl:w-[1200px] mx-auto"
    >
      {[
        { Star: Start5, comment: 'comment1' },
        { Star: Start5, comment: 'comment2' },
        { Star: Start5, comment: 'comment3' },
        { Star: Start5, comment: 'comment4' }
      ].map((comment, index) => (
        <motion.div key={index} variants={item} className="flex flex-col items-center">
          {comment.Star && <comment.Star />}
          <p className="mt-2">&quot;{t(comment.comment)}&quot;</p>
        </motion.div>
      ))}
    </motion.div>
  )
}
