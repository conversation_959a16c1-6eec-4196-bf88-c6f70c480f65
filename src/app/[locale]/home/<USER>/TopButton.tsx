'use client'
import { motion } from 'framer-motion'

interface TopButtonProps {
  children: React.ReactNode
  scrollTarget?: HTMLElement | null
}

export function TopButton({ children, scrollTarget }: TopButtonProps) {
  const scrollToTop = () => {
    const targetElement = scrollTarget || window
    targetElement?.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <div className="flex gap-3 cursor-pointer">
      <motion.div
        onClick={scrollToTop}
        className="justify-center min-w-[140px] h-11 w-fit p-1 inline-flex pr-2 items-center text-sm font-medium rounded-full border border-transparent bg-primary text-white hover:bg-primary/90 focus:outline-hidden focus:primary/90 disabled:opacity-50 disabled:pointer-events-none group cursor-pointer" // 添加 cursor-pointer
        whileHover="hover"
        initial="initial"
        animate="initial"
        variants={{
          initial: {
            scale: 1
          },
          hover: {
            scale: 1.05,
            transition: {
              type: 'spring',
              stiffness: 400,
              damping: 10
            }
          }
        }}
      >
        {children}
      </motion.div>
    </div>
  )
}
