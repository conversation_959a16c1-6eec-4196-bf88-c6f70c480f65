import { getTranslations } from 'next-intl/server'
import { FAQ } from '../../../../components/Block/FAQ'
import Link from 'next/link'

export async function HomeFAQ() {
  const t = await getTranslations('FAQ.Home')

  const items = [
    {
      question: t('q1'),
      answer: t('a1'),
      id: '1'
    },
    {
      question: t('q2'),
      answer: t.rich('a2', {
        pricingLink: (chunks) => {
          return (
            <Link
              href="https://lufe.ai/pricing"
              target="_blank"
              className="text-orange-600 hover:underline"
            >
              {chunks}
            </Link>
          )
        }
      }),
      id: '2'
    },
    {
      question: t('q3'),
      answer: t('a3'),
      id: '3'
    },
    {
      question: t('q4'),
      answer: t('a4'),
      id: '4'
    }
  ]

  return (
    <div className="w-4/5 xl:w-[1200px] mx-auto">
      <FAQ title={t('title')} items={items} />
    </div>
  )
}
