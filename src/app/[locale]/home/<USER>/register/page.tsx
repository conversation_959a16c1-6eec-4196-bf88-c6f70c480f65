'use client'
import { useCallback, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { AlertCircle, Shuffle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { motion } from 'framer-motion'
import { ClipboardButton } from '@/components/ClipboardButton'
import { useForm, FieldError } from 'react-hook-form'
import { registerAffiliation, distributionCheckCode, RegisterAffiliationData } from './actions'
import { useRouter } from 'next/navigation'
import { ResponseCode } from '@/utils/constants'
import { useToast } from '@/hooks/use-toast'
import { useTranslations } from 'next-intl'
import PaypalIcon from '../components/paypal-icon'

// 定义动画变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 12
    }
  }
}

export default function AffiliationRegisterPage() {
  const [promotionUrl, setPromotionUrl] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const t = useTranslations('Affiliation')
  const { toast } = useToast()

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<RegisterAffiliationData>()

  const router = useRouter()

  // 生成随机推广链接
  const generateRandomUrl = useCallback(() => {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    const newUrl = result.toUpperCase()
    setPromotionUrl(newUrl)
    setValue('promotionUrl', newUrl)
  }, [setValue])

  useEffect(() => {
    if (!promotionUrl) {
      generateRandomUrl()
    }
  }, [generateRandomUrl, promotionUrl, setValue])

  const onSubmit = async (data: RegisterAffiliationData) => {
    setIsSubmitting(true)
    try {
      const response = await registerAffiliation(data)
      if (response && response.code === ResponseCode.Success) {
        // 注册成功后跳转到 dashboard 页面
        router.push('/affiliation/dashboard')
      }
      if (response && response.code === 400018) {
        toast({
          variant: 'destructive',
          title: t('errors.title'),
          description: t('errors.imageSizeError')
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const checkDistribution = async () => {
    try {
      const res = await distributionCheckCode()
      if (res && res.code === 200) {
        // 先判断是否有分销码,有分销码跳转到分销看板
        if (res.data.distribution_code && res.data.distribution_code.length > 0) {
          router.push('/affiliation/dashboard')
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    checkDistribution()
  })

  // 错误消息渲染函数
  const renderErrorMessage = (error?: FieldError) => {
    return error ? <p className="text-red-500 text-sm mt-1">{error.message}</p> : null
  }

  return (
    <motion.div
      className="container mx-auto px-4 py-12 max-w-3xl"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <motion.h1
        className="text-3xl font-bold text-center mb-12 bg-linear-to-r from-orange-500 to-red-500 bg-clip-text text-transparent"
        variants={itemVariants}
      >
        {t('fillInformation')}
      </motion.h1>

      <motion.div className="space-y-12" variants={containerVariants}>
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Promotion Channels */}
          <motion.div className="space-y-3 mb-8" variants={itemVariants}>
            <h2 className="text-lg font-medium">{t('promotionChannels.title')}</h2>
            <Textarea
              placeholder={t('promotionChannels.placeholder')}
              className="min-h-[120px] transition-all focus:border-orange-400 focus:ring-orange-400"
              {...register('promotionChannels', {
                required: t('promotionChannels.error')
              })}
            />
            {renderErrorMessage(errors.promotionChannels)}
          </motion.div>

          {/* Promotion URL */}
          <motion.div className="space-y-3 mb-8" variants={itemVariants}>
            <h2 className="text-lg font-medium">{t('promotionUrl.title')}</h2>
            <div className="flex space-x-2">
              <div className="flex items-center relative w-full">
                <span className="text-gray-500 absolute left-0 pl-4">{`${baseUrl}?ref=`}</span>
                <Input
                  placeholder={t('promotionUrl.placeholder')}
                  className="flex-1 transition-all focus:border-orange-400 focus:ring-orange-400 pt-0.5"
                  style={{ paddingLeft: `${`${baseUrl}?ref=`.length}ch` }}
                  {...register('promotionUrl', {
                    required: t('promotionUrl.error')
                  })}
                  value={promotionUrl}
                  onChange={(e) => setPromotionUrl(e.target.value)}
                />
              </div>
              <Button
                type="button"
                className="bg-orange-500 hover:bg-orange-600 transition-all duration-300 flex items-center gap-2"
                onClick={generateRandomUrl}
              >
                <Shuffle className="h-4 w-4" />
                {t('promotionUrl.generateRandom')}
              </Button>
            </div>
            {renderErrorMessage(errors.promotionUrl)}
            {promotionUrl && (
              <motion.div
                className="text-sm text-gray-600 mt-3 p-3 bg-orange-50 rounded-lg border border-orange-100 flex items-center justify-between"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <span className="mr-2">
                  {t('promotionUrl.fullLinkText')}{' '}
                  <span className="font-medium">
                    {baseUrl}
                    {'?ref='}
                    {promotionUrl}
                  </span>
                </span>
                <ClipboardButton
                  text={`${baseUrl}${promotionUrl}`}
                  tooltipCopy={t('promotionUrl.copyLinkText')}
                  tooltipCopied={t('promotionUrl.copiedLinkText')}
                />
              </motion.div>
            )}
          </motion.div>

          {/* PayPal Account */}
          <motion.div variants={itemVariants} className="mb-8">
            <Card className="border-gray-200 shadow-xs hover:shadow-md transition-shadow duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <PaypalIcon />
                  <span className="font-medium">{t('paypal.title')}</span>
                </div>
                <Input
                  placeholder={t('paypal.placeholder')}
                  className="w-full transition-all focus:border-orange-400 focus:ring-orange-400"
                  {...register('paypalAccount', {
                    required: t('paypal.error')
                  })}
                />
                {renderErrorMessage(errors.paypalAccount)}
              </CardContent>
            </Card>
          </motion.div>

          {/* Warning */}
          <motion.div variants={itemVariants} className="mb-8">
            <Alert
              variant="destructive"
              className="bg-white border-red-200 text-red-500 shadow-xs flex items-center"
            >
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="ml-2">{t('warning.title')}</AlertDescription>
              </div>
            </Alert>
          </motion.div>

          {/* Submit Button */}
          <motion.div className="flex justify-center mt-12" variants={itemVariants}>
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
              <Button
                disabled={isSubmitting}
                type="submit"
                className="bg-linear-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 px-10 py-6 rounded-3xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <span className="mr-2">{t('submitButton')}</span>
                <svg
                  width="15"
                  height="15"
                  viewBox="0 0 15 15"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                >
                  <path
                    d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z"
                    fill="currentColor"
                    fillRule="evenodd"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </Button>
            </motion.div>
          </motion.div>
        </form>
      </motion.div>
    </motion.div>
  )
}
