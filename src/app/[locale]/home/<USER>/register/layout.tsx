import { Metadata } from 'next'
import { getUserInfo } from '@/services/server/userService'
import { redirect } from 'next/navigation'
import React from 'react'

export const metadata: Metadata = {
  title: 'Register',
  description: 'Register',
  robots: {
    index: false,
    follow: false
  }
}

export default async function RegisterLayout({ children }: { children: React.ReactNode }) {
  try {
    const userInfo = await getUserInfo()

    if (!userInfo) {
      redirect('/login?from=' + encodeURIComponent('/affiliation/register'))
    }

    return <>{children}</>
  } catch {
    redirect('/login?from=' + encodeURIComponent('/affiliation/register'))
  }
}
