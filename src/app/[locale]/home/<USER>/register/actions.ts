import apiClient from '@/utils/apiClient'
import { ResponseCode } from '@/utils/constants'

export interface RegisterAffiliationData {
  promotionChannels: string
  promotionUrl: string
  paypalAccount: string
}

export interface CheckDistributionResponse {
  code: number
  message: string
  data: {
    distribution_code: string
    status: number
    is_logged_in: boolean
  }
}

type RegisterResponse = ApiResponse<null>

export async function registerAffiliation(data: RegisterAffiliationData) {
  try {
    const response = await apiClient
      .post(`distribution`, {
        json: {
          customize_code: data.promotionUrl,
          paypal: data.paypalAccount,
          application_reason: data.promotionChannels
        }
      })
      .json<RegisterResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.warn(error)
    return null
  }
}

export async function distributionCheckCode() {
  try {
    const response = await apiClient
      .get(`distribution/check-code`)
      .json<CheckDistributionResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}
