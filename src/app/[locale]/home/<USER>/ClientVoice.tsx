'use client'

import { useTranslations } from 'next-intl'
import { Quotes } from './Quotes'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Image from 'next/image'
interface TestimonialProps {
  text: string
  author: {
    name: string
    title: string
    avatar: string
  }
  style: {
    backgroundColor: string
    rotate?: string
    quotesColor: string
  }
}

export default function ClientVoice() {
  const containerRef = useRef(null)
  const isInView = useInView(containerRef, {
    once: true,
    amount: 1
  })
  const t = useTranslations('Testimonials')

  const testimonials: TestimonialProps[] = [
    {
      text: t('comment1'),
      author: {
        name: t('user1'),
        title: t('job1'),
        avatar: '/avatar1.png'
      },
      style: {
        backgroundColor: 'bg-red-50',
        rotate: '-rotate-[8deg]',
        quotesColor: '#fca5a5'
      }
    },
    {
      text: t('comment2'),
      author: {
        name: t('user2'),
        title: t('job2'),
        avatar: '/avatar2.png'
      },
      style: {
        backgroundColor: 'bg-yellow-50',
        quotesColor: '#fcd34d'
      }
    },
    {
      text: t('comment3'),
      author: {
        name: t('user3'),
        title: t('job3'),
        avatar: '/avatar3.png'
      },
      style: {
        backgroundColor: 'bg-blue-50',
        rotate: 'rotate-[8deg]',
        quotesColor: '#93c5fd'
      }
    }
    // ... 可以添加更多评价
  ]

  const getInitialX = (index: number) => {
    if (index === 0) return '100%'
    if (index === 2) return '-100%'
    return 0
  }

  return (
    <section className="py-12 px-6">
      <div className="container relative mx-auto px-4" ref={containerRef}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.author.name}
              initial={{
                rotate: 0,
                x: getInitialX(index)
              }}
              animate={
                isInView
                  ? {
                      rotate: testimonial.style.rotate
                        ? parseInt(
                            testimonial.style.rotate.replace('rotate-[', '').replace('deg]', '')
                          )
                        : 0,
                      x: 0
                    }
                  : {}
              }
              transition={{
                type: 'spring',
                stiffness: 100,
                damping: 12,
                delay: 0.1 * index
              }}
              className={`relative transform rounded-3xl py-6 px-8 max-w-[380px] w-full mx-auto ${testimonial.style.backgroundColor}`}
            >
              <Quotes color={testimonial.style.quotesColor} />
              <div className="mt-8 mb-6">
                <p
                  className="text-gray-800 text-lg leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: testimonial.text }}
                />
              </div>

              <div className="flex items-center gap-4">
                <Image
                  src={testimonial.author.avatar}
                  height={40}
                  width={40}
                  alt={testimonial.author.name}
                  className="w-10 h-10 rounded-full border-2 border-white"
                />
                <div>
                  <h4 className="font-medium text-gray-900">{testimonial.author.name}</h4>
                  <p className="text-sm text-gray-600">{testimonial.author.title}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
