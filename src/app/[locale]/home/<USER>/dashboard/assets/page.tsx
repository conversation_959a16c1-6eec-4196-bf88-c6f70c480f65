import { getTranslations } from 'next-intl/server'
import { TabsContent } from '@/components/ui/tabs'
import { Card, CardContent } from '@/components/ui/card'
import { AffiliationTable } from '@/app/[locale]/home/<USER>/dashboard/components/affiliation-table'
import { distributionCommissionList, distributionShow } from '../actions'
import {
  SearchParams,
  CommissionListModel
} from '@/app/[locale]/home/<USER>/dashboard/types'

export default async function AffiliationDashboardStatisticsPage({
  searchParams = Promise.resolve({ page: 1, per_page: 10 })
}: {
  searchParams?: Promise<SearchParams>
}) {
  const t = await getTranslations('Affiliation')

  // Parse pagination parameters from URL
  const { page = 1, per_page = 10 } = await searchParams

  const distributionCommissionListResponse = await distributionCommissionList({
    page,
    per_page
  })
  const distributionShowResponse = await distributionShow()

  if (!distributionCommissionListResponse) return null
  const {
    data,
    current_page,
    per_page: responsePerPage,
    total
  } = distributionCommissionListResponse.data

  const translateData = (data: CommissionListModel[]) => {
    const formatTimestamp = (value: number | undefined) =>
      value !== undefined
        ? new Date(Number(value) * 1000).toISOString().slice(0, 19).replace('T', ' ')
        : value

    return data.map((item) => ({
      ...item,
      money: `${item.type === 1 ? '-' : '+'} $${item.money}`,
      created_at: formatTimestamp(item.created_at),
      current_assets:
        item.current_assets !== undefined ? `$${item.current_assets}` : item.current_assets
    }))
  }

  const commissionData = translateData(data)

  const columns: { header: string; accessorKey: keyof (typeof commissionData)[number] }[] = [
    { header: t('assetsCreatedTime'), accessorKey: 'created_at' },
    { header: t('assetsUpdatedTime'), accessorKey: 'money' },
    { header: t('currentAssets'), accessorKey: 'current_assets' }
  ]

  if (!distributionShowResponse) return null
  const { data: affiliateData } = distributionShowResponse

  return (
    <div className="top-0">
      <TabsContent value="assets">
        <div className="flex items-center justify-between pb-6 px-6">
          <h3 className="text-2xl font-medium text-gray-900">{t('assetsTip1')}</h3>
          <div className="flex items-center gap-6">
            <div className="flex items-center">
              <h3 className="text-gray-500 text-2xl">{t('assetsTip2')}</h3>
              <h3 className="ml-2 text-2xl font-semibold text-gray-900">
                ${affiliateData.confirmed_commission}
              </h3>
            </div>
            <div className="flex items-center">
              <h3 className="text-gray-500 text-2xl">{t('assetsTip3')}</h3>
              <h3 className="ml-2 text-2xl font-semibold text-gray-900">${affiliateData.assets}</h3>
            </div>
          </div>
        </div>
        <Card>
          <CardContent className="p-8">
            <div>
              <h3 className="text-center text-2xl font-medium text-gray-900">
                {t('assetsTableTitle')}
              </h3>

              <div className="mt-4">
                <AffiliationTable
                  columns={columns}
                  data={commissionData}
                  pagination={{
                    current: current_page,
                    total: total,
                    pageSize: responsePerPage
                  }}
                  messages={{
                    emptyText: t('table.empty'),
                    totalText: t('table.total', { count: total }),
                    previousText: t('table.previous'),
                    nextText: t('table.next')
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  )
}
