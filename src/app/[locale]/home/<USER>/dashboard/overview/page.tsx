import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { getTranslations } from 'next-intl/server'
import { AlertCircle } from 'lucide-react'
import { NoticeItem } from '../components/notice-item'
import { AffiliationDashboardTable } from '../components/affiliation-dashboard-table'
import { StatsCard } from '../components/stats-card'
import { TabsContent } from '@/components/ui/tabs'
import CopyButton from '../components/copy-button'

import { distributionCodeList, distributionShow } from '../actions'

export default async function AffiliationDashboardOverviewPage() {
  const t = await getTranslations('Affiliation')

  const distributionCodeListResponse = await distributionCodeList()
  const distributionShowResponse = await distributionShow()

  if (!distributionShowResponse || !distributionCodeListResponse) return null
  const {
    order_effect_num = '0', // 推荐
    // order_invalid_num,
    confirmed_commission = '0', // 已确认佣金
    not_confirmed_commission = '0', // 未确认佣金
    assets = '0', // 资产
    cancel_commission = '0', // 已取消佣金?
    // settlement_commission,
    pay_customer = '0', // 客户
    click_num = '0', // 截止目前点击
    register_num = '0', // 截止目前注册
    income_num = '0'
    // commission_rate
  } = distributionShowResponse.data

  // 用于显示多个推广链接（目前暂时没有这个逻辑）
  const { list: lists } = distributionCodeListResponse.data

  const filteredLists = lists.map((item) => {
    return {
      ...item,
      rank_data: item.rank_data.filter((v) => v.check === '1')
    }
  })

  return (
    <div className="top-0">
      <TabsContent value="overview">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <StatsCard title={t('recommendations')} value={order_effect_num} />
          <StatsCard title={t('clients')} value={pay_customer} />
          <StatsCard title={t('totalClicks')} subtext={t('lastThirtyDays')} value={click_num} />
          <StatsCard title={t('registrations')} value={register_num} />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <StatsCard isMoney title={t('assets')} value={assets} />
          <StatsCard isMoney title={t('paidCommission')} value={confirmed_commission} />
          <StatsCard
            isMoney
            title={t('unpaidCommission')}
            subtext={t('thirtyDaysDelay')}
            value={not_confirmed_commission}
          />
          <StatsCard isMoney title={t('receivedPayment')} value={income_num} />
          <StatsCard isMoney title={t('pendingPayment')} value={cancel_commission} />
        </div>

        <Alert className="mt-8 bg-amber-50 border-amber-200">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">{t('googleAffiliationNotice')}</AlertDescription>
          </div>
        </Alert>
        {filteredLists.map((list, index) => {
          return (
            <div className="mt-8" key={index}>
              <h2 className="text-xl font-bold mb-4">
                {t('promotion')} {index + 1}
              </h2>

              <Card>
                <CardContent className="p-0">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-6">
                    <div className="flex flex-col justify-center items-center col-span-3">
                      <p className="text-lg text-gray-500 mb-2">{t('promotionLink')}</p>
                      <div className="w-full text-center">
                        <p className="text-lg mb-2 break-all">
                          {`${process.env.NEXT_PUBLIC_BASE_URL || `https://lufe.ai`}/?ref=${list.distribution_code}`}
                        </p>
                        <CopyButton
                          text={`${process.env.NEXT_PUBLIC_BASE_URL || `https://lufe.ai`}/?ref=${list.distribution_code}`}
                          successText={t('linkCopySuccess')}
                          errorText={t('linkCopyError')}
                        >
                          {t('copy')}
                        </CopyButton>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <AffiliationDashboardTable data={list.rank_data} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Alert className="mt-4 bg-gray-50">
                <AlertDescription className="ml-2 text-gray-600">
                  {t('commissionCalculation')}
                </AlertDescription>
              </Alert>
            </div>
          )
        })}

        <div className="mt-8">
          <h2 className="text-xl font-bold mb-4">{t('promotionNotice')}</h2>

          <div className="space-y-8">
            <NoticeItem number={1} text={t('promotionNotice1')}>
              <p className="text-xs text-gray-500 mt-2">{t('promotionNotice1Text1')}</p>
            </NoticeItem>
            <NoticeItem number={2} text={t('promotionNotice2')} />
            <NoticeItem number={3} text={t('promotionNotice3')} />
            <NoticeItem number={4} text={t('commissionExample')}>
              <div className="ml-8 mt-2 p-4 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600 mb-2">{t('exampleTitle')}</p>
                <p className="text-sm text-gray-600 mb-1">{t('example1')}</p>
                <p className="text-sm text-gray-600 mb-2">{t('example2')}</p>
                <p className="text-xs text-gray-500 italic mt-2">{t('exampleFooter')}</p>
              </div>
            </NoticeItem>
            <NoticeItem number={5} text={t('promotionNotice5')} />
            <NoticeItem number={6} text={t('promotionNotice6')} />
            <NoticeItem number={7} text={t('promotionNotice7')} />
            <NoticeItem number={8} text={t('promotionNotice8')} />
            <NoticeItem number={9} text={t('promotionNotice9')} />
          </div>
        </div>
      </TabsContent>
    </div>
  )
}
