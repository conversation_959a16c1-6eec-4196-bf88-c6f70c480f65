import { getTranslations } from 'next-intl/server'
import { TabsContent } from '@/components/ui/tabs'
import { Card, CardContent } from '@/components/ui/card'
import { AffiliationTable } from '../components/affiliation-table'
import { distributionSettleList } from '../actions'
import PaypalInput from '../components/paypay-input'
import PaypalIcon from '../../components/paypal-icon'
import { PayStatus, SearchParams, SettleListModel } from '../types'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

// 结算
export default async function AffiliationDashboardStatisticsPage({
  searchParams = Promise.resolve({ page: 1, per_page: 10 })
}: {
  searchParams?: Promise<SearchParams>
}) {
  const t = await getTranslations('Affiliation')

  // Parse pagination parameters from URL
  const { page = 1, per_page = 10 } = await searchParams

  // Pass pagination parameters to the server action
  const distributionSettleListResponse = await distributionSettleList({
    page,
    per_page
  })

  if (!distributionSettleListResponse) return

  const translateData = (data: SettleListModel[]) => {
    const formatTimestamp = (value: number | undefined) =>
      value !== undefined
        ? new Date(Number(value) * 1000).toISOString().slice(0, 19).replace('T', ' ')
        : value

    return data.map((item) => ({
      ...item,
      status:
        item.status === PayStatus.Paid
          ? t('table.paid')
          : item.status === PayStatus.Refund
            ? t('table.refund')
            : t('table.unknown'),
      created_at: formatTimestamp(item.created_at),
      pay_time: formatTimestamp(item.pay_time)
    }))
  }

  const {
    data,
    current_page,
    per_page: responsePerPage,
    total
  } = distributionSettleListResponse.data

  const staticsData = translateData(data)

  const columns: { header: string; accessorKey: keyof (typeof staticsData)[number] }[] = [
    { header: t('status'), accessorKey: 'status' },
    { header: t('payAccount'), accessorKey: 'pay_account' },
    { header: t('createdAt'), accessorKey: 'created_at' },
    { header: t('payTime'), accessorKey: 'pay_time' }
  ]

  return (
    <div className="top-0">
      <TabsContent value="statistics">
        <Alert className="mt-2 bg-amber-50 border-amber-200">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">{t('statisticsContent')}</AlertDescription>
          </div>
        </Alert>

        <Card className="mt-7">
          <CardContent className="p-6">
            <div className=" flex justify-center items-center flex-col">
              <div className="flex justify-center items-center">
                <PaypalIcon />
                <span className="text-lg font-bold text-gray-900 leading-6">{t('paypalText')}</span>
              </div>

              <PaypalInput />
            </div>
          </CardContent>
        </Card>

        <Card className="mt-7">
          <CardContent className="p-6">
            <AffiliationTable
              columns={columns}
              data={staticsData}
              pagination={{
                current: current_page,
                total: total,
                pageSize: responsePerPage
              }}
              messages={{
                emptyText: t('table.empty'),
                totalText: t('table.total', { count: total }),
                previousText: t('table.previous'),
                nextText: t('table.next')
              }}
            />
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  )
}
