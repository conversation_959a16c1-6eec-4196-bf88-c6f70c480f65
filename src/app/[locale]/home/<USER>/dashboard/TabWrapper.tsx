'use client'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DashBoardSkeleton } from './components/dashboard-skeleton'
import { Link } from '@/i18n/routing'
import { usePathname } from 'next/navigation'
import React, { Suspense, useState, useTransition, useEffect, useCallback } from 'react'
interface TabWrapperProps {
  children: React.ReactNode
  overviewLabel: string
  detailsLabel: string
  statisticsLabel: string
  assetsLabel: string
}
export default function TabWrapper({
  children,
  overviewLabel,
  detailsLabel,
  statisticsLabel,
  assetsLabel
}: TabWrapperProps) {
  const pathname = usePathname()
  const [isPending, startTransition] = useTransition()

  // 获取当前 tab 值
  const getTabValue = useCallback(() => {
    if (pathname.includes('overview')) {
      return 'overview'
    } else if (pathname.includes('details')) {
      return 'details'
    } else if (pathname.includes('statistics')) {
      return 'statistics'
    } else if (pathname.includes('assets')) {
      return 'assets'
    }
    return 'overview' // 默认值
  }, [pathname])

  const [activeTab, setActiveTab] = useState<string>(getTabValue())

  useEffect(() => {
    startTransition(() => {
      setActiveTab(getTabValue())
    })
  }, [getTabValue, pathname])
  return (
    <Tabs value={activeTab} defaultValue={getTabValue()}>
      <TabsList className="border-b justify-start mb-5">
        <Link href={'/affiliation/dashboard/overview'}>
          <TabsTrigger value="overview" className="px-4">
            {overviewLabel}
          </TabsTrigger>
        </Link>
        <Link href={'/affiliation/dashboard/details'}>
          <TabsTrigger value="details" className="px-4">
            {detailsLabel}
          </TabsTrigger>
        </Link>
        <Link href={'/affiliation/dashboard/statistics'}>
          <TabsTrigger value="statistics" className="px-4">
            {statisticsLabel}
          </TabsTrigger>
        </Link>
        <Link href={'/affiliation/dashboard/assets'}>
          <TabsTrigger value="assets" className="px-4">
            {assetsLabel}
          </TabsTrigger>
        </Link>
      </TabsList>
      <div className="relative">
        {isPending ? (
          <div className="animate-fadeIn">
            <DashBoardSkeleton />
          </div>
        ) : (
          <Suspense fallback={<DashBoardSkeleton variant={getTabValue()} />}>{children}</Suspense>
        )}
      </div>
    </Tabs>
  )
}
