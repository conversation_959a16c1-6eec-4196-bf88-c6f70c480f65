import apiServer from '@/utils/apiServer'
import { ResponseCode } from '@/utils/constants'
import {
  DistributionShowResponse,
  DistributionRecordListResponse,
  DistributionCodeListResponse,
  DistributionCommissionListResponse,
  SearchParams,
  SettleListResponse
} from './types'

export async function distributionCodeList() {
  try {
    const response = await apiServer
      .get(`distribution/get-code-list`)
      .json<DistributionCodeListResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}

export async function distributionSettleList(params: SearchParams) {
  try {
    const response = await apiServer
      .get(`distribution/settle-list`, {
        searchParams: {
          ...params
        }
      })
      .json<SettleListResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}

export async function distributionShow() {
  try {
    const response = await apiServer.get(`distribution/show`).json<DistributionShowResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}

// 查看分销详细记录列表
export async function distributionRecordList(params: SearchParams) {
  'use server'
  try {
    const response = await apiServer
      .get(`distribution/record-list`, {
        searchParams: {
          ...params
        }
      })
      .json<DistributionRecordListResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}

// 查看分销佣金记录列表
export async function distributionCommissionList(params: SearchParams) {
  try {
    const response = await apiServer
      .get(`distribution/commission`, {
        searchParams: {
          ...params
        }
      })
      .json<DistributionCommissionListResponse>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.error(error)
    return null
  }
}

interface PaypalUpdatePayload {
  paypalValue: string
}

// 更新paypal账号
export async function distributionUpdatePaypal(data: PaypalUpdatePayload) {
  try {
    const response = await apiServer
      .post(`distribution/update-info`, {
        json: {
          paypal: data.paypalValue
        }
      })
      .json<{ code: number }>()
    if (response.code !== ResponseCode.Success) return null
    return response
  } catch (error) {
    console.warn(error)
    return null
  }
}
