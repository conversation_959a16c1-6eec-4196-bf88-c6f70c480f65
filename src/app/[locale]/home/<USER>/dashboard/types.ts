export enum RecordStatus {
  Paid = '1',
  Void = '2'
}

export enum PayStatus {
  Paid = 1,
  Refund = 2
}

export enum RankName {
  Basic = 'Basic',
  FREE = 'Free',
  Starter = 'Starter',
  Standard = 'Standard',
  Premium = 'Premium'
}

export interface SearchParams {
  page?: number
  per_page?: number
}

export interface DistributionShowResponse {
  code: number
  message: string
  data: {
    id: string
    user_id: string
    distribution_code: string
    status: string
    order_effect_num: string
    order_invalid_num: string
    confirmed_commission: string
    not_confirmed_commission: string
    assets: string
    cancel_commission: string
    settlement_commission: string
    pay_customer: string
    click_num: string
    register_num: string
    income_num: string
    commission_rate: string
    paypal: string
    is_logged_in: boolean
  }
}

// mock
export interface ListData {
  /**
   * 分销码
   */
  distribution_code?: string
  /**
   * id
   */
  id?: number
  rank_data: RankData[]
}

export interface RankData {
  /**
   * 是否勾选
   */
  check: string
  /**
   * 套餐佣金率
   */
  commission_rate: string
  /**
   * 套餐时长
   */
  duration: string
  /**
   * rank_id
   */
  rank_id: number
  /**
   * 套餐名字
   */
  rank_name: string
}

export interface DistributionCodeListResponse {
  code: number
  message: string
  data: {
    list: ListData[]
    is_logged_in: boolean
  }
}

export interface DistributionRecordListResponse {
  code: number
  data: {
    current_page: number
    data: RecodeListModel[]
    first_page_url: string
    from: number
    last_page: number
    last_page_url: string
    next_page_url: null
    path: string
    per_page: number
    prev_page_url: null
    to: number
    total: number
  }
  message: string
}

export interface RecodeListModel {
  /**
   * 创建时间
   */
  created_at: string
  /**
   * 支付时间
   */
  paid_at: number
  /**
   * 分销账号的id
   */
  distribution_info_id: string
  /**
   * 分销账号/来自客户
   */
  email: string
  /**
   * 会员资格
   */
  rank_name: RankName
  /**
   * 会员时长
   */
  rank_duration: string
  /**
   * 记录ID
   */
  id: number
  /**
   * 付款账号
   */
  pay_account: string
  /**
   * 结算金额
   */
  pay_money: number
  /**
   * 结算时间
   */
  pay_time: string
  /**
   * 支付状态
   * 对应表格中的状态字段
   */
  pay_status: RecordStatus
  /**
   * 支付金额
   */
  paid_amount: number
  /**
   * 佣金率 (百分比)
   */
  commission_rate: number
  /**
   * 佣金金额
   */
  commission: number
  /**
   * 备注
   */
  remark: string
  /**
   * 状态 1=> 已付款 2=>已作废
   */
  status: RecordStatus
  /**
   * 修改时间
   */
  updated_at: string
  /**
   * 用户ID
   */
  user_id: number
  /**
   * 作废时间
   */
  void_time: string | null
}

export interface DistributionCommissionListResponse {
  code: number
  data: {
    current_page: number
    data: CommissionListModel[]
    first_page_url: string
    from: number
    last_page: number
    last_page_url: string
    next_page_url: null
    path: string
    per_page: number
    prev_page_url: null
    to: number
    total: number
  }
  message: string
}

export interface CommissionListModel {
  /**
   * 创建时间
   */
  created_at: number
  /**
   * 当前资产
   */
  current_assets: number
  id: number
  /**
   * 金额
   */
  money: number
  /**
   * 类型 1=>结算, 2=>收入
   */
  type: number
  /**
   * 修改时间
   */
  updated_at: string
  /**
   * 用户id
   */
  user_id: number
}

export interface SettleListResponse {
  code: number
  data: SettleListResponseData
  message: string
}

export interface SettleListResponseData {
  current_page: number
  data: SettleListModel[]
  first_page_url: string
  from: number
  last_page: number
  last_page_url: string
  next_page_url: null
  path: string
  per_page: number
  prev_page_url: null
  to: number
  total: number
}

export interface SettleListModel {
  /**
   * 创建时间(时间戳)
   */
  created_at: number
  id: number
  /**
   * 付款账号
   */
  pay_account: string
  /**
   * 付款金额
   */
  pay_money: number
  /**
   * 付款时间
   */
  pay_time: number
  /**
   * 备注
   */
  remark: string
  /**
   * 状态, 1=>已付款 2=>已作废
   */
  status: PayStatus
  updated_at: string
  /**
   * 用户id
   */
  user_id: number
}
