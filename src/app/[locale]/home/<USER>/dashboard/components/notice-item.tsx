export function NoticeItem({
  number,
  text,
  children
}: {
  number: number
  text: string
  children?: React.ReactNode
}) {
  return (
    <div className="flex">
      <div className="shrink-0 w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center mr-3">
        {number}
      </div>
      <div className="flex flex-col">
        <div className="flex items-center h-8">
          <p className="text-gray-700">{text}</p>
        </div>
        {children}
      </div>
    </div>
  )
}
