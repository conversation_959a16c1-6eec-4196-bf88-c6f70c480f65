'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'

interface AffiliationTableProps<T> {
  columns: {
    header: string
    accessorKey: keyof T
  }[]
  data: T[]
  pagination: {
    current: number
    total: number
    pageSize: number
  }
  messages: {
    emptyText?: string
    totalText?: string
    previousText?: string
    nextText?: string
  }
}

export function AffiliationTable<T>({
  columns,
  data,
  pagination,
  messages
}: AffiliationTableProps<T>) {
  const {
    emptyText = 'No data',
    totalText = 'Total data',
    previousText = 'Previous',
    nextText = 'Next'
  } = messages
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const handleCurrentChange = (page: number) => {
    // Create a new URLSearchParams instance to maintain other query parameters
    const params = new URLSearchParams(searchParams)
    params.set('page', page.toString())

    // Navigate to the new URL with updated page parameter
    router.push(`${pathname}?${params.toString()}`)
  }

  return (
    <div>
      <Table className="min-w-[64rem]">
        <TableHeader>
          <TableRow className="flex w-full">
            {columns.map((column) => (
              <TableHead
                key={String(column.accessorKey)}
                className="text-center flex-1 flex items-center justify-center"
                style={{
                  minWidth: `${100 / columns.length}%`,
                  maxWidth: `${100 / columns.length}%`
                }}
              >
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((item, index) => (
              <TableRow key={index} className="flex w-full">
                {columns.map((column) => (
                  <TableCell
                    key={String(column.accessorKey)}
                    className="text-center flex-1 flex items-center justify-center text-ellipsis"
                    style={{
                      minWidth: `${100 / columns.length}%`,
                      maxWidth: `${100 / columns.length}%`
                    }}
                  >
                    {String(item[column.accessorKey])}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow className="flex w-full">
              <TableCell
                colSpan={columns.length}
                className="h-12 text-center text-gray-500 flex-1 mt-2"
              >
                {emptyText}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Pagination className="flex justify-end items-center mt-4">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handleCurrentChange(Math.max(1, pagination.current - 1))}
              className={pagination.current <= 1 ? 'pointer-events-none opacity-50' : ''}
            >
              {previousText}
            </PaginationPrevious>
          </PaginationItem>

          <PaginationItem>
            <PaginationLink isActive>{pagination.current}</PaginationLink>
          </PaginationItem>

          <PaginationItem>
            <PaginationNext
              onClick={() =>
                handleCurrentChange(
                  Math.min(
                    Math.ceil(pagination.total / pagination.pageSize),
                    pagination.current + 1
                  )
                )
              }
              className={
                pagination.current >= Math.ceil(pagination.total / pagination.pageSize)
                  ? 'pointer-events-none opacity-50'
                  : ''
              }
            >
              {nextText}
            </PaginationNext>
          </PaginationItem>

          <span className="mx-4 text-muted-foreground">{totalText}</span>
        </PaginationContent>
      </Pagination>
    </div>
  )
}
