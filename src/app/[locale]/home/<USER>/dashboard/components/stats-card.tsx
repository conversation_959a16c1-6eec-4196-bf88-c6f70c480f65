import { Card, CardContent } from '@/components/ui/card'

export function StatsCard({
  title,
  value,
  subtext,
  isMoney
}: {
  title: string
  value: string
  subtext?: string
  isMoney?: boolean
}) {
  return (
    <Card>
      <CardContent className="p-4 text-center">
        <p className="text-4xl font-bold">
          {isMoney && <span>$</span>}
          {value}
        </p>
        <p className="text-sm text-gray-500">{title}</p>
        {subtext && <p className="text-xs text-gray-400">{subtext}</p>}
      </CardContent>
    </Card>
  )
}
