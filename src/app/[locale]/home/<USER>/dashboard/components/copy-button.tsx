'use client'

import { But<PERSON> } from '@/components/ui/button'
import React from 'react'
import { useToast } from '@/hooks/useToast'

interface CopyButtonProps {
  text: string
  children?: React.ReactNode
  successText?: string
  errorText?: string
}

const CopyButton: React.FC<CopyButtonProps> = ({ text, children, successText, errorText }) => {
  const { showToast } = useToast()
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text)
      showToast(successText || 'Copied success', 'success')
    } catch (error) {
      console.error('Failed to copy text to clipboard', error)
      showToast(errorText || 'error', 'error')
    }
  }

  return (
    <Button className="bg-orange-500 hover:bg-orange-600" onClick={copyToClipboard}>
      {children}
    </Button>
  )
}

export default CopyButton
