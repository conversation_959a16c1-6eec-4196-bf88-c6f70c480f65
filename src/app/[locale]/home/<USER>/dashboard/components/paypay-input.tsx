import React from 'react'
import { getTranslations } from 'next-intl/server'
import PaypalInputClient from './paypal-input-client'
import { distributionUpdatePaypal, distributionShow } from '../actions'

interface PaypalInputProps {
  isLoading?: boolean
  initialValue?: string
  className?: string
}

// 这是服务端组件
const PaypalInput = async ({
  isLoading = false,
  initialValue = '',
  className = ''
}: PaypalInputProps) => {
  // 在服务端获取翻译
  const t = await getTranslations('Affiliation')

  const paypalRes = await distributionShow()
  if (paypalRes) {
    initialValue = paypalRes.data.paypal
  }

  // 定义一个服务端action
  const savePaypalAccount = async (
    formData: FormData
  ): Promise<{
    message: { type: 'normal' | 'success' | 'error' | 'warning'; content: string }
  }> => {
    'use server' // 使函数成为服务端函数
    const paypalAccount = formData.get('paypalAccount') as string

    // 在服务端函数中获取翻译
    const t = await getTranslations('Affiliation')

    if (paypalAccount && paypalAccount.trim()) {
      // 这里添加实际的保存逻辑，如调用API或数据库操作
      try {
        // 例如：await db.paypalAccounts.create({ userId: currentUser.id, account: paypalAccount });
        await distributionUpdatePaypal({
          paypalValue: paypalAccount
        })
        return {
          message: {
            type: 'success',
            content: t('paypalSaveSuccess')
          }
        }
      } catch (error) {
        console.error('保存Paypal账号失败:', error)
        return {
          message: {
            type: 'error',
            content: t('paypalSaveError')
          }
        }
      }
    }
    return { message: { type: 'error', content: t('paypalSaveInvalid') } }
  }

  return (
    <div className={`flex justify-center items-center ${className}`}>
      <div className="relative mt-[18px] w-[641px]">
        <PaypalInputClient
          savePaypalAccount={savePaypalAccount}
          isLoading={isLoading}
          initialValue={initialValue}
          placeholder={t('paypalText')}
          saveButtonText={t('PaypalSaveBtn')}
        />
      </div>
    </div>
  )
}

export default PaypalInput
