'use client'

import React, { useState, useTransition } from 'react'
import { useToast } from '@/hooks/useToast'

interface PaypalInputClientProps {
  savePaypalAccount: (
    formData: FormData
  ) => Promise<{ message: { type: 'normal' | 'success' | 'error' | 'warning'; content: string } }>
  isLoading?: boolean
  initialValue?: string
  placeholder: string
  saveButtonText: string
}

const PaypalInputClient: React.FC<PaypalInputClientProps> = ({
  savePaypalAccount,
  initialValue = '',
  placeholder,
  saveButtonText
}) => {
  const { showToast } = useToast()
  const [inputValue, setInputValue] = useState(initialValue)
  const [isPending, startTransition] = useTransition()

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      const result = await savePaypalAccount(formData)

      showToast(result.message?.content, result.message?.type)
    })
  }

  return (
    <form action={handleSubmit}>
      <input
        type="text"
        name="paypalAccount"
        disabled={isPending}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        placeholder={placeholder}
        className="w-full h-[59px] px-3 text-base font-medium text-[#191919] border border-primary rounded-md focus:outline-hidden focus:ring-2 focus:ring-primary focus:border-primary disabled:bg-gray-100 disabled:text-gray-400"
      />
      <button
        type="submit"
        disabled={isPending}
        className="absolute right-0 top-0 min-w-[170px] h-[59px] text-white bg-primary hover:bg-primary/90 rounded-r-md text-base active:outline-0 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 ease-in-out"
      >
        {saveButtonText}
      </button>
    </form>
  )
}

export default PaypalInputClient
