import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { getTranslations, getLocale } from 'next-intl/server'
import { RankData } from '../types'

interface AffiliationDashboardTableProps {
  data: RankData[]
}

export async function AffiliationDashboardTable({ data }: AffiliationDashboardTableProps) {
  const t = await getTranslations('Affiliation')
  const currentLocale = await getLocale()

  const translateName = (originalText: string): string => {
    let translations
    switch (originalText) {
      case 'basic':
        translations = t('table.basic')
        break
      case 'Starter':
        translations = t('table.starter')
        break
      case 'Standard':
        translations = t('table.Standard')
        break
      case 'Premium':
        translations = t('table.Premium')
        break
      default:
        translations = originalText
        break
    }

    return translations
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-1/3">{t('membershipType')}</TableHead>
          <TableHead className="w-1/3 text-right">{t('commission')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item, index) => {
          // Format the membership type string
          const period = item.duration.includes('years') ? t('table.yearly') : t('table.monthly')
          const translatedName = translateName(item.rank_name)
          const formattedType =
            currentLocale === 'zh'
              ? `${period}${translatedName}`
              : `${translatedName} for ${period}`
          // Format the commission string
          const formattedCommission = `${t('table.pricePaid')} * ${item.commission_rate}%`

          return (
            <TableRow key={item.rank_id} className={index % 2 === 0 ? 'bg-gray-100' : ''}>
              <TableCell className="px-4 py-4">
                <span className="text-base">{formattedType}</span>
              </TableCell>
              <TableCell className="text-right px-4 py-4">
                <span className="text-base">{formattedCommission}</span>
              </TableCell>
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
}
