import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'
import { Alert } from '@/components/ui/alert'

interface DashBoardSkeletonProps {
  // 可选参数来控制骨架屏的不同部分
  variant?: 'overview' | 'details' | 'statistics' | 'assets'
}

export function DashBoardSkeleton({ variant = 'overview' }: DashBoardSkeletonProps) {
  if (variant === 'overview') {
    return <OverviewSkeleton />
  }

  return (
    <div className="space-y-5 w-full animate-fadeIn">
      {/* Assets页面特有的头部信息 */}
      {variant === 'assets' && (
        <div className="flex items-center justify-between pb-6 px-6">
          <Skeleton className="h-8 w-56" /> {/* 标题 */}
          <div className="flex items-center gap-6">
            <div className="flex items-center">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="ml-2 h-8 w-24" />
            </div>
            <div className="flex items-center">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="ml-2 h-8 w-24" />
            </div>
          </div>
        </div>
      )}

      {/* Statistics页面的Alert区域 */}
      {variant === 'statistics' && (
        <div className="mt-2">
          <div className="p-4 border rounded-lg bg-slate-50">
            <div className="flex items-center">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="ml-2 h-5 w-full max-w-2xl" />
            </div>
          </div>
        </div>
      )}

      {/* 主卡片内容 - 所有页面通用 */}
      <Card>
        <CardContent className="p-6">
          {/* 详情页或资产页的标题 */}
          {(variant === 'details' || variant === 'assets') && (
            <div className="py-4 text-center">
              <Skeleton className="h-6 w-64 mx-auto mb-4" />
            </div>
          )}

          {/* Statistics页面的Paypal输入框 */}
          {variant === 'statistics' && (
            <div className="flex justify-center items-center flex-col">
              <div className="flex justify-center items-center">
                <Skeleton className="h-6 w-6 rounded mr-2" />
                <Skeleton className="h-6 w-32" />
              </div>
              <div className="mt-4 w-full max-w-sm">
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="mt-4 h-10 w-36" />
            </div>
          )}

          {/* 表格骨架 - 所有页面通用 */}
          <div className="mt-4">
            {/* 表头 */}
            <div className="flex items-center justify-between pb-4 border-b">
              {[...Array(5)].map((_, i) => (
                <Skeleton
                  key={`header-${i}`}
                  className="h-4"
                  style={{ width: `${15 + Math.random() * 10}%` }}
                />
              ))}
            </div>

            {/* 表格行 */}
            {[...Array(5)].map((_, rowIndex) => (
              <div key={`row-${rowIndex}`} className="flex items-center py-4 border-b">
                {[...Array(5)].map((_, cellIndex) => (
                  <Skeleton
                    key={`cell-${rowIndex}-${cellIndex}`}
                    className="h-4"
                    style={{
                      width: `${15 + Math.random() * 10}%`,
                      marginRight: '8px'
                    }}
                  />
                ))}
              </div>
            ))}

            {/* 分页器 */}
            <div className="flex justify-between items-center pt-4">
              <Skeleton className="h-4 w-32" />
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics页面的第二个表格 */}
      {variant === 'statistics' && (
        <Card className="mt-5">
          <CardContent className="p-6">
            {/* 表格骨架 - 与上面相同 */}
            <div className="mt-4">
              <div className="flex items-center justify-between pb-4 border-b">
                {[...Array(5)].map((_, i) => (
                  <Skeleton
                    key={`header2-${i}`}
                    className="h-4"
                    style={{ width: `${15 + Math.random() * 10}%` }}
                  />
                ))}
              </div>

              {[...Array(5)].map((_, rowIndex) => (
                <div key={`row2-${rowIndex}`} className="flex items-center py-4 border-b">
                  {[...Array(5)].map((_, cellIndex) => (
                    <Skeleton
                      key={`cell2-${rowIndex}-${cellIndex}`}
                      className="h-4"
                      style={{
                        width: `${15 + Math.random() * 10}%`,
                        marginRight: '8px'
                      }}
                    />
                  ))}
                </div>
              ))}

              <div className="flex justify-between items-center pt-4">
                <Skeleton className="h-4 w-32" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Overview页面专属的骨架屏组件
function OverviewSkeleton() {
  return (
    <div className="space-y-8 w-full animate-fadeIn">
      {/* 第一排统计卡片 - 4列 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <Card key={`top-stats-${i}`} className="p-6">
            <div className="flex flex-col">
              <Skeleton className="h-5 w-24 mb-2" />
              {i === 3 && <Skeleton className="h-3 w-32 mb-1" />}
              <Skeleton className="h-8 w-16" />
            </div>
          </Card>
        ))}
      </div>

      {/* 第二排统计卡片 - 5列 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={`bottom-stats-${i}`} className="p-6">
            <div className="flex flex-col">
              <Skeleton className="h-5 w-32 mb-2" />
              {i === 3 && <Skeleton className="h-3 w-32 mb-1" />}
              <div className="flex items-center">
                <Skeleton className="h-8 w-20" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* 警告提示 */}
      <Alert className="mt-8 bg-amber-50 border-amber-200">
        <div className="flex items-center">
          <Skeleton className="h-4 w-4 rounded-full" />
          <Skeleton className="ml-2 h-5 w-full max-w-3xl" />
        </div>
      </Alert>

      {/* 推广链接部分 */}
      <div className="mt-8">
        <Skeleton className="h-7 w-40 mb-4" /> {/* 标题 */}
        <Card>
          <CardContent className="p-0">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-6">
              <div className="flex flex-col justify-center items-center col-span-3">
                <Skeleton className="h-6 w-40 mb-2" />
                <div className="w-full text-center">
                  <Skeleton className="h-6 w-full mb-3" />
                  <Skeleton className="h-9 w-24 mx-auto" />
                </div>
              </div>

              <div className="col-span-2">
                {/* 小表格 */}
                <div className="border rounded-lg overflow-hidden">
                  {[1, 2, 3].map((rowIndex) => (
                    <div key={`promo-row-${rowIndex}`} className="flex items-center p-3 border-b">
                      <Skeleton className="h-4 w-2/5 mr-2" />
                      <Skeleton className="h-4 w-2/5" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Alert className="mt-4 bg-gray-50">
          <Skeleton className="h-4 w-full max-w-2xl ml-2" />
        </Alert>
      </div>

      {/* 通知项列表 */}
      <div className="mt-8">
        <Skeleton className="h-7 w-48 mb-4" /> {/* 标题 */}
        <div className="space-y-8">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
            <div key={`notice-${i}`} className="flex">
              <div className="shrink-0 mr-3">
                <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-white">
                  <Skeleton className="h-4 w-4 rounded-full bg-white/40" />
                </div>
              </div>
              <div className="grow">
                <Skeleton className="h-5 w-full max-w-3xl" />
                {i === 1 && (
                  <div className="mt-2">
                    <Skeleton className="h-3 w-full max-w-md" />
                  </div>
                )}
                {i === 4 && (
                  <div className="ml-8 mt-2 p-4 bg-gray-50 rounded-md">
                    <Skeleton className="h-4 w-48 mb-2" />
                    <Skeleton className="h-3 w-full max-w-2xl mb-1" />
                    <Skeleton className="h-3 w-full max-w-xl mb-2" />
                    <Skeleton className="h-3 w-full max-w-lg mt-2" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
