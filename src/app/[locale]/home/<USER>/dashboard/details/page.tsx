import { getTranslations } from 'next-intl/server'
import { TabsContent } from '@/components/ui/tabs'
import { AffiliationTable } from '../components/affiliation-table'
import { Card, CardContent } from '@/components/ui/card'
import { distributionRecordList } from '../actions'
import { RecordStatus, RankName, SearchParams, RecodeListModel } from '../types'

export default async function AffiliationDashboardStatisticsPage({
  searchParams = Promise.resolve({ page: 1, per_page: 10 })
}: {
  searchParams?: Promise<SearchParams>
}) {
  const t = await getTranslations('Affiliation')

  // Parse pagination parameters from URL
  const { page = 1, per_page = 10 } = await searchParams

  // Pass pagination parameters to the server action
  const distributionRecordListResponse = await distributionRecordList({
    page,
    per_page
  })
  if (!distributionRecordListResponse) return null

  const {
    data,
    current_page,
    per_page: responsePerPage,
    total
  } = distributionRecordListResponse.data

  const translateData = (data: RecodeListModel[]) => {
    // 创建映射对象，避免使用多个 switch 语句
    const rankNameMap = {
      [RankName.Basic]: t('table.unknown'),
      [RankName.FREE]: t('table.free'),
      [RankName.Starter]: t('table.starter'),
      [RankName.Standard]: t('table.Standard'),
      [RankName.Premium]: t('table.Premium')
    }

    const recordStatusMap = {
      [RecordStatus.Paid]: t('table.paid'),
      [RecordStatus.Void]: t('table.void')
    }

    // 创建格式化函数，避免重复代码
    const formatCurrency = (value: number | undefined) =>
      value !== undefined ? `$${value}` : value

    const formatPercent = (value: number | undefined) => (value !== undefined ? `${value}%` : value)

    const formatTimestamp = (value: number | undefined) =>
      value !== undefined
        ? new Date(Number(value) * 1000).toISOString().slice(0, 19).replace('T', ' ')
        : value

    // 先过滤掉 rank_name 为 "basic" 的项目，然后再进行映射转换
    return data
      .filter((item) => item.rank_name !== 'Basic')
      .map((item) => ({
        ...item,
        rank_name: rankNameMap[item.rank_name] || t('table.unknown'),
        pay_status: recordStatusMap[item.pay_status] || t('table.unknown'),
        commission_rate: formatPercent(item.commission_rate),
        paid_amount: formatCurrency(item.paid_amount),
        commission: formatCurrency(item.commission),
        paid_at: formatTimestamp(item.paid_at)
      }))
  }
  const detailsTableData = translateData(data)

  const columns: { header: string; accessorKey: keyof (typeof detailsTableData)[number] }[] = [
    { header: t('detailsPaidAt'), accessorKey: 'paid_at' },
    { header: t('detailsPayStatus'), accessorKey: 'pay_status' },
    { header: t('detailsEmail'), accessorKey: 'email' },
    { header: t('detailsRankName'), accessorKey: 'rank_name' },
    { header: t('detailsRankDuration'), accessorKey: 'rank_duration' },
    { header: t('detailsPaidAmount'), accessorKey: 'paid_amount' },
    { header: t('detailsCommissionRate'), accessorKey: 'commission_rate' },
    { header: t('detailsCommission'), accessorKey: 'commission' }
  ]

  return (
    <div className="top-0">
      <TabsContent value="details">
        <Card>
          <CardContent className="p-6">
            <div className="py-4 text-center text-gray-500">{t('detailsContent')}</div>
            <AffiliationTable
              columns={columns}
              data={detailsTableData}
              pagination={{
                current: current_page,
                total: total,
                pageSize: responsePerPage
              }}
              messages={{
                emptyText: t('table.empty'),
                totalText: t('table.total', { count: total }),
                previousText: t('table.previous'),
                nextText: t('table.next')
              }}
            />
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  )
}
