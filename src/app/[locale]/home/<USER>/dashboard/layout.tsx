import { getTranslations } from 'next-intl/server'
import Tab<PERSON>rapper from './TabWrapper'
import { getUserInfo } from '@/services/server/userService'
import { redirect } from 'next/navigation'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Dashboard',
  description: 'Dashboard',
  robots: {
    index: false,
    follow: false
  }
}

export default async function AffiliationDashboardLayout({
  children
}: {
  children: React.ReactNode
}) {
  const t = await getTranslations('Affiliation')

  try {
    const userInfo = await getUserInfo()

    if (!userInfo) {
      redirect('/login')
    }

    return (
      <div className="container mx-auto py-6 space-y-8">
        <h1 className="text-2xl font-bold">{t('myAffiliation')}</h1>

        <TabWrapper
          overviewLabel={t('overview')}
          detailsLabel={t('details')}
          statisticsLabel={t('statistics')}
          assetsLabel={t('assets')}
        >
          {children}
        </TabWrapper>
      </div>
    )
  } catch {
    redirect('/login')
  }
}
