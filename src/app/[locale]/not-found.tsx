import { Logo } from '@/components/Logo'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

export const metadata = {
  title: '404 - Page Not Found',
  description: 'The page you are looking for does not exist.',
  robots: {
    index: false,
    follow: false
  }
}

export default async function NotFound() {
  const t = await getTranslations('NotFound')
  return (
    <div className="flex items-center min-h-screen px-4 py-12 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      <div className="w-full space-y-6 text-center">
        <div className="flex gap-4 items-center justify-center mx-auto w-auto h-12">
          <div className="shrink-0">
            <Logo size={40} />
          </div>
          <div className="h-8 w-px bg-gray-300 dark:bg-gray-700" />
          <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl animate-[bounce_1s_ease-in-out_3] text-center">
            404
          </h1>
        </div>
        <div className="space-y-3">
          <h2 className="text-xl font-semibold">{t('title')}</h2>
          <p className="text-gray-500">{t('message')}</p>
        </div>
        <Link
          href="/"
          className="inline-flex h-10 items-center rounded-md bg-orange-600 px-8 text-sm font-medium text-gray-50 shadow-sm transition-colors hover:bg-orange-700/100 focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
          prefetch={false}
        >
          {t('backToHome')}
        </Link>
      </div>
    </div>
  )
}
