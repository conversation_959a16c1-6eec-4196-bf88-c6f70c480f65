@import 'tailwindcss';
@import './fix-line-height.css';

/* 修复bg-black/80类的问题 */
.bg-black\/80 {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

@custom-variant dark (&:is(.dark *));

@theme {
  /* 屏幕断点配置 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1216px;

  /* 颜色配置 */
  /* 基础颜色 */
  --color-transparent: transparent;
  --color-current: currentColor;
  --color-black: #000;
  --color-white: #fff;

  /* 灰色系列 */
  --color-gray-50: #fafaf9;
  --color-gray-100: #f5f5f4;
  --color-gray-200: #e7e5e4;
  --color-gray-300: #d6d3d1;
  --color-gray-400: #a8a29e;
  --color-gray-500: #78716c;
  --color-gray-600: #57534e;
  --color-gray-700: #44403c;
  --color-gray-800: #292524;
  --color-gray-900: #1c1917;

  /* 绿色系列 */
  --color-green-50: #e6efee;
  --color-green-100: #cce0dc;
  --color-green-200: #99c0b9;
  --color-green-300: #66a197;
  --color-green-400: #338174;
  --color-green-500: #006251;
  --color-green-600: #004e41;
  --color-green-700: #003b31;
  --color-green-800: #002720;
  --color-green-900: #001410;

  /* 黄色系列 */
  --color-yellow-50: #fef8e8;
  --color-yellow-100: #fef5dd;
  --color-yellow-200: #fdeec7;
  --color-yellow-300: #fce7b0;
  --color-yellow-400: #fbe099;
  --color-yellow-500: #fbdc8e;
  --color-yellow-600: #c9b072;
  --color-yellow-700: #978455;
  --color-yellow-800: #645839;
  --color-yellow-900: #322c1c;

  /* 翠绿色系列 */
  --color-emerald-50: #eff8f4;
  --color-emerald-100: #e0f1ea;
  --color-emerald-200: #c2e3d5;
  --color-emerald-300: #a2d5bf;
  --color-emerald-400: #84c7aa;
  --color-emerald-500: #65b996;
  --color-emerald-600: #519477;
  --color-emerald-700: #3c6f59;
  --color-emerald-800: #294a3c;
  --color-emerald-900: #14251e;

  /* 红色系列 */
  --color-red-50: #ffefec;
  --color-red-100: #fedfd9;
  --color-red-200: #febfb4;
  --color-red-300: #fc9d8f;
  --color-red-400: #fc7d6a;
  --color-red-500: #fc5c46;
  --color-red-600: #c94a38;
  --color-red-700: #97372a;
  --color-red-800: #65251c;
  --color-red-900: #32130e;

  /* 橙色系列 */
  --color-orange-50: #fdf5ee;
  --color-orange-100: #fcebdd;
  --color-orange-200: #f7d8ba;
  --color-orange-300: #f4c498;
  --color-orange-400: #f0b077;
  --color-orange-500: #ed9c56;
  --color-orange-600: #bd7d44;
  --color-orange-700: #8e5e33;
  --color-orange-800: #5e3f22;
  --color-orange-900: #301f11;

  /* 蓝色系列 */
  --color-blue-50: #eef6f9;
  --color-blue-100: #ddecf2;
  --color-blue-200: #bbdae4;
  --color-blue-300: #99c7d7;
  --color-blue-400: #77b5ca;
  --color-blue-500: #56a2bd;
  --color-blue-600: #448297;
  --color-blue-700: #346171;
  --color-blue-800: #22414c;
  --color-blue-900: #112026;

  /* 原有的配置保留 */
  --color-primary: #006251;
  --color-primary-light: #3291ff;
  --color-primary-dark: #0761d1;

  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-secondary: #7928ca;
  --color-secondary-light: #8a3ffc;
  --color-secondary-dark: #6a1fb1;

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* 间距配置 */
  --spacing-0: 0px;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-18: 4.5rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-27: 6.75rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-34: 8.5rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-62: 15.5rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-74: 18.5rem;
  --spacing-80: 20rem;
  --spacing-87: 21.75rem;
  --spacing-96: 24rem;
  --spacing-100: 25rem;
  --spacing-112: 28rem;
  --spacing-116: 29rem;
  --spacing-128: 32rem;
  --spacing-135: 33.75rem;
  --spacing-148: 37rem;
  --spacing-160: 40rem;
  --spacing-170: 42.5rem;
  --spacing-186: 46.5rem;
  --spacing-235: 58.75rem;
  --spacing-470: 117.5rem;
  --spacing-px: 1px;
  --spacing-0\.5: 0.125rem;
  --spacing-1\.5: 0.375rem;
  --spacing-2\.5: 0.625rem;
  --spacing-3\.5: 0.875rem;

  /* 动画配置 */
  --animation-none: none;
  --animation-spin: spin 1s linear infinite;
  --animation-spinSlow: spin 6s linear infinite;
  --animation-spinStar: spin 16s linear infinite;
  --animation-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animation-bounce: bounce 1s infinite;

  /* 背景色配置 */
  --bg-transparent: transparent;
  --bg-current: currentColor;
  --bg-black: #000;
  --bg-white: #fff;
  --bg-body: #fff;

  /* 背景图像配置 */
  --bg-image-none: none;
  --bg-image-gradient-to-t: linear-gradient(to top, var(--tw-gradient-stops));
  --bg-image-gradient-to-tr: linear-gradient(to top right, var(--tw-gradient-stops));
  --bg-image-gradient-to-r: linear-gradient(to right, var(--tw-gradient-stops));
  --bg-image-gradient-to-br: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --bg-image-gradient-to-b: linear-gradient(to bottom, var(--tw-gradient-stops));
  --bg-image-gradient-to-bl: linear-gradient(to bottom left, var(--tw-gradient-stops));
  --bg-image-gradient-to-l: linear-gradient(to left, var(--tw-gradient-stops));
  --bg-image-gradient-to-tl: linear-gradient(to top left, var(--tw-gradient-stops));
  --bg-image-radial: radial-gradient(var(--tw-gradient-stops));

  /* 背景不透明度配置 */
  --bg-opacity-0: 0;
  --bg-opacity-5: 0.05;
  --bg-opacity-10: 0.1;
  --bg-opacity-20: 0.2;
  --bg-opacity-25: 0.25;
  --bg-opacity-30: 0.3;
  --bg-opacity-40: 0.4;
  --bg-opacity-50: 0.5;
  --bg-opacity-60: 0.6;
  --bg-opacity-70: 0.7;
  --bg-opacity-75: 0.75;
  --bg-opacity-80: 0.8;
  --bg-opacity-90: 0.9;
  --bg-opacity-95: 0.95;
  --bg-opacity-100: 1;

  /* 背景位置配置 */
  --bg-position-bottom: bottom;
  --bg-position-center: center;
  --bg-position-left: left;
  --bg-position-left-bottom: left bottom;
  --bg-position-left-top: left top;
  --bg-position-right: right;
  --bg-position-right-bottom: right bottom;
  --bg-position-right-top: right top;
  --bg-position-top: top;

  /* 背景大小配置 */
  --bg-size-auto: auto;
  --bg-size-cover: cover;
  --bg-size-contain: contain;

  /* 模糊效果配置 */
  --blur-none: 0px;
  --blur-DEFAULT: 20px;
  --blur-sm: 4px;
  --blur-md: 16px;
  --blur-lg: 24px;
  --blur-xl: 28px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;
  --blur-4xl: 150px;

  /* 背景模糊效果配置 */
  --backdrop-blur-none: 0px;
  --backdrop-blur-DEFAULT: 20px;
  --backdrop-blur-sm: 4px;
  --backdrop-blur-md: 16px;
  --backdrop-blur-lg: 24px;
  --backdrop-blur-xl: 28px;
  --backdrop-blur-2xl: 40px;
  --backdrop-blur-3xl: 64px;
  --backdrop-blur-4xl: 150px;

  /* 背景亮度配置 */
  --backdrop-brightness-DEFAULT: 1;
  --backdrop-brightness-0: 0;
  --backdrop-brightness-50: 0.5;
  --backdrop-brightness-75: 0.75;
  --backdrop-brightness-90: 0.9;
  --backdrop-brightness-95: 0.95;
  --backdrop-brightness-100: 1;
  --backdrop-brightness-105: 1.05;
  --backdrop-brightness-110: 1.1;
  --backdrop-brightness-125: 1.25;
  --backdrop-brightness-150: 1.5;
  --backdrop-brightness-200: 2;

  /* 背景对比度配置 */
  --backdrop-contrast-DEFAULT: 1;
  --backdrop-contrast-0: 0;
  --backdrop-contrast-50: 0.5;
  --backdrop-contrast-75: 0.75;
  --backdrop-contrast-100: 1;
  --backdrop-contrast-125: 1.25;
  --backdrop-contrast-150: 1.5;
  --backdrop-contrast-200: 2;

  /* 背景灰度配置 */
  --backdrop-grayscale-DEFAULT: 0;
  --backdrop-grayscale-0: 0;
  --backdrop-grayscale-100: 1;

  /* 背景色相旋转配置 */
  --backdrop-hue-rotate-DEFAULT: 0deg;
  --backdrop-hue-rotate-0: 0deg;
  --backdrop-hue-rotate-15: 15deg;
  --backdrop-hue-rotate-30: 30deg;
  --backdrop-hue-rotate-60: 60deg;
  --backdrop-hue-rotate-90: 90deg;
  --backdrop-hue-rotate-180: 180deg;

  /* 背景反转配置 */
  --backdrop-invert-DEFAULT: 0;
  --backdrop-invert-0: 0;
  --backdrop-invert-100: 1;

  /* 背景不透明度配置 */
  --backdrop-opacity-DEFAULT: 1;
  --backdrop-opacity-0: 0;
  --backdrop-opacity-5: 0.05;
  --backdrop-opacity-10: 0.1;
  --backdrop-opacity-20: 0.2;
  --backdrop-opacity-25: 0.25;
  --backdrop-opacity-30: 0.3;
  --backdrop-opacity-40: 0.4;
  --backdrop-opacity-50: 0.5;
  --backdrop-opacity-60: 0.6;
  --backdrop-opacity-70: 0.7;
  --backdrop-opacity-75: 0.75;
  --backdrop-opacity-80: 0.8;
  --backdrop-opacity-90: 0.9;
  --backdrop-opacity-95: 0.95;
  --backdrop-opacity-100: 1;

  /* 背景饱和度配置 */
  --backdrop-saturate-DEFAULT: 1;
  --backdrop-saturate-0: 0;
  --backdrop-saturate-50: 0.5;
  --backdrop-saturate-100: 1;
  --backdrop-saturate-150: 1.5;
  --backdrop-saturate-200: 2;

  /* 背景褐色配置 */
  --backdrop-sepia-DEFAULT: 0;
  --backdrop-sepia-0: 0;
  --backdrop-sepia-100: 1;

  /* 字体大小配置 */
  --text-xs: 0.75rem;
  /* line-height: 1rem */
  --text-sm: 0.875rem;
  /* line-height: 1.25rem */
  --text-base: 1rem;
  /* line-height: 1.5rem */
  --text-lg: 1.125rem;
  /* line-height: 1.75rem */
  --text-xl: 1.25rem;
  /* line-height: 1.75rem */
  --text-2xl: 1.5rem;
  /* line-height: 2rem */
  --text-3xl: 1.875rem;
  /* line-height: 2.25rem */
  --text-4xl: 2.25rem;
  /* line-height: 2.5rem */
  --text-5xl: 3rem;
  /* line-height: 1 */
  --text-6xl: 4rem;
  /* line-height: 1 */
  --text-7xl: 4.5rem;
  /* line-height: 1 */
  --text-8xl: 6rem;
  /* line-height: 1 */
  --text-9xl: 8rem;
  /* line-height: 1 */

  /* 字重配置 */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* 网格间距配置 */
  --gap-0: 0px;
  --gap-1: 0.25rem;
  --gap-2: 0.5rem;
  --gap-3: 0.75rem;
  --gap-4: 1rem;
  --gap-5: 1.25rem;
  --gap-6: 1.5rem;
  --gap-8: 2rem;
  --gap-10: 2.5rem;
  --gap-12: 3rem;
  --gap-16: 4rem;
  --gap-20: 5rem;
  --gap-24: 6rem;
  --gap-32: 8rem;
  --gap-40: 10rem;
  --gap-48: 12rem;
  --gap-56: 14rem;
  --gap-64: 16rem;

  /* z-index 配置 */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  /* 透明度配置 */
  --opacity-0: 0;
  --opacity-5: 0.05;
  --opacity-10: 0.1;
  --opacity-20: 0.2;
  --opacity-25: 0.25;
  --opacity-30: 0.3;
  --opacity-40: 0.4;
  --opacity-50: 0.5;
  --opacity-60: 0.6;
  --opacity-70: 0.7;
  --opacity-75: 0.75;
  --opacity-80: 0.8;
  --opacity-90: 0.9;
  --opacity-95: 0.95;
  --opacity-100: 1;

  /* 过渡时间配置 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* 过渡延迟配置 */
  --delay-75: 75ms;
  --delay-100: 100ms;
  --delay-150: 150ms;
  --delay-200: 200ms;
  --delay-300: 300ms;
  --delay-500: 500ms;
  --delay-700: 700ms;
  --delay-1000: 1000ms;

  /* 过渡时间函数配置 */
  --ease-DEFAULT: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* 旋转角度配置 */
  --rotate-0: 0deg;
  --rotate-1: 1deg;
  --rotate-2: 2deg;
  --rotate-3: 3deg;
  --rotate-6: 6deg;
  --rotate-12: 12deg;
  --rotate-45: 45deg;
  --rotate-90: 90deg;
  --rotate-180: 180deg;
  --rotate-n180: -180deg;
  --rotate-n90: -90deg;
  --rotate-n45: -45deg;
  --rotate-n12: -12deg;
  --rotate-n6: -6deg;
  --rotate-n3: -3deg;
  --rotate-n2: -2deg;
  --rotate-n1: -1deg;

  /* 文本颜色配置 */
  --text-transparent: transparent;
  --text-current: currentColor;
  --text-black: #000;
  --text-white: #fff;
  --text-body: #1c1917;

  /* 文本不透明度配置 */
  --text-opacity-0: 0;
  --text-opacity-5: 0.05;
  --text-opacity-10: 0.1;
  --text-opacity-20: 0.2;
  --text-opacity-25: 0.25;
  --text-opacity-30: 0.3;
  --text-opacity-40: 0.4;
  --text-opacity-50: 0.5;
  --text-opacity-60: 0.6;
  --text-opacity-70: 0.7;
  --text-opacity-75: 0.75;
  --text-opacity-80: 0.8;
  --text-opacity-90: 0.9;
  --text-opacity-95: 0.95;
  --text-opacity-100: 1;

  /* 过渡属性配置 */
  --transition-none: none;
  --transition-all: all;
  --transition-default: background-color, border-color, color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  --transition-colors: background-color, border-color, color, fill, stroke;
  --transition-opacity: opacity;
  --transition-shadow: box-shadow;
  --transition-transform: transform;

  /* 平移配置 */
  --translate-1\/2: 50%;
  --translate-1\/3: 33.333333%;
  --translate-2\/3: 66.666667%;
  --translate-1\/4: 25%;
  --translate-2\/4: 50%;
  --translate-3\/4: 75%;
  --translate-full: 100%;
  --translate-n1\/2: -50%;
  --translate-n1\/3: -33.333333%;
  --translate-n2\/3: -66.666667%;
  --translate-n1\/4: -25%;
  --translate-n2\/4: -50%;
  --translate-n3\/4: -75%;
  --translate-n-full: -100%;

  /* 变换原点配置 */
  --transform-origin-center: center;
  --transform-origin-top: top;
  --transform-origin-top-right: top right;
  --transform-origin-right: right;
  --transform-origin-bottom-right: bottom right;
  --transform-origin-bottom: bottom;
  --transform-origin-bottom-left: bottom left;
  --transform-origin-left: left;
  --transform-origin-top-left: top left;

  /* 轮廓配置 */
  --outline-none: 2px solid transparent;
  --outline-white: 2px dotted white;
  --outline-black: 2px dotted black;

  /* 环形颜色配置 */
  --ring-color-DEFAULT: #3b82f6;
  --ring-color-transparent: transparent;
  --ring-color-current: currentColor;
  --ring-color-black: #000;
  --ring-color-white: #fff;

  /* 环形偏移颜色配置 */
  --ring-offset-color-transparent: transparent;
  --ring-offset-color-current: currentColor;
  --ring-offset-color-black: #000;
  --ring-offset-color-white: #fff;

  /* 环形偏移宽度配置 */
  --ring-offset-width-0: 0px;
  --ring-offset-width-1: 1px;
  --ring-offset-width-2: 2px;
  --ring-offset-width-4: 4px;
  --ring-offset-width-8: 8px;

  /* 环形不透明度配置 */
  --ring-opacity-DEFAULT: 0.5;
  --ring-opacity-0: 0;
  --ring-opacity-5: 0.05;
  --ring-opacity-10: 0.1;
  --ring-opacity-20: 0.2;
  --ring-opacity-25: 0.25;
  --ring-opacity-30: 0.3;
  --ring-opacity-40: 0.4;
  --ring-opacity-50: 0.5;
  --ring-opacity-60: 0.6;
  --ring-opacity-70: 0.7;
  --ring-opacity-75: 0.75;
  --ring-opacity-80: 0.8;
  --ring-opacity-90: 0.9;
  --ring-opacity-95: 0.95;
  --ring-opacity-100: 1;

  /* 环形宽度配置 */
  --ring-width-0: 0px;
  --ring-width-1: 1px;
  --ring-width-2: 2px;
  --ring-width-4: 4px;
  --ring-width-8: 8px;
  --ring-width-DEFAULT: 3px;

  /* 描边配置 */
  --stroke-current: currentColor;

  /* 描边宽度配置 */
  --stroke-width-0: 0;
  --stroke-width-1: 1;
  --stroke-width-2: 2;

  /* 字体系列配置 */
  --font-family-body: var(--font-dm-sans), 'ui-sans-serif', 'system-ui', '-apple-system',
    'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', '"Noto Sans"',
    'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"',
    '"Noto Color Emoji"';
  --font-family-heading: var(--font-poppins), 'ui-sans-serif', 'system-ui', '-apple-system',
    'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', '"Noto Sans"',
    'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"',
    '"Noto Color Emoji"';
  --font-family-sans: var(--font-dm-sans), 'ui-sans-serif', 'system-ui', '-apple-system',
    'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', '"Noto Sans"',
    'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"',
    '"Noto Color Emoji"';
  --font-family-serif: 'ui-serif', 'Georgia', 'Cambria', '"Times New Roman"', 'Times', 'serif';
  --font-family-mono: 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas',
    '"Liberation Mono"', '"Courier New"', 'monospace';

  /* 阴影效果配置 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-DEFAULT: 0px 0px 0px 1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-none: 0 0 #0000;

  /* 边框颜色配置 */
  --border-color-transparent: transparent;
  --border-color-current: currentColor;
  --border-color-black: #000;
  --border-color-white: #fff;
  --border-color-DEFAULT: #e7e5e4;

  /* 边框不透明度配置 */
  --border-opacity-0: 0;
  --border-opacity-5: 0.05;
  --border-opacity-10: 0.1;
  --border-opacity-20: 0.2;
  --border-opacity-25: 0.25;
  --border-opacity-30: 0.3;
  --border-opacity-40: 0.4;
  --border-opacity-50: 0.5;
  --border-opacity-60: 0.6;
  --border-opacity-70: 0.7;
  --border-opacity-75: 0.75;
  --border-opacity-80: 0.8;
  --border-opacity-90: 0.9;
  --border-opacity-95: 0.95;
  --border-opacity-100: 1;

  /* 分割线颜色配置 */
  --divide-color-transparent: transparent;
  --divide-color-current: currentColor;
  --divide-color-black: #000;
  --divide-color-white: #fff;
  --divide-color-DEFAULT: #e7e5e4;

  /* 分割线不透明度配置 */
  --divide-opacity-0: 0;
  --divide-opacity-5: 0.05;
  --divide-opacity-10: 0.1;
  --divide-opacity-20: 0.2;
  --divide-opacity-25: 0.25;
  --divide-opacity-30: 0.3;
  --divide-opacity-40: 0.4;
  --divide-opacity-50: 0.5;
  --divide-opacity-60: 0.6;
  --divide-opacity-70: 0.7;
  --divide-opacity-75: 0.75;
  --divide-opacity-80: 0.8;
  --divide-opacity-90: 0.9;
  --divide-opacity-95: 0.95;
  --divide-opacity-100: 1;

  /* 分割线宽度配置 */
  --divide-width-0: 0px;
  --divide-width-2: 2px;
  --divide-width-4: 4px;
  --divide-width-8: 8px;
  --divide-width-DEFAULT: 1px;

  /* 填充配置 */
  --fill-current: currentColor;

  /* 渐变色停止点配置 */
  --gradient-from-transparent: transparent;
  --gradient-from-current: currentColor;
  --gradient-from-black: #000;
  --gradient-from-white: #fff;
  --gradient-via-transparent: transparent;
  --gradient-via-current: currentColor;
  --gradient-via-black: #000;
  --gradient-via-white: #fff;
  --gradient-to-transparent: transparent;
  --gradient-to-current: currentColor;
  --gradient-to-black: #000;
  --gradient-to-white: #fff;

  /* 边框圆角配置 */
  --rounded-none: 0px;
  --rounded-sm: 0.125rem;
  --rounded-DEFAULT: 0.25rem;
  --rounded-md: 0.375rem;
  --rounded-lg: 0.5rem;
  --rounded-xl: 0.75rem;
  --rounded-2xl: 1rem;
  --rounded-3xl: 1.25rem;
  --rounded-4xl: 1.875rem;
  --rounded-5xl: 5rem;
  --rounded-full: 9999px;

  /* 边框宽度配置 */
  --border-0: 0px;
  --border-2: 2px;
  --border-4: 4px;
  --border-8: 8px;
  --border-DEFAULT: 1px;

  /* 弹性盒子配置 */
  --flex-1: 1 1 0%;
  --flex-auto: 1 1 auto;
  --flex-initial: 0 1 auto;
  --flex-none: none;

  /* 行高配置 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* 网格模板列配置 */
  --grid-cols-1: repeat(1, minmax(0, 1fr));
  --grid-cols-2: repeat(2, minmax(0, 1fr));
  --grid-cols-3: repeat(3, minmax(0, 1fr));
  --grid-cols-4: repeat(4, minmax(0, 1fr));
  --grid-cols-5: repeat(5, minmax(0, 1fr));
  --grid-cols-6: repeat(6, minmax(0, 1fr));
  --grid-cols-7: repeat(7, minmax(0, 1fr));
  --grid-cols-8: repeat(8, minmax(0, 1fr));
  --grid-cols-9: repeat(9, minmax(0, 1fr));
  --grid-cols-10: repeat(10, minmax(0, 1fr));
  --grid-cols-11: repeat(11, minmax(0, 1fr));
  --grid-cols-12: repeat(12, minmax(0, 1fr));
  --grid-cols-none: none;

  /* 网格行配置 */
  --grid-rows-1: repeat(1, minmax(0, 1fr));
  --grid-rows-2: repeat(2, minmax(0, 1fr));
  --grid-rows-3: repeat(3, minmax(0, 1fr));
  --grid-rows-4: repeat(4, minmax(0, 1fr));
  --grid-rows-5: repeat(5, minmax(0, 1fr));
  --grid-rows-6: repeat(6, minmax(0, 1fr));
  --grid-rows-none: none;

  /* 网格列跨度配置 */
  --col-auto: auto;
  --col-span-1: span 1 / span 1;
  --col-span-2: span 2 / span 2;
  --col-span-3: span 3 / span 3;
  --col-span-4: span 4 / span 4;
  --col-span-5: span 5 / span 5;
  --col-span-6: span 6 / span 6;
  --col-span-7: span 7 / span 7;
  --col-span-8: span 8 / span 8;
  --col-span-9: span 9 / span 9;
  --col-span-10: span 10 / span 10;
  --col-span-11: span 11 / span 11;
  --col-span-12: span 12 / span 12;
  --col-span-full: 1 / -1;

  /* 网格行跨度配置 */
  --row-auto: auto;
  --row-span-1: span 1 / span 1;
  --row-span-2: span 2 / span 2;
  --row-span-3: span 3 / span 3;
  --row-span-4: span 4 / span 4;
  --row-span-5: span 5 / span 5;
  --row-span-6: span 6 / span 6;
  --row-span-full: 1 / -1;

  /* 最大宽度配置 */
  --max-w-none: none;
  --max-w-0: 0rem;
  --max-w-xs: 20rem;
  --max-w-sm: 24rem;
  --max-w-md: 28rem;
  --max-w-lg: 32rem;
  --max-w-xl: 36rem;
  --max-w-2xl: 42rem;
  --max-w-3xl: 48rem;
  --max-w-4xl: 50rem;
  --max-w-5xl: 60rem;
  --max-w-6xl: 72rem;
  --max-w-7xl: 80rem;
  --max-w-full: 100%;
  --max-w-min: min-content;
  --max-w-max: max-content;
  --max-w-prose: 65ch;
  --max-w-screen-sm: 640px;
  --max-w-screen-md: 768px;
  --max-w-screen-lg: 1024px;
  --max-w-screen-xl: 1280px;
  --max-w-screen-2xl: 1536px;

  /* 最小宽度配置 */
  --min-w-0: 0px;
  --min-w-full: 100%;
  --min-w-min: min-content;
  --min-w-max: max-content;

  /* 最大高度配置 */
  --max-h-0: 0px;
  --max-h-full: 100%;
  --max-h-screen: 100vh;
  --max-h-min: min-content;
  --max-h-max: max-content;

  /* 最小高度配置 */
  --min-h-0: 0px;
  --min-h-full: 100%;
  --min-h-screen: 100vh;
  --min-h-min: min-content;
  --min-h-max: max-content;

  /* 高度配置 */
  --h-auto: auto;
  --h-0: 0px;
  --h-1: 0.25rem;
  --h-2: 0.5rem;
  --h-3: 0.75rem;
  --h-4: 1rem;
  --h-5: 1.25rem;
  --h-6: 1.5rem;
  --h-8: 2rem;
  --h-10: 2.5rem;
  --h-12: 3rem;
  --h-14: 3.5rem;
  --h-16: 4rem;
  --h-20: 5rem;
  --h-24: 6rem;
  --h-28: 7rem;
  --h-32: 8rem;
  --h-36: 9rem;
  --h-40: 10rem;
  --h-44: 11rem;
  --h-48: 12rem;
  --h-52: 13rem;
  --h-56: 14rem;
  --h-60: 15rem;
  --h-64: 16rem;
  --h-72: 18rem;
  --h-80: 20rem;
  --h-96: 24rem;
  --h-full: 100%;
  --h-screen: 100vh;

  /* 定位配置 */
  --inset-auto: auto;
  --inset-0: 0px;
  --inset-1: 0.25rem;
  --inset-2: 0.5rem;
  --inset-3: 0.75rem;
  --inset-4: 1rem;
  --inset-5: 1.25rem;
  --inset-6: 1.5rem;
  --inset-8: 2rem;
  --inset-10: 2.5rem;
  --inset-12: 3rem;
  --inset-16: 4rem;
  --inset-20: 5rem;
  --inset-24: 6rem;
  --inset-32: 8rem;
  --inset-40: 10rem;
  --inset-48: 12rem;
  --inset-56: 14rem;
  --inset-64: 16rem;
  --inset-1\/2: 50%;
  --inset-1\/3: 33.333333%;
  --inset-2\/3: 66.666667%;
  --inset-1\/4: 25%;
  --inset-2\/4: 50%;
  --inset-3\/4: 75%;
  --inset-full: 100%;
  --inset-n1\/2: -50%;
  --inset-n1\/3: -33.333333%;
  --inset-n2\/3: -66.666667%;
  --inset-n1\/4: -25%;
  --inset-n2\/4: -50%;
  --inset-n3\/4: -75%;
  --inset-n-full: -100%;

  /* 行间距配置 */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  /* 列表样式配置 */
  --list-none: none;
  --list-disc: disc;
  --list-decimal: decimal;

  /* 对象位置配置 */
  --object-bottom: bottom;
  --object-center: center;
  --object-left: left;
  --object-left-bottom: left bottom;
  --object-left-top: left top;
  --object-right: right;
  --object-right-bottom: right bottom;
  --object-right-top: right top;
  --object-top: top;

  /* 对象适应方式配置 */
  --object-contain: contain;
  --object-cover: cover;
  --object-fill: fill;
  --object-none: none;
  --object-scale-down: scale-down;

  /* 宽度配置 */
  --w-auto: auto;
  --w-0: 0px;
  --w-1: 0.25rem;
  --w-2: 0.5rem;
  --w-3: 0.75rem;
  --w-4: 1rem;
  --w-5: 1.25rem;
  --w-6: 1.5rem;
  --w-8: 2rem;
  --w-10: 2.5rem;
  --w-12: 3rem;
  --w-14: 3.5rem;
  --w-16: 4rem;
  --w-20: 5rem;
  --w-24: 6rem;
  --w-28: 7rem;
  --w-32: 8rem;
  --w-36: 9rem;
  --w-40: 10rem;
  --w-44: 11rem;
  --w-48: 12rem;
  --w-52: 13rem;
  --w-56: 14rem;
  --w-60: 15rem;
  --w-64: 16rem;
  --w-72: 18rem;
  --w-80: 20rem;
  --w-96: 24rem;
  --w-1\/2: 50%;
  --w-1\/3: 33.333333%;
  --w-2\/3: 66.666667%;
  --w-1\/4: 25%;
  --w-2\/4: 50%;
  --w-3\/4: 75%;
  --w-1\/5: 20%;
  --w-2\/5: 40%;
  --w-3\/5: 60%;
  --w-4\/5: 80%;
  --w-1\/6: 16.666667%;
  --w-2\/6: 33.333333%;
  --w-3\/6: 50%;
  --w-4\/6: 66.666667%;
  --w-5\/6: 83.333333%;
  --w-1\/12: 8.333333%;
  --w-2\/12: 16.666667%;
  --w-3\/12: 25%;
  --w-4\/12: 33.333333%;
  --w-5\/12: 41.666667%;
  --w-6\/12: 50%;
  --w-7\/12: 58.333333%;
  --w-8\/12: 66.666667%;
  --w-9\/12: 75%;
  --w-10\/12: 83.333333%;
  --w-11\/12: 91.666667%;
  --w-full: 100%;
  --w-screen: 100vw;
  --w-min: min-content;
  --w-max: max-content;

  /* 内边距配置 */
  --p-0: 0px;
  --p-1: 0.25rem;
  --p-2: 0.5rem;
  --p-3: 0.75rem;
  --p-4: 1rem;
  --p-5: 1.25rem;
  --p-6: 1.5rem;
  --p-8: 2rem;
  --p-10: 2.5rem;
  --p-12: 3rem;
  --p-14: 3.5rem;
  --p-16: 4rem;
  --p-20: 5rem;
  --p-24: 6rem;
  --p-32: 8rem;
  --p-40: 10rem;
  --p-44: 11rem;
  --p-48: 12rem;
  --p-52: 13rem;
  --p-56: 14rem;
  --p-64: 16rem;
  --p-px: 1px;

  /* 外边距配置 */
  --m-auto: auto;
  --m-0: 0px;
  --m-1: 0.25rem;
  --m-2: 0.5rem;
  --m-3: 0.75rem;
  --m-4: 1rem;
  --m-5: 1.25rem;
  --m-6: 1.5rem;
  --m-8: 2rem;
  --m-10: 2.5rem;
  --m-12: 3rem;
  --m-14: 3.5rem;
  --m-16: 4rem;
  --m-20: 5rem;
  --m-24: 6rem;
  --m-32: 8rem;
  --m-40: 10rem;
  --m-44: 11rem;
  --m-48: 12rem;
  --m-52: 13rem;
  --m-56: 14rem;
  --m-64: 16rem;
  --m-px: 1px;
  --m-n1: -0.25rem;
  --m-n2: -0.5rem;
  --m-n3: -0.75rem;
  --m-n4: -1rem;
  --m-n5: -1.25rem;
  --m-n6: -1.5rem;
  --m-n8: -2rem;
  --m-n10: -2.5rem;
  --m-n12: -3rem;
  --m-n16: -4rem;
  --m-n20: -5rem;
  --m-n24: -6rem;
  --m-n32: -8rem;
  --m-n40: -10rem;
  --m-n48: -12rem;
  --m-n56: -14rem;
  --m-n64: -16rem;

  /* 文本对齐方式 */
  --text-align-left: left;
  --text-align-center: center;
  --text-align-right: right;
  --text-align-justify: justify;

  /* 垂直对齐方式 */
  --align-baseline: baseline;
  --align-top: top;
  --align-middle: middle;
  --align-bottom: bottom;
  --align-text-top: text-top;
  --align-text-bottom: text-bottom;

  /* 缩放度 */
  --scale-0: 0;
  --scale-50: 0.5;
  --scale-75: 0.75;
  --scale-90: 0.9;
  --scale-95: 0.95;
  --scale-100: 1;
  --scale-105: 1.05;
  --scale-110: 1.1;
  --scale-125: 1.25;
  --scale-150: 1.5;

  /* 倾斜度 */
  --skew-0: 0deg;
  --skew-1: 1deg;
  --skew-2: 2deg;
  --skew-3: 3deg;
  --skew-6: 6deg;
  --skew-12: 12deg;
  --skew-n1: -1deg;
  --skew-n2: -2deg;
  --skew-n3: -3deg;
  --skew-n6: -6deg;
  --skew-n12: -12deg;

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  /* button点击样式 */
  button,
  [role='button'] {
    cursor: pointer;
  }
  button:disabled,
  [role='button']:disabled {
    cursor: default;
  }
}

@layer utilities {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 11 86% 54%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 346.8 77.2% 49.8%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 346.8 77.2% 49.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  .font-body {
    font-family: var(--font-family-body);
  }

  .font-heading {
    font-family:
      Poppins,
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      'Noto Sans',
      sans-serif,
      'Apple Color Emoji',
      'Segoe UI Emoji',
      'Segoe UI Symbol',
      'Noto Color Emoji';
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* 隐藏滚动条但保留滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* 自定义react-h5-audio-player样式 - 隐藏默认UI */
.rhap_container {
  display: none !important;
}

.rhap_main-controls {
  display: none !important;
}

.rhap_progress-section {
  display: none !important;
}

.rhap_volume-controls {
  display: none !important;
}

.rhap_additional-controls {
  display: none !important;
}

/* 确保音频元素可以被访问但不显示 */
.rhap_container audio {
  display: none !important;
}
