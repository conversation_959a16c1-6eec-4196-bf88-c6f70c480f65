'use client'
import CardArray from '../components/CardArray'

export default function DailyTarot() {
  return (
    <div className="flex flex-col gap-y-16 w-full justify-center items-center relative">
      <div className="text-4xl mb-3">过去-现在-未来牌阵</div>
      <CardArray
        data={{
          handle: 'past-present-future-spread',
          name: '过去-现在-未来牌阵',
          card_count: 3,
          weight: 3,
          summary:
            '过去-现在-未来牌阵的核心优势在于其简洁性和直观性。通过三个时间维度的视角，它提供了一个完整而连贯的叙事，揭示事件如何随时间演变，以及当前情况可能如何发展。这种牌阵既适合初学者入门，也能为经验丰富的读者提供深刻洞察。',
          tags: ['时间脉络', '全局预判'],
          positions: ['过去牌', '现在牌', '未来牌']
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' }
        ]}
      />

      <div className="text-4xl mb-3">恋人十字牌阵</div>
      <CardArray
        data={{
          handle: 'lovers-cross-spread',
          name: '恋人十字牌阵',
          card_count: 5,
          weight: 10,
          summary:
            '恋人十字五牌阵是一种专为恋爱关系设计的塔罗牌阵，采用十字形排列五张牌，深入探索爱情关系的多个维度。这种牌阵特别关注伴侣之间的情感动态、挑战和潜力，为恋爱关系提供全面而深入的洞察。',
          tags: ['情感链接', '未来走向'],
          positions: ['询问者位', '关系核心位', '伴侣位', '关系基础位', '关系未来位']
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' },
          { handle: '3', img: '/cards/3.webp' },
          { handle: '4', img: '/cards/4.webp' },
          { handle: '5', img: '/cards/5.webp' }
        ]}
      />

      <div className="text-4xl mb-3">直指核心牌阵</div>
      <CardArray
        data={{
          handle: 'direct-core-four-card-spread',
          name: '直指核心牌阵',
          card_count: 4,
          weight: 7,
          summary:
            '直指核心牌阵的主要优势在于其精准性和效率。与更复杂的牌阵相比，它避免了过多细节的干扰，直接聚焦于最关键的信息点，同时仍然提供足够全面的视角。这种平衡使它成为需要快速而深入洞察的理想选择。',
          tags: ['快速切入', '明确方向'],
          positions: ['当前情况牌', '挑战或阻碍牌', '行动建议牌', '结果牌']
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' },
          { handle: '3', img: '/cards/3.webp' }
        ]}
      />

      <div className="text-4xl mb-3">我-对方-我们牌阵</div>
      <CardArray
        data={{
          handle: 'me-you-us-spread',
          name: '我-对方-我们牌阵',
          card_count: 3,
          weight: 6,
          summary:
            '我-对方-我们牌阵的核心价值在于其关系洞察力和平衡视角。它不仅关注个体的需求和贡献，还考察两人如何共同创造关系动态，从而促进更深入的理解和更健康的互动。这种牌阵特别适合任何想要深入了解关系本质的情况。',
          tags: ['人际关系', '情感链接'],
          positions: ['我牌', '对方牌', '我们牌']
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' }
        ]}
      />

      <div className="text-4xl mb-3">凯尔特十字牌阵</div>
      <CardArray
        data={{
          handle: 'celtic-cross',
          name: '凯尔特十字',
          card_count: 10,
          weight: 19,
          summary:
            '凯尔特十字牌阵是塔罗牌中最经典、最广泛使用的牌阵之一。这种结构精巧的十牌布局提供了对询问情况的全面分析，涵盖当前状况、挑战、过去影响、未来可能性以及询问者的心态和环境因素。凯尔特十字牌阵以其深度和全面性著称，适用于几乎任何类型的问题和生活情境。',
          tags: ['复杂剖析', '全盘洞察'],
          positions: [
            '当前状况位',
            '交叉影响位',
            '基础位',
            '过去影响位',
            '可能结果位',
            '近期未来位',
            '自我位',
            '环境位',
            '希望与恐惧位',
            '最终结果位'
          ]
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' },
          { handle: '3', img: '/cards/3.webp' },
          { handle: '4', img: '/cards/4.webp' },
          { handle: '5', img: '/cards/5.webp' },
          { handle: '6', img: '/cards/6.webp' },
          { handle: '7', img: '/cards/7.webp' },
          { handle: '8', img: '/cards/8.webp' },
          { handle: '9', img: '/cards/9.webp' }
        ]}
      />

      <div className="text-4xl mb-3">十二宫牌阵</div>
      <CardArray
        data={{
          handle: 'twelve-houses',
          name: '十二宫',
          card_count: 12,
          weight: 20,
          summary:
            '十二宫牌阵是一种基于占星学十二宫位系统的塔罗牌阵，为询问者提供生活全方位的综合分析。这种深度牌阵将塔罗牌与西方占星学的基础框架相结合，通过映射十二个生活领域，为询问者创建一幅完整的生活全景图。十二宫牌阵特别适合年度预测、全面生活评估或寻求对所有主要生活领域深入了解的情况。',
          tags: ['多面解读', '综合评估'],
          positions: [
            '自我宫',
            '财富宫',
            '沟通宫',
            '家庭宫',
            '创造宫',
            '健康宫',
            '伴侣宫',
            '转化宫',
            '探索宫',
            '事业宫',
            '社群宫',
            '潜意识宫'
          ]
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' },
          { handle: '3', img: '/cards/3.webp' },
          { handle: '4', img: '/cards/4.webp' },
          { handle: '5', img: '/cards/5.webp' },
          { handle: '6', img: '/cards/6.webp' },
          { handle: '7', img: '/cards/7.webp' },
          { handle: '8', img: '/cards/8.webp' },
          { handle: '9', img: '/cards/9.webp' },
          { handle: '10', img: '/cards/10.webp' },
          { handle: '11', img: '/cards/11.webp' }
        ]}
      />

      <div className="text-4xl mb-3">生命之树牌阵</div>
      <CardArray
        data={{
          handle: 'tree-of-life',
          name: '生命之树',
          card_count: 10,
          weight: 18,
          summary:
            '生命之树十牌阵是一种基于卡巴拉生命之树结构的深度塔罗解读方法。这种复杂而全面的牌阵将塔罗牌与卡巴拉神秘学的核心象征系统相结合，为询问者提供关于生活全方位的深入洞察。通过映射十个赛菲罗特（神圣光明的球体）的能量和特质，这种牌阵能够捕捉询问者生活中的多个维度，以及灵性、心理和物质层面的状态与发展。',
          tags: ['精神探索', '深层意义'],
          positions: [
            '克塔位',
            '霍克玛位',
            '比纳位',
            '切赛德位',
            '革布拉位',
            '提法烈特位',
            '涅札位',
            '霍德位',
            '耶索德位',
            '马尔库特位'
          ]
        }}
        cardResults={[
          { handle: '0', img: '/cards/0.webp' },
          { handle: '1', img: '/cards/1.webp' },
          { handle: '2', img: '/cards/2.webp' },
          { handle: '3', img: '/cards/3.webp' },
          { handle: '4', img: '/cards/4.webp' },
          { handle: '5', img: '/cards/5.webp' },
          { handle: '6', img: '/cards/6.webp' },
          { handle: '7', img: '/cards/7.webp' },
          { handle: '8', img: '/cards/8.webp' },
          { handle: '9', img: '/cards/9.webp' },
          { handle: '10', img: '/cards/10.webp' },
          { handle: '11', img: '/cards/11.webp' }
        ]}
      />
    </div>
  )
}
