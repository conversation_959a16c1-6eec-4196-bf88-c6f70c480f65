'use client'
import { ReactNode } from 'react'
import '@/app/[locale]/globals.css'
import { Sidebar } from './components/Sidebar'
import { Topbar } from './components/Topbar'

export default function DashboardLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex min-h-screen bg-muted/40">
      <Sidebar />
      <div className="flex flex-col flex-1 w-full">
        <Topbar />
        <main className="p-4">{children}</main>
      </div>
    </div>
  )
}
