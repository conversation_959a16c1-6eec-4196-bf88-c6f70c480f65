'use client'
import { useState } from 'react'
import CardPicker from '../components/CardPicker'
import type { CardTypes } from '@/types/tarot'

export default function AiDivination() {
  const [resultCards] = useState<CardTypes[]>([
    {
      handle: '1',
      img: '/cards/0.webp'
    },
    {
      handle: '2',
      img: '/cards/1.webp'
    },
    {
      handle: '3',
      img: '/cards/2.webp'
    }
  ])
  return (
    <div className="flex w-full justify-center items-center relative">
      <CardPicker
        resultCards={resultCards}
        cardStyle={{
          width: 80,
          height: 143,
          borderRadius: '8px'
        }}
      />
    </div>
  )
}
