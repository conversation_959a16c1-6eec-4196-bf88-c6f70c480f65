'use client'
import React, { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { motion, useAnimation } from 'framer-motion'
import type { CardTypes } from '@/types/tarot'

interface Card {
  key: number
  style: React.CSSProperties
  angle: number
  removed: boolean
}

interface CardStyle {
  width: number
  height: number
  borderRadius: string
}

interface containerSize {
  width: number
  height: number
}

interface CardPickerProps {
  totalCount?: number
  limtedCount?: number
  shuffleCount?: number
  resultCards: CardTypes[]
  cardBgImg?: string
  cardStyle: CardStyle
  onShuffleStart?: () => void
  onShuffleEnd?: () => void
  onSelected?: (data: CardTypes) => void
}

const CardPicker = ({
  totalCount = 78,
  shuffleCount = 6,
  resultCards = [],
  cardBgImg = '/cards/default-card-bg.webp',
  cardStyle = {
    width: 80,
    height: 143,
    borderRadius: '8px'
  },
  onShuffleStart = () => { },
  // onShuffleEnd 暂未使用
  // onShuffleEnd = () => { },
  onSelected = () => { }
}: CardPickerProps) => {
  let isDragging = false
  let currentRatateAngle = 0
  let currentRotation = 0
  let selectedCardIndex = 0
  const containerRef = useRef<HTMLDivElement>(null)
  const drawCardContainerrRef = useRef<HTMLDivElement>(null)
  const resultContinerRef = useRef<HTMLDivElement>(null)
  const resultCardRef = useRef<HTMLDivElement>(null)
  const [cards, setCards] = useState<Card[]>([])
  const [showBtn, setShowBtn] = useState<boolean>(true)
  const [isShuffleEnd, setIsShuffleEnd] = useState<boolean>(false)
  const [containerSize, setContainerSize] = useState<containerSize>({
    width: 0,
    height: 0
  })
  const controls = useAnimation()

  useEffect(() => {
    const cardsNodes = Array.from(document.querySelectorAll('.tarot-card')) || []
    if (cardsNodes.length) {
      cardsNodes.forEach((node) => {
        node.remove()
      })
    }
    createCards()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const startSequence = async () => {
    await controls.start({
      scale: 2,
      transition: { duration: 0.7, ease: 'easeOut' }
    })
    await controls.start({
      rotateY: 180,
      transition: { duration: 0.5, ease: 'easeIn' }
    })
  }

  const resetSequence = async () => {
    await controls.start({
      scale: 1,
      rotateY: 0
    })
  }

  const getContainerSize = () => {
    const rect = containerRef.current!.getBoundingClientRect()
    return { width: rect.width, height: rect.height }
  }

  const createCards = () => {
    const { width, height } = getContainerSize()
    const newCards = Array.from({ length: totalCount }, (_, i) => {
      const angle = Math.random() * 360
      return {
        key: i,
        style: {
          left: `${Math.random() * (width - cardStyle.width)}px`,
          top: `${Math.random() * (height - cardStyle.height)}px`,
          transform: `rotate(${angle}deg)`,
          zIndex: i + 1,
          backgroundImage: `url(${cardBgImg})`
        },
        angle,
        removed: false
      }
    })
    setCards(newCards)
  }

  const handleShuffleCards = () => {
    let count = 0
    const { width, height } = getContainerSize()
    setShowBtn(false)
    onShuffleStart()
    const shuffleInterval = setInterval(() => {
      const newCards = cards.map((card: Card, i) => {
        const angle = Math.random() * 360
        const el = document.querySelector(`.tarot-card[data-index="${i}"]`) as HTMLDivElement
        const newX = Math.random() * (width - el.offsetWidth)
        const newY = Math.random() * (height - el.offsetHeight)
        card.style = {
          ...card.style,
          left: `${newX}px`,
          top: `${newY}px`,
          transform: `rotate(${angle}deg)`
        }
        return card
      })
      setCards(newCards)
      count++
      if (count >= shuffleCount) {
        clearInterval(shuffleInterval)
        setTimeout(() => {
          stackToCenter().then(dealInCircle)
        }, 500)
      }
    }, 500)
  }

  const stackToCenter = () => {
    const { width, height } = getContainerSize()
    const centerX = (width - cardStyle.width) / 2
    const centerY = (height - cardStyle.height) / 2

    return new Promise<void>((resolve) => {
      const newCards = cards.map((card: Card) => {
        card.style = {
          ...card.style,
          left: `${centerX}px`,
          top: `${centerY}px`,
          transform: `rotate(0deg)`
        }
        return card
      })
      setCards(newCards)
      setTimeout(resolve, 500)
    })
  }

  const dealInCircle = () => {
    const { width, height } = getContainerSize()
    const centerX = (width - cardStyle.width) / 2
    const centerY = (height - cardStyle.height) / 2
    const radius = Math.min(width, height) / 2.5
    const startAngle = (150 * Math.PI) / 180
    const angleStep = (2 * Math.PI) / totalCount

    if (drawCardContainerrRef.current && containerRef.current) {
      drawCardContainerrRef.current.style.overflow = 'hidden'
      const drawCardContainerrRect = drawCardContainerrRef.current.getBoundingClientRect()
      containerRef.current.style.position = 'absolute'
      containerRef.current.style.left = '0'
      containerRef.current.style.top = `-${drawCardContainerrRect.height * 0.714}px`
      containerRef.current.style.overflow = 'initial'
      drawCardContainerrRef.current.style.height = `${drawCardContainerrRect.height * 0.286 + 60}px`
    }

    cards.forEach((_, i) => {
      setTimeout(
        () => {
          const angle = startAngle - i * angleStep
          const x = centerX + radius * Math.cos(angle)
          const y = centerY + radius * Math.sin(angle)
          const rotationAngle = angle + Math.PI / 2
          setCards((prevCards) => {
            const newCards = [...prevCards]
            const updatedCard = {
              ...newCards[i],
              style: {
                ...newCards[i].style,
                left: `${x}px`,
                top: `${y}px`,
                transform: `rotate(${rotationAngle}rad)`,
                zIndex: i + 1,
                cursor: 'pointer'
              },
              angle: rotationAngle
            }
            newCards[i] = updatedCard
            return newCards
          })
          if (i === 28) {
            setIsShuffleEnd(true)
          }
        },
        i > 28 ? 0 : i * 100
      )
    })

    setContainerSize({
      width,
      height
    })

    addEventListeners()
  }

  const handleSelectedCard = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isShuffleEnd) return
    e.stopPropagation()
    const { width, height } = containerSize
    const target = e.target as HTMLDivElement
    const i = Number(target.dataset.index)
    const startAngle = (150 * Math.PI) / 180
    const centerX = (width - cardStyle.width) / 2
    const centerY = (height - cardStyle.height) / 2
    const radius = Math.min(width, height) / 2.5
    const angleStep = (2 * Math.PI) / totalCount
    if (selectedCardIndex >= resultCards.length) return
    if (resultContinerRef.current?.style?.display === 'block') return
    const angle = startAngle - i * angleStep
    const newX = centerX + (radius + 35) * Math.cos(angle)
    const newY = centerY + (radius + 35) * Math.sin(angle)
    setCards((prevCards) => {
      const newCards = [...prevCards]
      const updatedCard = {
        ...newCards[i],
        style: {
          ...newCards[i].style,
          left: `${newX}px`,
          top: `${newY}px`
        },
        removed: true
      }
      newCards[i] = updatedCard
      return newCards
    })

    setTimeout(() => {
      target.remove()
      onSelected(resultCards[selectedCardIndex + 1])
      selectedCardIndex += 1
      if (resultContinerRef.current && resultCardRef.current) {
        resultContinerRef.current.style.display = 'block'
        startSequence()
        const backCard = resultContinerRef.current?.querySelector('.back') as HTMLDivElement
        backCard.style.backgroundImage = `url(${resultCards[selectedCardIndex - 1]?.img})`
        setTimeout(() => {
          resultContinerRef.current!.style.display = 'none'
          resetSequence()
        }, 2000)
      }
    }, 500)
  }

  const getAngle = (x: number, y: number) => {
    const rect = containerRef.current!.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    return (Math.atan2(y - centerY, x - centerX) * 180) / Math.PI
  }

  const startDrag = (x: number, y: number) => {
    isDragging = true
    currentRatateAngle = getAngle(x, y) - currentRotation
  }

  const onDrag = (x: number, y: number) => {
    if (!isDragging) return
    const angle = getAngle(x, y)
    currentRotation = angle - currentRatateAngle
    if (containerRef.current) {
      containerRef.current.style.rotate = `${currentRotation}deg`
    }
  }

  const endDrag = () => {
    isDragging = false
  }

  const addEventListeners = () => {
    const target = drawCardContainerrRef.current
    if (!target) return

    // Mouse events
    target.addEventListener('mousedown', (e) => startDrag(e.clientX, e.clientY))
    target.addEventListener('mousemove', (e) => onDrag(e.clientX, e.clientY))
    target.addEventListener('mouseup', endDrag)
    target.addEventListener('mouseleave', endDrag)

    // Touch events
    target.addEventListener('touchstart', (e) => {
      const touch = e.touches[0]
      if (touch) startDrag(touch.clientX, touch.clientY)
    })

    target.addEventListener('touchmove', (e) => {
      const touch = e.touches[0]
      if (touch) onDrag(touch.clientX, touch.clientY)
    })

    target.addEventListener('touchend', endDrag)
    target.addEventListener('touchcancel', endDrag)
  }

  return (
    <div>
      <div
        className="relative w-[90vw] h-[90vw] max-w-[700px] max-h-[700px] overflow-hidden sm:overflow-visible"
        ref={drawCardContainerrRef}
      >
        <div
          className="relative w-[90vw] h-[90vw] max-w-[700px] max-h-[700px] rounded-[10px]"
          ref={containerRef}
        >
          {cards.map((card) => (
            <div
              data-index={card.key}
              className="absolute w-[80px] h-[143px] rounded-[8px] bg-cover transition-[left,top,transform] duration-400 ease-[ease] rotate-0 tarot-card"
              style={card.style}
              key={card.key}
              onClick={handleSelectedCard}
            ></div>
          ))}
        </div>
      </div>
      <div
        className="relative"
        style={{ display: 'none', height: cardStyle.height * 2 }}
        ref={resultContinerRef}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex justify-center items-center z-[9999]">
          <motion.div
            className="relative"
            initial={{ scale: 1, rotateY: 0 }}
            animate={controls}
            style={{
              ...cardStyle,
              transformStyle: 'preserve-3d',
              backfaceVisibility: 'hidden',
              perspective: '600px'
            }}
            ref={resultCardRef}
          >
            <div
              className="absolute top-0 left-0 bg-cover bg-no-repeat"
              style={{
                ...cardStyle,
                backgroundImage: `url(${cardBgImg})`,
                backfaceVisibility: 'hidden'
              }}
            ></div>
            <div
              className="absolute top-0 left-0 rotate-y-180 bg-cover bg-no-repeat back"
              style={{ ...cardStyle, backfaceVisibility: 'hidden' }}
            ></div>
          </motion.div>
        </div>
      </div>

      {showBtn && (
        <div className="flex justify-center mt-4">
          <Button variant="default" type="button" onClick={handleShuffleCards}>
            洗牌
          </Button>
        </div>
      )}
    </div>
  )
}

export default CardPicker
