'use client'
import React, { useEffect, useState } from 'react'
import type { CardArrayTypes, CardTypes } from '@/types/tarot'

interface CardStyle {
  width: number
  height: number
  borderRadius: string
}

interface CardArrayProps {
  data: CardArrayTypes
  showSummary?: boolean
  cardBg?: string
  selectdCount?: number
  cardResults?: CardTypes[]
  cardStyle?: CardStyle
}

const CardArrayConfig = {
  'single-card-reading': {
    layout: 'default', // 从左向右
    count: 1
  },
  'situation-action-outcome-spread': {
    layout: 'default',
    count: 3
  },
  'past-present-future-spread': {
    layout: 'default',
    count: 3
  },
  'obstacle-resource-advice-spread': {
    layout: 'default',
    count: 3
  },
  'thoughts-emotions-behavior-spread': {
    layout: 'default',
    count: 3
  },
  'job-interview-five-card-spread': {
    layout: 'default',
    count: 5
  },
  'goal-achievement-spread': {
    layout: 'default',
    count: 5
  },
  'progress-five-card-spread': {
    layout: 'default',
    count: 5
  },
  'me-you-us-spread': {
    layout: 'triangle', // 倒三角型
    count: 5
  },
  'direct-core-four-card-spread': {
    layout: 'core', // 直指核心字型
    count: 4
  },
  'relationship-spread': {
    layout: 'cross', // 十字型
    count: 5,
    sort: [2, 0, 1, 4, 3]
  },
  'lovers-cross-spread': {
    layout: 'cross',
    count: 5,
    sort: [2, 0, 1, 4, 3]
  },
  'soul-growth-five-card-spread': {
    layout: 'cross',
    count: 5,
    sort: [4, 1, 2, 3, 0]
  },
  'inner-exploration-five-card-spread': {
    layout: 'cross',
    count: 5,
    sort: [2, 1, 4, 3, 0]
  },
  'decision-five-card-spread': {
    layout: 'cross',
    count: 5,
    sort: [2, 0, 4, 1, 3]
  },
  'either-or-five-card-spread': {
    layout: 'square-brackets', // ]字型
    count: 5,
    sort: [0, 2, 4, 1, 3]
  },
  'career-path-seven-card-spread': {
    layout: 'fighter', // 战斗飞机型
    count: 7
  },
  'tree-of-life': {
    layout: 'tree-of-life', // 生命之树型
    count: 7
  },
  'celtic-cross': {
    layout: 'celtic-cross', // 凯尔特十字型
    count: 10
  },
  'twelve-houses': {
    layout: 'twelve-houses', // 十二宫型
    count: 12,
    sort: [8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 10, 9]
  }
} as any

const CardArray = ({
  data,
  // 以下参数暂未使用
  // showSummary = false,
  // cardBg = '/cards/default-card-bg.webp',
  // selectdCount = 0,
  cardResults = [],
  cardStyle = { width: 60, height: 100, borderRadius: '8px' }
}: CardArrayProps) => {
  const config = CardArrayConfig[data.handle]
  const [cards] = useState<CardTypes[]>(cardResults)

  useEffect(() => { }, [cardResults])

  const DefaultLayout = () => {
    return (
      <div className="flex justify-center gap-10">
        {cards.map((card, index) => {
          return (
            <div key={index} style={cardStyle}>
              <img className="w-full h-full" src={card.img} alt={`Card ${index}`} />
            </div>
          )
        })}
      </div>
    )
  }

  const CrossLayout = () => {
    return (
      <div className="grid grid-cols-3 gap-y-10 gap-x-20">
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[0].img} />
        </div>
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[1].img} />
        </div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[2].img} />
        </div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[3].img} />
        </div>
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[4].img} />
        </div>
        <div style={cardStyle}></div>
      </div>
    )
  }

  const CoreLayout = () => {
    return (
      <div className="grid grid-cols-3 gap-y-10 gap-x-20">
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img src={cards[1].img} />
        </div>
        <div style={cardStyle}></div>
        <div className="relative" style={cardStyle}>
          <img
            className="w-full h-full absolute top-[-20px]"
            style={{
              rotate: '15deg'
            }}
            src={cards[0].img}
          />
        </div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[2].img} />
        </div>
        <div className="relative" style={cardStyle}>
          <img
            className="w-full h-full absolute top-[-20px]"
            style={{
              rotate: '-15deg'
            }}
            src={cards[3].img}
          />
        </div>
      </div>
    )
  }

  const TriangleLayout = () => {
    return (
      <div className="grid grid-cols-3 gap-y-10 gap-x-10">
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[0].img} />
        </div>
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[1].img} />
        </div>
        <div style={cardStyle}></div>
        <div style={cardStyle}>
          <img className="w-full h-full" src={cards[2].img} />
        </div>
        <div style={cardStyle}></div>
      </div>
    )
  }

  const TwelveHousesLayout = () => {
    const getPositionStyle = (index: number) => {
      const angle = ((index - 3) * 30 * Math.PI) / 180 // 转换为弧度，3点钟方向为0°
      const radius = 250 // 半径
      const x = radius * Math.cos(angle)
      const y = radius * Math.sin(angle)

      return {
        transform: `translate(${x}px, ${y}px)`
      }
    }

    return (
      <div className="min-w-[500px] min-h-[500px] rounded-full relative flex items-center justify-center mx-auto my-10">
        {cards.map((card: any, index) => (
          <div
            key={card.handle}
            className="absolute text-lg font-bold text-gray-800"
            style={{
              ...getPositionStyle(index + 1),
              width: '60px',
              height: '100px'
            }}
          >
            <img className="w-full h-full" src={cards[config.sort[index]].img} />
          </div>
        ))}
      </div>
    )
  }

  const CelticCrossLayout = () => {
    return (
      <div className="flex items-center gap-24">
        <div className="grid grid-cols-3 gap-y-10 gap-x-20">
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[3].img} />
          </div>
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[4].img} />
          </div>
          <div className="relative">
            <img style={cardStyle} src={cards[0].img} />
            <img
              style={{
                ...cardStyle,
                bottom: `-${(cardStyle.height - cardStyle.width) / 2}px`
              }}
              className="absolute z-10 rotate-90"
              src={cards[1].img}
            />
          </div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[5].img} />
          </div>
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[3].img} />
          </div>
          <div style={cardStyle}></div>
        </div>
        <div className="flex flex-col gap-4">
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[9].img} />
          </div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[8].img} />
          </div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[7].img} />
          </div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[6].img} />
          </div>
        </div>
      </div>
    )
  }

  const LifeOfTreeLayout = () => {
    return (
      <div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[0].img} />
          </div>
          <div style={cardStyle}></div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[2].img} />
          </div>
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[1].img} />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative mt-10"></div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[4].img} />
          </div>
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[3].img} />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[5].img} />
          </div>
          <div style={cardStyle}></div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[7].img} />
          </div>
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[6].img} />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative">
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[10].img} />
          </div>
          <div style={cardStyle}></div>
        </div>
        <div className="grid grid-cols-3 gap-y-10 gap-x-20 relative mt-10">
          <div style={cardStyle}></div>
          <div style={cardStyle}>
            <img className="w-full h-full" src={cards[11].img} />
          </div>
          <div style={cardStyle}></div>
        </div>
      </div>
    )
  }

  const renderCardArray = () => {
    switch (config.layout) {
      case 'default':
        return <DefaultLayout />
      case 'cross':
        return <CrossLayout />
      case 'core':
        return <CoreLayout />
      case 'triangle':
        return <TriangleLayout />
      case 'twelve-houses':
        return <TwelveHousesLayout />
      case 'celtic-cross':
        return <CelticCrossLayout />
      case 'tree-of-life':
        return <LifeOfTreeLayout />
    }
  }

  return renderCardArray() as JSX.Element
}

export default CardArray
