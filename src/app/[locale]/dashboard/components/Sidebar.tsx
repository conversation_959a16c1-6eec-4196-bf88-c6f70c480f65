'use client'

import { Home, LineC<PERSON>, Settings } from 'lucide-react'
import Link from 'next/link'

export function Sidebar() {
  return (
    <aside className="hidden md:block w-64 bg-white border-r shadow-sm p-4 space-y-4">
      <h1 className="text-xl font-bold px-2">My Dashboard</h1>
      <nav className="space-y-2">
        <Link
          href="/dashboard"
          className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-muted"
        >
          <Home className="w-5 h-5" />
          <span>Home</span>
        </Link>
        <Link
          href="/dashboard/analytics"
          className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-muted"
        >
          <LineChart className="w-5 h-5" />
          <span>Analytics</span>
        </Link>
        <Link
          href="/dashboard/settings"
          className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-muted"
        >
          <Settings className="w-5 h-5" />
          <span>Settings</span>
        </Link>
      </nav>
    </aside>
  )
}
