import Image from "next/image";

export default function EditorsSection() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap items-center -m-4">
          <div className="w-full lg:w-1/2 p-4">
            <h2 className="font-heading font-bold text-4xl mb-16 max-w-md">100+ Expert Book Editors Review and Refine the Summaries</h2>
            <div className="flex gap-4 mb-6">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">100+ Expert Editors</h3>
                <p className="text-gray-500 max-w-sm">Over 100 expert editors review and enhance every summary, ensuring accuracy, clarity, and quality.</p>
              </div>
            </div>
            <div className="flex gap-4 mb-6">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">10+ Years of Expertise in Book Editing</h3>
                <p className="text-gray-500 max-w-sm">Backed by over a decade of book editing experience, we deliver professional, trustworthy summaries you can rely on.</p>
              </div>
            </div>
            <div className="flex gap-4">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">10000+ Premium Book Summaries</h3>
                <p className="text-gray-500 max-w-sm">Explore 10,000+ high-quality book summaries, offering key insights, chapter breakdowns, and smart analysis.</p>
              </div>
            </div>
          </div>
          <div className="w-full lg:w-1/2 p-4">
            <div className="max-w-xl lg:ml-auto relative">
              <Image src='/images/home-summary.png' alt="Book Summaries" width={576} height={390} className="w-full" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
