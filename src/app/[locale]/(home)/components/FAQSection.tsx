'use client'

import { useState } from 'react'

interface FAQItem {
  question: string
  answer: string
}

const faqItems: FAQItem[] = [
  {
    question: "What is included with my subscription?",
    answer: "You get access to 10,000+ book summaries, downloadable PDFs/EPUBs, audiobook summaries, and premium support."
  },
  {
    question: "Can I download summaries to read offline?",
    answer: "Yes! You can download all book summaries in PDF or EPUB format and read them anytime, anywhere."
  },
  {
    question: "Are audiobooks full versions or summaries?",
    answer: "We offer audiobook summaries, giving you the key ideas and main points in a quick, easy-to-listen format."
  },
  {
    question: "How often are new book summaries added?",
    answer: "We update our library regularly, adding new summaries every week to keep you up-to-date with the latest titles."
  },
  {
    question: "Can I cancel my subscription anytime?",
    answer: "Absolutely. You can cancel your monthly or yearly subscription at any time through your account settings."
  }
]

export default function FAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="flex justify-center mb-6">
          <div className="bg-yellow-100 inline-block px-2 py-1 text-xs font-bold">FAQ</div>
        </div>
        <h2 className="font-heading text-4xl lg:text-5xl text-center mx-auto mb-8 font-bold max-w-md lg:max-w-xl">Frequently Asked Questions</h2>
        <p className="text-gray-600 max-w-lg mx-auto text-center mb-8">Find quick answers about subscriptions, downloads, audiobook summaries, cancellations, and more in our FAQ section.</p>
        <div className="flex flex-wrap justify-center mb-16"></div>

        <div className="max-w-3xl mx-auto">
          <ul>
            {faqItems.map((item, index) => (
              <li
                key={index}
                className={`${index < faqItems.length - 1 ? 'mb-8 px-6 pb-8 border-b border-gray-100' : 'px-6 pb-4'}`}
              >
                <a
                  className="flex justify-between items-center w-full"
                  onClick={(e) => {
                    e.preventDefault()
                    toggleAccordion(index)
                  }}
                  href="#"
                >
                  <h3 className="text-gray-600 text-xl font-bold">{item.question}</h3>
                  <span className={`transform ${openIndex === index ? 'rotate-180' : ''}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M15.1746 10.1204C15.3843 9.94067 15.6999 9.96495 15.8796 10.1746C16.0593 10.3843 16.0351 10.6999 15.8254 10.8796L12.3254 13.8796C12.1382 14.0401 11.8619 14.0401 11.6746 13.8796L8.17461 10.8796C7.96495 10.6999 7.94067 10.3843 8.12038 10.1746C8.30009 9.96495 8.61574 9.94067 8.8254 10.1204L12 12.8415L15.1746 10.1204Z" fill="#001410"></path>
                    </svg>
                  </span>
                </a>
                <div
                  className={`overflow-hidden transition-all duration-500 ${openIndex === index ? 'max-h-50' : 'max-h-0'}`}
                >
                  <p className="text-gray-600 mt-6">{item.answer}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  )
}
