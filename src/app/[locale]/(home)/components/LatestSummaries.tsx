'use client'

import { <PERSON> } from 'next-view-transitions'
import { BookListItem } from '@/types/book.types'
import { urlGenerator } from '@/services/url.service'
import BookCarousel from '@/components/common/BookCarousel';
import BookCover from '@/components/common/BookCover';

// 定义备用书籍类型，兼容 BookListItem
interface FallbackBook {
  id?: number;
  coverUrl?: string;
  title: string;
  author?: string;
  authors?: string[];
  description?: string;
  subtitle?: string;
}

// 备用数据，当没有传入books时使用
const fallbackBooks: FallbackBook[] = [
  {
    id: 101,
    coverUrl: "https://placehold.co/176x256/cce4cc/333333?text=New+Book+1",
    title: "Four Thousand Weeks",
    author: "<PERSON>",
    authors: ["<PERSON>"],
    description: "Time management for mortals: embracing your limitations to live a more meaningful life."
  },
  {
    id: 102,
    coverUrl: "https://placehold.co/176x256/d8cce4/333333?text=New+Book+2",
    title: "The Almanack of Naval Ravikant",
    author: "<PERSON>",
    authors: ["<PERSON>"],
    description: "A guide to wealth and happiness from the angel investor and philosopher."
  },
  {
    id: 103,
    coverUrl: "https://placehold.co/176x256/e4cccc/333333?text=New+Book+3",
    title: "Essentialism",
    author: "Greg McKeown",
    authors: ["Greg McKeown"],
    description: "The disciplined pursuit of less but better in every aspect of your life."
  },
  {
    id: 104,
    coverUrl: "https://placehold.co/176x256/ccd8e4/333333?text=New+Book+4",
    title: "Range",
    author: "David Epstein",
    authors: ["David Epstein"],
    description: "Why generalists triumph in a specialized world: the power of diverse experiences."
  }
]

interface LatestSummariesProps {
  books?: BookListItem[];
}

export default function LatestSummaries({ books }: LatestSummariesProps) {
  console.log('LatestSummaries books:', books)
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="font-heading text-3xl font-bold tracking-tight mb-10 text-center">Latest Summaries</h2>
        <BookCarousel containerRef="latestScrollContainer">
          {(books && books.length > 0 ? books : fallbackBooks).map((book, index) => {
            const bookDetailUrl = urlGenerator.book.detail({
              id: book.id || index,
              title: book.title
            });

            return (
              <Link
                key={index}
                href={bookDetailUrl}
                className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col cursor-pointer"
                aria-label={`Read summary of ${book.title} by ${book.authors ? book.authors.join(', ') : 'Unknown Author'}`}
              >
                <div className="p-4 flex justify-center">
                  <BookCover
                    coverUrl={book.coverUrl}
                    title={book.title}
                    width={176}
                    height={256}
                    className="mx-auto"
                    fallbackColor="cce4cc"
                    priority={index < 2} // 优先加载前两个图片
                  />
                </div>
                <div className="p-4 flex flex-col flex-grow">
                  <h3 className="font-bold mb-2">{book.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {book.authors ? book.authors.join(', ') : 'Unknown Author'}
                  </p>
                  <p className="text-sm text-gray-500 mb-4 flex-grow">
                    {book.subtitle || 'No description available'}
                  </p>
                  <span className="text-green-500 font-semibold text-sm hover:text-green-600 mt-auto">
                    Read summary →
                  </span>
                </div>
              </Link>
            )
          })}
        </BookCarousel>
      </div>
    </section>
  )
}
