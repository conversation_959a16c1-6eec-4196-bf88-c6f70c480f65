'use client'

import { useLocale, useTranslations } from 'next-intl'
import { usePathname } from '@/i18n/routing'

export default function LocaleDisplay() {
  // 使用useLocale hook获取当前语言
  const locale = useLocale()
  
  // 使用usePathname获取当前路径
  const pathname = usePathname()
  
  // 使用useTranslations获取翻译
  const t = useTranslations('Metadata')
  
  return (
    <div className="p-4 bg-gray-100 rounded-lg mb-4">
      <h3 className="text-lg font-medium mb-2">当前语言信息</h3>
      <p><strong>当前语言:</strong> {locale}</p>
      <p><strong>当前路径:</strong> {pathname}</p>
      <p><strong>标题翻译示例:</strong> {t('title')}</p>
    </div>
  )
}
