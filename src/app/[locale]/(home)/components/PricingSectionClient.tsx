'use client'
import React from 'react'
import { User } from '@/store/userStore'
import { PricePlan } from '@/services/server/payService'
import PricingCard from '@/components/common/pricingModal/PricingCard'

/**
 * PricingSection 客户端组件属性
 */
interface PricingSectionClientProps {
  /** 用户信息 */
  user: User | null
  /** 年度计划 */
  yearlyPlan?: PricePlan | null
  /** 月度计划 */
  monthlyPlan?: PricePlan | null
}

/**
 * PricingSection 的客户端组件
 *
 * 负责处理支付逻辑，保持原有的 UI 样式不变
 * 使用新的 PaymentButton 组件来处理支付流程
 */
export default function PricingSectionClient({
  user,
  yearlyPlan,
  monthlyPlan
}: PricingSectionClientProps) {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap items-center -m-4">
          {/* 左侧描述区域 */}
          <div className="w-full lg:w-1/3 p-4">
            <h2 className="font-heading text-4xl font-bold mb-6">Pricing & Plans</h2>
            <p className="text-gray-500 max-w-xs">
              Get 10,000+ book summaries, PDF/EPUB downloads, audiobook access & premium support for $5.99/month or $35.99/year.
            </p>
          </div>

          {/* 年度计划卡片 */}
          {yearlyPlan && (
            <PricingCard
              plan={yearlyPlan}
              user={user}
              mode="page"
            />
          )}

          {/* 月度计划卡片 */}
          {monthlyPlan && (
            <PricingCard
              plan={monthlyPlan}
              user={user}
              mode="page"
            />
          )}
        </div>
      </div>
    </section>
  )
}
