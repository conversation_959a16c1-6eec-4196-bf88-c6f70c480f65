'use client'

import { <PERSON> } from 'next-view-transitions'
import { BookListItem } from '@/types/book.types'
import { urlGenerator } from '@/services/url.service'
import BookCarousel from '@/components/common/BookCarousel';
import BookCover from '@/components/common/BookCover';

// 定义备用书籍类型，兼容 BookListItem
interface FallbackBook {
  id?: number;
  coverUrl?: string;
  title: string;
  author?: string;
  authors?: string[];
  description?: string;
  subtitle?: string;
}

// 备用数据，当没有传入books时使用
const fallbackBooks: FallbackBook[] = [
  {
    id: 201,
    coverUrl: "https://placehold.co/176x256/e2f4ea/1a7a4c?text=Featured+Book+1",
    title: "The Power of Habit",
    author: "<PERSON>",
    authors: ["<PERSON>"],
    description: "Why we do what we do in life and business: the science of habit formation and change."
  },
  {
    id: 202,
    coverUrl: "https://placehold.co/176x256/f4e2ea/7a1a4c?text=Featured+Book+2",
    title: "Sapie<PERSON>",
    author: "<PERSON><PERSON>",
    authors: ["<PERSON><PERSON>"],
    description: "A brief history of humankind: how we came to rule the world."
  },
  {
    id: 203,
    coverUrl: "https://placehold.co/176x256/e2eaf4/1a4c7a?text=Featured+Book+3",
    title: "Mindset",
    author: "Carol S. Dweck",
    authors: ["Carol S. Dweck"],
    description: "The new psychology of success: how we can learn to fulfill our potential."
  },
  {
    id: 204,
    coverUrl: "https://placehold.co/176x256/e2d8cc/333333?text=Featured+Book+4",
    title: "Outliers",
    author: "Malcolm Gladwell",
    authors: ["Malcolm Gladwell"],
    description: "The story of success and what makes high-achievers different."
  }
]

interface MonthlyPickedSummariesProps {
  books?: BookListItem[];
}

export default function MonthlyPickedSummaries({ books }: MonthlyPickedSummariesProps) {
  // 使用传入的books或fallbackBooks
  const displayBooks = books && books.length > 0 ? books : fallbackBooks;

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="font-heading text-3xl font-bold tracking-tight mb-10 text-center">Monthly Picked Summaries</h2>
        <BookCarousel containerRef="scrollContainerMonthly">
          {displayBooks.map((book, index) => {
            const bookDetailUrl = urlGenerator.book.detail({
              id: book.id || index,
              title: book.title
            });
            return (
              <Link
                key={index}
                href={bookDetailUrl}
                className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col cursor-pointer"
                aria-label={`Read summary of ${book.title} by ${book.authors ? book.authors.join(', ') : 'Unknown Author'}`}
              >
                <div className="p-4 flex justify-center">
                  <BookCover
                    coverUrl={book.coverUrl}
                    title={book.title}
                    width={176}
                    height={256}
                    className="mx-auto"
                    fallbackColor="e2f4ea"
                    priority={index < 2} // 优先加载前两个图片
                  />
                </div>
                <div className="p-4 flex flex-col flex-grow">
                  <h3 className="font-bold mb-2">{book.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {book.authors ? book.authors.join(', ') : 'Unknown Author'}
                  </p>
                  <p className="text-sm text-gray-500 mb-4 flex-grow">
                    {book.subtitle || 'No description available'}
                  </p>
                  <span className="text-green-500 font-semibold text-sm hover:text-green-600 mt-auto">
                    Read summary →
                  </span>
                </div>
              </Link>
            )
          })}
        </BookCarousel>
      </div>
    </section>
  )
}
