import Image from "next/image";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap items-center">
          <div className="w-full lg:w-1/2 mb-12 lg:mb-0">
            <span className="text-green-500 font-semibold uppercase tracking-wider">READ SMARTER, NOT HARDER</span>
            <h1 className="font-heading font-bold text-4xl md:text-6xl tracking-tight mb-6 mt-3">Best Book Summaries within 15 Minutes</h1>
            <p className="text-lg mb-8 text-gray-600">15Minutes.ai offers AI-driven Book Summaries, Audio Books, and Podcasts with detailed chapter analysis and plots. 100+ editors refine summaries from over 100,000 books into quick, quality PDFs!</p>
            <Link className="px-8 py-4 block text-center bg-green-500 text-white font-bold hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200 rounded-md w-48" href="/register">Get Started</Link>
          </div>
          <div className="w-full lg:w-1/2">
            {/* <Image className="w-full rounded-lg shadow-xl" width={600} height={400} src="https://placehold.co/600x400/e2f4ea/1a7a4c?text=Book+Insights" alt="Book summary illustration" /> */}
            <div
              className="relative w-full rounded-lg overflow-hidden"
              style={{
                maxWidth: '677px',
                height: '547px',
                backgroundColor: '#fafaf9'
              }}
            >
              <Image
                className="rounded-lg"
                width={677}
                height={547}
                src="/images/banner.png"
                alt="Book summary illustration"
                style={{
                  objectFit: 'contain',
                  width: '100%',
                  height: '100%',
                  mixBlendMode: 'darken'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
