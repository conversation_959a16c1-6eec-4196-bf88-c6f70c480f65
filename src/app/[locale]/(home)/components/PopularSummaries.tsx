'use client'

import { Link } from 'next-view-transitions'
import BookCarousel from '../../../../components/common/BookCarousel'
import BookCover from '../../../../components/common/BookCover'
import { BookListItem } from '@/types/book.types'
import { urlGenerator } from '@/services/url.service'

interface PopularSummariesProps {
  books?: BookListItem[];
}

export default function PopularSummaries({ books }: PopularSummariesProps) {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="font-heading text-3xl font-bold tracking-tight mb-10 text-center">Popular Summaries</h2>
        <BookCarousel containerRef="popularScrollContainer">
          {(books && books.length > 0 ? books : []).map((book, index) => {
            const bookDetailUrl = urlGenerator.book.detail({
              id: book.id || index,
              title: book.title
            });

            return (
              <Link
                key={index}
                href={bookDetailUrl}
                className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col cursor-pointer"
                aria-label={`Read summary of ${book.title} by ${book.authors ? book.authors.join(', ') : 'Unknown Author'}`}
              >
                <div className="p-4 flex justify-center">
                  <BookCover
                    coverUrl={book.coverUrl}
                    title={book.title}
                    width={176}
                    height={256}
                    className="mx-auto"
                    fallbackColor="e9e1cc"
                    priority={index < 2} // 优先加载前两个图片
                  />
                </div>
                <div className="p-4 flex flex-col flex-grow">
                  <h3 className="font-bold mb-2">{book.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {book.authors ? book.authors.join(', ') : 'Unknown Author'}
                  </p>
                  <p className="text-sm text-gray-500 mb-4 flex-grow">
                    {book.subtitle || 'No description available'}
                  </p>
                  <span className="text-green-500 font-semibold text-sm hover:text-green-600 mt-auto">
                    Read summary →
                  </span>
                </div>
              </Link>
            );
          })}
        </BookCarousel>
      </div>
    </section>
  )
}
