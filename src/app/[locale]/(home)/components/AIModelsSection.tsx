import Image from "next/image";

export default function AIModelsSection() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap items-center -m-4">
          <div className="w-full lg:w-1/2 p-4">
            <div className="max-w-xl lg:mr-auto relative">
              <Image src='/images/home-robot.png' alt="AI Models" width={576} height={390} className="w-full" />
            </div>
          </div>
          <div className="w-full lg:w-1/2 p-4">
            <h2 className="font-heading font-bold text-4xl mb-16 max-w-md">Powered by Cutting-edge AI Models</h2>
            <div className="flex gap-4 mb-6">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">ChatGPT</h3>
                <p className="text-gray-500 max-w-sm">ChatGPT delivers clear, concise book summaries, helping you capture key ideas in minutes.</p>
              </div>
            </div>
            <div className="flex gap-4 mb-6">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">Claude</h3>
                <p className="text-gray-500 max-w-sm">Claude excels at deep, thoughtful book summaries, offering rich insights and detailed analysis.</p>
              </div>
            </div>
            <div className="flex gap-4">
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  <path d="M8 11.5L11 14.5L17 8.5" stroke="#006251" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              </span>
              <div>
                <h3 className="text-xl font-bold mb-3">Genmini</h3>
                <p className="text-gray-500 max-w-sm">Gemini creates fast, accurate book summaries with smart analysis and easy-to-digest takeaways.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
