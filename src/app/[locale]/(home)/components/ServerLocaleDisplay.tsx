import { getLocale, getTranslations } from 'next-intl/server'

export default async function ServerLocaleDisplay() {
  // 使用getLocale获取当前语言
  const locale = await getLocale()
  
  // 使用getTranslations获取翻译
  const t = await getTranslations('Metadata')
  
  return (
    <div className="p-4 bg-green-100 rounded-lg mb-4">
      <h3 className="text-lg font-medium mb-2">服务器组件中的语言信息</h3>
      <p><strong>当前语言:</strong> {locale}</p>
      <p><strong>标题翻译示例:</strong> {t('title')}</p>
    </div>
  )
}
