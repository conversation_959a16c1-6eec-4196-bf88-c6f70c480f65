import React from 'react'
import { getPrice } from '@/services/server/payService'
import { getUserInfo } from '@/services/server/userService'
import PricingSectionClient from './PricingSectionClient'

/**
 * PricingSection 服务器端组件
 *
 * 负责获取价格计划和用户信息，然后传递给客户端组件处理支付逻辑
 * 保持原有的 UI 样式和布局不变
 */
export default async function PricingSection() {
  // 获取价格计划和用户信息
  const plans = await getPrice()
  const userInfo = await getUserInfo()

  // 找到年度和月度计划
  const yearlyPlan = plans?.find(plan => plan.duration === '1 years')
  const monthlyPlan = plans?.find(plan => plan.duration === '1 months')

  return (
    <PricingSectionClient
      user={userInfo}
      yearlyPlan={yearlyPlan}
      monthlyPlan={monthlyPlan}
    />
  )
}
