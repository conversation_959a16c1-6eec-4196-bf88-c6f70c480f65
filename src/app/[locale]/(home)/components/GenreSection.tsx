'use client'

import { Link } from 'next-view-transitions'
import { CategoryListItem } from '@/models/category.model'

// 分类对应的图标和背景色
const categoryIcons: Record<string, { icon: string; bgColor: string; textColor: string }> = {
  'Business': { icon: '📊', bgColor: 'bg-blue-100', textColor: 'text-blue-500' },
  'Psychology': { icon: '🧠', bgColor: 'bg-green-100', textColor: 'text-green-500' },
  'Self-Improvement': { icon: '💡', bgColor: 'bg-purple-100', textColor: 'text-purple-500' },
  'Health': { icon: '❤️', bgColor: 'bg-red-100', textColor: 'text-red-500' },
  'Fiction': { icon: '📚', bgColor: 'bg-yellow-100', textColor: 'text-yellow-500' },
  'History': { icon: '🏛️', bgColor: 'bg-indigo-100', textColor: 'text-indigo-500' },
  'Science': { icon: '🔬', bgColor: 'bg-teal-100', textColor: 'text-teal-500' },
  'Personal Development': { icon: '🚀', bgColor: 'bg-orange-100', textColor: 'text-orange-500' }
};

// 备用分类数据
const fallbackCategories = [
  { id: 1, name: 'Business', description: 'Business books', bookCount: 352 },
  { id: 2, name: 'Psychology', description: 'Psychology books', bookCount: 287 },
  { id: 3, name: 'Self-Improvement', description: 'Self-Improvement books', bookCount: 423 },
  { id: 4, name: 'Health', description: 'Health books', bookCount: 185 },
  { id: 5, name: 'Fiction', description: 'Fiction books', bookCount: 276 },
  { id: 6, name: 'History', description: 'History books', bookCount: 198 },
  { id: 7, name: 'Science', description: 'Science books', bookCount: 245 },
  { id: 8, name: 'Personal Development', description: 'Personal Development books', bookCount: 312 }
];

// 将分类名称转换为 slug 格式
function convertToSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/-+/g, '-'); // 移除连续的连字符
}

interface GenreSectionProps {
  categories?: CategoryListItem[];
}

export default function GenreSection({ categories }: GenreSectionProps) {
  // 使用传入的categories或fallbackCategories
  const displayCategories = categories && categories.length > 0 ? categories : fallbackCategories;

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="font-heading text-3xl font-bold tracking-tight mb-10 text-center">Browse by Genre</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {displayCategories.map((category) => {
            const { icon, bgColor, textColor } = categoryIcons[category.name] ||
              { icon: '📖', bgColor: 'bg-gray-100', textColor: 'text-gray-500' };

            return (
              <Link
                href={`/categories/${convertToSlug(category.name)}`}
                key={category.id}
                className="bg-white rounded-lg shadow-md p-6 transition-transform duration-300 hover:shadow-lg hover:-translate-y-1"
              >
                <div className={`w-12 h-12 ${bgColor} rounded-full flex items-center justify-center ${textColor} mb-4`}>
                  <span className="text-xl">{icon}</span>
                </div>
                <h3 className="font-bold text-lg mb-2">{category.name}</h3>
                <p className="text-gray-500">{category.bookCount} books</p>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  )
}
