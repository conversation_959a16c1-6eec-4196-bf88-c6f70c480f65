import React from 'react';

export default function AboutUsPage() {
  return (
    <div>
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <h1 className="font-heading text-4xl font-bold mb-4">About us</h1>
            <p className="mb-6">Building websites from wireframes that I had received. Some of those questions were:</p>
            <p className="mb-6">These types of questions led me to miss numerous deadlines, and I wasted time and energy in back-and-forth communication. Sadly, this situation could have been avoided if the wireframes had provided enough detail.</p>
            <p>Now that I am a UX designer, I notice that some designers tend to forget that wireframes are equally creative and technical. We are responsible for designing great ideas, but we are also responsible for creating product specifications. I admit that there can be so many details to remember that it&apos;s easy to lose track. To save time and energy for myself, I gathered all of my years of wireframing knowledge into a single checklist that I refer to throughout the process. And now I am sharing this knowledge with you, so that you can get back to being creative.</p>
          </div>
        </div>
      </section>
    </div>
  );
}
