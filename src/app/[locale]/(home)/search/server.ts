import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

/**
 * 生成搜索页面的元数据
 * 这个函数会在服务器端运行，用于生成搜索页面的元数据
 */
export async function generateMetadata({
  params,
  searchParams
}: {
  params: Promise<{ locale: string }>,
  searchParams?: Promise<{ query?: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const resolvedSearchParams = await (searchParams || Promise.resolve({ query: undefined }));
  const { query } = resolvedSearchParams;

  // 获取品牌名称
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const brandName = t('brandName');

  // 如果有搜索查询，则使用查询词构建标题和描述
  if (query) {
    const title = `"${query}" Book Summaries & Analysis | ${brandName}`;
    const description = `Find book summaries and analysis for "${query}" on ${brandName}. Explore concise chapter synopses, plot reviews, and choose to read online, download PDF, or listen to audiobooks.`;

    // 构建规范URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    const canonicalUrl = `${baseUrl}/search?q=${encodeURIComponent(query)}`;

    return {
      title,
      description,
      robots: {
        index: true,
        follow: true,
        nocache: true
      },
      alternates: {
        canonical: canonicalUrl,
        languages: {
          'en-US': `${baseUrl}/search?q=${encodeURIComponent(query)}`
        }
      },
      openGraph: {
        title,
        description,
        url: canonicalUrl,
        type: 'website'
      },
      twitter: {
        card: 'summary',
        title: `"${query}" Book Summaries | ${brandName}`,
        description: `Find book summaries and analysis for "${query}" on ${brandName}.`
      }
    };
  }

  // 默认元数据（无搜索查询时）
  return {
    title: `Search Book Summaries & Analysis | ${brandName}`,
    description: `Search through thousands of book summaries and analyses on ${brandName}. Find your next great read with our comprehensive collection.`,
    robots: {
      index: true,
      follow: true
    }
  };
}
