'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import SearchHeader from './components/SearchHeader';
import BookGrid from './components/BookGrid';
import Pagination from '@/components/common/Pagination';
import { BookListItem, PaginatedResult } from '@/types/book.types';

// 注意：由于这是客户端组件，我们需要在服务器端组件中设置元数据
// 创建一个server.ts文件来处理元数据

// 搜索结果类型（与BookCard组件兼容）
interface SearchResult {
  id: number | string;
  title: string;
  author: string;
  description: string;
  rating: number;
  coverImage: string;
}

// API响应类型
interface ApiResponse {
  success: boolean;
  data?: PaginatedResult<BookListItem>;
  error?: string;
}

// 创建一个包含useSearchParams的组件
function SearchContent() {
  // 获取URL参数和当前语言
  const searchParams = useSearchParams();
  const router = useRouter();
  const locale = useLocale(); // 获取当前用户的语言设置
  const query = searchParams.get('query') || '';
  const pageParam = searchParams.get('page') || '1';
  const currentPage = parseInt(pageParam, 10);

  // 组件状态
  const [searchQuery, setSearchQuery] = useState(query);
  const [books, setBooks] = useState<SearchResult[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // 获取搜索结果
  useEffect(() => {
    if (!query.trim()) return;

    const fetchSearchResults = async () => {
      setIsLoading(true);

      try {
        // 使用当前用户的语言设置
        const response = await fetch(
          `/api/book/search?query=${encodeURIComponent(query)}&page=${currentPage}&limit=12&language=${locale}`
        );

        const data: ApiResponse = await response.json();

        if (data.success && data.data) {
          // 将API返回的数据转换为组件需要的格式
          const searchResults: SearchResult[] = data.data.data.map(book => ({
            id: book.id,
            title: book.title,
            author: book.authors && book.authors.length > 0 ? book.authors[0] : '',
            description: book.subtitle || '',
            rating: book.rating || 0,
            coverImage: book.coverUrl || 'https://placehold.co/176x256'
          }));

          setBooks(searchResults);
          setTotalResults(data.data.meta.total);
          setTotalPages(data.data.meta.totalPages);
        } else {
          console.error(data.error || '搜索失败');
          setBooks([]);
          setTotalResults(0);
          setTotalPages(1);
        }
      } catch (err) {
        console.error('搜索请求失败，请稍后重试', err);
        setBooks([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSearchResults();
  }, [query, currentPage, locale]);

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // 处理搜索表单提交
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    // 更新URL参数并触发新的搜索
    router.push(`/search?query=${encodeURIComponent(searchQuery)}&page=1`);
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    // 更新URL参数并触发新的搜索
    router.push(`/search?query=${encodeURIComponent(query)}&page=${page}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div>
      <SearchHeader
        searchQuery={searchQuery}
        totalResults={totalResults}
        onSearchChange={handleSearchChange}
        onSearch={handleSearch}
      />
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="w-full">
            {isLoading ? (
              // 加载状态显示骨架屏
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="bg-gray-100 rounded-lg h-80 animate-pulse"></div>
                ))}
              </div>
            ) : books.length > 0 ? (
              // 有搜索结果时显示书籍列表
              <BookGrid books={books} />
            ) : query ? (
              // 无搜索结果时显示提示
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No results found for &quot;{query}&quot;</p>
              </div>
            ) : null}

            {totalPages > 1 && (
              <div className="mt-12 flex justify-center">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}

/**
 * 搜索结果页面组件
 */
const SearchResultsPage = () => {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-12">
        <div className="w-full text-center">
          <div className="h-10 w-48 bg-gray-200 rounded animate-pulse mx-auto mb-8"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="bg-gray-100 rounded-lg h-80 animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
};

export default SearchResultsPage;
