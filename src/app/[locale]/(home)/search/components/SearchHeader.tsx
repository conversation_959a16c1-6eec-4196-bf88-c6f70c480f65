import React, { ChangeEvent, FormEvent } from 'react';

interface SearchHeaderProps {
  searchQuery: string;
  totalResults: number;
  onSearchChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onSearch: (e: FormEvent<HTMLFormElement>) => void;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  totalResults,
  onSearchChange,
  onSearch
}) => {
  return (
    <section className="bg-green-50 py-12">
      <div className="container mx-auto px-4">
        <h1 className="font-heading font-bold text-5xl tracking-tight mb-2">Search Results</h1>
        <p className="text-lg mb-4 text-gray-600">Found {totalResults} books for &quot;{searchQuery}&quot;</p>
        <div className="max-w-4xl w-full mx-auto">
          <form onSubmit={onSearch} className="flex">
            <input
              className="flex-grow px-4 py-3 rounded-l-md border border-gray-300 focus:outline-none focus:ring focus:ring-green-400"
              type="text"
              placeholder="Search books..."
              value={searchQuery}
              onChange={onSearchChange}
            />
            <button
              className="bg-green-500 text-white px-4 py-3 rounded-r-md hover:bg-green-600 transition duration-200 flex items-center justify-center"
              type="submit"
            >
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default SearchHeader;
