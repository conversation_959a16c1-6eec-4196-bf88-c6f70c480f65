import { Link } from 'next-view-transitions';
import React from 'react';
import { urlGenerator } from '@/services/url.service';
import Image from 'next/image';

interface Book {
  id: number | string;
  title: string;
  author: string;
  description: string;
  coverImage: string;
  rating: number;
}

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  // 生成书籍详情页面URL
  const bookDetailUrl = urlGenerator.book.detail({
    id: book.id,
    title: book.title
  });

  // 根据评分生成星星
  const renderStars = (rating: number): string => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;
    const stars = [];

    // 添加实心星星
    for (let i = 0; i < fullStars; i++) {
      stars.push('★');
    }

    // 添加半星（这里简化处理，实际上HTML中没有使用半星）
    if (hasHalfStar) {
      stars.push('★');
    }

    // 添加空星星
    while (stars.length < 5) {
      stars.push('☆');
    }

    return stars.join('');
  };

  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-200">
      <Link
        href={bookDetailUrl}
        className="block"
      >
        <Image
          width={176}
          height={256}
          className="w-full object-cover max-w-176 max-h-256"
          src={book.coverImage}
          alt={`${book.title} Cover`}
        />
      </Link>
      <div className="p-4">
        <h2 className="font-bold text-lg mb-1 tracking-tight">{book.title}</h2>
        <p className="text-gray-600 text-sm mb-1">{book.author}</p>
        <p className="text-gray-500 text-xs mb-2 italic">{book.description}</p>
        <div className="flex items-center">
          <span className="text-yellow-500">{renderStars(book.rating)}</span>
          <span className="text-sm text-gray-500 ml-1">{book.rating.toFixed(1)}</span>
        </div>
      </div>
    </div>
  );
};

export default BookCard;
