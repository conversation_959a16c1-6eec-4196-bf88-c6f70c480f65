import { getLocale, getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'
import PopularSummaries from './components/PopularSummaries'
import HowItWorks from './components/HowItWorks'
import LatestSummaries from './components/LatestSummaries'
import MonthlyPickedSummaries from './components/MonthlyPickedSummaries'
import GenreSection from './components/GenreSection'
import AIModelsSection from './components/AIModelsSection'
import EditorsSection from './components/EditorsSection'
import PricingSection from './components/PricingSection'
import FAQSection from './components/FAQSection'
import StatsSection from './components/StatsSection'
import { getHomePageData } from '@/services/book.service'
import { Metadata } from 'next'

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'Metadata' })
  const brandName = t('brandName')

  // 根据SEO规范构建首页元数据
  return {
    title: `${brandName} | Book Summaries & Analysis PDF – Over 5000 Books`,
    description: `Explore 5000+ book summaries & analysis in ${brandName}. Enjoy 15‑min reads, or listen to our podcast and audiobooks. Quick plots, reviews & chapter synopses.`,
    openGraph: {
      title: `${brandName} | Book Summaries & Analysis PDF – Over 5000 Books`,
      description: `Explore 5000+ book summaries & analysis in ${brandName}. Enjoy 15‑min reads, or listen to our podcast and audiobooks. Quick plots, reviews & chapter synopses.`,
      images: [
        {
          url: '/images/og-home.jpg',
          width: 1200,
          height: 630,
          alt: `${brandName} - Book Summaries`
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: `${brandName} | Book Summaries & Analysis`,
      description: `Explore 5000+ book summaries & analysis in ${brandName}. Enjoy 15‑min reads, or listen to our podcast and audiobooks.`,
      images: ['/images/og-home.jpg']
    }
  }
}

export default async function Home() {
  const locale = await getLocale()

  // 使用locale参数获取数据
  const data = await getHomePageData(locale)
  console.log('Home data:', data)

  // 解构获取的数据
  const { popularBooks, newestBooks, monthlyPickedBooks, popularCategories } = data

  return (
    <main>
      <HeroSection />
      {/* 如果没有popularBooks，则使用newestBooks */}
      <PopularSummaries books={popularBooks.length > 0 ? popularBooks : newestBooks} />
      <HowItWorks />
      <LatestSummaries books={newestBooks} />
      <MonthlyPickedSummaries books={monthlyPickedBooks} />
      <GenreSection categories={popularCategories} />
      <AIModelsSection />
      <EditorsSection />
      <PricingSection />
      <FAQSection />
      <StatsSection />
    </main>
  )
}
