'use client'
import { session } from '@/services/client/payService'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/routing'
import { usePaymentSuccess } from '@/hooks/usePaymentSuccess'

export default function PaySuccessClient() {
  const t = useTranslations('PaySuccess')
  const [url, setUrl] = useState<string | undefined>()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<boolean>(false)
  const searchParams = useSearchParams()

  // 使用新的支付成功特效 Hook
  const shouldTriggerConfetti = !loading && !error && !!url
  usePaymentSuccess(shouldTriggerConfetti)

  useEffect(() => {
    const checkSession = async () => {
      setLoading(true)
      setError(false)
      const sessionId = searchParams.get('session_id')
      if (!sessionId) {
        setError(true)
        setLoading(false)
        return
      }
      try {
        const response = await session(sessionId)
        const invoiceUrl = response.data?.order?.invoice_pdf
        if (response?.code === 200 && invoiceUrl) {
          setUrl(invoiceUrl)
        } else {
          setError(true)
        }
      } catch (error) {
        console.error('Failed to check session:', error)
        setError(true)
      } finally {
        setLoading(false)
      }
    }

    checkSession()
  }, [searchParams])

  // 特效已由 usePaymentSuccess Hook 自动处理

  const handleDownload = () => {
    if (url && !loading) {
      try {
        const a = document.createElement('a')
        document.body.appendChild(a)
        a.style.display = 'none'
        // Use the URL to download the file with the correct content type
        a.href = `${url}?response-content-type=application%2Foctet-stream`
        a.rel = 'noopener noreferrer'
        a.click()
        document.body.removeChild(a)
        // Clean up the URL object
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Failed to download invoice:', error)
      }
    }
  }

  return (
    <div
      className="flex flex-col items-center justify-center overflow-hidden"
      style={{
        height: 'calc(100vh - 340px)', // Header (~72px) + Footer (~268px) = 340px
        minHeight: '400px' // 确保在小屏幕上有最小高度
      }}
    >
      <div className="flex flex-col items-center justify-center p-4 max-w-md w-full">
        {/* Success or error icon */}
        <div className={`w-16 h-16 sm:w-20 sm:h-20 ${error ? 'bg-red-600' : 'bg-green-600'} rounded-full flex items-center justify-center mb-4 sm:mb-6 flex-shrink-0`}>
          {error ? (
            <svg className="w-8 h-8 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-8 h-8 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          )}
        </div>

        {/* Title text */}
        <h1 className="text-xl sm:text-2xl font-bold mb-6 sm:mb-8 text-center flex-shrink-0 px-2">
          {t('paymentSuccess')}
        </h1>

        {/* Get started button */}
        <Link
          href="/"
          className="w-full max-w-xs bg-primary text-white py-3 rounded-lg mb-3 sm:mb-4 text-center flex-shrink-0 text-sm sm:text-base"
        >
          {t('getStarted')}
        </Link>

        {/* Download invoice button - only shown if not error and URL is available */}
        {!error && (
          <button
            onClick={handleDownload}
            className={`text-gray-700 underline flex items-center gap-2 flex-shrink-0 text-sm sm:text-base ${loading || !url ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-900'}`}
            disabled={loading || !url}
          >
            {t('downloadVoucher')}
          </button>
        )}
      </div>
    </div>
  )
}
