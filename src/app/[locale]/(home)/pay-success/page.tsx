import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { Suspense } from 'react'
import PaySuccessClient from './PaySuccessClient'

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  const { locale } = params
  const t = await getTranslations({ locale, namespace: 'Metadata' })
  const brandName = t('brandName')

  return {
    title: `Payment Successful | ${brandName}`,
    description: `Your payment has been successfully processed. Thank you for your purchase.`,
    robots: {
      index: false,
      follow: false
    }
  }
}

export default function PaySuccessPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mb-6 animate-pulse">
          <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
        </div>
        <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-8"></div>
        <div className="w-full max-w-xs h-12 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>
    }>
      <PaySuccessClient />
    </Suspense>
  )
}
