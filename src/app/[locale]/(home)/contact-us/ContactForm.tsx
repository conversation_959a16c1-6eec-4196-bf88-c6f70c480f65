import React from 'react';

export default function ContactForm() {
  return (
    <div className="w-full md:w-2/3 bg-white p-8 rounded-lg shadow-sm">
      <h4 className="font-heading text-2xl font-bold tracking-tight mb-6">Send us a message</h4>
      <form>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block mb-2 text-sm font-bold" htmlFor="name">Full Name</label>
            <input
              className="px-6 py-3 w-full border border-gray-100 placeholder-gray-300 text-sm focus:ring focus:ring-gray-200 transition duration-200 outline-none"
              type="text"
              id="name"
              name="name"
              placeholder="Your name"
            />
          </div>
          <div>
            <label className="block mb-2 text-sm font-bold" htmlFor="email">Email Address</label>
            <input
              className="px-6 py-3 w-full border border-gray-100 placeholder-gray-300 text-sm focus:ring focus:ring-gray-200 transition duration-200 outline-none"
              type="email"
              id="email"
              name="email"
              placeholder="Your email"
            />
          </div>
        </div>
        <div className="mb-6">
          <label className="block mb-2 text-sm font-bold" htmlFor="inquiry">Type of Inquiry</label>
          <select
            className="px-6 py-3 w-full border border-gray-100 placeholder-gray-300 text-sm focus:ring focus:ring-gray-200 transition duration-200 outline-none"
            id="inquiry"
            name="inquiry"
            defaultValue=""
          >
            <option value="" disabled>Select your inquiry type</option>
            <option value="general">General Question</option>
            <option value="suggestion">Book Suggestion</option>
            <option value="feedback">Feedback</option>
            <option value="partnership">Partnership Opportunity</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div className="mb-6">
          <label className="block mb-2 text-sm font-bold" htmlFor="message">Message</label>
          <textarea
            className="px-6 py-3 w-full border border-gray-100 placeholder-gray-300 text-sm focus:ring focus:ring-gray-200 transition duration-200 outline-none"
            id="message"
            name="message"
            rows={5}
            placeholder="Write your message here..."
          ></textarea>
        </div>
        <div className="mb-6">
          <div className="flex items-center">
            <div className="mr-2">
              <input
                className="custom-radio-1 opacity-0 absolute z-10 h-4 w-4"
                id="newsletter"
                type="checkbox"
              />
              <div className="bg-white text-white border border-gray-200 w-4 h-4 flex justify-center items-center rounded-full">
                <svg className="hidden" xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8" fill="none">
                  <circle cx="4" cy="4" r="4" fill="#006251"></circle>
                </svg>
              </div>
            </div>
            <label className="text-gray-600 text-sm" htmlFor="newsletter">
              Subscribe to our monthly newsletter with the latest book summaries
            </label>
          </div>
        </div>
        <button
          className="px-6 py-3 w-full md:w-auto text-center bg-green-500 text-white text-sm font-bold hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200"
          type="submit"
        >
          Send Message
        </button>
      </form>
    </div>
  );
}
