import React, { Suspense } from 'react';
import PricingPlan from './PricingPlan';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getPrice } from '@/services/server/payService';
import { getUserInfo } from '@/services/server/userService';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const brandName = t('brandName');

  // 根据SEO规范构建价格页面元数据
  return {
    title: `Pricing Plans | Affordable Book Summaries & Audiobooks | ${brandName}`,
    description: `Explore ${brandName}'s flexible pricing plans for access to 5000+ book summaries, analyses, and audiobooks. Choose the best plan for your reading and listening needs.`,
    openGraph: {
      title: `Pricing Plans | Affordable Book Summaries & Audiobooks | ${brandName}`,
      description: `Explore ${brandName}'s flexible pricing plans for access to 5000+ book summaries, analyses, and audiobooks. Choose the best plan for your reading and listening needs.`,
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/pricing`,
      type: 'website'
    },
    twitter: {
      card: 'summary',
      title: `Pricing Plans | ${brandName}`,
      description: `Explore ${brandName}'s flexible pricing plans for access to 5000+ book summaries, analyses, and audiobooks.`
    }
  };
};

// 创建一个包含实际内容的组件
async function PricingContent() {
  // 获取价格计划和用户信息
  const plans = await getPrice();
  const userInfo = await getUserInfo();

  console.log('plans', plans, 'userInfo', userInfo)

  // 找到年度和月度计划
  const yearlyPlan = plans?.find(plan => plan.duration === '1 years');
  const monthlyPlan = plans?.find(plan => plan.duration === '1 months');

  return (
    <div>
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="flex justify-center mb-6">
            <div className="bg-yellow-100 px-2 py-1 text-xs font-bold inline-block">New feature</div>
          </div>
          <h1 className="font-heading text-center text-4xl lg:text-5xl font-bold max-w-2xl lg:max-w-5xl mx-auto mb-4">Pricing & Plans</h1>
          <p className="text-gray-600 text-center max-w-xl mx-auto mb-12">Get 10,000+ book summaries, PDF/EPUB downloads, audiobook access & premium support for $5.99/month or $35.99/year.</p>

          {/* Yearly Membership */}
          {
            yearlyPlan && <PricingPlan
              title="Yearly Membership"
              description="Get full access for just $35.99/year — enjoy 10,000+ book summaries, downloads, audiobooks, and premium support!"
              price={yearlyPlan ? `$${yearlyPlan.price}` : "$35,99"}
              discount="50% OFF"
              billingPeriod="Auto-billed yearly"
              licenseType="12-Months License"
              planId={yearlyPlan?.id}
              user={userInfo}
            />
          }

          {/* Monthly Membership */}
          {
            monthlyPlan && <PricingPlan
              title="Monthly Membership"
              description="Enjoy full access for $5.99/month — dive into book summaries, PDF downloads, audiobooks, and more anytime!"
              price={monthlyPlan ? `$${monthlyPlan.price}` : "$5,99"}
              discount="Sale -30%"
              billingPeriod="Auto-billed monthly"
              licenseType="1-Month License"
              className="mt-8"
              planId={monthlyPlan?.id}
              user={userInfo}
            />
          }

        </div>
      </section>
    </div>
  );
}

// 主页面组件，使用Suspense包裹内容
export default function Pricing() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-20">
        <div className="w-full text-center">
          <div className="h-10 w-48 bg-gray-200 rounded animate-pulse mx-auto mb-8"></div>
          <div className="h-6 w-96 bg-gray-200 rounded animate-pulse mx-auto mb-12"></div>

          {/* 骨架屏 - 年度会员卡 */}
          <div className="max-w-3xl mx-auto mb-8">
            <div className="bg-gray-100 rounded-lg p-8 animate-pulse">
              <div className="h-8 w-48 bg-gray-200 rounded mb-4 mx-auto"></div>
              <div className="h-4 w-full bg-gray-200 rounded mb-6"></div>
              <div className="h-10 w-32 bg-gray-200 rounded mx-auto"></div>
            </div>
          </div>

          {/* 骨架屏 - 月度会员卡 */}
          <div className="max-w-3xl mx-auto">
            <div className="bg-gray-100 rounded-lg p-8 animate-pulse">
              <div className="h-8 w-48 bg-gray-200 rounded mb-4 mx-auto"></div>
              <div className="h-4 w-full bg-gray-200 rounded mb-6"></div>
              <div className="h-10 w-32 bg-gray-200 rounded mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    }>
      <PricingContent />
    </Suspense>
  );
}
