'use client'
import React from 'react';
import CheckIcon from './CheckIcon';
import PricingClient from './PricingClient';
import { User } from '@/store/userStore';

interface PricingPlanProps {
  title: string;
  description: string;
  price: string;
  discount: string;
  billingPeriod: string;
  licenseType: string;
  className?: string;
  planId?: number;
  user?: User | null;
}

export default function PricingPlan({
  title,
  description,
  price,
  discount,
  billingPeriod,
  licenseType,
  className = '',
  planId,
  user
}: PricingPlanProps) {
  const features = [
    '10,000+ book summaries',
    'PDF & EPUB downloads',
    'Read online anytime',
    'Audiobook summaries'
  ];

  return (
    <div className={`bg-gray-50 py-7 pr-7 pl-7 md:pl-12 ${className}`}>
      <div className="flex flex-wrap items-center -m-4">
        <div className="w-full lg:w-2/3 p-4">
          <div className="font-heading text-4xl font-bold mb-4 max-w-xs">{title}</div>
          <p className="text-gray-500 mb-6 max-w-md">{description}</p>
          <div className="flex flex-wrap -m-2">
            {features.map((feature, index) => (
              <div key={index} className="w-full lg:w-1/2 p-2">
                <div className="flex items-center gap-2">
                  <span>
                    <CheckIcon />
                  </span>
                  <span className="text-gray-500">{feature}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="w-full lg:w-1/3 p-4">
          <div className="bg-white py-6 px-12">
            <p className="text-center text-xl font-bold mb-1">{discount}</p>
            <p className="text-gray-500 text-center mb-8">{billingPeriod}</p>
            <div className="font-heading text-green-500 font-bold text-center text-5xl mb-8">{price}</div>
            {planId && user ? (
              <PricingClient planId={planId} user={user}>
                <span className="px-6 py-3 mb-3 block text-center w-full sm:w-auto bg-green-500 text-white text-sm font-bold hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200">Start Now</span>
              </PricingClient>
            ) : (
              <span className="px-6 py-3 mb-3 block text-center w-full sm:w-auto bg-green-500 text-white text-sm font-bold hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200">Start Now</span
              >
            )}
            <p className="text-center text-gray-500">{licenseType}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
