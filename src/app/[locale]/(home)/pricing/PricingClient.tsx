'use client'
import React from 'react'
import { useLocale } from 'next-intl'
import { usePayment } from '@/hooks/usePayment'
import { User } from '@/store/userStore'
import { Loading } from '@/components/Loading'

interface PricingClientProps {
  planId: number
  user: User | null
  children: React.ReactNode
}

/**
 * PricingClient 组件 - 重构版本
 *
 * 使用新的 usePayment Hook 来处理支付逻辑
 * 保持与原版本完全相同的 API 和行为
 */
export default function PricingClient({ planId, user, children }: PricingClientProps) {
  const locale = useLocale()

  // 使用新的支付 Hook
  const { isLoading, handlePayment } = usePayment({
    successUrl: `${typeof window !== 'undefined' ? window.location.origin : ''}/${locale}/pay-success`,
    cancelUrl: `${typeof window !== 'undefined' ? window.location.origin : ''}/${locale}/pricing`,
    onError: (error) => {
      // 处理特定的错误消息翻译
      if (error.includes('请先取消现有订阅')) {
        // 这个错误已经在 usePayment 中处理了，这里可以添加额外的逻辑
      }
    }
  })

  const handleSubscribe = async () => {
    await handlePayment(planId, user)
  }

  return (
    <div
      onClick={handleSubscribe}
      className={`relative ${isLoading ? 'cursor-wait' : 'cursor-pointer'}`}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10 rounded">
          <Loading />
        </div>
      )}
      {children}
    </div>
  )
}
