import { redirect } from 'next/navigation';
import { getUserInfo } from '@/services/server/userService';
import MyLibraryClient from './MyLibraryClient';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

/**
 * 生成页面元数据
 */
export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const brandName = t('brandName');

  return {
    title: `My Library | Your Saved Book Summaries & Audiobooks | ${brandName}`,
    description: `Access your saved book summaries, reading progress, and favorite audiobooks in your personal library on ${brandName}. Continue reading or listening anytime, anywhere.`,
    robots: {
      index: false,
      follow: false
    },
    openGraph: {
      title: `My Library | Your Personal Collection | ${brandName}`,
      description: `Access your saved book summaries, reading progress, and favorite audiobooks in your personal library on ${brandName}.`,
      type: 'website'
    }
  };
};

/**
 * My Library 页面
 * 服务端组件，负责用户身份验证和页面渲染
 */
export default async function MyLibraryPage() {
  try {
    // 获取用户信息
    const userInfo = await getUserInfo();

    // 如果用户未登录，重定向到登录页面
    if (!userInfo) {
      // 将当前路径作为 from 参数传递给登录页面，以便登录后返回
      redirect('/login?from=' + encodeURIComponent('/my-library'));
    }

    // 用户已登录，渲染客户端组件
    return <MyLibraryClient />;
  } catch (error) {
    // console.error('验证用户登录状态失败:', error);
    // 出现错误时也重定向到登录页面
    redirect('/login?from=' + encodeURIComponent('/my-library'));
  }
}
