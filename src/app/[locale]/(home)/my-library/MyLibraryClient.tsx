'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import Pagination from '@/components/common/Pagination';
import { getLibraryData } from '@/services/actions/my-library.action';
import { MyLibraryBookItem } from '@/types/my-library.types';
import BookCard from './BookCard';
import Link from 'next/link';

// 定义有效的标签类型
type TabType = 'listened' | 'favorites' | 'rating' | 'history';

// 内部组件，使用 useSearchParams
function MyLibraryContent() {
  // 获取URL参数和当前语言
  const searchParams = useSearchParams();
  const router = useRouter();
  const locale = useLocale(); // 获取当前语言

  // 从URL参数中获取tab和page
  const tabParam = searchParams.get('tab') as TabType || 'listened';
  const pageParam = searchParams.get('page') || '1';
  const currentPage = parseInt(pageParam, 10) || 1;

  const [activeTab, setActiveTab] = useState<TabType>(
    ['listened', 'favorites', 'rating', 'history'].includes(tabParam) ? tabParam : 'listened'
  );
  const [books, setBooks] = useState<MyLibraryBookItem[]>([]);
  const [meta, setMeta] = useState({ total: 0, page: 1, limit: 12, totalPages: 0 });
  const [isLoading, setIsLoading] = useState(true);

  // 获取数据函数
  const fetchData = async (tab: string, page: number) => {
    setIsLoading(true);
    try {
      const result = await getLibraryData(tab, page, locale);

      // 检查是否是错误响应
      if (result && 'success' in result && result.success === false) {
        console.error('获取数据失败:', result.error);
        // 如果项目中使用了toast，可以显示错误提示
        // toast.error(result.error || '获取数据失败，请稍后重试');
        setBooks([]);
        setMeta({ total: 0, page: 1, limit: 12, totalPages: 0 });
        return;
      }

      // 检查是否是正常的分页结果
      if (result && 'data' in result && 'meta' in result) {
        setBooks(result.data);
        setMeta(result.meta);
      } else {
        // 处理其他意外情况
        setBooks([]);
        setMeta({ total: 0, page: 1, limit: 12, totalPages: 0 });
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      // 如果项目中使用了toast，可以显示错误提示
      // toast.error('获取数据失败，请稍后重试');
      setBooks([]);
      setMeta({ total: 0, page: 1, limit: 12, totalPages: 0 });
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchData(activeTab, currentPage);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, currentPage]);

  // 处理标签切换
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    // 更新URL参数
    router.push(`/my-library?tab=${tab}&page=1`);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    // 更新URL参数
    router.push(`/my-library?tab=${activeTab}&page=${page}`);
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <nav className="flex text-sm">
          <Link className="text-gray-500 hover:text-green-500" href="/">Home</Link>
          <span className="mx-2 text-gray-500">/</span>
          <span className="text-green-500 font-medium">My Library</span>
        </nav>
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="font-heading font-bold text-4xl tracking-tight mb-3">My Library</h1>
        <p className="text-lg text-gray-600">Your personal collection of book summaries. Save your favorites and track your reading progress.</p>
      </div>

      {/* 标签页导航 */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <a
              className={`${activeTab === 'listened' ? 'text-green-600 border-b-2 border-green-500' : 'text-gray-500 hover:text-gray-700'} px-4 py-3 font-medium text-sm`}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleTabChange('listened');
              }}
            >
              Listened
            </a>
            <a
              className={`${activeTab === 'favorites' ? 'text-green-600 border-b-2 border-green-500' : 'text-gray-500 hover:text-gray-700'} px-4 py-3 font-medium text-sm`}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleTabChange('favorites');
              }}
            >
              My Favorites
            </a>
            <a
              className={`${activeTab === 'rating' ? 'text-green-600 border-b-2 border-green-500' : 'text-gray-500 hover:text-gray-700'} px-4 py-3 font-medium text-sm`}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleTabChange('rating');
              }}
            >
              Rating
            </a>
            <a
              className={`${activeTab === 'history' ? 'text-green-600 border-b-2 border-green-500' : 'text-gray-500 hover:text-gray-700'} px-4 py-3 font-medium text-sm`}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleTabChange('history');
              }}
            >
              History
            </a>
          </nav>
        </div>
      </div>

      {/* 加载状态 */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          {/* 加载骨架屏 */}
          {Array(6).fill(0).map((_, index) => (
            <div key={index} className="bg-gray-100 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      ) : (
        <>
          {/* 图书卡片网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
            {books.length > 0 ? (
              books.map(book => (
                <BookCard key={book.id} book={book} />
              ))
            ) : (
              <div className="col-span-full text-center py-10">
                <p className="text-gray-500">No books found.</p>
              </div>
            )}
          </div>

          {/* 分页控件 */}
          {meta.totalPages > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={meta.totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </div>
  );
}

/**
 * My Library 客户端组件
 * 负责处理客户端状态和交互逻辑
 */
const MyLibraryClient = () => {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <nav className="flex text-sm">
            <span className="text-gray-500">Home</span>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-green-500 font-medium">My Library</span>
          </nav>
        </div>
        <div className="mb-8">
          <div className="h-10 w-48 bg-gray-200 rounded animate-pulse mb-3"></div>
          <div className="h-6 w-96 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <div className="flex -mb-px h-10 bg-gray-100"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          {Array(6).fill(0).map((_, index) => (
            <div key={index} className="bg-gray-100 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      </div>
    }>
      <MyLibraryContent />
    </Suspense>
  );
};

export default MyLibraryClient;
