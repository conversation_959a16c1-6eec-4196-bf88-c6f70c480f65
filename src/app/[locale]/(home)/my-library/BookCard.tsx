'use client';

import React from 'react';
import { MyLibraryBookItem } from '@/types/my-library.types';
import Link from 'next/link';
import { urlGenerator } from '@/services/url.service';

interface BookCardProps {
  book: MyLibraryBookItem;
}

/**
 * 图书卡片组件
 * 用于在 My Library 页面中展示单本书籍
 * 保持与原 JSX 版本相同的 UI 和结构
 */
const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const {
    id,
    coverUrl,
    title,
    author,
    description,
    rating,
    progress,
  } = book;

  // 确保 rating 是数字类型，防御性处理 Decimal 对象
  const safeRating = typeof rating === 'object' && rating !== null
    ? Number(rating)
    : Number(rating) || 0;

  // 构建书籍详情页链接
  const bookDetailUrl = urlGenerator.book.detail({
    id,
    title
  });

  console.log('BookCard props:', book);

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform hover:shadow-md">
      <div className="h-64 overflow-hidden relative">
        <Link href={bookDetailUrl}>
          <img className="w-full h-full object-cover" src={coverUrl} alt={title} />
        </Link>
        {progress !== undefined && (
          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs py-1 px-2">
            <div className="flex items-center mb-1">
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-1.5">
              <div className="bg-green-500 h-1.5 rounded-full" style={{ width: `${progress}%` }}></div>
            </div>
          </div>
        )}
      </div>
      <div className="p-4">
        <Link href={bookDetailUrl}>
          <h3 className="font-bold text-lg mb-1 truncate hover:text-green-600 transition-colors duration-200">{title}</h3>
        </Link>
        <p className="text-sm text-gray-500 mb-1">{author}</p>
        <p className="text-xs text-gray-400 mb-2 truncate">{description}</p>
        <div className="flex items-center">
          <span className="text-yellow-400">{'★'.repeat(Math.floor(safeRating))}</span>
          {safeRating < 5 && <span className="text-gray-300">{'★'.repeat(5 - Math.floor(safeRating))}</span>}
          <span className="text-xs text-gray-500 ml-1">{safeRating.toFixed(1)}</span>
        </div>

        {/* 最后阅读时间 (如果有) */}
        {/* {lastReadAt && (
          <div className="mt-2 text-xs text-gray-500">
            Last read: {lastReadAt}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default BookCard;
