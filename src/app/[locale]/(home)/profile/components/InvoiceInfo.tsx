'use client'
import { CountryRegionSelect } from './CountryRegionSelect'
import { FC, useState } from 'react'
import { updateUserInfo } from '@/services/client/userService'
import { ResponseCode } from '@/utils/constants'
import { useToast } from '@/hooks/useToast'
import { useRouter } from '@/i18n/routing'
import { useTranslations } from 'next-intl'

interface InputFieldProps {
  id: string
  label: string
  type?: string
  placeholder?: string
  disabled?: boolean
  defaultValue?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
}

const InputField: FC<InputFieldProps> = ({
  id,
  label,
  type = 'text',
  placeholder,
  disabled = false,
  defaultValue = '',
  onChange
}) => {
  return (
    <div className="space-y-3 w-full inline-block text-sm font-medium dark:text-white">
      <label htmlFor={id} className="inline-block text-sm font-medium dark:text-white">
        {label}
      </label>
      <input
        id={id}
        type={type}
        disabled={disabled}
        defaultValue={defaultValue}
        onChange={onChange}
        className="py-2 px-3 pe-11 block w-full border-gray-200 shadow-xs text-sm rounded-lg focus:border-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600"
        placeholder={placeholder}
      />
    </div>
  )
}

interface InvoiceInfoProps {
  userInfo: User | null
  onUserInfoUpdate?: (updatedUserInfo: User) => void
}

export const InvoiceInfo: FC<InvoiceInfoProps> = ({ userInfo, onUserInfoUpdate }) => {
  const { showToast } = useToast()

  const router = useRouter()

  const t = useTranslations('Profile.InvoiceInfo')

  const [formData, setFormData] = useState({
    email: userInfo?.email || '',
    username: userInfo?.username || '',
    company: userInfo?.company || '',
    country: userInfo?.country || '',
    phone: userInfo?.phone || '',
    address: userInfo?.address || '',
    vat: userInfo?.vat || '',
    city: userInfo?.city || '',
    province: userInfo?.province || '',
    postal: userInfo?.postal || ''
  })

  const [isLoading, setIsLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target
    setFormData((prevData) => ({ ...prevData, [id]: value }))
  }

  const handleCountryChange = (value: string) => {
    setFormData((prevData) => ({ ...prevData, country: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const response = await updateUserInfo(formData as User)
      if (response.code !== ResponseCode.Success) return
      showToast('update user info success', 'success')
      onUserInfoUpdate?.(response.data.user)
      router.refresh()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      {/** Card Section */}
      <div className="max-w-4xl px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
        {/** Card */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-xs dark:bg-neutral-900">
          <div className="mb-8">
            <h2 className="border-b border-gray-200 text-xl py-4 px-7 font-bold text-gray-800 dark:text-neutral-200">
              {t('title')}
            </h2>
          </div>

          <form className="py-4 px-7">
            {/** Section */}
            <div className="py-6 first:pt-0 last:pb-0 border-t first:border-transparent border-gray-200 dark:border-neutral-700 dark:first:border-transparent">
              <div className="mt-2 space-y-3">
                <div className="flex flex-col sm:flex-row gap-3">
                  <InputField
                    id="email"
                    label={t('email')}
                    type="text"
                    placeholder={t('emailPlaceholder')}
                    disabled
                    defaultValue={formData.email}
                    onChange={handleChange}
                  />
                  <InputField
                    id="username"
                    label={t('username')}
                    type="text"
                    placeholder={t('usernamePlaceholder')}
                    defaultValue={formData.username}
                    onChange={handleChange}
                  />
                </div>

                <InputField
                  id="company"
                  label={t('company')}
                  type="text"
                  placeholder={t('companyPlaceholder')}
                  defaultValue={formData.company}
                  onChange={handleChange}
                />
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="space-y-3 w-full inline-block text-sm font-medium dark:text-white">
                    <label
                      htmlFor="country"
                      className="inline-block text-sm font-medium dark:text-white"
                    >
                      {t('countryRegion')}
                    </label>
                    <CountryRegionSelect defaultValue={formData.country} onChange={handleCountryChange} />
                  </div>
                  <InputField
                    id="phone"
                    label={t('phone')}
                    type="text"
                    placeholder={t('phonePlaceholder')}
                    defaultValue={formData.phone}
                    onChange={handleChange}
                  />
                </div>
                <InputField
                  id="address"
                  label={t('address')}
                  type="text"
                  placeholder={t('addressPlaceholder')}
                  defaultValue={formData.address}
                  onChange={handleChange}
                />
                <InputField
                  id="vat"
                  label={t('vat')}
                  type="text"
                  placeholder={t('vatPlaceholder')}
                  defaultValue={formData.vat}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div className="mt-2 space-y-3">
              <div className="flex flex-col sm:flex-row gap-3">
                <InputField
                  id="city"
                  label={t('city')}
                  type="text"
                  placeholder={t('cityPlaceholder')}
                  defaultValue={formData.city}
                  onChange={handleChange}
                />
                <InputField
                  id="province"
                  label={t('province')}
                  type="text"
                  placeholder={t('provincePlaceholder')}
                  defaultValue={formData.province}
                  onChange={handleChange}
                />
                <InputField
                  id="postal"
                  label={t('postal')}
                  type="text"
                  placeholder={t('postalPlaceholder')}
                  defaultValue={formData.postal}
                  onChange={handleChange}
                />
              </div>
            </div>
            {/** End Section */}
          </form>

          <div className="pb-4 px-7 mt-5 flex justify-end gap-x-2">
            <button
              type="button"
              disabled={isLoading}
              className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-primary focus:outline-hidden focus:bg-primary disabled:opacity-50 disabled:pointer-events-none"
              onClick={handleSave}
            >
              {isLoading ? (
                <>
                  <span
                    className="animate-spin inline-block w-4 h-4 border-[2px] border-current border-t-transparent text-white rounded-full"
                    role="status"
                    aria-label="loading"
                  ></span>
                </>
              ) : null}
              {t('saveButton')}
            </button>
          </div>
        </div>
        {/** End Card */}
      </div>
      {/** End Card Section */}
    </>
  )
}
