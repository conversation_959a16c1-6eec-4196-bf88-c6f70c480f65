import { getUserQuota } from '@/services/server/userService'
import { getTranslations } from 'next-intl/server'

export async function TokenUsage() {
  const quota = await getUserQuota()
  const t = await getTranslations('TokenUsage')

  return (
    <div className="max-w-4xl px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
      <div className="flex flex-col">
        <div className="-m-1.5 overflow-x-auto">
          <div className="p-1.5 min-w-full inline-block align-middle">
            <div className="bg-white border border-gray-200 rounded-xl shadow-xs overflow-hidden dark:bg-neutral-900 dark:border-neutral-700">
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-neutral-700">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    {t('title')}
                  </h2>
                </div>
              </div>
              <div className="px-6 py-4 flex flex-col gap-2">
                <h4 className="text-lg font-bold">{t('monthlyUsage')}</h4>
                <p className="text-gray-600 text-sm">
                  <span className="mr-2">{t('nextReset')}:</span>
                  {quota?.reset_at ? new Date(quota.reset_at * 1000).toLocaleString() : '-'}
                </p>
                <p className="text-sm">
                  <span className="mr-2">{t('usedQuota')}:</span>
                  <span className="text-gray-600">
                    {quota?.used}/{quota?.limit}
                  </span>{' '}
                  Tokens
                </p>

                <div
                  className="flex w-full h-1.5 bg-gray-200 rounded-full overflow-hidden dark:bg-neutral-700"
                  role="progressbar"
                  aria-valuenow={((quota?.used ?? 0) / (quota?.limit ?? 1)) * 100}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  <div
                    className="flex flex-col justify-center rounded-full overflow-hidden bg-blue-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-blue-500"
                    style={{ width: `${((quota?.used ?? 0) / (quota?.limit ?? 1)) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
