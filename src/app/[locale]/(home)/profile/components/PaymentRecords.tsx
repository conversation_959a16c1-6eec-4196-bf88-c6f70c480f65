import { Link } from '@/i18n/routing'
import { InvoiceData, invoices } from '@/services/server/userService'
import { DownloadInvoice } from './DownloadInvoice'
import React from 'react'
import { format } from 'date-fns'
import { getTranslations } from 'next-intl/server'
import { headers } from 'next/headers'

export interface InvoicePageItem {
  order_sn?: string
  order_status: 1 | 2 | 3
  paid_at?: number
  paid_amount?: number
  next_period_start?: number
  rank?: {
    rank_name: string
  }
}

const formatDate = (timestamp?: number) => {
  if (!timestamp) return '-'
  return format(new Date(timestamp * 1000), 'MMM dd yyyy')
}

export type InvoiceRecord = InvoicePageItem

interface RecordTableProps {
  data: InvoiceRecord[]
  user: User | null
}

const mapOrderStatus = (orderStatus: number): 'unpaid' | 'paid' | 'refunded' => {
  switch (orderStatus) {
    case 1:
      return 'unpaid'
    case 2:
      return 'paid'
    default:
      return 'refunded'
  }
}

const RecordTable: React.FC<RecordTableProps> = async ({ data, user }) => {
  const t = await getTranslations('PaymentRecords')

  return (
    <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
      <thead className="bg-gray-50 dark:bg-neutral-900">
        <tr>
          <th scope="col" className="px-6 py-3 text-start">
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                {t('invoiceNumber')}
              </span>
            </div>
          </th>
          <th scope="col" className="px-6 py-3 text-start">
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                {t('status')}
              </span>
            </div>
          </th>
          <th scope="col" className="px-6 py-3 text-start">
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                {t('created')}
              </span>
            </div>
          </th>
          <th scope="col" className="px-6 py-3 text-start">
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                {t('actions')}
              </span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200 dark:divide-neutral-700">
        {data.length > 0 ? (
          data.map((record, index) => (
            <tr
              key={index}
              className="bg-white hover:bg-gray-50 dark:bg-neutral-900 dark:hover:bg-neutral-800"
            >
              <td className="size-px whitespace-nowrap">
                <span className="block px-6 py-2">
                  <span className="font-mono text-sm text-blue-600 dark:text-blue-500">
                    {record.order_sn}
                  </span>
                </span>
              </td>
              <td className="size-px whitespace-nowrap">
                <span className="block px-6 py-2">
                  <span
                    className={`py-1 px-1.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-full ${
                      mapOrderStatus(record.order_status) === 'paid'
                        ? 'bg-teal-100 text-teal-800 dark:bg-teal-500/10 dark:text-teal-500'
                        : mapOrderStatus(record.order_status) === 'unpaid'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-500/10 dark:text-yellow-500'
                    }`}
                  >
                    {t(mapOrderStatus(record.order_status))}
                  </span>
                </span>
              </td>
              <td className="size-px whitespace-nowrap">
                <span className="block px-6 py-2">
                  <span className="text-sm text-gray-600 dark:text-neutral-400">
                    {record.paid_at ? formatDate(record.paid_at) : '-'}
                  </span>
                </span>
              </td>
              <td className="size-px whitespace-nowrap">
                <span className="block px-6 py-2">
                  {mapOrderStatus(record.order_status) === 'paid' && (
                    <DownloadInvoice data={{ ...record, ...(user || {}) }} />
                  )}
                </span>
              </td>
            </tr>
          ))
        ) : (
          <tr className="bg-white dark:bg-neutral-900">
            <td colSpan={4} className="px-6 py-8 text-center">
              <div className="flex flex-col items-center">
                <svg
                  className="size-16 text-gray-400 dark:text-neutral-500 mb-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p className="text-gray-500 dark:text-neutral-400">{t('noRecords')}</p>
              </div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

function parseUrl(url: string, offset: number) {
  const parsedUrl = new URL(url)
  const currentPage = parsedUrl.searchParams.get('page')
  const newPage = currentPage ? Number(currentPage) + offset : 1 + offset
  parsedUrl.searchParams.set('page', newPage.toString())

  return parsedUrl.toString()
}

export async function PaymentRecords({ userInfo, page }: { userInfo: User | null; page: number }) {
  const headersList = headers()
  const currentPath = (await headersList).get('x-next-url') || ''

  const nextPagePath = parseUrl(currentPath, +1)
  const prevPagePath = parseUrl(currentPath, -1)
  const t = await getTranslations('PaymentRecords')
  const currentPage = page

  const response: InvoiceData = ((await invoices({
    page: currentPage,
    per_page: 10
  })) || {
    data: [],
    total: 0,
    from: 0,
    to: 0
  }) as InvoiceData

  return (
    <div className="max-w-4xl px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
      <div className="flex flex-col">
        <div className="-m-1.5 overflow-x-auto">
          <div className="p-1.5 min-w-full inline-block align-middle">
            <div className="bg-white border border-gray-200 rounded-xl shadow-xs overflow-hidden dark:bg-neutral-900 dark:border-neutral-700">
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-neutral-700">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    {t('title')}
                  </h2>
                </div>
              </div>
              <RecordTable data={response?.data || []} user={userInfo} />
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-neutral-700">
                <div>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    <span className="font-semibold mr-2 text-gray-800 dark:text-neutral-200">
                      {response.total}
                    </span>
                    {t('results')}
                  </p>
                </div>
                <div>
                  <div className="inline-flex gap-x-2">
                    {response.from > 1 && (
                      <Link href={prevPagePath}>
                        <div className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                          <svg
                            className="size-3"
                            width="16"
                            height="16"
                            viewBox="0 0 16 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M10.506 1.64001L4.85953 7.28646C4.66427 7.48172 4.66427 7.79831 4.85953 7.99357L10.506 13.64"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          </svg>
                          {t('prev')}
                        </div>
                      </Link>
                    )}
                    {response.to < response.total && (
                      <Link href={nextPagePath}>
                        <div className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                          {t('next')}
                          <svg
                            className="size-3"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M4.50598 2L10.1524 7.64645C10.3477 7.84171 10.3477 8.15829 10.1524 8.35355L4.50598 14"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          </svg>
                        </div>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
