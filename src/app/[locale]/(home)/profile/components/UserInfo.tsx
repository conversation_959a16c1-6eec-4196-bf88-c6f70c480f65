// import { Link } from '@/i18n/routing'
import { Loading } from '@/components/Loading'
import { getBgColor, getFirst<PERSON>har } from '@/utils'
import { getUserInfo } from '@/services/server/userService'
import { getTranslations } from 'next-intl/server'

export async function UserInfo() {
  const userInfo = await getUserInfo()
  const t = await getTranslations('UserInfo')

  return (
    <>
      <div className="max-w-4xl px-4 pt-10 sm:px-6 lg:px-8 lg:pt-14 mx-auto">
        <div className="flex items-center justify-between border px-6 py-4 border-gray-200 rounded-xl">
          {userInfo ? (
            <>
              <div className="flex gap-4 items-center">
                {/* Avatar */}
                <div
                  style={{ backgroundColor: getBgColor(userInfo.username) }}
                  className="inline-flex items-center justify-center rounded-full text-4xl text-white w-16 h-16"
                >
                  {getFirst<PERSON>har(userInfo.username)}
                </div>
                {/* Name */}
                <div className="ml-2">
                  <p>{userInfo.username}</p>
                  <p className="text-gray-400 text-sm">{userInfo.email}</p>
                </div>
              </div>
              {userInfo?.vip_expired_at ? (
                <div>
                  <span>{userInfo?.rank_name}</span>
                  <span>
                    {t('expiredAt')}: {userInfo?.vip_expired_at}
                  </span>
                </div>
              ) : null}
              <div>
                {/* <Link href="/pricing">
                  <button
                    type="button"
                    className="py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200  hover:border-primary hover:text-primary focus:outline-hidden focus:border-primary focus:text-primary disabled:opacity-50 disabled:pointer-events-none dark:border-neutral-700 dark:text-neutral-400 dark:hover:text-primary dark:hover:border-primary dark:focus:text-primary dark:focus:border-primary"
                  >
                    {t('changePlan')}
                  </button>
                </Link> */}
              </div>
            </>
          ) : (
            <div className="w-full h-full min-h-[80px] flex justify-center items-center">
              <Loading />
            </div>
          )}
        </div>
      </div>
    </>
  )
}
