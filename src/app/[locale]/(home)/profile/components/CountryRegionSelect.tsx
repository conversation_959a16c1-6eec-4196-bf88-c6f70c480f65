import React, { useState } from 'react'
import { Check, ChevronsUpDown } from 'lucide-react'

import countries from '@/utils/countries.json'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

interface CountryRegionSelectProps {
  defaultValue?: string
  onChange?: (value: string) => void
  disabled?: boolean
  placeholder?: string
  searchPlaceholder?: string
  notFoundText?: string
}

export const CountryRegionSelect = ({
  defaultValue = '',
  onChange,
  disabled,
  placeholder = 'Select country...',
  searchPlaceholder = 'Search country...',
  notFoundText = 'No country found.'
}: CountryRegionSelectProps) => {
  const [open, setOpen] = useState(false)
  const [value, setValue] = useState(defaultValue)

  const selectedLabel = countries.find((country) => country.value === value)?.label ?? placeholder

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between text-left font-normal"
          disabled={disabled}
        >
          {value ? selectedLabel : <span className="text-muted-foreground">{placeholder}</span>}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-(--radix-popover-trigger-width) max-h-(--radix-popover-content-available-height) p-0">
        <Command>
          <CommandInput className="bg-gray-100 my-2 border-none" placeholder={searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{notFoundText}</CommandEmpty>
            <CommandGroup>
              {countries.map((country) => (
                <CommandItem
                  key={country.value}
                  value={country.label}
                  onSelect={(currentLabel) => {
                    const selectedCountry = countries.find((c) => c.label === currentLabel)
                    const newValue =
                      currentLabel === selectedLabel ? '' : (selectedCountry?.value ?? '')

                    setValue(newValue)
                    onChange?.(newValue)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === country.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {country.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

CountryRegionSelect.displayName = 'CountryRegionSelect'
