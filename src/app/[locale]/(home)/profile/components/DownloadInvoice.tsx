'use client'
import jsPDF from 'jspdf'
import { format } from 'date-fns'
import { InvoiceRecord } from './PaymentRecords'
import { useTranslations } from 'next-intl'

const formatDate = (timestamp?: number) => {
  if (!timestamp) return '-'
  return format(new Date(timestamp * 1000), 'MMM dd yyyy')
}

export function DownloadInvoice({ data }: { data: Partial<InvoiceRecord & User> }) {
  const t = useTranslations('DownloadInvoice')

  const handleDownloadInvoice = () => {
    generateInvoice()
  }

  const generateInvoice = async () => {
    const doc = new jsPDF()
    // Title
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text(t('invoiceTitle'), 105, 20)
    doc.setFont('helvetica', 'normal')

    // Invoice Details
    doc.setFontSize(10)
    doc.text(`${t('invoiceNumber')} 15Minutes - ${data.order_sn?.slice(-6)}`, 14, 30)
    doc.text(t('dateOfIssue'), 14, 38)
    doc.text(formatDate(data?.created_at), 60, 38)
    doc.text(t('dateDue'), 14, 46)
    doc.text(formatDate(data?.created_at), 60, 46)

    // Company Info
    doc.text('15Minutes Inc.', 14, 60)
    doc.text('<EMAIL>', 14, 66)
    doc.text('80 Raffles Place', 14, 72)
    doc.text('UOB Plaza 1, #26-01', 14, 78)
    doc.text('Singapore 048624', 14, 84)

    // Bill To Info
    doc.text(t('billTo'), 105, 60)
    doc.text(data?.email || '', 105, 66)
    doc.text(data?.company || '', 105, 72)
    doc.text(data?.address || '', 105, 78)
    doc.text(`${data?.city} ${data?.province} ${data?.postal}`, 105, 84)
    doc.text(data?.country || '', 105, 90)
    doc.text(`VAT: ${data?.vat}`, 105, 96)

    // Payment Link (commented out for now)
    // doc.setTextColor(0, 0, 255)
    // doc.textWithLink(t('payOnline'), 14, 104, {
    //   url: 'https://15minutes.tangshu.com/pricing'
    // })
    // doc.setTextColor(0, 0, 0)

    // Amount Details
    doc.setFontSize(12)
    doc.text(`$${data?.paid_amount} due ${formatDate(data?.created_at)}`, 14, 118)

    // Table Headers
    doc.setFontSize(10)
    doc.text(t('description'), 14, 130)
    doc.text(t('qty'), 120, 130)
    doc.text(t('unitPrice'), 140, 130)
    doc.text(t('amount'), 180, 130)

    // Table Data
    doc.text(t('planName', { planName: data?.rank?.rank_name || '' }), 14, 140)
    doc.text(`${formatDate(data?.paid_at)} - ${formatDate(data?.next_period_start)}`, 14, 146)
    doc.text('1', 122, 140)
    doc.text(`$${data?.paid_amount}`, 142, 140)
    doc.text(`$${data?.paid_amount}`, 182, 140)

    // Subtotal, Total, Amount Due
    doc.text(t('subtotal'), 140, 160)
    doc.text(`$${data?.paid_amount}`, 182, 160)
    doc.text(t('total'), 140, 166)
    doc.text(`$${data?.paid_amount}`, 182, 166)
    doc.text(t('amountDue'), 140, 172)
    doc.text(`$${data?.paid_amount}`, 182, 172)

    // Footer
    doc.setFontSize(8)
    doc.text(
      `15Minutes - ${data.order_sn} · $${data.paid_amount} due ${formatDate(data.paid_at)}`,
      14,
      290,
      {
        align: 'left'
      }
    )
    doc.text(t('pageInfo'), 200, 290, { align: 'right' })

    // Save the PDF
    doc.save(`15minutes-invoice-${data.order_sn}.pdf`)
  }

  return (
    <button
      type="button"
      className="py-1 px-2 text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 hover:text-gray-900 focus:outline-hidden focus:bg-gray-50 active:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:active:bg-neutral-600"
      onClick={handleDownloadInvoice}
    >
      {t('downloadButton')}
    </button>
  )
}
