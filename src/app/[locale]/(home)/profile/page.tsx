import { redirect } from 'next/navigation'
import { InvoiceInfo } from './components/InvoiceInfo'
import { PaymentRecords } from './components/PaymentRecords'
// import { TokenUsage } from './components/TokenUsage'
import { UserInfo } from './components/UserInfo'
import { getUserInfo } from '@/services/server/userService'

export default async function Profile({
  searchParams
}: {
  searchParams: Promise<{
    page: string
  }>
}) {
  const page = parseInt(((await searchParams)?.page as string) ?? '1', 10)
  try {
    const userInfo = await getUserInfo()

    if (!userInfo) {
      redirect('/login')
    }

    return (
      <>
        <UserInfo />
        {/* <TokenUsage /> */}
        <PaymentRecords userInfo={userInfo} page={page} />
        <InvoiceInfo userInfo={userInfo} />
      </>
    )
  } catch {
    redirect('/login')
  }
}
