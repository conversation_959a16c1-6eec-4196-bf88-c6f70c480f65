'use client'

import { useState } from 'react'
import { useTransition } from 'react'

export default function LogForm() {
  const [message, setMessage] = useState('')
  const [isPending, startTransition] = useTransition()

  const addLog = async () => {
    if (!message.trim()) return

    startTransition(async () => {
      try {
        const response = await fetch('/api/logs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            level: 'info', // 默认级别
            message: message,
            metadata: {}, // 可选元数据
            service: 'web-app', // 服务名称
            userId: 'anonymous' // 用户ID
          })
        })

        const data = await response.json()
        if (data.status === 200) {
          alert('日志添加成功！')
          setMessage('') // 清空输入框
        } else {
          alert(`错误: ${data.error || '未知错误'}`)
        }
      } catch (error) {
        alert(`提交失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    })
  }

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">MongoDB 日志系统</h1>
      <p className="mb-4">在下方输入日志信息并点击按钮添加到数据库</p>
      <div className="space-y-4">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="输入日志内容..."
          className="w-full p-2 border rounded"
          aria-label="日志内容"
        />
        <button
          onClick={addLog}
          disabled={isPending || !message.trim()}
          className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {isPending ? '添加中...' : '添加日志'}
        </button>
      </div>
    </div>
  )
}
