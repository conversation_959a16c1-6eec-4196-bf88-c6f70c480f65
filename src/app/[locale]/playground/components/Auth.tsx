'use client'

import { useState } from 'react'
import { verifyJwtToken } from '@/services/actions/authServer'
import Cookies from 'js-cookie'

export default function Auth() {
  const [authStatus, setAuthStatus] = useState(false)
  const [loading, setLoading] = useState(false)

  const checkAuth = async () => {
    try {
      setLoading(true)
      const token = Cookies.get('TAROT_ACCESS_TOKEN-dev')
      console.log('token:', token)
      if (!token) {
        setAuthStatus(false)
        return
      }

      const result = await verifyJwtToken(token)
      setAuthStatus(result.isValid)
    } catch (error) {
      console.error('Auth check failed:', error)
      setAuthStatus(false)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-4">
      <button
        onClick={checkAuth}
        disabled={loading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? '检查中...' : '鉴权'}
      </button>
      <p className="mt-2">鉴权状态：{authStatus ? '已认证' : '未认证'}</p>
    </div>
  )
}
