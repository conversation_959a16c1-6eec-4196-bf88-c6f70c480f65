'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

// 服务器操作
async function setRedisValue(key: string, value: string) {
  const response = await fetch('/api/redis', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ action: 'set', key, value })
  })
  return response.json()
}

async function getRedisValue(key: string) {
  const response = await fetch(`/api/redis?action=get&key=${encodeURIComponent(key)}`)
  return response.json()
}

async function deleteRedisValue(key: string) {
  const response = await fetch('/api/redis', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ action: 'delete', key })
  })
  return response.json()
}

export default function RedisTest() {
  const t = useTranslations('RedisTest')
  const [key, setKey] = useState('')
  const [value, setValue] = useState('')
  const [result, setResult] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function handleSet() {
    if (!key || !value) {
      setError(t('keyValueRequired'))
      return
    }

    setLoading(true)
    setError(null)
    try {
      const response = await setRedisValue(key, value)
      if (response.success) {
        setResult(t('setSuccess', { key }))
      } else {
        setError(response.error || t('unknownError'))
      }
    } catch {
      setError(t('requestFailed'))
    } finally {
      setLoading(false)
    }
  }

  async function handleGet() {
    if (!key) {
      setError(t('keyRequired'))
      return
    }

    setLoading(true)
    setError(null)
    try {
      const response = await getRedisValue(key)
      if (response.success) {
        if (response.value) {
          setResult(t('getValue', { key, value: response.value }))
        } else {
          setResult(t('keyNotFound', { key }))
        }
      } else {
        setError(response.error || t('unknownError'))
      }
    } catch {
      setError(t('requestFailed'))
    } finally {
      setLoading(false)
    }
  }

  async function handleDelete() {
    if (!key) {
      setError(t('keyRequired'))
      return
    }

    setLoading(true)
    setError(null)
    try {
      const response = await deleteRedisValue(key)
      if (response.success) {
        setResult(t('deleteSuccess', { key }))
      } else {
        setError(response.error || t('unknownError'))
      }
    } catch {
      setError(t('requestFailed'))
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
        <CardDescription>{t('description')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid w-full gap-1.5">
            <Label htmlFor="redis-key">{t('key')}</Label>
            <Input
              id="redis-key"
              value={key}
              onChange={(e) => setKey(e.target.value)}
              placeholder={t('keyPlaceholder')}
            />
          </div>

          <div className="grid w-full gap-1.5">
            <Label htmlFor="redis-value">{t('value')}</Label>
            <Input
              id="redis-value"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder={t('valuePlaceholder')}
            />
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={handleSet} disabled={loading}>
              {loading ? t('loading') : t('set')}
            </Button>
            <Button onClick={handleGet} disabled={loading} variant="outline">
              {t('get')}
            </Button>
            <Button onClick={handleDelete} disabled={loading} variant="destructive">
              {t('delete')}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert>
              <AlertDescription>{result}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
