'use client'

import { useState, useEffect } from 'react'
import { useTransition } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Todo {
  id: string
  text: string
  completed: boolean
}

export default function TodoList() {
  const [todos, setTodos] = useState<Todo[]>([])
  const [newTodo, setNewTodo] = useState('')
  const [isPending, startTransition] = useTransition()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取所有待办事项
  const fetchTodos = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/todo')

      if (!response.ok) {
        throw new Error('获取待办事项失败')
      }

      const data = await response.json()
      setTodos(data)
      setError(null)
    } catch (err) {
      console.error('获取待办事项出错:', err)
      setError('获取待办事项失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 组件加载时获取数据
  useEffect(() => {
    fetchTodos()
  }, [])

  // 添加新的TODO
  const addTodo = async () => {
    if (!newTodo.trim()) return

    try {
      setIsLoading(true)
      const response = await fetch('/api/todo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: newTodo })
      })

      if (!response.ok) {
        throw new Error('添加待办事项失败')
      }

      const addedTodo = await response.json()
      setTodos([addedTodo, ...todos])
      setNewTodo('')
      setError(null)
    } catch (err) {
      console.error('添加待办事项出错:', err)
      setError('添加待办事项失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 标记TODO为完成/未完成
  const toggleTodo = async (id: string) => {
    try {
      const todoToUpdate = todos.find((todo) => todo.id === id)
      if (!todoToUpdate) return

      startTransition(async () => {
        const response = await fetch('/api/todo', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id,
            completed: !todoToUpdate.completed
          })
        })

        if (!response.ok) {
          throw new Error('更新待办事项状态失败')
        }

        const updatedTodo = await response.json()
        setTodos(todos.map((todo) => (todo.id === id ? updatedTodo : todo)))
        setError(null)
      })
    } catch (err) {
      console.error('更新待办事项状态出错:', err)
      setError('更新待办事项状态失败，请稍后重试')
    }
  }

  // 删除TODO
  const deleteTodo = async (id: string) => {
    try {
      startTransition(async () => {
        const response = await fetch(`/api/todo?id=${id}`, {
          method: 'DELETE'
        })

        if (!response.ok) {
          throw new Error('删除待办事项失败')
        }

        setTodos(todos.filter((todo) => todo.id !== id))
        setError(null)
      })
    } catch (err) {
      console.error('删除待办事项出错:', err)
      setError('删除待办事项失败，请稍后重试')
    }
  }

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">MySQL 待办事项列表</h1>

      {/* 添加新的TODO表单 */}
      <div className="flex space-x-2 mb-6">
        <input
          type="text"
          value={newTodo}
          onChange={(e) => setNewTodo(e.target.value)}
          placeholder="添加新的待办事项..."
          className="flex-1 p-2 border rounded"
          aria-label="新待办事项"
          onKeyDown={(e) => e.key === 'Enter' && addTodo()}
          disabled={isPending}
        />
        <button
          onClick={addTodo}
          disabled={!newTodo.trim() || isPending}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          aria-label="添加待办事项"
        >
          {isPending ? '添加中...' : '添加'}
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
          role="alert"
        >
          <p>{error}</p>
        </div>
      )}

      {/* 加载状态 */}
      {isLoading && !isPending && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <span className="sr-only">加载中...</span>
        </div>
      )}

      {/* TODO列表 */}
      <ul className="space-y-2">
        <AnimatePresence>
          {todos.map((todo) => (
            <motion.li
              key={todo.id}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, height: 0 }}
              className="flex items-center justify-between p-3 border rounded bg-gray-50"
            >
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={todo.completed}
                  onChange={() => toggleTodo(todo.id)}
                  className="mr-3 h-5 w-5"
                  aria-label={`标记 ${todo.text} 为${todo.completed ? '未完成' : '完成'}`}
                  disabled={isPending}
                />
                <span className={`${todo.completed ? 'line-through text-gray-400' : ''}`}>
                  {todo.text}
                </span>
              </div>
              <button
                onClick={() => deleteTodo(todo.id)}
                className="text-red-500 hover:text-red-700"
                aria-label={`删除待办事项: ${todo.text}`}
                disabled={isPending}
              >
                <span className="sr-only">删除</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </motion.li>
          ))}
        </AnimatePresence>

        {!isLoading && todos.length === 0 && (
          <p className="text-center text-gray-500 py-4">暂无待办事项</p>
        )}
      </ul>

      {/* 统计信息 */}
      {todos.length > 0 && (
        <div className="mt-4 text-sm text-gray-600">
          <p>
            总计: {todos.length} 项, 已完成: {todos.filter((t) => t.completed).length} 项
          </p>
        </div>
      )}
    </div>
  )
}
