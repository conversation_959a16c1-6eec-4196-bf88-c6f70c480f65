'use client'

import { useState, useRef, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Loader2, CheckCircle, Clock } from 'lucide-react'

// 定义表单数据类型
interface FormData {
  text: string
}

// 定义任务类型
interface Task {
  id: string
  text: string
  result?: string
  status: 'pending' | 'completed'
  error?: string
  submittedAt: Date
  completedAt?: Date
  processingTime?: number
}

export function UpperCaseConverter() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const pollingInterval = useRef<NodeJS.Timeout | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<FormData>()

  // 获取所有任务状态
  const fetchAllTasks = async () => {
    try {
      const response = await fetch('/api/uppercase/status')

      if (!response.ok) throw new Error('获取任务状态失败')

      const data = await response.json()
      console.log('获取任务状态响应:', data)

      if (data.tasks && data.tasks.length > 0) {
        console.log(`获取到 ${data.tasks.length} 个任务`)

        // 更新本地任务状态
        setTasks(
          data.tasks.map((task: any) => ({
            id: task.id,
            text: task.text,
            result: task.result,
            status: task.status,
            submittedAt: new Date(task.submittedAt),
            completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
            processingTime: task.completedAt
              ? (new Date(task.completedAt).getTime() - new Date(task.submittedAt).getTime()) / 1000
              : undefined
          }))
        )
      } else {
        setTasks([])
      }
    } catch (err) {
      console.error('获取任务状态出错:', err)
    }
  }

  // 设置轮询获取任务状态
  const setupPolling = () => {
    // 立即获取一次任务状态
    fetchAllTasks()

    if (!pollingInterval.current) {
      console.log('开始轮询任务状态')
      pollingInterval.current = setInterval(fetchAllTasks, 5000) // 5秒轮询一次
    }
  }

  // 组件初始化时自动开始轮询
  useEffect(() => {
    setupPolling()

    return () => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current)
        pollingInterval.current = null
      }
    }
  }, []) // 不依赖于tasks，确保只在组件初始化时运行一次

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true)
      setError(null)

      const response = await fetch('/api/uppercase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: data.text })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '提交任务失败')
      }

      const { taskId } = await response.json()

      // 添加新任务到列表
      const newTask: Task = {
        id: taskId,
        text: data.text,
        status: 'pending',
        submittedAt: new Date()
      }

      setTasks((prevTasks) => [newTask, ...prevTasks])

      // 重置表单
      reset()
    } catch (err) {
      setError(err instanceof Error ? err.message : '提交任务失败')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理按Enter键提交
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      handleSubmit(onSubmit)()
    }
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 space-y-6 bg-white rounded-lg shadow-md">
      <div>
        <h2 className="text-2xl font-bold text-center">文本转大写服务</h2>
        <p className="text-center text-gray-500">输入文本将被异步转换为大写</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <div className="relative">
            <label htmlFor="text" className="block text-sm font-medium mb-1">
              输入文本
            </label>
            <Input
              id="text"
              placeholder="请输入要转换的文本..."
              {...register('text', { required: '请输入文本' })}
              className="w-full"
              disabled={isSubmitting}
              onKeyDown={handleKeyDown}
            />
          </div>
          {errors.text && <p className="mt-1 text-sm text-red-500">{errors.text.message}</p>}
        </div>

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span>处理中...</span>
            </>
          ) : (
            '提交转换'
          )}
        </Button>
      </form>

      {error && <div className="p-3 rounded-md bg-red-50 text-red-700 text-sm">{error}</div>}

      {tasks.length > 0 && (
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">任务列表</h3>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {tasks.map((task) => (
              <motion.div
                key={task.id}
                className={`p-4 rounded-md border ${
                  task.status === 'completed'
                    ? 'bg-green-50 border-green-200'
                    : 'bg-yellow-50 border-yellow-200'
                }`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium truncate max-w-[180px]">{task.text}</span>
                  <span className="flex items-center text-xs">
                    {task.status === 'completed' ? (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        <span className="text-green-600">已完成</span>
                      </>
                    ) : (
                      <>
                        <Clock className="h-4 w-4 text-yellow-500 mr-1 animate-pulse" />
                        <span className="text-yellow-600">处理中</span>
                      </>
                    )}
                  </span>
                </div>

                {task.status === 'completed' && (
                  <>
                    <p className="font-mono bg-white p-2 rounded border border-green-100 text-sm break-all">
                      {task.result}
                    </p>
                    {task.processingTime && (
                      <p className="text-xs text-gray-500 mt-1">
                        处理用时: {task.processingTime.toFixed(2)} 秒
                      </p>
                    )}
                  </>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500 text-center">
        <p>由 RabbitMQ 和 MongoDB 提供支持</p>
      </div>
    </div>
  )
}
