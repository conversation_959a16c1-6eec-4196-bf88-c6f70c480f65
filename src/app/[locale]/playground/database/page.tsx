import LogForm from '../components/LogForm'
import TodoList from '../components/TodoList'
import RedisTest from '../components/RedisTest'
import Auth from '../components/Auth'
import { UpperCaseConverter } from '../components/UpperCaseConverter'

export default function DatabasePage() {
  return (
    <main className="p-4">
      <div className="grid md:grid-cols-2 gap-8">
        <section>
          {/* MongoDB */}
          <LogForm />
        </section>
        <section>
          {/* MySQL */}
          <TodoList />
        </section>
        <section>
          {/* RabbitMQ */}
          <UpperCaseConverter />
        </section>
        <section>
          {/* Redis */}
          <RedisTest />
        </section>
        <section>
          {/* JWT */}
          <Auth />
        </section>
      </div>
    </main>
  )
}
