'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

type TarotCard = {
  id: number
  card_id: number
  locale: string
  name: string
  meaning: string | null
  upright_keywords: any
  reversed_keywords: any
  created_at: number
  updated_at: number
}

export default function Auth() {
  const [tarotCards, setTarotCards] = useState<TarotCard[]>([])
  const [loading, setLoading] = useState(false)

  const fetchTarotCards = async () => {
    setLoading(true)
    try {
      const response = await fetch('/playground/auth/api')
      const data = await response.json()
      setTarotCards(data.tarotCards)
    } catch (error) {
      console.error('获取塔罗牌数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <main className="container py-8">
      <h1 className="text-2xl font-bold mb-4">塔罗牌数据</h1>
      <Button onClick={fetchTarotCards} disabled={loading} className="mb-6">
        {loading ? '加载中...' : '获取塔罗牌数据'}
      </Button>

      {tarotCards.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tarotCards.map((card) => (
            <div key={card.id} className="border p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold">{card.name}</h2>
              <p className="text-sm text-gray-500">语言: {card.locale}</p>
              {card.meaning && <p className="mt-2">{card.meaning}</p>}

              {card.upright_keywords && (
                <div className="mt-2">
                  <h3 className="text-sm font-medium">正位关键词:</h3>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {Array.isArray(card.upright_keywords) &&
                      card.upright_keywords.map((keyword: string, idx: number) => (
                        <span
                          key={idx}
                          className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                        >
                          {keyword}
                        </span>
                      ))}
                  </div>
                </div>
              )}

              {card.reversed_keywords && (
                <div className="mt-2">
                  <h3 className="text-sm font-medium">逆位关键词:</h3>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {Array.isArray(card.reversed_keywords) &&
                      card.reversed_keywords.map((keyword: string, idx: number) => (
                        <span
                          key={idx}
                          className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded"
                        >
                          {keyword}
                        </span>
                      ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        !loading && <p className="text-gray-500">暂无数据，请点击按钮获取塔罗牌数据</p>
      )}
    </main>
  )
}
