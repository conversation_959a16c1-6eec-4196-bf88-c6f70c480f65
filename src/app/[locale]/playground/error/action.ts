'use server'
import 'server-only'
import withServerActionErrorHandling from '@/lib/error/withServerActionErrorHandling'

// 一个会抛出异常的Server Action
async function throwErrorAction(message: string): Promise<string> {
  if (message) {
    throw new Error(message)
  }
  return '成功执行'
}

// 使用错误处理包装器包装的Server Action
export const safeThrowErrorAction = await withServerActionErrorHandling(throwErrorAction, {
  environment: 'development',
  path: '/playground/error',
  userId: undefined
})
