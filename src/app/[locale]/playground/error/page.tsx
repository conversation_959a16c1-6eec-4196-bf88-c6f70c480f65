'use client'

import { useState } from 'react'
import { safeThrowErrorAction } from './action'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function PlaygroundErrorPage() {
  const [errorMessage, setErrorMessage] = useState<string>('这是一个测试错误')
  const [result, setResult] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleTriggerError = async () => {
    try {
      setIsLoading(true)
      const res = await safeThrowErrorAction(errorMessage)
      setResult(res)
    } catch (error) {
      setResult(`捕获到错误: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">服务端错误处理演示</h1>

      <div className="space-y-2">
        <label htmlFor="error-message" className="block text-sm font-medium">
          错误信息
        </label>
        <Input
          id="error-message"
          value={errorMessage}
          onChange={(e) => setErrorMessage(e.target.value)}
          placeholder="输入错误信息"
          className="w-full"
        />
      </div>

      <Button onClick={handleTriggerError} disabled={isLoading}>
        {isLoading ? '处理中...' : '触发服务端错误'}
      </Button>

      {result && (
        <div className="mt-4 p-4 border rounded bg-gray-50">
          <h2 className="font-semibold">结果:</h2>
          <p>{result}</p>
        </div>
      )}

      <div className="mt-6 text-sm text-gray-600">
        <p>
          说明: 这个示例会触发一个Server
          Action并抛出异常，异常会被withServerActionErrorHandling捕获并上报到MongoDB，同时返回给客户端。
        </p>
        <p>留空错误信息可以测试成功执行的情况。</p>
      </div>
    </div>
  )
}
