import React from 'react';
import { Link } from 'next-view-transitions';
import { urlGenerator } from '@/services/url.service';
import AuthorAvatar from '@/components/common/AuthorAvatar';

interface PopularAuthorProps {
  id: string;
  name: string;
  avatar: string | null;
  bookCount: number;
}

const PopularAuthor: React.FC<PopularAuthorProps> = ({
  id,
  name,
  avatar,
  bookCount
}) => {
  // 生成作者详情页面URL
  const authorUrl = urlGenerator.author.detail({ id, name });

  return (
    <div className="flex items-center">
      <Link href={authorUrl}>
        <AuthorAvatar
          avatarUrl={avatar}
          authorName={name}
          size="md"
          className="w-12 h-12 mr-3"
        />
      </Link>
      <div>
        <h3 className="font-medium">
          <Link href={authorUrl} className="hover:text-green-500 transition-colors duration-200">
            {name}
          </Link>
        </h3>
        <p className="text-sm text-gray-500">{bookCount} books</p>
      </div>
    </div>
  );
};

export default PopularAuthor;
