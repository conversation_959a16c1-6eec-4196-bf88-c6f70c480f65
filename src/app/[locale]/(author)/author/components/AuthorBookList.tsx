"use client";

import React from 'react';
import AuthorInfo from './AuthorInfo';
import AuthorBookCard from './AuthorBookCard';
import PopularAuthors from './PopularAuthors';
import Pagination from '@/components/common/Pagination';
import { useRouter, usePathname } from 'next/navigation';
import { AuthorData, PopularAuthorData } from '@/services/actions/author.action';

// 定义组件接收的数据类型
interface AuthorBookListProps {
  authorId: string;
  authorData: {
    success: boolean;
    error?: string;
    data?: AuthorData;
  };
  popularAuthorsData: {
    success: boolean;
    error?: string;
    data: PopularAuthorData[];
  };
}

const AuthorBookList: React.FC<AuthorBookListProps> = ({ authorData, popularAuthorsData }) => {
  const router = useRouter();
  const pathname = usePathname();



  // 页面变化处理函数
  const handlePageChange = (newPage: number) => {
    // 使用路由导航到新页面
    router.push(`${pathname}?page=${newPage}`);
    // 滚动到顶部
    window.scrollTo(0, 0);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* 左侧：作者详情和书籍列表 */}
        <div className="lg:w-3/4">
          {authorData.success && authorData.data ? (
            <>
              {/* 作者信息 */}
              <AuthorInfo
                name={authorData.data.author.name}
                avatar={authorData.data.author.avatar}
                bio={authorData.data.author.bio}
                twitter={authorData.data.author.twitter}
                website={authorData.data.author.website}
              />

              {/* 书籍列表 */}
              <h2 className="font-heading font-bold text-3xl tracking-tight mb-8">
                Books by {authorData.data.author.name}
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                {authorData.data.books.length > 0 ? (
                  authorData.data.books.map((book) => (
                    <AuthorBookCard
                      key={book.id}
                      id={book.id.toString()}
                      title={book.title}
                      coverImage={book.coverUrl}
                    />
                  ))
                ) : (
                  <div className="col-span-full text-center py-10">
                    <p className="text-gray-500">No books found for this author.</p>
                  </div>
                )}
              </div>

              {/* 分页 */}
              {authorData.data.pagination.totalPages > 1 && (
                <div className="mt-12">
                  <Pagination
                    currentPage={authorData.data.pagination.page}
                    totalPages={authorData.data.pagination.totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          ) : (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{authorData.error || "Author not found"}</span>
            </div>
          )}
        </div>

        {/* 右侧：热门作者侧边栏 */}
        <div className="lg:w-1/4 relative">
          {popularAuthorsData.success && popularAuthorsData.data ? (
            <PopularAuthors authors={popularAuthorsData.data.map(author => ({
              id: author.id.toString(),
              name: author.name,
              avatar: author.avatar,
              bookCount: author.bookCount
            }))} />
          ) : (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded">
              <p className="text-gray-500">Unable to load popular authors.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthorBookList;
