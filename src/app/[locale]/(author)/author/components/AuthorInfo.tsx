import React from 'react';
import AuthorAvatar from '@/components/common/AuthorAvatar';

interface AuthorInfoProps {
  name: string;
  avatar: string | null;
  bio: string;
  twitter?: string;
  website?: string;
}

const AuthorInfo: React.FC<AuthorInfoProps> = ({
  name,
  avatar,
  bio,
  twitter,
  website
}) => {
  // 格式化 Twitter URL
  const formatTwitterUrl = (twitterValue: string): string => {
    if (!twitterValue) return '';

    // 如果已经是完整的 URL，直接返回
    if (twitterValue.startsWith('http')) {
      return twitterValue;
    }

    // 移除可能的 @ 符号
    const username = twitterValue.replace(/^@/, '');

    // 构建完整的 Twitter URL
    return `https://twitter.com/${username}`;
  };

  // 格式化 Twitter 显示文本
  const formatTwitterDisplay = (twitterValue: string): string => {
    if (!twitterValue) return '';

    // 如果是完整 URL，提取用户名
    if (twitterValue.startsWith('http')) {
      const username = twitterValue.split('/').pop() || '';
      return username.replace(/^@/, '');
    }

    // 移除可能的 @ 符号
    return twitterValue.replace(/^@/, '');
  };

  // 格式化 Website URL
  const formatWebsiteUrl = (websiteValue: string): string => {
    if (!websiteValue) return '';

    // 如果没有协议前缀，添加 https://
    if (!websiteValue.startsWith('http')) {
      return `https://${websiteValue}`;
    }

    return websiteValue;
  };

  // 格式化 Website 显示文本
  const formatWebsiteDisplay = (websiteValue: string): string => {
    if (!websiteValue) return '';

    // 移除协议和 www 前缀
    return websiteValue.replace(/^https?:\/\/(www\.)?/, '');
  };

  const twitterUrl = formatTwitterUrl(twitter || '');
  const twitterDisplay = formatTwitterDisplay(twitter || '');
  const websiteUrl = formatWebsiteUrl(website || '');
  const websiteDisplay = formatWebsiteDisplay(website || '');

  return (
    <div className="flex flex-col md:flex-row items-center md:items-start gap-8 mb-12">
      <AuthorAvatar
        avatarUrl={avatar}
        authorName={name}
        size="xl"
        className="w-32 h-32"
      />
      <div>
        <h1 className="font-heading font-bold text-4xl tracking-tight mb-2">{name}</h1>
        <div className="flex flex-wrap gap-4 mb-4">
          {twitter && twitterUrl && (
            <a className="text-blue-500 hover:underline flex items-center" href={twitterUrl} target="_blank" rel="noopener noreferrer nofollow">
              <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.07 10.07 0 01-3.127 1.195 4.93 4.93 0 00-8.397 4.49A14.1 14.1 0 011.64 3.162a4.929 4.929 0 001.523 6.574 4.907 4.907 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"></path>
              </svg>
              @{twitterDisplay}
            </a>
          )}
          {website && websiteUrl && (
            <a className="text-gray-700 hover:underline flex items-center" href={websiteUrl} target="_blank" rel="noopener noreferrer nofollow">
              <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              {websiteDisplay}
            </a>
          )}
        </div>
        <p className="text-gray-700 max-w-3xl">{bio}</p>
      </div>
    </div>
  );
};

export default AuthorInfo;
