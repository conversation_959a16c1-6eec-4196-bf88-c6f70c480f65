import React from 'react';
import PopularAuthor from './PopularAuthor';

interface PopularAuthorsProps {
  authors: {
    id: string;
    name: string;
    avatar: string | null;
    bookCount: number;
  }[];
}

const PopularAuthors: <AUTHORS>
  return (
    <div className="sticky top-24">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="font-heading font-bold text-xl tracking-tight mb-6">Popular Authors</h2>
        <div className="space-y-4">
          {authors.map((author) => (
            <PopularAuthor
              key={author.id}
              id={author.id}
              name={author.name}
              avatar={author.avatar}
              bookCount={author.bookCount}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PopularAuthors;
