"use client";

import React from 'react';
import { Link } from 'next-view-transitions';
import { urlGenerator } from '@/services/url.service';

interface AuthorBookCardProps {
  id: string;
  title: string;
  coverImage: string;
}

const AuthorBookCard: React.FC<AuthorBookCardProps> = ({ id, title, coverImage }) => {
  // 生成书籍详情页面URL（统一slug格式）
  const bookDetailUrl = urlGenerator.book.detail({
    id,
    title
  });

  return (
    <div className="flex flex-col items-center">
      <Link href={bookDetailUrl}>
        <img
          className="mb-3 shadow-md hover:shadow-lg transition-shadow duration-300"
          src={coverImage}
          alt={`${title} Cover`}
          style={{ maxWidth: '176px', maxHeight: '256px' }}
        />
      </Link>
      <h3 className="font-medium text-center">{title}</h3>
    </div>
  );
};

export default AuthorBookCard;
