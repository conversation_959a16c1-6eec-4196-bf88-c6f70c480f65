'use client';

import { useEffect } from 'react';

interface AuthorViewTrackerProps {
  authorId: number;
  language: string;
}

/**
 * 客户端组件，用于追踪作者访问量
 * 在组件挂载时发送请求到 API 端点
 * 参考书籍页面的 ViewTracker 组件实现
 */
export default function AuthorViewTracker({ authorId, language }: AuthorViewTrackerProps) {
  useEffect(() => {
    const trackView = async () => {
      try {
        // 发送请求到 API 端点
        const response = await fetch('/api/author-view', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: authorId, language }),
        });

        if (!response.ok) {
          console.error('Failed to track author view:', await response.text());
        }
      } catch (error) {
        console.error('Error tracking author view:', error);
      }
    };

    trackView();

  }, [authorId, language]);

  // 这个组件不渲染任何内容
  return null;
}
