import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { getAuthorData, getPopularAuthorsData } from '@/services/actions/author.action';
import AuthorBookList from '@/app/[locale]/(author)/author/components/AuthorBookList';
import AuthorViewTracker from './components/AuthorViewTracker';
import { parseAuthorSlug, isValidAuthorId, generateAuthorSlug } from '@/utils/author.utils';

// 动态生成页面元数据
export async function generateMetadata({ params }: { params: Promise<{ slug: string, locale: string }> }): Promise<Metadata> {
  const { slug, locale } = await params;

  try {
    // 解析slug获取作者ID
    const parsed = parseAuthorSlug(slug);
    if (!isValidAuthorId(parsed.id)) {
      throw new Error('Invalid author ID');
    }

    // 获取作者数据
    const authorData = await getAuthorData(parsed.id.toString(), 1, 1);

    if (authorData.success && authorData.data) {
      // 获取品牌名称
      const t = await getTranslations({ locale, namespace: 'Metadata' });
      const brandName = t('brandName');

      // 获取作者信息
      const authorName = authorData.data.author.name;
      const mostPopularBook = authorData.data.books[0]?.title || '';

      // 构建SEO标题和描述
      const title = `${authorName} (Author of ${mostPopularBook}) Book Lists | ${brandName}`;
      const description = authorData.data.author.bio.length > 160
        ? authorData.data.author.bio.substring(0, 157) + '...'
        : authorData.data.author.bio;

      // 构建规范URL - 使用新的slug格式
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      const authorSlug = generateAuthorSlug(authorName, parsed.id);
      const canonicalUrl = `${baseUrl}/author/${authorSlug}`;

      // 处理头像URL，提供默认值
      const avatarUrl = authorData.data.author.avatar || `${baseUrl}/images/default-author-avatar.svg`;

      return {
        title,
        description,
        alternates: {
          canonical: canonicalUrl,
          languages: {
            'en-US': `${baseUrl}/author/${authorSlug}`,
            'zh-CN': `${baseUrl}/zh/author/${authorSlug}`
          }
        },
        openGraph: {
          title,
          description,
          url: canonicalUrl,
          type: 'profile',
          images: [
            {
              url: avatarUrl,
              width: 200,
              height: 200,
              alt: authorName
            }
          ]
        },
        twitter: {
          card: 'summary',
          title: `${authorName} | Author Profile | ${brandName}`,
          description: description,
          images: [avatarUrl]
        }
      };
    }
  } catch (error) {
    console.error('Get author metadata failed:', error);
  }

  // 默认元数据
  return {
    title: 'Author Profile | 15Minutes',
    description: 'Explore author profiles and their book summaries on 15Minutes.'
  };
};

export default async function AuthorDetailPage({
  params,
  searchParams
}: {
  params: Promise<{ slug: string, locale: string }>,
  searchParams: Promise<{ page?: string }>
}) {
  const { slug, locale } = await params;
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1;

  try {
    // 1. 解析slug获取作者ID（支持新旧格式）
    const parsed = parseAuthorSlug(slug);

    if (!isValidAuthorId(parsed.id)) {
      console.error('Invalid author ID:', parsed.id);
      notFound();
    }

    // 2. 并行获取作者详情和热门作者数据
    const [authorData, popularAuthorsData] = await Promise.all([
      getAuthorData(parsed.id.toString(), page),
      getPopularAuthorsData(12)
    ]);

    // 如果没有找到作者数据，返回404页面
    if (!authorData.success || !authorData.data) {
      notFound();
    }

    // 将数据传递给客户端组件，并包含AuthorViewTracker组件
    return (
      <>
        {/* 客户端组件，用于追踪作者访问量 */}
        <AuthorViewTracker authorId={parsed.id} language={locale} />
        <AuthorBookList
          authorId={parsed.id.toString()}
          authorData={authorData}
          popularAuthorsData={popularAuthorsData}
        />
      </>
    );
  } catch (error) {
    console.error('Error in AuthorDetailPage:', error);

    // 如果是解析错误，返回404
    if (error instanceof Error && error.message.includes('Unable to parse author slug')) {
      notFound();
    }

    // 其他错误，显示错误页面
    return <div>Failed to load author data. Please try again later.</div>;
  }
}
