/* 修复text-6xl行高问题 */
:root {
  /* 定义所有文本大小的行高变量 */
  --tw-leading: 1.2; /* 默认行高 */

  /* 文本大小变量 */
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;

  --text-sm: 0.875rem;
  --text-sm--line-height: 1.25rem;

  --text-base: 1rem;
  --text-base--line-height: 1.5rem;

  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;

  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;

  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;

  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;

  --text-4xl: 2.25rem;
  --text-4xl--line-height: 1.2;

  --text-5xl: 3rem;
  --text-5xl--line-height: 1.2;

  --text-6xl: 4rem;
  --text-6xl--line-height: 1.2;

  --text-7xl: 4.5rem;
  --text-7xl--line-height: 1;

  --text-8xl: 6rem;
  --text-8xl--line-height: 1;

  --text-9xl: 8rem;
  --text-9xl--line-height: 1;
}

/* 确保在媒体查询中也能正确应用 */
@media (min-width: 768px) {
  .md\:text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--text-6xl--line-height, var(--tw-leading));
  }
}

/* 为所有文本大小类添加行高变量 */
.text-xs { line-height: var(--text-xs--line-height, var(--tw-leading)); }
.text-sm { line-height: var(--text-sm--line-height, var(--tw-leading)); }
.text-base { line-height: var(--text-base--line-height, var(--tw-leading)); }
.text-lg { line-height: var(--text-lg--line-height, var(--tw-leading)); }
.text-xl { line-height: var(--text-xl--line-height, var(--tw-leading)); }
.text-2xl { line-height: var(--text-2xl--line-height, var(--tw-leading)); }
.text-3xl { line-height: var(--text-3xl--line-height, var(--tw-leading)); }
.text-4xl { line-height: var(--text-4xl--line-height, var(--tw-leading)); }
.text-5xl { line-height: var(--text-5xl--line-height, var(--tw-leading)); }
.text-6xl { line-height: var(--text-6xl--line-height, var(--tw-leading)); }
.text-7xl { line-height: var(--text-7xl--line-height, var(--tw-leading)); }
.text-8xl { line-height: var(--text-8xl--line-height, var(--tw-leading)); }
.text-9xl { line-height: var(--text-9xl--line-height, var(--tw-leading)); }

/* 响应式文本大小类 */
.sm\:text-6xl { line-height: var(--text-6xl--line-height, var(--tw-leading)); }
.md\:text-6xl { line-height: var(--text-6xl--line-height, var(--tw-leading)); }
.lg\:text-6xl { line-height: var(--text-6xl--line-height, var(--tw-leading)); }
.xl\:text-6xl { line-height: var(--text-6xl--line-height, var(--tw-leading)); }
