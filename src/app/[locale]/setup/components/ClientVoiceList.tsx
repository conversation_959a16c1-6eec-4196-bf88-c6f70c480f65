'use client'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

export function ClientVoiceList() {
  const t = useTranslations('ClientVoice')

  const testimonials = [
    {
      id: 1,
      content: t('testimonial1'),
      author: {
        name: '<PERSON>',
        avatar: '/avatars/avatar-1.png'
      }
    },
    {
      id: 2,
      content: t('testimonial2'),
      author: {
        name: '<PERSON>',
        avatar: '/avatars/avatar-2.png'
      }
    },
    {
      id: 3,
      content: t('testimonial3'),
      author: {
        name: '<PERSON>',
        avatar: '/avatars/avatar-3.png'
      }
    },
    {
      id: 4,
      content: t('testimonial4'),
      author: {
        name: '<PERSON>',
        avatar: '/avatars/avatar-4.png'
      }
    }
  ]

  return (
    <div className="w-full space-y-6">
      <h2 className="text-2xl font-bold text-center mb-8">{t('title')}</h2>
      
      <div className="space-y-6">
        {testimonials.map((testimonial) => (
          <div 
            key={testimonial.id}
            className="bg-white p-4 rounded-lg shadow-sm"
          >
            <p className="text-gray-700 mb-4">{testimonial.content}</p>
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                <Image 
                  src={testimonial.author.avatar} 
                  alt={testimonial.author.name}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <span className="font-medium">{testimonial.author.name}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
