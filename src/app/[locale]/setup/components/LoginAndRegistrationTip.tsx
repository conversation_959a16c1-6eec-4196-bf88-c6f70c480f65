'use client'
import { useState } from 'react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { Link, useRouter } from '@/i18n/routing'
import { useGoogleLogin } from '@react-oauth/google'
import { googleAuth, login } from '@/services/client/authService'
import { ResponseCode } from '@/utils/constants'
import { useToast } from '@/hooks/useToast'
import { useForm } from 'react-hook-form'
import { AuthForm, FormData } from '@/components/AuthForm'
import { ClientVoiceList } from './ClientVoiceList'

interface Props {
  onNext: () => void
  onPrev?: () => void
}

export function LoginAndRegistrationTip({ onNext }: Props) {
  const [showLoginForm, setShowLoginForm] = useState(false)
  const t = useTranslations('Account')
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { showToast } = useToast()
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>()
  const [rememberMe, setRememberMe] = useState(false)

  const googleLogin = useGoogleLogin({
    flow: 'auth-code',
    onSuccess: async (response) => {
      setLoading(true)
      try {
        const res = await googleAuth(response.code)
        if (res.code === ResponseCode.Success) {
          router.push('/welcome')
          router.refresh()
          onNext()
        } else {
          showToast(res.message, 'error')
        }
      } catch (err) {
        console.warn(err)
        showToast('Login failed', 'error')
      } finally {
        setLoading(false)
      }
    }
  })

  const handleGoogleLogin = () => {
    googleLogin()
  }

  const onSubmit = async (data: FormData): Promise<void> => {
    setLoading(true)
    try {
      const response = await login(data.email, data.password)
      if (response.code === ResponseCode.Success) {
        router.push('/welcome')
        router.refresh()
        onNext()
      } else {
        showToast(response.message, 'error')
      }
    } catch (err) {
      console.warn(err)
      showToast('Login failed', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleEmailLogin = () => {
    setShowLoginForm(true)
  }

  return (
    <div className="notranslate h-[680px] grid grid-cols-[680px_680px] justify-center">
      {/* 左侧评论区 */}
      <div className="h-[680px] flex">
        <div className="bg-[#e8e9ff] w-full h-full flex overflow-y-auto p-8 flex-col justify-start items-center rounded-[40px]">
          <ClientVoiceList />
        </div>
      </div>

      {/* 右侧登录区 */}
      <div className="flex justify-center items-center p-4">
        <div className="flex flex-col justify-between px-8 w-full h-full">
          <div>
            {/* Progress bar */}
            <div className="w-[180px] mb-4 bg-gray-200 rounded-full h-2">
              <div className="bg-primary w-full h-2 rounded-full"></div>
            </div>

            <div className="mb-8 text-center">
              <h1 className="text-4xl font-semibold text-text mb-6 text-left notranslate">
                👋 {t('title')}
              </h1>
              <p className="text-lg text-left text-gray-500 notranslate">{t('subtitle')}</p>
            </div>

            <div className="flex flex-col gap-4">
              {!showLoginForm ? (
                <>
                  <button
                    type="button"
                    className="w-full py-3 px-4 inline-flex justify-center items-center gap-2 rounded-lg border font-medium bg-white text-gray-700 shadow-xs align-middle hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary transition-all text-sm"
                    onClick={handleGoogleLogin}
                    disabled={loading}
                  >
                    <Image src="/google.svg" alt="" width={20} height={20} />
                    <span className="notranslate">{loading ? t('loading') : t('google')}</span>
                  </button>

                  <div className="notranslate py-3 flex items-center text-xs text-gray-400 uppercase before:flex-[1_1_0%] before:border-t before:border-gray-200 before:mr-6 after:flex-[1_1_0%] after:border-t after:border-gray-200 after:ml-6">
                    {t('or')}
                  </div>

                  <button
                    type="button"
                    className="w-full py-3 px-4 inline-flex justify-center items-center gap-2 rounded-lg border font-medium bg-white text-gray-700 shadow-xs align-middle hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary transition-all text-sm"
                    onClick={handleEmailLogin}
                  >
                    <span className="notranslate">{t('email')}</span>
                  </button>
                </>
              ) : (
                <AuthForm
                  type="sign-in"
                  onSubmit={onSubmit}
                  register={register}
                  handleSubmit={handleSubmit}
                  errors={errors}
                  loading={loading}
                  rememberMe={rememberMe}
                  setRememberMe={setRememberMe}
                />
              )}
            </div>
          </div>

          <Link href="/welcome">
            <button
              onClick={onNext}
              className="mb-[48px] mt-2 w-full h-11 text-base text-gray-500 rounded-lg disabled:bg-gray-400 disabled:hover:bg-gray-400 hover:text-gray-700"
            >
              {t('skip')}
            </button>
          </Link>
        </div>
      </div>
    </div>
  )
}
