'use client'
import { <PERSON>, useRouter } from '@/i18n/routing'
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { GoogleIcon } from '@/components/svg/GoogleIcon'
import { useCookies } from 'react-cookie'
import { AuthForm, FormData } from '@/components/AuthForm'
import { googleAuth, login } from '@/services/client/authService'
import { useToast } from '@/hooks/useToast'
import Button from '@/components/Button'
import { ResponseCode } from '@/utils/constants'
import { useGoogleLogin } from '@react-oauth/google'
import { useTranslations } from 'next-intl'

export default function Login() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<FormData>()
  const [loading, setLoading] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [cookies, setCookie] = useCookies(['accountForm', 'rememberMe'])
  const router = useRouter()

  const { showToast } = useToast()
  const t = useTranslations('Login')

  const [googleLoading, setGoogleLoading] = useState(false)

  useEffect(() => {
    if (cookies.rememberMe) {
      setRememberMe(true)
      if (cookies.accountForm) {
        setValue('email', cookies.accountForm.email)
        setValue('password', cookies.accountForm.password)
      }
    }
  }, [cookies, setValue])

  const onSubmit = async (data: FormData): Promise<void> => {
    setLoading(true)
    try {
      if (rememberMe) {
        setCookie('accountForm', { email: data.email, password: data.password }, { path: '/' })
      }
      const response = await login(data.email, data.password)
      if (response.code === ResponseCode.Success) {
        const from = new URLSearchParams(window.location.search).get('from')
        if (from) {
          router.push(from)
        } else {
          router.push('/')
        }
      } else {
        showToast(response.message, 'error')
      }
    } catch (err) {
      console.warn(err)
    } finally {
      setLoading(false)
    }
  }

  const googleLogin = useGoogleLogin({
    flow: 'auth-code',
    onSuccess: async (response) => {
      try {
        const res = await googleAuth(response.code)
        if (res.code === ResponseCode.Success) {
          const from = new URLSearchParams(window.location.search).get('from')
          if (from) {
            router.push(from)
          } else {
            router.push('/')
          }
        }
      } finally {
        setGoogleLoading(false)
      }
    },
    onError: () => {
      setGoogleLoading(false)
    }
  })

  const handleGoogleSignin = () => {
    setGoogleLoading(true)
    googleLogin()
  }

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <p className="text-start mt-2 text-gray-500">{t('description')}</p>
      <Button variant="outline" type="button" onClick={handleGoogleSignin} loading={googleLoading}>
        <GoogleIcon size={16} />
        <span className="ml-2">{t('continueWithGoogle')}</span>
      </Button>
      <div className="flex items-center my-4">
        <hr className="grow border-t border-gray-300" />
        <span className="mx-2 text-gray-500">{t('orContinueWith')}</span>
        <hr className="grow border-t border-gray-300" />
      </div>
      <AuthForm
        type="sign-in"
        onSubmit={onSubmit}
        register={register}
        handleSubmit={handleSubmit}
        errors={errors}
        loading={loading}
        rememberMe={rememberMe}
        setRememberMe={setRememberMe}
      />
      <p className="mt-4 text-center">
        {t('noAccount')}
        <Link href="/register" className="text-primary ml-2">
          {t('signUp')}
        </Link>
      </p>
    </>
  )
}
