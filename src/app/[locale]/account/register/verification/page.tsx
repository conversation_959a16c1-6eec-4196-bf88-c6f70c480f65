'use client'
import { Suspense } from 'react'
import Button from '@/components/Button'
import Input from '@/components/Input'
import { useForm } from 'react-hook-form'
import { useState } from 'react'
import { completeSignup, resendActivationCode } from '@/services/client/authService'
import { useToast } from '@/hooks/useToast'
import { useRouter } from '@/i18n/routing'
import { useTranslations } from 'next-intl'
import { useSearchParams } from 'next/navigation'

function VerificationForm() {
  const searchParams = useSearchParams()
  const email = searchParams.get('email')

  interface FormData {
    code: string
  }

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>()
  const [loading, setLoading] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)
  const { showToast } = useToast()
  const router = useRouter()
  const t = useTranslations('Register')

  const onSubmit = async (data: { code: string }) => {
    setLoading(true)
    if (!email) {
      showToast('Invalid email', 'error')
      router.push('/register')
      return
    }
    try {
      const response = await completeSignup(email, data.code)
      if (response.code === 200) {
        showToast('Account activated successfully', 'success')
        router.push('/login')
      } else {
        showToast(response.message, 'error')
      }
    } catch (err) {
      console.warn(err)
    } finally {
      setLoading(false)
    }
  }

  const handleResend = async () => {
    setResendLoading(true)
    if (!email) {
      showToast('Invalid email', 'error')
      router.push('/register')
      return
    }
    try {
      const response = await resendActivationCode(email)
      if (response.code === 200) {
        showToast('Activation code resent successfully', 'success')
      } else {
        showToast(response.message, 'error')
      }
    } catch (err) {
      console.warn(err)
    } finally {
      setResendLoading(false)
    }
  }

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('finishRegister')}</h1>
      <p>
        {t('emailSentMessage')}
        <span className="px-2 font-bold">{email}</span>
        {t('pleaseActivate')}
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Input
          placeholder={t('activationCodePlaceholder')}
          label={t('activationCodeLabel')}
          required
          className={errors.code ? 'border-red-500' : ''}
          error={errors.code?.message}
          register={register('code', {
            required: t('activationCodeRequired'),
            pattern: {
              value: /^\d{4}$/,
              message: t('activationCodeFormat')
            }
          })}
        />
        <Button className="mt-8" type="submit" variant="filled" loading={loading}>
          {t('activateButton')}
        </Button>
      </form>
      <Button type="button" variant="text" onClick={handleResend} loading={resendLoading}>
        {t('resendButton')}
      </Button>
    </>
  )
}

export default function RegisterVerification() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerificationForm />
    </Suspense>
  )
}
