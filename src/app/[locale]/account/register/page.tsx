'use client'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Link, useRouter } from '@/i18n/routing'
import { AuthForm, FormData } from '@/components/AuthForm'
import { signup } from '@/services/client/authService'
import { useToast } from '@/hooks/useToast'
import { useTranslations } from 'next-intl'

export default function Register() {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>()

  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const { showToast } = useToast()

  const t = useTranslations('Register')

  const onSubmit = async (data: FormData): Promise<void> => {
    setLoading(true)
    try {
      const response = await signup(data.email, data.password)
      setLoading(false)
      if (response.code === 200) {
        router.push(`/register/verification?email=${data.email}`)
      } else {
        showToast(response.message, 'error')
      }
    } catch {
      setLoading(false)
    }
  }

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <AuthForm
        type="sign-up"
        handleSubmit={handleSubmit}
        onSubmit={onSubmit}
        register={register}
        errors={errors}
        loading={loading}
      />
      <p className="mt-4 text-center">
        {t('alreadyHaveAccount')}{' '}
        <Link href="/login" className="text-primary">
          {t('clickToLogin')}
        </Link>
      </p>
    </>
  )
}
