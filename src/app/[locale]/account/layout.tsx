import { Logo } from '@/components/Logo'
import { Link } from '@/i18n/routing'

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="grow container mx-auto p-4">
        <div className="flex flex-col gap-4 items-center justify-center min-h-screen">
          <Link href="/">
            <Logo size={60} />
          </Link>
          <div className="flex flex-col gap-2 w-[480px] p-6 bg-white rounded-lg shadow-lg">
            {children}
          </div>
        </div>
      </main>
    </div>
  )
}
