'use client'
import { Link } from '@/i18n/routing'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Suspense } from 'react'

// 创建一个内部组件来使用 useSearchParams
function ResetPasswordContent() {
  const t = useTranslations('resetPasswordSent')
  const searchParams = useSearchParams()
  const email = searchParams.get('email')

  return (
    <>
      <h1 className="flex gap-2 text-2xl font-bold text-center">{t('title')}</h1>
      <p>{t('emailSentMessage', { email })}</p>
      <Link href="/login">
        <button type="submit" className="w-full py-2 bg-primary text-white rounded-lg">
          {t('loginButton')}
        </button>
      </Link>
      <Link href="/register">
        <button type="submit" className="w-full py-2 text-primary rounded-lg">
          {t('signUpButton')}
        </button>
      </Link>
    </>
  )
}

// 主组件使用 Suspense 包装内容组件
export default function ResetPasswordSent() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordContent />
    </Suspense>
  )
}
