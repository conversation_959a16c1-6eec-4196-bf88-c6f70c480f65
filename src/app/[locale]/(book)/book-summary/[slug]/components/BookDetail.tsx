'use client';

import React, { useState, useEffect } from 'react';
import B<PERSON><PERSON><PERSON>bNav from './BreadcrumbNav';
import BookHeader from './BookHeader';
import AccordionSection from './AccordionSection';
import TabNavigation from './TabNavigation';
import SummarySection from './ContentSections/SummarySection';
import QuoteSection from './ContentSections/QuoteSection';
import ReviewSection from './ContentSections/ReviewSection';
import AuthorSection from './ContentSections/AuthorSection';
import RelatedBooksSection from './ContentSections/RelatedBooksSection';
import TrendingBooksSection from './ContentSections/TrendingBooksSection';
import DownloadSection from './ContentSections/DownloadSection';
import SidebarTOC from './SidebarTOC';
import AudioPlayer from './AudioPlayer';
import BookDetailData from '../types';
import { calculateChaptersReadingTime } from '@/utils/readingTime';

interface BookDetailProps {
  data: BookDetailData;
  locale?: string;
}

const BookDetail: React.FC<BookDetailProps> = ({ data, locale = 'en' }) => {
  const [activeTab, setActiveTab] = useState('summary');
  const [activeTocItem, setActiveTocItem] = useState('summary');
  const [isAudioPlayerActive, setIsAudioPlayerActive] = useState(false);
  const [shouldAutoPlay, setShouldAutoPlay] = useState(false);

  // 固定精确偏移量：Header(60px) + TabNav(56px) = 116px
  const SCROLL_OFFSET = 136;

  // 标签页配置
  const tabs = [
    { id: 'summary', label: 'Summary' },
    { id: 'quote', label: 'Quote' },
    // 只有当reviewSummary存在且不为空时才显示Review tab
    ...(data.book.reviewSummary && data.book.reviewSummary.trim() !== ''
      ? [{ id: 'review', label: 'Review' }]
      : []
    ),
    { id: 'author', label: 'Author' },
    { id: 'related', label: 'Related' },
    { id: 'trending', label: 'Trending' },
    { id: 'download', label: 'Download' }
  ];

  // 处理"Listen"按钮点击
  const handleListenClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsAudioPlayerActive(true);
    setShouldAutoPlay(true); // 设置自动播放意图
  };

  // 处理音频播放器关闭
  const handleCloseAudioPlayer = () => {
    setIsAudioPlayerActive(false);
    setShouldAutoPlay(false); // 重置自动播放意图
  };

  // 处理"Read Online"按钮点击 - 与Tab Summary功能一致
  const handleReadOnlineClick = () => {
    setActiveTab('summary'); // 更新Tab状态

    // 平滑滚动到目标元素（与TabNavigation逻辑一致）
    const target = document.getElementById('summary');
    if (target) {
      const y = target.getBoundingClientRect().top + window.scrollY - SCROLL_OFFSET;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  // 处理目录项点击
  const handleTocItemClick = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    const target = document.getElementById(targetId);
    if (target) {
      const y = target.getBoundingClientRect().top + window.scrollY - SCROLL_OFFSET;
      window.scrollTo({ top: y, behavior: 'smooth' });
      setActiveTocItem(targetId);
    }
  };

  // 从章节数据创建适合组件使用的格式
  const formattedChapters = data.chapters.map((chapter) => ({
    title: `${chapter.title}`,
    content: chapter.summary
  }));

  // 监听滚动，更新活动目录项
  useEffect(() => {
    const handleScroll = () => {
      const scrollPos = window.scrollY;

      // 定义所有可能的目录项ID
      const ids = [
        'summary',
        ...formattedChapters.map((_, index) => `chapter-${index + 1}`),
        'quote',
        // 只有当reviewSummary存在时才添加review到滚动监听
        ...(data.book.reviewSummary && data.book.reviewSummary.trim() !== ''
          ? ['review']
          : []
        ),
        'author',
        'related',
        'trending',
        'download'
      ];

      // 找出当前滚动位置对应的目录项
      let active = ids[0];
      for (const id of ids) {
        const el = document.getElementById(id);
        if (el && el.offsetTop - SCROLL_OFFSET <= scrollPos) {
          active = id;
        }
      }

      // 更新活动目录项状态
      setActiveTocItem(active);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始化

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [formattedChapters, data.book.reviewSummary]);

  // 计算实际阅读时长（基于章节内容）
  const calculatedReadingTime = calculateChaptersReadingTime(data.chapters);

  // 准备BookHeader组件所需的数据
  const headerBookData = {
    id: Number(data.book.id),
    title: data.book.title,
    subtitle: data.book.subtitle,
    author: data.authors[0]?.name || '',
    authorId: data.authors[0]?.id || '',
    coverImage: data.book.cover.imageUrl,
    rating: data.ratings.averageScore,
    ratingCount: data.ratings.totalCount,
    categories: data.categories.map((cat) => cat.name),
    readTime: `${calculatedReadingTime} minutes`,  // 使用计算的阅读时长
    format: data.book.hasAudio ? 'Text & Audiobook' : 'Text',
    keyIdeas: data.chapters.length  // 使用章节数量作为关键要点数
  };

  // 准备书籍详情数据
  const bookDetailsData = {
    categories: data.categories.map((cat) => cat.name).join(', '),
    contentType: data.book.contentType || 'Books',
    binding: data.book.binding || '',
    year: data.book.publicationYear.toString(),
    publisher: data.book.publisher.name,
    language: data.book.language.name,
    // 分开处理ISBN相关字段
    asin: data.book.asin || '',
    isbn: data.book.isbn || '',
    isbn13: data.book.isbn13 || '',
    // fileDownload: `PDF, ${data.book.fileInfo.pdf?.sizeInMb}MB | EPUB, ${data.book.fileInfo.epub?.sizeInMb}MB`,
    fileDownload: `PDF | EPUB`,
    ipfs: `${!data.book.ipfs.cid && data.book.ipfs.cidBlake2b ? 'null' : data.book.ipfs.cid + ', ' + data.book.ipfs.cidBlake2b}`
  };

  // 准备作者信息
  const authorInfo = {
    id: data.authors[0]?.id || '',
    name: data.authors[0]?.name || '',
    avatar: data.authors[0]?.imageUrl || '',
    bio: data.authors[0]?.biography || ''
  };

  // 准备相关书籍数据
  const relatedBooks = data.relatedBooks.map((book) => ({
    id: book.id,
    title: book.title,
    author: book.author, // 添加作者信息
    subtitle: book.subtitle || '', // 添加副标题信息
    coverImage: book.coverUrl,
    rating: book.rating.score
  }));

  // 准备热门书籍数据
  const trendingBooks = data.trendingBooks.map((book) => ({
    id: book.id,
    title: book.title,
    author: book.author, // 添加作者信息
    subtitle: book.subtitle || '', // 添加副标题信息
    coverImage: book.coverUrl,
    rating: book.rating.score
  }));

  // 准备下载信息
  const downloadInfo = {
    pdfSize: data.book.fileInfo.pdf ? `${data.book.fileInfo.pdf.sizeInMb} MB` : '0 MB',
    epubSize: data.book.fileInfo.epub ? `${data.book.fileInfo.epub.sizeInMb} MB` : '0 MB'
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <BreadcrumbNav bookTitle={data.book.title} category={data.categories[0]?.name} />

      <BookHeader
        book={headerBookData}
        handleListenClick={handleListenClick}
        onReadOnlineClick={handleReadOnlineClick}
      />

      <AccordionSection title="Brief Analysis">
        {data.book.plotSummary}
      </AccordionSection>

      <AccordionSection title="Book Details & Editions">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8">
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Categories</h3>
              <p className="text-gray-600">{bookDetailsData.categories}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Content Type</h3>
              <p className="text-gray-600">{bookDetailsData.contentType}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Binding</h3>
              <p className="text-gray-600">{bookDetailsData.binding}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Year</h3>
              <p className="text-gray-600">{bookDetailsData.year}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Publisher</h3>
              <p className="text-gray-600">{bookDetailsData.publisher}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Language</h3>
              <p className="text-gray-600">{bookDetailsData.language}</p>
            </div>
            {/* 分开展示ISBN相关字段，只有有值时才显示 */}
            {bookDetailsData.asin && (
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">ASIN</h3>
                <p className="text-gray-600">{bookDetailsData.asin}</p>
              </div>
            )}
            {bookDetailsData.isbn && (
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">ISBN</h3>
                <p className="text-gray-600">{bookDetailsData.isbn}</p>
              </div>
            )}
            {bookDetailsData.isbn13 && (
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">ISBN13</h3>
                <p className="text-gray-600">{bookDetailsData.isbn13}</p>
              </div>
            )}
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">File Download</h3>
              <p className="text-gray-600">{bookDetailsData.fileDownload}</p>
            </div>
            {/* <div>
              <h3 className="font-semibold text-gray-700 mb-2">IPFS</h3>
              <p className="text-gray-600 break-all">{bookDetailsData.ipfs}</p>
            </div> */}
          </div>
        </div>
      </AccordionSection>

      <section>
        <div className="container mx-auto py-8">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-2/3">
              <TabNavigation tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />

              <SummarySection
                bookName={data.book.title}
                synopsis={data.book.synopsis}
                chapters={formattedChapters}
              />

              <QuoteSection quote={data.bestQuote.text} />

              <ReviewSection reviewSummary={data.book.reviewSummary} />

              <AuthorSection author={authorInfo} />

              {
                relatedBooks?.length > 0 && <RelatedBooksSection
                  authorName={authorInfo.name}
                  books={relatedBooks}
                />
              }


              <TrendingBooksSection books={trendingBooks} />

              <DownloadSection
                fileInfo={data.book.fileInfo}
                pdfSize={downloadInfo.pdfSize}
                epubSize={downloadInfo.epubSize}
              />
            </div>

            <SidebarTOC
              book={{
                title: data.book.title,
                author: authorInfo.name,
                coverImage: data.book.cover.imageUrl,
                asin: data.book.asin,
                isbn: data.book.isbn
              }}
              chapters={formattedChapters}
              activeTocItem={activeTocItem}
              onTocItemClick={handleTocItemClick}
              handleListenClick={handleListenClick}
            />
          </div>
        </div>
      </section>

      <AudioPlayer
        book={{
          id: data.book.id,
          title: data.book.title,
          author: authorInfo.name,
          coverImage: data.book.cover.imageUrl
        }}
        isActive={isAudioPlayerActive}
        autoPlay={shouldAutoPlay}
        locale={locale}
        onClose={handleCloseAudioPlayer}
      />
    </div>
  );
};

export default BookDetail;
