'use client';

import React, { useState } from 'react';

interface AccordionSectionProps {
  title: string;
  children: React.ReactNode;
}

const AccordionSection: React.FC<AccordionSectionProps> = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4 border-b border-gray-200 last:border-0">
      <button
        type="button"
        className="w-full flex items-center justify-between py-4 px-2 focus:outline-none hover:bg-gray-50 transition rounded cursor-pointer group"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className="font-heading font-bold text-2xl text-left">{title}</h2>
        <svg
          className={`w-5 h-5 ml-2 transition-transform duration-300 group-hover:text-green-500 ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>

      <div
        className={`overflow-hidden transition-all duration-300 ease-out ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}
      >
        <div className="text-gray-700 leading-relaxed py-4 px-2">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AccordionSection;
