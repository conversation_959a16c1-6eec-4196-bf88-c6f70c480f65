'use client';

import React, { useCallback } from 'react';
import RatingSystem from './RatingSystem';
import FavoriteButton from '@/components/ui/FavoriteButton';
import { useFavorite } from '@/hooks/useFavorite';
import { useAuthAndPremiumCheck } from '@/hooks/useAuthAndPremiumCheck';
import { Link } from 'next-view-transitions';
import { urlGenerator } from '@/services/url.service';
import BookCover from '@/components/common/BookCover';

interface BookHeaderProps {
  book: {
    id: number;
    title: string;
    subtitle: string;
    author: string;
    authorId: string;
    coverImage: string;
    rating: number;
    ratingCount: number;
    categories: string[];
    readTime: string;
    format: string;
    keyIdeas: number;
  };
  handleListenClick?: (e: React.MouseEvent) => void;
  onReadOnlineClick?: () => void;
}

const BookHeader: React.FC<BookHeaderProps> = ({ book, handleListenClick, onReadOnlineClick }) => {
  // 使用收藏功能Hook
  const {
    isFavorited,
    isLoading,
    toggleFavorite,
    buttonState
  } = useFavorite(book.id);

  // 使用认证和付费验证Hook
  const { checkAuthAndPremium } = useAuthAndPremiumCheck({
    featureName: 'listen'
  });

  // 包装Listen按钮点击处理，使用新的认证和付费验证Hook
  const handleListenClickWithAuth = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();

    // 纯粹的状态检查，不执行任何业务逻辑
    const canProceed = await checkAuthAndPremium();

    // 只有检查通过才执行业务逻辑
    if (canProceed) {
      handleListenClick?.(e);
    }
  }, [handleListenClick, checkAuthAndPremium]);

  // 生成评分星星
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    // 全星
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={`full-${i}`} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
      );
    }

    // 半星（如果有）
    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
      );
    }

    // 空星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
      );
    }

    return stars;
  };

  return (
    <div className="flex flex-col md:flex-row gap-8 mb-12">
      <div className="md:w-1/4">
        <BookCover
          coverUrl={book.coverImage}
          title={book.title}
          width={300}
          height={400}
          className="rounded-lg shadow-md w-full"
          style={{ width: '100%', height: 'auto', aspectRatio: '2/3' }}
          fallbackColor="e9e1cc"
          priority={true}
        />
      </div>
      <div className="md:w-3/4">
        <h1 className="font-heading font-bold text-3xl md:text-4xl tracking-tight mb-2">{book.title}</h1>
        <h2 className="text-xl text-gray-600 mb-3">{book.subtitle}</h2>
        <div className="mb-4">
          <span className="text-lg">By <Link className="text-green-600 hover:underline" href={urlGenerator.author.detail({ id: book.authorId, name: book.author })}>{book.author}</Link></span>
        </div>
        <div className="flex items-center mb-4">
          <div className="flex">
            {renderStars(book.rating)}
          </div>
          <span className="ml-2 text-gray-600">{book.rating.toFixed(1)} ({book.ratingCount.toLocaleString()} ratings)</span>
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          {book.categories.map((category, index) => (
            <Link
              key={index}
              href={urlGenerator.category.detail(category)}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 hover:text-gray-800 transition-colors duration-200 cursor-pointer"
            >
              {category}
            </Link>
          ))}
        </div>
        <div className="flex items-center mb-6">
          <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span className="text-gray-600">{book.readTime} read | {book.format} | {book.keyIdeas} key ideas</span>
        </div>

        <RatingSystem bookId={book.id} />

        <div className="flex flex-wrap gap-4">
          <a
            className="px-6 py-3 block text-center bg-green-500 text-white text-sm font-bold hover:bg-green-600 focus:ring focus:ring-green-300 transition duration-200 rounded-lg"
            href="#"
            id="listenButton"
            onClick={handleListenClickWithAuth}
          >
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.465a5 5 0 001.06-7.06m-2.11 9.88a9 9 0 010-12.727"></path>
              </svg>
              Listen
            </div>
          </a>
          {/* <a
            className="px-6 py-3 block text-center border border-green-500 text-green-500 text-sm font-bold hover:bg-green-50 focus:ring focus:ring-green-300 transition duration-200 rounded-lg cursor-pointer"
            href="#summary"
            onClick={(e) => {
              e.preventDefault();
              onReadOnlineClick?.();
            }}
          >
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
              Read Online
            </div>
          </a> */}

          <FavoriteButton
            isFavorited={isFavorited}
            isLoading={isLoading}
            disabled={buttonState.disabled}
            text={buttonState.text}
            onClick={toggleFavorite}
          />
        </div>
      </div>
    </div>
  );
};

export default BookHeader;
