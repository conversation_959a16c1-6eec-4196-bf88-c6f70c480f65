'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuthAndPremiumCheck } from '@/hooks/useAuthAndPremiumCheck';
import { buildOptimizedAmazonUrl } from '@/utils/amazon.utils';

interface SidebarTOCProps {
  book: {
    title: string;
    author: string;
    coverImage: string;
    asin?: string;
    isbn?: string;
  };
  chapters: {
    title: string;
  }[];
  activeTocItem?: string; // 当前活动的目录项
  onTocItemClick?: (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => void; // 目录项点击处理函数
  handleListenClick?: (e: React.MouseEvent) => void; // Listen按钮点击处理函数
}

const SidebarTOC: React.FC<SidebarTOCProps> = ({ book, chapters, activeTocItem = 'summary', onTocItemClick, handleListenClick }) => {
  const [tocHeight, setTocHeight] = useState<string>('300px');
  const sidebarRef = useRef<HTMLDivElement>(null);
  const bookInfoRef = useRef<HTMLDivElement>(null);
  const tocHeaderRef = useRef<HTMLHeadingElement>(null);

  // 使用认证和付费验证Hook
  const { checkAuthAndPremium } = useAuthAndPremiumCheck({
    featureName: 'listen'
  });

  // 使用优化的Amazon URL构建策略：直接搜索而不是尝试直接链接
  const handleAmazonClick = () => {
    const amazonUrl = buildOptimizedAmazonUrl(book.title, book.author);
    window.open(amazonUrl, '_blank', 'noopener,noreferrer');
  };

  // 包装Listen按钮点击处理，使用认证和付费验证Hook
  const handleListenClickWithAuth = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();

    // 纯粹的状态检查，不执行任何业务逻辑
    const canProceed = await checkAuthAndPremium();

    // 只有检查通过才执行业务逻辑
    if (canProceed) {
      handleListenClick?.(e);
    }
  }, [handleListenClick, checkAuthAndPremium]);

  // 计算并设置高度
  useEffect(() => {
    const updateHeights = () => {
      if (typeof window !== 'undefined' && sidebarRef.current) {
        // 计算目录列表可用高度
        // 获取书籍信息区域和目录标题的高度
        const viewportHeight = window.innerHeight;
        const sidebarTop = 96; // 顶部导航栏高度 + top-24 的值
        const bookInfoHeight = bookInfoRef.current?.offsetHeight || 0;
        const tocHeaderHeight = tocHeaderRef.current?.offsetHeight || 0;

        // 目录列表高度 = 侧边栏高度 - 书籍信息高度 - 目录标题高度 - 内边距
        const padding = 40; // 上下内边距总和
        const availableTocHeight = viewportHeight - sidebarTop - bookInfoHeight - tocHeaderHeight - padding;

        // 设置目录列表高度，最小值为 150px
        setTocHeight(`${Math.max(150, availableTocHeight)}px`);
      }
    };

    // 初始计算
    updateHeights();

    // 监听窗口大小变化
    window.addEventListener('resize', updateHeights);

    // 清理函数
    return () => {
      window.removeEventListener('resize', updateHeights);
    };
  }, []);

  // 处理目录项点击
  const handleItemClick = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    if (onTocItemClick) {
      onTocItemClick(e, targetId);
    } else {
      e.preventDefault();
      const target = document.getElementById(targetId);
      if (target) {
        const y = target.getBoundingClientRect().top + window.scrollY - 120; // 考虑导航栏和标签栏的高度
        window.scrollTo({ top: y, behavior: 'smooth' });
      }
    }
  };
  return (
    <div className="md:w-1/3">
      <div
        className="sticky top-24 overflow-y-auto scrollbar-hide"
        ref={sidebarRef}
        style={{ maxHeight: `calc(100vh - 96px)` }}
      >
        <div className="flex flex-col items-center mb-8 p-6 border rounded-lg">
          <div className="w-full" ref={bookInfoRef}>
            <img
              className="mb-4 max-w-[176px] max-h-[256px] mx-auto"
              src={book.coverImage}
              alt={book.title}
            />
            <h2 className="text-2xl font-bold text-center mb-2">{book.title}</h2>
            <p className="text-lg text-gray-700 text-center mb-4">by {book.author}</p>

            <div className="flex gap-3 w-full mb-6">
              <button
                className="bg-yellow-400 hover:bg-yellow-500 text-black font-semibold py-2 px-4 rounded flex-1 text-center cursor-pointer"
                onClick={handleAmazonClick}
              >
                Buy on Amazon
              </button>
              <button
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded flex-1 text-center cursor-pointer"
                onClick={handleListenClickWithAuth}
              >
                <div className="flex items-center justify-center">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.465a5 5 0 001.06-7.06m-2.11 9.88a9 9 0 010-12.727"></path>
                  </svg>
                  Listen
                </div>
              </button>
            </div>
          </div>

          <div className="w-full">
            <h3 className="text-xl font-bold mb-4" ref={tocHeaderRef}>Table of Contents</h3>
            <div
              className="overflow-y-auto scrollbar-hide"
              style={{ maxHeight: tocHeight }}
            >
              <ul className="space-y-2 pb-2">
                <li className="border-b pb-2">
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'summary' ? 'text-blue-600 font-medium' : ''}`}
                    href="#summary"
                    onClick={(e) => handleItemClick(e, "summary")}
                  >
                    {book.title} Plot Summary
                  </a>
                </li>

                {chapters.map((chapter, index) => {
                  const chapterId = `chapter-${index + 1}`;
                  return (
                    <li key={index} className="pl-4 border-b pb-2">
                      <a
                        className={`hover:text-blue-600 ${activeTocItem === chapterId ? 'text-blue-600 font-medium' : ''}`}
                        href={`#${chapterId}`}
                        onClick={(e) => handleItemClick(e, chapterId)}
                      >
                        {chapter.title}
                      </a>
                    </li>
                  );
                })}

                <li className="border-b pb-2">
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'quote' ? 'text-blue-600 font-medium' : ''}`}
                    href="#quote"
                    onClick={(e) => handleItemClick(e, "quote")}
                  >
                    Best Quote
                  </a>
                </li>
                <li className="border-b pb-2">
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'author' ? 'text-blue-600 font-medium' : ''}`}
                    href="#author"
                    onClick={(e) => handleItemClick(e, "author")}
                  >
                    About Author
                  </a>
                </li>
                <li className="border-b pb-2">
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'related' ? 'text-blue-600 font-medium' : ''}`}
                    href="#related"
                    onClick={(e) => handleItemClick(e, "related")}
                  >
                    Related Books
                  </a>
                </li>
                <li className="border-b pb-2">
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'trending' ? 'text-blue-600 font-medium' : ''}`}
                    href="#trending"
                    onClick={(e) => handleItemClick(e, "trending")}
                  >
                    Trending Books
                  </a>
                </li>
                <li>
                  <a
                    className={`hover:text-blue-600 ${activeTocItem === 'download' ? 'text-blue-600 font-medium' : ''}`}
                    href="#download"
                    onClick={(e) => handleItemClick(e, "download")}
                  >
                    Download PDF & EPUB
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidebarTOC;
