'use client';

import React, { useEffect } from 'react';
import styles from './TabNavigation.module.css';

interface TabItem {
  id: string;
  label: string;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  setActiveTab: (id: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ tabs, activeTab, setActiveTab }) => {
  // 固定精确偏移量：Header(60px) + TabNav(56px) = 116px
  const SCROLL_OFFSET = 136;

  // 处理点击事件
  const handleTabClick = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    setActiveTab(id);

    // 平滑滚动到目标元素
    const target = document.getElementById(id);
    if (target) {
      const y = target.getBoundingClientRect().top + window.scrollY - SCROLL_OFFSET;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  // 监听滚动事件，更新活动标签
  useEffect(() => {
    const handleScroll = () => {
      const scrollPos = window.scrollY;
      const ids = tabs.map(tab => tab.id);

      let active = ids[0];
      for (const id of ids) {
        const el = document.getElementById(id);
        if (el && el.offsetTop - SCROLL_OFFSET <= scrollPos) { // 使用相同的偏移量保持一致
          active = id;
        }
      }

      setActiveTab(active);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始化

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [tabs, setActiveTab]);

  return (
    <nav className="sticky top-16 z-20 bg-white py-2 pb-2 mb-8 shadow-sm">
      <ul className="flex gap-4 px-4 py-2 overflow-x-auto whitespace-nowrap">
        {tabs.map((tab) => (
          <li key={tab.id}>
            <a
              href={`#${tab.id}`}
              className={`${styles['tab-btn']} ${activeTab === tab.id ? styles.active : ''}`}
              onClick={(e) => handleTabClick(e, tab.id)}
            >
              {tab.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default TabNavigation;
