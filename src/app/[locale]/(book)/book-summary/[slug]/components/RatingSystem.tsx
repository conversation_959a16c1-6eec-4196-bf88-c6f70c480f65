'use client'

/**
 * 用户评分组件
 * 集成认证系统、Server Actions 和全局登录弹窗
 * 支持乐观更新和完整的错误处理
 */

import React, { useState, useEffect, useCallback } from 'react'
import { getUserRating, submitRating } from '@/actions/rating.action'
import { useAuthModal } from '@/store/authModalStore'
import { useToast } from '@/hooks/useToast'
import { checkAuthStatus } from '@/lib/auth/server-actions'

/**
 * 评分组件属性
 */
interface RatingSystemProps {
  bookId: number
  className?: string
}

/**
 * 星级评分状态
 */
interface RatingState {
  userRating: number // 用户当前评分 (0-5)
  isAuthenticated: boolean // 是否已认证
  isInitialLoading: boolean // 是否初始加载中
}

/**
 * 用户评分组件
 */
const RatingSystem: React.FC<RatingSystemProps> = ({
  bookId,
  className = "mb-6 p-4 rounded-lg flex items-center"
}) => {
  // 状态管理
  const [state, setState] = useState<RatingState>({
    userRating: 0,
    isAuthenticated: false,
    isInitialLoading: true
  })

  // 鼠标悬停状态
  const [hoverRating, setHoverRating] = useState<number>(0)

  // 全局服务
  const { showToast } = useToast()
  const { openLogin, setOnSuccess } = useAuthModal()

  /**
   * 检查认证状态并加载用户评分
   */
  const loadUserRating = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isInitialLoading: true }))

      // 检查认证状态
      const isAuth = await checkAuthStatus()

      if (!isAuth) {
        setState(prev => ({
          ...prev,
          isAuthenticated: false,
          userRating: 0,
          isInitialLoading: false
        }))
        return
      }

      // 获取用户评分
      const result = await getUserRating(bookId)
      if (result.success) {
        setState(prev => ({
          ...prev,
          isAuthenticated: true,
          userRating: result.data?.score || 0,
          isInitialLoading: false
        }))
      } else {
        setState(prev => ({
          ...prev,
          isAuthenticated: true,
          userRating: 0,
          isInitialLoading: false
        }))
      }
    } catch (error) {
      console.error('Error loading user rating:', error)
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        userRating: 0,
        isInitialLoading: false
      }))
    }
  }, [bookId])

  /**
   * 组件挂载时加载数据
   */
  useEffect(() => {
    loadUserRating()
  }, [loadUserRating])

  /**
   * 处理评分点击（采用无状态认证检查，类似 Listen 按钮）
   */
  const handleRatingClick = useCallback(async (rating: number) => {
    // 实时检查认证状态（不依赖组件状态）
    const isLoggedIn = await checkAuthStatus()

    if (!isLoggedIn) {
      // 设置登录成功后的回调
      setOnSuccess(() => {
        // 登录成功后重新调用评分函数（递归调用，重新检查认证）
        handleRatingClick(rating)
      })

      // 打开登录弹窗
      openLogin()
      return
    }

    // 已认证，直接提交评分
    await handleSubmitRating(rating)
  }, [setOnSuccess, openLogin])

  /**
   * 提交评分
   */
  const handleSubmitRating = useCallback(async (rating: number) => {
    // 保存当前评分用于回滚
    const previousRating = state.userRating

    try {
      // 乐观更新：立即更新UI
      setState(prev => ({ ...prev, userRating: rating }))

      // 提交评分
      const result = await submitRating(bookId, rating)

      if (result.success) {
        // 提交成功
        showToast(result.message || 'Rating submitted successfully', 'success')
      } else {
        // 提交失败，回滚乐观更新
        setState(prev => ({
          ...prev,
          userRating: previousRating
        }))
        showToast(result.error || 'Failed to submit rating', 'error')
      }
    } catch (error) {
      // 网络错误，回滚乐观更新
      setState(prev => ({
        ...prev,
        userRating: previousRating
      }))
      showToast('Network error. Please try again.', 'error')
      console.error('Error submitting rating:', error)
    }
  }, [bookId, state.userRating, showToast])

  /**
   * 渲染星级按钮
   */
  const renderStars = () => {
    const displayRating = hoverRating || state.userRating
    return [1, 2, 3, 4, 5].map((starValue) => (
      <button
        key={starValue}
        onClick={() => handleRatingClick(starValue)}
        onMouseEnter={() => setHoverRating(starValue)}
        onMouseLeave={() => setHoverRating(0)}
        disabled={state.isInitialLoading}
        className={`
          focus:outline-none px-1 transition-all duration-200
          ${state.isInitialLoading
            ? 'cursor-not-allowed opacity-50'
            : 'cursor-pointer hover:scale-110'
          }
        `}
        aria-label={`Rate ${starValue} star${starValue > 1 ? 's' : ''}`}
      >
        <svg
          className={`
            w-6 h-6 transition-colors duration-200
            ${starValue <= displayRating
              ? 'text-yellow-400'
              : 'text-gray-300 hover:text-yellow-200'
            }
          `}
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      </button>
    ))
  }

  /**
   * 渲染加载状态
   */
  if (state.isInitialLoading) {
    return (
      <div className={className}>
        <div className="flex items-center">
          <div className="mr-3 text-gray-800 font-semibold">My Rating:</div>
          <div className="flex">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="px-1">
                <div className="w-6 h-6 bg-gray-200 animate-pulse rounded" />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="flex items-center">
        <div className="mr-3 text-gray-800 font-semibold">
          My Rating:
        </div>
        <div className="flex items-center">
          {renderStars()}
          {!state.isAuthenticated && !state.isInitialLoading && (
            <div className="ml-2 text-sm text-gray-500">
              Click to rate (login required)
            </div>
          )}
          {state.isAuthenticated && state.userRating > 0 && (
            <div className="ml-2 text-sm text-gray-600">
              You rated: {state.userRating} star{state.userRating > 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default RatingSystem
