'use client';

import { useEffect } from 'react';

interface ViewTrackerProps {
  bookId: number;
  language: string;
}

/**
 * 客户端组件，用于追踪书籍访问量和阅读历史
 * 在组件挂载时并行发送两个请求到不同的 API 端点
 */
export default function ViewTracker({ bookId, language }: ViewTrackerProps) {
  useEffect(() => {
    // 定义一个异步函数来并行发送请求
    const trackViewAndHistory = async () => {
      try {
        // 并行发送两个请求，使用 Promise.allSettled 确保单个失败不影响整体
        const [viewResponse, historyResponse] = await Promise.allSettled([
          // 现有的访问量统计
          fetch('/api/book-view', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id: bookId, language }),
          }),
          // 新增的阅读历史记录
          fetch('/api/reading-history', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ bookId, language }),
          })
        ]);

        // 处理访问量统计结果
        if (viewResponse.status === 'fulfilled') {
          if (!viewResponse.value.ok) {
            console.info('Failed to track book view:', await viewResponse.value.text());
          }
        } else {
          console.error('Error tracking book view:', viewResponse.reason);
        }

        // 处理阅读历史结果
        if (historyResponse.status === 'fulfilled') {
          if (!historyResponse.value.ok) {
            console.error('Failed to record reading history:', await historyResponse.value.text());
          }
        } else {
          console.error('Error recording reading history:', historyResponse.reason);
        }

      } catch (error) {
        // 这个 catch 块应该很少被触发，因为我们使用了 Promise.allSettled
        console.error('Unexpected error in view tracking:', error);
      }
    };

    // 调用函数
    trackViewAndHistory();

    // 这个组件不需要清理函数
  }, [bookId, language]); // 依赖项：书籍ID和语言

  // 这个组件不渲染任何内容
  return null;
}
