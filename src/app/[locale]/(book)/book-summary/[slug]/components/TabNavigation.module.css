.tab-btn {
  display: inline-block;
  padding: 0.5rem 1.25rem; /* 增加垂直padding */
  border-radius: 9999px;
  font-weight: 500;
  color: #222;
  background: transparent;
  transition: all 0.2s ease;
  position: relative; /* 为伪元素定位做准备 */
  z-index: 30; /* 提高层级确保阴影不被遮盖 */
}

.tab-btn.active,
.active,
.tab-btn:focus,
.tab-btn:hover {
  background: #fff;
  color: #111;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  z-index: 31; /* 活动状态时进一步提高层级 */
}
