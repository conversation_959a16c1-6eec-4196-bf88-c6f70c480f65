import { Link } from 'next-view-transitions'
import { convertCategoryNameToSlug } from '@/utils/category.utils';

interface BreadcrumbNavProps {
  bookTitle: string;
  category: string | undefined;
}

const BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({ bookTitle, category }) => {
  return (
    <div className="text-sm mb-8">
      <Link className="text-gray-500 hover:text-green-600" href="/">Home</Link>
      <span className="text-gray-400 mx-2">/</span>
      {category ? (
        <>
          <Link className="text-gray-500 hover:text-green-600" href={`/categories/${convertCategoryNameToSlug(category)}`}>
            {category}
          </Link>
          <span className="text-gray-400 mx-2">/</span>
        </>
      ) : null}
      <span className="text-gray-700">{bookTitle}</span>
    </div>
  );
};

export default BreadcrumbNav;
