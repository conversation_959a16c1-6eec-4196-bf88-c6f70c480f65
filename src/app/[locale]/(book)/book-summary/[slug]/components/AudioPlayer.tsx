'use client';

import React, { useState, useEffect, useRef, useCallback, CSSProperties } from 'react';
import AudioPlayerComponent from 'react-h5-audio-player';
import { useAudioProgress } from '@/hooks/useAudioProgress';
import 'react-h5-audio-player/lib/styles.css';

interface AudioPlayerProps {
  book: {
    id: string;
    title: string;
    author: string;
    coverImage: string;
  };
  isActive: boolean;
  autoPlay?: boolean; // 新增：自动播放意图
  locale?: string; // 新增：语言参数
  onClose: () => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ book, isActive, autoPlay = false, locale = 'en', onClose }) => {
  // 音频播放状态
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState('0:00');
  const [duration, setDuration] = useState('0:00');
  const [progress, setProgress] = useState(0);
  const [, setPlaybackRate] = useState(1.0);
  const [, setVolume] = useState(1.0);

  // 自动播放相关状态
  const [autoPlayAttempted, setAutoPlayAttempted] = useState(false);
  const [showPlayPrompt, setShowPlayPrompt] = useState(false);
  const [progressRestored, setProgressRestored] = useState(false);

  // 进度条交互状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(0);

  // 拖拽性能优化和状态管理
  const dragAnimationRef = useRef<number | null>(null);
  const wasPlayingBeforeDragRef = useRef<boolean>(false);
  const progressBarRef = useRef<HTMLDivElement | null>(null);
  const dragStartTimeRef = useRef<number>(0);
  const dragStartPositionRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const isDragThresholdExceededRef = useRef<boolean>(false);

  // 关键修复：使用ref存储最新的拖拽位置，避免React状态异步更新问题
  const currentDragProgressRef = useRef<number>(0);

  // 引用
  const audioPlayerRef = useRef<AudioPlayerComponent>(null);

  const {
    progress: savedProgress,
    updateProgress,
    updatePlayingState,
    markAsCompleted,
  } = useAudioProgress({
    bookId: parseInt(book.id),
    autoSave: true,
    saveInterval: 3000 // 每3秒保存一次
  });

  // 获取音频URL
  const fetchAudioUrl = async () => {
    if (!isActive || audioUrl) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/audio/${book.id}?locale=${locale}`, {
        method: 'GET',
        credentials: 'include', // 包含认证信息
      });

      const data = await response.json();

      if (data.success && data.data.audioUrl) {
        setAudioUrl(data.data.audioUrl);
        // console.log('[AudioPlayer] Audio URL loaded:', data.data.audioUrl);
      } else {
        setError(data.error || 'Failed to load audio');
        console.error('[AudioPlayer] Failed to load audio:', data.error);
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('[AudioPlayer] Network error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };



  /**
   * 内部点击处理函数（用于统一的跳转逻辑）
   */
  const handleProgressClickInternal = useCallback((clientX: number) => {
    const audio = audioPlayerRef.current?.audio?.current;
    if (!audio || !audio.duration || isNaN(audio.duration) || !progressBarRef.current) {
      console.warn('[AudioPlayer] Cannot seek: audio or progress bar not ready');
      return;
    }

    // 获取进度条的位置信息
    const rect = progressBarRef.current.getBoundingClientRect();
    const clickX = clientX - rect.left;
    const progressBarWidth = rect.width;

    // 计算点击位置的百分比
    const clickPercentage = Math.max(0, Math.min(100, (clickX / progressBarWidth) * 100));

    // 计算对应的时间点
    const seekTime = (clickPercentage / 100) * audio.duration;

    console.log('[AudioPlayer] Progress click:', {
      clickX,
      progressBarWidth,
      clickPercentage: clickPercentage.toFixed(2),
      seekTime: seekTime.toFixed(2),
      duration: audio.duration
    });

    try {
      // 暂时停止自动保存，避免跳转时的冲突
      const wasPlaying = !audio.paused;
      if (wasPlaying) {
        updatePlayingState(false);
      }

      // 设置音频播放位置
      audio.currentTime = seekTime;

      // 立即更新本地进度状态
      setProgress(clickPercentage);
      setCurrentTime(formatTime(seekTime));

      // 更新播放进度到Hook（立即保存）
      updateProgress(
        seekTime,
        audio.duration,
        audio.playbackRate,
        audio.volume,
        wasPlaying
      );

      // 如果之前在播放，恢复播放状态
      if (wasPlaying) {
        setTimeout(() => {
          updatePlayingState(true);
        }, 100);
      }

      console.log('[AudioPlayer] Seek completed to:', seekTime);
    } catch (error) {
      console.error('[AudioPlayer] Seek failed:', error);
      setError('Failed to seek audio');
    }
  }, [updateProgress, updatePlayingState, formatTime]);

  /**
   * 处理鼠标按下事件（区分点击和拖拽）
   */
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const audio = audioPlayerRef.current?.audio?.current;
    if (!audio || !audio.duration || isNaN(audio.duration)) {
      console.warn('[AudioPlayer] Cannot interact: audio not ready');
      return;
    }

    // 记录拖拽开始的时间和位置
    dragStartTimeRef.current = Date.now();
    dragStartPositionRef.current = { x: e.clientX, y: e.clientY };
    isDragThresholdExceededRef.current = false;

    // 初始化拖拽进度ref为当前进度
    currentDragProgressRef.current = progress;

    // 存储进度条引用
    progressBarRef.current = e.currentTarget;

    // 记录拖拽前的播放状态
    wasPlayingBeforeDragRef.current = !audio.paused;

    // 添加全局鼠标事件监听
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const currentTime = Date.now();
      const timeDiff = currentTime - dragStartTimeRef.current;
      const distanceX = Math.abs(moveEvent.clientX - dragStartPositionRef.current.x);
      const distanceY = Math.abs(moveEvent.clientY - dragStartPositionRef.current.y);

      // 判断是否超过拖拽阈值（时间>150ms 或 距离>5px）
      if (timeDiff > 150 || distanceX > 5 || distanceY > 5) {
        if (!isDragThresholdExceededRef.current) {
          // 第一次超过阈值，开始真正的拖拽
          isDragThresholdExceededRef.current = true;
          setIsDragging(true);

          // 暂停自动保存机制
          if (wasPlayingBeforeDragRef.current) {
            updatePlayingState(false);
          }

          console.log('[AudioPlayer] Drag threshold exceeded, starting drag');
        }

        // 只有在真正拖拽时才更新进度
        if (isDragThresholdExceededRef.current && progressBarRef.current) {
          // 使用 requestAnimationFrame 优化性能
          if (dragAnimationRef.current) {
            cancelAnimationFrame(dragAnimationRef.current);
          }

          dragAnimationRef.current = requestAnimationFrame(() => {
            if (!progressBarRef.current) return;

            const rect = progressBarRef.current.getBoundingClientRect();
            const moveX = moveEvent.clientX - rect.left;
            const progressBarWidth = rect.width;

            // 计算拖拽位置的百分比
            const dragPercentage = Math.max(0, Math.min(100, (moveX / progressBarWidth) * 100));

            // 关键修复：同时更新ref和state，确保最新值可用
            currentDragProgressRef.current = dragPercentage;
            setDragProgress(dragPercentage);

            // 实时更新时间显示
            const dragTime = (dragPercentage / 100) * audio.duration;
            setCurrentTime(formatTime(dragTime));
          });
        }
      }
    };

    const handleMouseUp = (upEvent: MouseEvent) => {
      // 清理动画帧
      if (dragAnimationRef.current) {
        cancelAnimationFrame(dragAnimationRef.current);
        dragAnimationRef.current = null;
      }

      // 移除全局事件监听
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      const currentTime = Date.now();
      const timeDiff = currentTime - dragStartTimeRef.current;
      const distanceX = Math.abs(upEvent.clientX - dragStartPositionRef.current.x);
      const distanceY = Math.abs(upEvent.clientY - dragStartPositionRef.current.y);

      // 判断是点击还是拖拽
      if (!isDragThresholdExceededRef.current && timeDiff < 150 && distanceX < 5 && distanceY < 5) {
        // 这是一个点击事件，执行点击跳转逻辑
        console.log('[AudioPlayer] Detected click, performing seek');
        handleProgressClickInternal(upEvent.clientX);
      } else if (isDragThresholdExceededRef.current) {
        // 这是一个拖拽事件，应用最终位置
        console.log('[AudioPlayer] Drag ended, applying final position');

        // 关键修复：使用ref中的最新值，而不是可能过时的state
        const finalDragProgress = currentDragProgressRef.current;
        const finalTime = (finalDragProgress / 100) * audio.duration;

        console.log('[AudioPlayer] Mouse drag final values:', {
          dragProgressState: dragProgress,
          finalDragProgressRef: finalDragProgress,
          finalTime: finalTime.toFixed(2)
        });

        try {
          // 设置音频播放位置
          audio.currentTime = finalTime;

          // 更新最终进度状态
          setProgress(finalDragProgress);

          // 立即保存进度
          updateProgress(
            finalTime,
            audio.duration,
            audio.playbackRate,
            audio.volume,
            false
          );

          console.log('[AudioPlayer] Drag seek completed to:', finalTime);
        } catch (error) {
          console.error('[AudioPlayer] Drag seek failed:', error);
          setError('Failed to seek audio');
        }

        // 如果之前在播放，短暂延迟后恢复播放状态
        if (wasPlayingBeforeDragRef.current) {
          setTimeout(() => {
            updatePlayingState(true);
          }, 100);
        }
      }

      // 重置拖拽状态
      setIsDragging(false);
      progressBarRef.current = null;
    };

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

  }, [updateProgress, updatePlayingState, formatTime, dragProgress]);

  /**
   * 处理触摸开始事件（移动端支持）
   */
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const audio = audioPlayerRef.current?.audio?.current;
    if (!audio || !audio.duration || isNaN(audio.duration)) {
      console.warn('[AudioPlayer] Cannot interact: audio not ready');
      return;
    }

    const touch = e.touches[0];

    // 记录拖拽开始的时间和位置
    dragStartTimeRef.current = Date.now();
    dragStartPositionRef.current = { x: touch.clientX, y: touch.clientY };
    isDragThresholdExceededRef.current = false;

    // 初始化拖拽进度ref为当前进度
    currentDragProgressRef.current = progress;

    // 存储进度条引用
    progressBarRef.current = e.currentTarget;

    // 记录拖拽前的播放状态
    wasPlayingBeforeDragRef.current = !audio.paused;

    // 添加全局触摸事件监听
    const handleTouchMove = (moveEvent: TouchEvent) => {
      moveEvent.preventDefault(); // 防止页面滚动

      const moveTouch = moveEvent.touches[0];
      const currentTime = Date.now();
      const timeDiff = currentTime - dragStartTimeRef.current;
      const distanceX = Math.abs(moveTouch.clientX - dragStartPositionRef.current.x);
      const distanceY = Math.abs(moveTouch.clientY - dragStartPositionRef.current.y);

      // 判断是否超过拖拽阈值
      if (timeDiff > 150 || distanceX > 5 || distanceY > 5) {
        if (!isDragThresholdExceededRef.current) {
          // 第一次超过阈值，开始真正的拖拽
          isDragThresholdExceededRef.current = true;
          setIsDragging(true);

          // 暂停自动保存机制
          if (wasPlayingBeforeDragRef.current) {
            updatePlayingState(false);
          }

          console.log('[AudioPlayer] Touch drag threshold exceeded, starting drag');
        }

        // 只有在真正拖拽时才更新进度
        if (isDragThresholdExceededRef.current && progressBarRef.current) {
          // 使用 requestAnimationFrame 优化性能
          if (dragAnimationRef.current) {
            cancelAnimationFrame(dragAnimationRef.current);
          }

          dragAnimationRef.current = requestAnimationFrame(() => {
            if (!progressBarRef.current) return;

            const rect = progressBarRef.current.getBoundingClientRect();
            const moveX = moveTouch.clientX - rect.left;
            const progressBarWidth = rect.width;

            // 计算拖拽位置的百分比
            const dragPercentage = Math.max(0, Math.min(100, (moveX / progressBarWidth) * 100));

            // 关键修复：同时更新ref和state，确保最新值可用
            currentDragProgressRef.current = dragPercentage;
            setDragProgress(dragPercentage);

            // 实时更新时间显示
            const dragTime = (dragPercentage / 100) * audio.duration;
            setCurrentTime(formatTime(dragTime));
          });
        }
      }
    };

    const handleTouchEnd = (endEvent: TouchEvent) => {
      // 清理动画帧
      if (dragAnimationRef.current) {
        cancelAnimationFrame(dragAnimationRef.current);
        dragAnimationRef.current = null;
      }

      // 移除全局事件监听
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      const currentTime = Date.now();
      const timeDiff = currentTime - dragStartTimeRef.current;
      const endTouch = endEvent.changedTouches[0];
      const distanceX = Math.abs(endTouch.clientX - dragStartPositionRef.current.x);
      const distanceY = Math.abs(endTouch.clientY - dragStartPositionRef.current.y);

      // 判断是点击还是拖拽
      if (!isDragThresholdExceededRef.current && timeDiff < 150 && distanceX < 5 && distanceY < 5) {
        // 这是一个点击事件，执行点击跳转逻辑
        console.log('[AudioPlayer] Detected touch tap, performing seek');
        handleProgressClickInternal(endTouch.clientX);
      } else if (isDragThresholdExceededRef.current) {
        // 这是一个拖拽事件，应用最终位置
        console.log('[AudioPlayer] Touch drag ended, applying final position');

        // 关键修复：使用ref中的最新值，而不是可能过时的state
        const finalDragProgress = currentDragProgressRef.current;
        const finalTime = (finalDragProgress / 100) * audio.duration;

        console.log('[AudioPlayer] Touch drag final values:', {
          dragProgressState: dragProgress,
          finalDragProgressRef: finalDragProgress,
          finalTime: finalTime.toFixed(2)
        });

        try {
          // 设置音频播放位置
          audio.currentTime = finalTime;

          // 更新最终进度状态
          setProgress(finalDragProgress);

          // 立即保存进度
          updateProgress(
            finalTime,
            audio.duration,
            audio.playbackRate,
            audio.volume,
            false
          );

          console.log('[AudioPlayer] Touch drag seek completed to:', finalTime);
        } catch (error) {
          console.error('[AudioPlayer] Touch drag seek failed:', error);
          setError('Failed to seek audio');
        }

        // 如果之前在播放，短暂延迟后恢复播放状态
        if (wasPlayingBeforeDragRef.current) {
          setTimeout(() => {
            updatePlayingState(true);
          }, 100);
        }
      }

      // 重置拖拽状态
      setIsDragging(false);
      progressBarRef.current = null;
    };

    // 添加全局事件监听
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);

  }, [updateProgress, updatePlayingState, formatTime, dragProgress]);

  // 音频播放器样式
  const audioPlayerStyles = {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
    transform: isActive ? 'translateY(0)' : 'translateY(100%)',
    transition: 'transform 0.3s ease-in-out',
    boxShadow: '0 -4px 6px -1px rgba(0, 0, 0, 0.1)',
  } as CSSProperties

  const progressBarStyles = {
    height: '6px', // 增加高度便于点击
    backgroundColor: '#e5e7eb',
    borderRadius: '3px',
    overflow: 'hidden',
    cursor: 'pointer', // 添加指针样式
    position: 'relative' as const,
  };

  const progressStyles = {
    height: '100%',
    backgroundColor: '#10b981',
    width: `${isDragging ? dragProgress : progress}%`, // 拖拽时使用拖拽进度
    transition: isDragging ? 'none' : 'width 0.1s linear', // 拖拽时禁用过渡
  };

  // 当播放器激活时获取音频
  useEffect(() => {
    if (isActive) {
      fetchAudioUrl();
    }
  }, [isActive, locale]); // 添加locale依赖，确保语言变化时重新获取音频

  // 重置部分状态当播放器关闭时（保留音频URL和进度）
  useEffect(() => {
    if (!isActive) {
      // 双重保护：确保音频在播放器关闭时被暂停
      if (audioPlayerRef.current?.audio?.current) {
        const audio = audioPlayerRef.current.audio.current;
        if (!audio.paused) {
          audio.pause();
          console.log('[AudioPlayer] Audio paused on player deactivate');
        }
      }

      // 重置播放状态
      setIsPlaying(false);
      updatePlayingState(false);
      setError(null);
      setAutoPlayAttempted(false);
      setShowPlayPrompt(false);
      // 不重置: audioUrl, progress, currentTime, duration, progressRestored
    }
  }, [isActive, updatePlayingState]);

  // 清理拖拽相关的事件监听器和动画帧
  useEffect(() => {
    return () => {
      // 清理动画帧
      if (dragAnimationRef.current) {
        cancelAnimationFrame(dragAnimationRef.current);
      }

      // 清理可能残留的全局事件监听器
      document.removeEventListener('mousemove', () => { });
      document.removeEventListener('mouseup', () => { });
      document.removeEventListener('touchmove', () => { });
      document.removeEventListener('touchend', () => { });
    };
  }, []);

  // 音频播放器事件处理
  const handlePlay = () => {
    setIsPlaying(true);
    updatePlayingState(true); // 通知 Hook 播放状态
    console.log('[AudioPlayer] Audio started playing');
  };

  const handlePause = () => {
    setIsPlaying(false);
    updatePlayingState(false); // 通知 Hook 暂停状态
    console.log('[AudioPlayer] Audio paused');
  };

  const handleLoadedMetaData = async (e: any) => {
    const audioDuration = e.target.duration;
    if (audioDuration && !isNaN(audioDuration)) {
      setDuration(formatTime(audioDuration));
      // console.log('[AudioPlayer] Audio metadata loaded, duration:', audioDuration);

      // 恢复播放进度 - 添加延迟确保savedProgress已加载
      setTimeout(() => {
        if (savedProgress && savedProgress.currentTime > 0 && !progressRestored) {
          try {
            const seekTime = Math.min(savedProgress.currentTime, audioDuration);
            e.target.currentTime = seekTime;
            setProgressRestored(true);
            console.log('[AudioPlayer] Progress restored to:', seekTime);
          } catch (error) {
            console.warn('[AudioPlayer] Failed to restore progress:', error);
            setProgressRestored(true); // 标记为已尝试，避免重复尝试
          }
        }
      }, 100); // 100ms延迟确保数据已加载

      // 如果有自动播放意图且未尝试过
      if (autoPlay && !autoPlayAttempted) {
        setAutoPlayAttempted(true);
        try {
          await e.target.play();
          setShowPlayPrompt(false); // 隐藏播放提示
        } catch (error) {
          setShowPlayPrompt(true);
          setError('Click play to start audio');
        }
      }
    }
  };

  /**
   * 处理播放器重新激活时的自动播放
   */
  useEffect(() => {
    // 当播放器重新激活且有自动播放意图时，尝试播放
    if (isActive && autoPlay && audioPlayerRef.current?.audio?.current && !autoPlayAttempted) {
      const audio = audioPlayerRef.current.audio.current;

      // 确保音频已加载元数据
      if (audio.readyState >= 1) { // HAVE_METADATA
        setAutoPlayAttempted(true);
        audio.play()
          .then(() => {
            setShowPlayPrompt(false);
          })
          .catch(error => {
            console.warn('[AudioPlayer] Resume play failed on reactivate:', error);
            setShowPlayPrompt(true);
            setError('Click play to start audio');
          });
      }
    }
  }, [isActive, autoPlay, autoPlayAttempted]);

  const handleListen = (e: any) => {
    // 如果正在拖拽，跳过进度更新，避免冲突
    if (isDragging) {
      return;
    }

    const currentTimeSeconds = e.target.currentTime;
    const totalDuration = e.target.duration;
    const currentPlaybackRate = e.target.playbackRate || 1.0;
    const currentVolume = e.target.volume || 1.0;

    if (currentTimeSeconds && !isNaN(currentTimeSeconds)) {
      setCurrentTime(formatTime(currentTimeSeconds));
    }

    if (totalDuration && !isNaN(totalDuration) && currentTimeSeconds) {
      const progressPercent = (currentTimeSeconds / totalDuration) * 100;
      setProgress(progressPercent);

      // 更新本地状态
      setPlaybackRate(currentPlaybackRate);
      setVolume(currentVolume);

      // 更新播放进度（智能保存）- 使用实际的播放设置和播放状态
      updateProgress(
        currentTimeSeconds,
        totalDuration,
        currentPlaybackRate,
        currentVolume,
        !e.target.paused // 传递当前播放状态
      );
    } else {
      console.log('❌ [AudioPlayer] Conditions NOT met:', {
        totalDuration,
        isNaN_totalDuration: isNaN(totalDuration),
        currentTimeSeconds,
        conditionResult: totalDuration && !isNaN(totalDuration) && currentTimeSeconds
      });
    }
  };

  const handleEnded = async () => {
    setIsPlaying(false);
    updatePlayingState(false); // 通知 Hook 播放结束
    setProgress(100);
    setCurrentTime('0:00');
    console.log('[AudioPlayer] Audio playback ended');

    // 标记为已完成
    try {
      await markAsCompleted();
    } catch (error) {
      console.error('[AudioPlayer] Failed to mark as completed:', error);
    }
  };

  const handleError = (e: any) => {
    console.error('[AudioPlayer] Audio playback error:', e);
    setError('Playback error occurred');
    setIsPlaying(false);
    updatePlayingState(false); // 通知 Hook 播放错误
  };

  /**
   * 处理播放器关闭
   */
  const handleClose = useCallback(() => {
    // 1. 立即暂停音频
    if (audioPlayerRef.current?.audio?.current) {
      const audio = audioPlayerRef.current.audio.current;
      if (!audio.paused) {
        audio.pause();
      }
    }

    // 2. 更新播放状态（这会触发智能保存）
    if (isPlaying) {
      setIsPlaying(false);
      updatePlayingState(false);
    }

    // 3. 调用父组件关闭回调
    onClose();
  }, [isPlaying, updatePlayingState, onClose]);

  return (
    <div className="audio-player bg-white" style={audioPlayerStyles}>
      {/* 顶部进度条 - 支持点击跳转和拖拽 */}
      <div
        className="progress-bar w-full hover:bg-gray-200 transition-colors duration-200"
        style={progressBarStyles}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        title={isDragging ? "Dragging..." : "Click or drag to seek"}
      >
        <div className="progress" style={progressStyles}></div>

        {/* 拖拽指示器 */}
        {isDragging && (
          <div
            className="absolute top-0 w-3 h-3 bg-green-600 rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2"
            style={{
              left: `${dragProgress}%`,
              top: '50%',
              zIndex: 10,
              border: '2px solid white',
              boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
            }}
          />
        )}
      </div>

      {/* 书籍信息和控制区域 */}
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* 书籍信息 */}
          <div className="flex items-center space-x-3">
            <img className="h-12 w-12 rounded shadow" src={book.coverImage} alt="Book Cover" />
            <div>
              <h4 className="font-medium text-gray-900">{book.title}</h4>
              <p className="text-sm text-gray-600">By {book.author}</p>
            </div>
          </div>

          {/* 播放控制区域 */}
          <div className="flex items-center space-x-6">
            {/* 加载状态 */}
            {isLoading && (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                <span className="text-sm text-gray-600">Loading...</span>
              </div>
            )}

            {/* 播放提示 */}
            {showPlayPrompt && !isLoading && !error && (
              <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded">
                <span className="text-sm text-blue-700">
                  🎵 Audio ready! Click play to start
                </span>
                <button
                  className="text-blue-500 underline text-sm"
                  onClick={() => setShowPlayPrompt(false)}
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-red-500">⚠️ {error}</span>
                <button
                  className="text-sm text-blue-500 underline"
                  onClick={() => {
                    setError(null);
                    setShowPlayPrompt(false);
                    fetchAudioUrl();
                  }}
                >
                  Retry
                </button>
              </div>
            )}

            {/* 音频播放器 - 隐藏默认UI */}
            {audioUrl && !isLoading && !error && (
              <div className="hidden">
                <AudioPlayerComponent
                  ref={audioPlayerRef}
                  src={audioUrl}
                  autoPlay={false}
                  listenInterval={1000}
                  onPlay={handlePlay}
                  onPause={handlePause}
                  onLoadedMetaData={handleLoadedMetaData}
                  onListen={handleListen}
                  onEnded={handleEnded}
                  onError={handleError}
                  preload="metadata"
                />
              </div>
            )}

            {/* 自定义播放控制按钮 */}
            {audioUrl && !isLoading && !error && (
              <>
                {/* 快退按钮 */}
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => {
                    const audio = audioPlayerRef.current?.audio?.current;
                    if (audio) {
                      audio.currentTime = Math.max(0, audio.currentTime - 15);
                    }
                  }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.333 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z"></path>
                  </svg>
                </button>

                {/* 播放/暂停按钮 */}
                <button
                  className="bg-green-500 rounded-full p-4 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-300"
                  onClick={() => {
                    const audio = audioPlayerRef.current?.audio?.current;
                    if (audio) {
                      if (isPlaying) {
                        audio.pause();
                      } else {
                        audio.play().catch(error => {
                          console.error('[AudioPlayer] Manual play failed:', error);
                          setError('Failed to play audio');
                        });
                        setShowPlayPrompt(false); // 用户手动播放时隐藏提示
                      }
                    }
                  }}
                >
                  {!isPlaying ? (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  )}
                </button>

                {/* 快进按钮 */}
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => {
                    const audio = audioPlayerRef.current?.audio?.current;
                    if (audio) {
                      audio.currentTime = Math.min(audio.duration, audio.currentTime + 15);
                    }
                  }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z"></path>
                  </svg>
                </button>
              </>
            )}
          </div>

          {/* 时间显示和关闭按钮 */}
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600 w-8">{currentTime}</span>
            <span className="text-sm text-gray-400">/</span>
            <span className="text-sm text-gray-600">{duration}</span>

            <button className="text-gray-500 hover:text-gray-700" onClick={handleClose}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
