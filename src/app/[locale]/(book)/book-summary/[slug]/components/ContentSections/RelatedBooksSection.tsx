'use client';

import React from 'react';
import BookCarousel from '@/components/common/BookCarousel';
import { Link } from 'next-view-transitions';
import { urlGenerator } from '@/services/url.service';
import BookCover from '@/components/common/BookCover';

interface Book {
  id: string;
  title: string;
  author: string; // 添加作者字段
  subtitle: string; // 添加副标题字段
  coverImage: string;
  rating: number;
}

interface RelatedBooksSectionProps {
  authorName: string;
  books: Book[];
}

const RelatedBooksSection: React.FC<RelatedBooksSectionProps> = ({ authorName, books }) => {
  // 渲染评分星星
  const renderRatingStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <span className="text-yellow-500">
        {'★'.repeat(fullStars)}
        {hasHalfStar ? '½' : ''}
        {'☆'.repeat(emptyStars)}
      </span>
    );
  };

  return (
    <div id="related" className="mb-8">
      <h2 className="text-2xl font-bold mb-4">Related Books by {authorName}</h2>
      <BookCarousel
        containerRef="relatedBooksCarousel"
        itemsPerRow={{ sm: 2, md: 3, lg: 3 }}
        leftBtnClassName="left-[8px]"
        rightBtnClassName="right-[8px]"
      >
        {books.map((book) => {
          // 生成书籍详情页面URL
          const bookDetailUrl = urlGenerator.book.detail({
            id: book.id,
            title: book.title
          });

          return (
            <Link
              key={book.id}
              href={bookDetailUrl}
              className="flex flex-col items-center px-2 cursor-pointer transition-transform duration-200 hover:scale-105 h-[420px]"
            >
              <BookCover
                coverUrl={book.coverImage}
                title={book.title}
                width={176}
                height={256}
                className="mb-3 shadow-md hover:shadow-lg transition-shadow duration-200 flex-shrink-0"
                style={{ maxWidth: '176px', maxHeight: '256px' }}
                fallbackColor="e9e1cc"
                priority={false}
              />
              {/* 文本内容区域 - 固定高度并使用flex布局 */}
              <div className="flex flex-col items-center text-center flex-grow w-full min-h-0">
                <h3 className="text-lg font-semibold hover:text-green-600 transition-colors duration-200 line-clamp-1 mb-1 w-full">
                  {book.title}
                </h3>
                {/* 作者信息 - 固定高度 */}
                <p className="text-gray-600 text-sm line-clamp-1 mb-1 w-full h-5">
                  {book.author}
                </p>
                {/* 副标题 - 固定高度，始终占用空间 */}
                <div className="h-8 mb-2 w-full flex items-center justify-center">
                  {book.subtitle && (
                    <p className="text-gray-500 text-xs italic line-clamp-2 leading-tight">
                      {book.subtitle}
                    </p>
                  )}
                </div>
                {/* 评分 - 固定在底部 */}
                <div className="flex items-center justify-center">
                  {renderRatingStars(book.rating)}
                  <span className="ml-1 text-sm text-gray-600">{book.rating.toFixed(1)}</span>
                </div>
              </div>
            </Link>
          );
        })}
      </BookCarousel>
    </div>
  );
};

export default RelatedBooksSection;
