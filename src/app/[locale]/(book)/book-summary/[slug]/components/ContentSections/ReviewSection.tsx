'use client';

import React from 'react';

interface ReviewSectionProps {
  reviewSummary: string;
}

const ReviewSection: React.FC<ReviewSectionProps> = ({ reviewSummary }) => {
  // 如果没有review summary内容，不渲染组件
  if (!reviewSummary || reviewSummary.trim() === '') {
    return null;
  }

  return (
    <div id="review" className="mb-8 p-6 bg-gray-100 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Review Summary</h2>
      <div className="prose prose-gray max-w-none">
        <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
          {reviewSummary}
        </p>
      </div>
    </div>
  );
};

export default ReviewSection;
