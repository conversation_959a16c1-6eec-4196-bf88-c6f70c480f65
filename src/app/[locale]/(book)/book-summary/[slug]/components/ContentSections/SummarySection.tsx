'use client';

import React from 'react';

interface SummarySectionProps {
  bookName: string;
  synopsis: string;
  chapters: {
    title: string;
    content: string;
  }[];
}

const SummarySection: React.FC<SummarySectionProps> = ({ bookName, synopsis, chapters }) => {
  return (
    <div id="summary" className="mb-8">
      <h2 className="text-2xl font-bold mb-4">{bookName} Plot Summary</h2>
      <div className="mb-4">
        <h3 className="text-xl font-semibold mb-2">Synopsis</h3>
        <p>{synopsis}</p>
      </div>

      {chapters.map((chapter, index) => (
        <React.Fragment key={index}>
          <h3 id={`chapter-${index + 1}`} className="text-xl font-semibold mt-6 mb-3">{chapter.title}</h3>
          <p className="mb-4 whitespace-pre-wrap">{chapter.content}</p>
        </React.Fragment>
      ))}
    </div>
  );
};

export default SummarySection;
