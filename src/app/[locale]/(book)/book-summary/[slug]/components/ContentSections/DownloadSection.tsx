'use client';

import React from 'react';
import { FileInfo } from '../../types';
import { useAuthAndPremiumCheck } from '@/hooks/useAuthAndPremiumCheck';

interface DownloadSectionProps {
  fileInfo: FileInfo;
  pdfSize: string;
  epubSize: string;
}

const DownloadSection: React.FC<DownloadSectionProps> = ({ pdfSize, epubSize, fileInfo }) => {
  // 使用认证和付费验证Hook
  const { checkAuthAndPremium } = useAuthAndPremiumCheck({
    featureName: 'download'
  });

  const downloadFile = async (url: string, filename: string) => {
    console.log('downloadFile url', JSON.stringify(url), filename)

    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename; // 设置下载的文件名

    document.body.appendChild(a);

    a.click();

    document.body.removeChild(a);
  };

  /**
   * 带认证和付费验证的下载处理函数
   */
  const handleDownloadWithAuth = async (url: string, filename: string) => {
    // 纯粹的状态检查，不执行任何业务逻辑
    const canProceed = await checkAuthAndPremium();

    // 只有检查通过才执行下载逻辑
    if (canProceed) {
      downloadFile(url, filename);
    }
  };

  return (
    <div id="download" className="mt-16 mb-16">
      <h2 className="text-2xl font-bold mb-6">Download PDF & EPUB</h2>
      <div className="bg-white rounded-lg shadow-sm p-8">
        <p className="text-lg text-gray-800 mb-8">
          To save this Black List summary for later, download the free PDF and EPUB.
          You can print it out, or read offline at your convenience.
        </p>
        <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
          {
            Object.entries(fileInfo).map(([key, file]) => {
              if (!file) return null;

              // 从 URL 中提取文件名，或者使用默认文件名
              const urlParts = file.url.split('/');
              const filename = urlParts[urlParts.length - 1] || `book.${key}`;

              return (
                <div key={key} className="flex flex-col items-center">
                  <button
                    onClick={() => handleDownloadWithAuth(file.url, filename)}
                    className="bg-gray-900 text-white px-8 py-3 rounded font-semibold text-base mb-2 hover:bg-gray-700 transition cursor-pointer"
                  >
                    Download {key.toUpperCase()}
                  </button>
                  {/* <span className="text-sm text-gray-600">File size: {file.sizeInMb} MB</span> */}
                </div>
              );
            })
          }
        </div>
      </div>
    </div>
  );
};

export default DownloadSection;
