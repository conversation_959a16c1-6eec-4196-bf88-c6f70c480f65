'use client';

import { Link } from 'next-view-transitions';
import React from 'react';
import { urlGenerator } from '@/services/url.service';
import AuthorAvatar from '@/components/common/AuthorAvatar';

interface AuthorSectionProps {
  author: {
    id: string;
    name: string;
    avatar: string;
    bio: string;
  };
}

const AuthorSection: React.FC<AuthorSectionProps> = ({ author }) => {
  // 生成作者详情页面URL
  const authorUrl = urlGenerator.author.detail({ id: author.id, name: author.name });

  return (
    <div id="author" className="mb-8">
      <h2 className="text-2xl font-bold mb-4">About Author</h2>
      <div className="flex items-start">
        <AuthorAvatar
          avatarUrl={author.avatar}
          authorName={author.name}
          size="lg"
          className="w-[100px] h-[100px] mr-4"
        />
        <div>
          <h3 className="text-xl font-semibold mb-2">{author.name}</h3>
          <p className="mb-3">{author.bio}</p>
          <Link className="text-blue-600 hover:underline" href={authorUrl}>Read more</Link>
        </div>
      </div>
    </div>
  );
};

export default AuthorSection;
