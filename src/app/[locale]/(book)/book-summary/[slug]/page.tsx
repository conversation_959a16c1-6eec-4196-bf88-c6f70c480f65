import React from 'react';
import BookDetail from './components/BookDetail';
import ViewTracker from './components/ViewTracker';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBookById } from '@/services/book.service';
import { parseBookSlug, isValidBookId, generateBookSlug } from '@/utils/book.utils';

export async function generateMetadata({ params }: { params: Promise<{ slug: string, locale: string }> }): Promise<Metadata> {
  const { slug, locale } = await params;

  try {
    // 解析slug获取书籍ID
    const parsed = parseBookSlug(slug);
    if (!isValidBookId(parsed.id)) {
      throw new Error('Invalid book ID');
    }

    // 获取书籍数据（带缓存）
    const bookData = await getBookById(parsed.id, locale);

    // 如果没有找到书籍数据，返回默认元数据
    if (!bookData) {
      const t = await getTranslations({ locale, namespace: 'Metadata' });
      return {
        title: t('bookDetail.defaultTitle'),
        description: t('bookDetail.defaultDescription')
      };
    }

    // 获取品牌名称
    const t = await getTranslations({ locale, namespace: 'Metadata' });
    const brandName = t('brandName');

    // 构建书籍标题
    const bookTitle = `${bookData.book.title} Summary & Analysis | ${brandName}`;

    // 构建书籍描述
    const authorName = bookData.authors[0]?.name || '';
    const categoryName = bookData.categories[0]?.name || '';
    const readingTime = bookData.book.readingTimeMinutes;

    const bookDescription = bookData.book?.description || `${bookData.book.title} by ${authorName}: a concise ${categoryName} book summary & analysis in ${readingTime} minutes. Read online or download PDF, listen to audiobook, with plot review & chapter synopsis.`;

    // 构建规范URL (canonical URL) - 使用新的slug格式
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    const bookSlug = generateBookSlug(bookData.book.title, bookData.book.id);
    const canonicalUrl = `${baseUrl}/book-summary/${bookSlug}`;

    return {
      title: bookTitle,
      description: bookDescription,
      alternates: {
        canonical: canonicalUrl,
        languages: {
          'en-US': `${baseUrl}/book-summary/${bookSlug}`,
        }
      },
      openGraph: {
        title: bookTitle,
        description: bookDescription,
        type: 'book',
        url: canonicalUrl,
        images: [
          {
            url: bookData.book.cover.imageUrl,
            width: 800,
            height: 600,
            alt: bookData.book.title
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        title: bookTitle,
        description: bookDescription,
        images: [bookData.book.cover.imageUrl]
      }
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    const t = await getTranslations({ locale, namespace: 'Metadata' });
    return {
      title: t('bookDetail.defaultTitle'),
      description: t('bookDetail.defaultDescription')
    };
  }
}


// 错误状态组件
const BookDetailError = ({ message }: { message: string }) => (
  <div className="container mx-auto px-4 py-8">
    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong className="font-bold">Load Book Error:</strong>
      <span className="block sm:inline"> {message}</span>
    </div>
  </div>
);

export default async function BookDetailPage({ params }: { params: Promise<{ slug: string, locale: string }> }) {
  const { slug, locale } = await params;

  try {
    // 1. 解析slug获取书籍ID（支持新旧格式）
    const parsed = parseBookSlug(slug);

    if (!isValidBookId(parsed.id)) {
      console.error('Invalid book ID:', parsed.id);
      notFound();
    }

    // 2. 获取书籍数据（带缓存）
    const bookData = await getBookById(parsed.id, locale);

    // 如果没有找到书籍数据，返回404页面
    if (!bookData) {
      notFound();
    }

    // 将数据传递给BookDetail组件，并包含ViewTracker组件
    return (
      <>
        {/* 客户端组件，用于追踪书籍访问量 */}
        <ViewTracker bookId={parsed.id} language={locale} />
        <BookDetail data={bookData} locale={locale} />
      </>
    );
  } catch (error) {
    console.error('Error in BookDetailPage:', error);

    // 如果是解析错误，返回404
    if (error instanceof Error && error.message.includes('Unable to parse book slug')) {
      notFound();
    }

    return <BookDetailError message="Failed to load book data. Please try again later." />;
  }
}
