/**
 * TypeScript类型定义 - 书籍详情数据
 */

// 书籍封面类型
export interface BookCover {
  id: string;
  imageUrl: string;
  isPrimary: boolean;
}

// 语言类型
export interface Language {
  code: string;
  name: string;
}

// 出版商类型
export interface Publisher {
  id: string;
  name: string;
}

// 文件信息类型
export interface FileInfo {
  pdf?: {
    sizeInMb: number;
    url: string;
  };
  epub?: {
    sizeInMb: number;
    url: string;
  };
}

// IPFS信息类型
export interface IPFSInfo {
  cid: string;
  cidBlake2b: string;
}

// 书籍基本信息类型
export interface Book {
  id: string;
  title: string;
  subtitle: string;
  isbn: string;
  asin: string;
  isbn13: string;
  issn: string | null;
  publicationYear: number;
  readingTimeMinutes: number;
  hasAudio: boolean;
  keyIdeasCount: number;
  fileInfo: FileInfo;
  ipfs: IPFSInfo;
  cover: BookCover;
  description: string;
  plotSummary: string;
  reviewSummary: string;
  synopsis: string;
  language: Language;
  publisher: Publisher;
  binding: string | null; // 装订类型
  contentType: string | null; // 内容类型
}

// 作者类型
export interface Author {
  id: string;
  name: string;
  biography: string;
  imageUrl: string;
}

// 分类类型
export interface Category {
  id: string;
  name: string;
}

// 评分分布类型
export interface RatingDistribution {
  "1": number;
  "2": number;
  "3": number;
  "4": number;
  "5": number;
}

// 评分信息类型
export interface Ratings {
  averageScore: number;
  totalCount: number;
  distribution: RatingDistribution;
}

// 章节类型
export interface Chapter {
  id: string;
  number: number;
  title: string;
  summary: string;
}

// 最佳引用类型
export interface BestQuote {
  text: string;
}

// 简化评分类型（用于相关书籍和热门书籍）
export interface SimpleRating {
  score: number;
  count: number;
}

// 相关书籍和热门书籍的简化书籍类型
export interface SimpleBook {
  id: string;
  title: string;
  subtitle?: string; // 添加副标题字段
  coverUrl: string;
  author: string;
  rating: SimpleRating;
}

// 音频信息类型
export interface AudioInfo {
  available: boolean;
  durationSeconds: number;
  fileUrl: string;
  fileSizeMb: number;
}

// 阅读进度类型
export interface ReadingProgress {
  lastReadAt: string; // ISO日期字符串
}

// 音频进度类型
export interface AudioProgress {
  positionSeconds: number;
  isCompleted: boolean;
  lastListenedAt: string; // ISO日期字符串
}

// 用户信息类型
export interface UserInfo {
  isFavorite: boolean;
  readingProgress: ReadingProgress;
  audioProgress: AudioProgress;
}

// 购买链接类型
export interface PurchaseLinks {
  amazon: string;
  [key: string]: string; // 允许其他购买链接
}

// 查看统计类型
export interface ViewStatistics {
  viewCount: number;
  lastViewedAt: string; // ISO日期字符串
}

// 完整的书籍详情数据类型
export interface BookDetailData {
  book: Book;
  authors: Author[];
  categories: Category[];
  ratings: Ratings;
  chapters: Chapter[];
  bestQuote: BestQuote;
  relatedBooks: SimpleBook[];
  trendingBooks: SimpleBook[];
  audioInfo: AudioInfo;
  userInfo: UserInfo;
  purchaseLinks: PurchaseLinks;
  viewStatistics: ViewStatistics;
}

// 默认导出完整类型
export default BookDetailData;
