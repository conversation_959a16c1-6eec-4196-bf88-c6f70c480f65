export default function Loading() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航骨架 */}
      <div className="flex items-center gap-2 mb-8">
        <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded-full w-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded-full w-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
      </div>

      {/* 书籍头部信息骨架 */}
      <div className="flex flex-col md:flex-row gap-8 mb-12">
        <div className="md:w-1/4">
          <div className="rounded-lg shadow-md w-full h-[480px] bg-gray-200 animate-pulse"></div>
        </div>
        <div className="md:w-3/4">
          <div className="h-10 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-3 animate-pulse"></div>
          <div className="h-5 bg-gray-200 rounded w-1/3 mb-4 animate-pulse"></div>

          <div className="flex items-center mb-4">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="w-5 h-5 mr-1 bg-gray-200 rounded-full animate-pulse"></div>
              ))}
            </div>
            <div className="ml-2 h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="px-3 py-1 bg-gray-200 rounded-full w-20 h-6 animate-pulse"></div>
            ))}
          </div>

          <div className="flex items-center mb-6">
            <div className="w-5 h-5 bg-gray-200 rounded-full mr-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>

          <div className="h-32 bg-gray-200 rounded w-full mb-6 animate-pulse"></div>

          <div className="flex flex-wrap gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="px-6 py-3 rounded-lg w-32 h-12 bg-gray-200 animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>

      {/* 简要分析骨架 */}
      <div className="mb-8">
        <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-4/5 animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* 书籍详情和版本骨架 */}
      <div className="mb-8">
        <div className="h-6 bg-gray-200 rounded w-64 mb-4 animate-pulse"></div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8">
            {[...Array(8)].map((_, i) => (
              <div key={i}>
                <div className="h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 内容部分骨架 */}
      <section>
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-2/3">
              {/* 标签导航骨架 */}
              <div className="border-b mb-8">
                <div className="flex space-x-8 overflow-x-auto">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-10 bg-gray-200 rounded w-24 animate-pulse"></div>
                  ))}
                </div>
              </div>

              {/* 摘要部分骨架 */}
              <div className="mb-16">
                <div className="h-7 bg-gray-200 rounded w-64 mb-4 animate-pulse"></div>
                <div className="h-5 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
                <div className="space-y-3 mb-6">
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6 animate-pulse"></div>
                </div>

                {/* 章节骨架 */}
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="mb-8">
                    <div className="h-6 bg-gray-200 rounded w-72 mb-3 animate-pulse"></div>
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                      <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6 animate-pulse"></div>
                      <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 侧边栏目录骨架 */}
            <div className="md:w-1/3">
              <div className="sticky top-24">
                <div className="flex flex-col items-center mb-8 p-6 border rounded-lg">
                  <div className="mb-4 w-[176px] h-[256px] bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-7 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
                  <div className="h-5 bg-gray-200 rounded w-32 mb-4 animate-pulse"></div>

                  <div className="flex gap-3 w-full mb-6">
                    <div className="h-10 bg-gray-200 rounded flex-1 animate-pulse"></div>
                    <div className="h-10 bg-gray-200 rounded flex-1 animate-pulse"></div>
                  </div>

                  <div className="w-full">
                    <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
                    <div className="space-y-2">
                      {[...Array(8)].map((_, i) => (
                        <div key={i} className="border-b pb-2">
                          <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
