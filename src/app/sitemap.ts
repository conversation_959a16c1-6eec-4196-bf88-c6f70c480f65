import type { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL as string

  // 只包含英文版本的路径
  const paths = [
    // 基础页面
    '',
    '/pricing',
    '/my-library',
    '/search',
  ]

  // 只为英文版本生成站点地图条目
  return paths.map((path) => {
    return {
      url: `${baseUrl}${path}`,
      lastModified: new Date(),
      // 添加更新频率和优先级
      changeFrequency: path === '' ? 'daily' : 'weekly',
      priority: path === '' ? 1.0 : 0.8
    }
  })
}
