<?php
declare(strict_types=1);

namespace Website\Common\Monolog\Handler;

use Hyperf\Elasticsearch\ClientBuilderFactory;
use Hyperf\Utils\ApplicationContext;
use Monolog\Handler\ElasticsearchHandler;
use Monolog\Logger;

class HyperfElasticsearchHandler extends ElasticsearchHandler
{
    public function __construct($client, array $options = [], $level = Logger::DEBUG, bool $bubble = true)
    {
        if (is_null($client)) {
            $client = ApplicationContext::getContainer()->get(ClientBuilderFactory::class)
                ->create()
                ->setSSLVerification(false)
                ->setHosts(explode(',', env('ES_HOST')))
                ->build();
        }

        parent::__construct($client, $options, $level, $bubble);
    }
}
