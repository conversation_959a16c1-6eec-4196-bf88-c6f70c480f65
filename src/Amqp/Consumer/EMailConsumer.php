<?php

declare(strict_types=1);

namespace Website\Common\Amqp\Consumer;

use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Utils\Str;
use Lete\Base\Abstraction\ConsumerMessage;
use Lete\Mail\Mail;
use Lete\MongoDB\MongoClient\MongoDb;
use PhpAmqpLib\Message\AMQPMessage;

/**
 * @Consumer(name="EMailConsumer", nums=2)
 */
class EMailConsumer extends ConsumerMessage
{
    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-email';
        $this->queue = env('APP_NAME') . '-email';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            retry(3, function () use ($data, &$result) {
                $Mail = (new Mail())
                    ->to($data['email'])
                    ->subject($data['subject']);
                if (isset($data['template'])) {
                    $Mail->render($data['template'], $data['view_data'] ?? []);
                } elseif (isset($data['html'])) {
                    $Mail->html($data['html']);
                } elseif (isset($data['text'])) {
                    $Mail->text($data['text']);
                }
                if (!$Mail->send()) {
                    throw $Mail->exception;
                }
            }, 500);
        } catch (\Throwable $e) {
            $error_message = $e->getMessage();
        } finally {
            MongoDb::collection(Str::snake(class_basename($this)) . '_logs')
                ->insertOne([
                    'data' => $data,
                    'error_message' => $error_message ?? null,
                    'created_at' => time(),
                ]);
            return Result::ACK;
        }
    }
}
