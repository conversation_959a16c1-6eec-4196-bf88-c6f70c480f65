<?php

declare(strict_types=1);

namespace Website\Common\Amqp\Consumer;

use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Utils\Str;
use Lete\Base\Abstraction\ConsumerMessage;
use Lete\MongoDB\MongoClient\MongoDb;
use PhpAmqpLib\Message\AMQPMessage;
use Website\Common\Service\UserService;

/**
 * @Consumer(name="SyncUserQuotaConsumer", nums=1)
 */
class SyncUserQuotaConsumer extends ConsumerMessage
{
    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-sync-user-quota';
        $this->queue = env('APP_NAME') . '-sync-user-quota';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            retry(3, function () use ($data) {
                UserService::updatePermissionQuota($data['user_id'], $data['permission_name'], $data['used'], $data['reset_at']);
            }, 1000);
        } catch (\Throwable $e) {
            MongoDb::collection(Str::snake(class_basename($this)) . '_errors')
                ->insertOne([
                    'data' => $data,
                    'message' => $e->getMessage(),
                    'created_at' => time(),
                ]);
            return Result::REQUEUE;
        } finally {
            return Result::ACK;
        }
    }
}
