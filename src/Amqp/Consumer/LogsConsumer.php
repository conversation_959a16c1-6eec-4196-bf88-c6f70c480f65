<?php

declare(strict_types=1);

namespace Website\Common\Amqp\Consumer;

use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;
use Lete\Base\Abstraction\ConsumerMessage;
use Lete\MongoDB\MongoClient\MongoDb;
use PhpAmqpLib\Message\AMQPMessage;

/**
 * @Consumer(name="LogsConsumer", nums=2)
 */
class LogsConsumer extends ConsumerMessage
{
    /**
     * @var string
     */
    protected $poolName = 'center';

    /**
     * @Inject
     * @var ConfigInterface
     */
    protected $config;

    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = ['backend-rpc-logs', env('APP_NAME') . '-backend-rpc-logs'];
        $this->queue = env('APP_NAME') . '-backend-rpc-logs';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            MongoDb::connection($this->config->get('admin.log.connection'))
                ->collection($this->config->get('admin.log.collection'))
                ->insertOne($data);
            $result_data = [
                'code' => 200,
                'message' => 'success',
                'data' => [],
                'error_code' => 0,
            ];
        } catch (\Throwable $e) {
            $result_data = [
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => [],
                'error_code' => $e->getCode(),
            ];
        }
        if ($message->has('correlation_id')) {
            $this->reply($result_data, $message);
        }
        return Result::ACK;
    }
}
