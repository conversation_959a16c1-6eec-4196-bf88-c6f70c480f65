<?php

declare(strict_types=1);

namespace Website\Common\Amqp\Consumer;

use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Utils\Str;
use Lete\Base\Abstraction\ConsumerMessage;
use Lete\MongoDB\MongoClient\MongoDb;
use PhpAmqpLib\Message\AMQPMessage;
use Website\Common\Utils\Alarm;

/**
 * @Consumer(name="AlarmConsumer", nums=1)
 */
class AlarmConsumer extends ConsumerMessage
{
    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-alarm';
        $this->queue = env('APP_NAME') . '-alarm';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \Throwable
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            Alarm::exec($data);

            return Result::ACK;
        } catch (\Throwable $e) {
            MongoDb::collection(Str::snake(class_basename($this)) . '_errors')
                ->insertOne([
                    'data' => $data,
                    'message' => $e->getMessage(),
                    'created_at' => time(),
                ]);
            return Result::REQUEUE;
        }
    }
}
