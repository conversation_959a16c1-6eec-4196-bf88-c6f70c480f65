<?php

declare(strict_types=1);

namespace Website\Common\Amqp\Producer;

use Hyperf\Amqp\Message\ProducerMessage;

class SyncUserQuotaProducer extends ProducerMessage
{
    /**
     * @param $data
     */
    public function __construct($data)
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-sync-user-quota';

        $this->payload = $data;
    }
}
