import mongoose, { Document, Model } from 'mongoose'

// 定义错误日志的接口
export interface ErrorLogSchema {
  timestamp: Date
  environment: string
  type: 'client' | 'server' | 'edge'
  path: string
  message: string
  stack: string
  userAgent?: string
  userId?: string
}

// 扩展为MongoDB文档接口
export interface ErrorLogDocument extends ErrorLogSchema, Document {}

// 定义Schema
const errorLogSchema = new mongoose.Schema<ErrorLogDocument>({
  timestamp: { type: Date, default: Date.now },
  environment: { type: String, required: true },
  type: { type: String, enum: ['client', 'server', 'edge'], required: true },
  path: { type: String, required: true },
  message: { type: String, required: true },
  stack: { type: String, required: true },
  userAgent: { type: String },
  userId: { type: String }
})

// 模型类型
type ErrorLogModel = Model<ErrorLogDocument>

// 创建或获取模型
const ErrorLog = (mongoose.models.ErrorLog ||
  mongoose.model<ErrorLogDocument>('ErrorLog', errorLogSchema)) as ErrorLogModel

export default ErrorLog
