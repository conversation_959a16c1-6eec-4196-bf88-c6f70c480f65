'server-only'

/**
 * 阅读历史模型层
 * 负责与数据库交互，提供基础的阅读历史相关 CRUD 操作
 */

import { prisma } from '@prisma/prisma';

/**
 * 添加或更新用户阅读历史
 * 使用 upsert 确保同一用户同一书籍只有一条记录
 *
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<void>
 */
export async function addOrUpdateReadingHistory(
  userId: number,
  bookId: number
): Promise<void> {
  try {
    // 先查找是否存在记录
    const existingRecord = await prisma.user_reading_history.findFirst({
      where: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    });

    if (existingRecord) {
      // 更新现有记录
      await prisma.user_reading_history.update({
        where: {
          id: existingRecord.id
        },
        data: {
          last_read_at: new Date()
        }
      });
    } else {
      // 创建新记录
      await prisma.user_reading_history.create({
        data: {
          user_id: BigInt(userId),
          book_id: bookId,
          last_read_at: new Date()
        }
      });
    }

    console.log(`Reading history recorded for user ${userId}, book ${bookId}`);
  } catch (error) {
    console.error(`Failed to record reading history for user ${userId}, book ${bookId}:`, error);
    // 不抛出错误，避免影响用户体验
    // 阅读历史记录失败不应该阻止页面正常显示
  }
}

/**
 * 批量删除用户阅读历史
 *
 * @param userId 用户ID
 * @param bookIds 书籍ID数组
 * @returns Promise<boolean> 是否删除成功
 */
export async function deleteReadingHistory(
  userId: number,
  bookIds: number[]
): Promise<boolean> {
  try {
    if (!bookIds.length) {
      return true;
    }

    await prisma.user_reading_history.deleteMany({
      where: {
        user_id: BigInt(userId),
        book_id: { in: bookIds }
      }
    });

    console.log(`Deleted reading history for user ${userId}, books: ${bookIds.join(', ')}`);
    return true;
  } catch (error) {
    console.error(`Failed to delete reading history for user ${userId}:`, error);
    return false;
  }
}

/**
 * 删除单个阅读历史记录
 *
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否删除成功
 */
export async function deleteSingleReadingHistory(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    // 先查找记录
    const existingRecord = await prisma.user_reading_history.findFirst({
      where: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    });

    if (existingRecord) {
      await prisma.user_reading_history.delete({
        where: {
          id: existingRecord.id
        }
      });
      console.log(`Deleted reading history for user ${userId}, book ${bookId}`);
    } else {
      console.log(`Reading history not found for user ${userId}, book ${bookId} - treating as success`);
    }

    return true;
  } catch (error) {
    console.error(`Failed to delete reading history for user ${userId}, book ${bookId}:`, error);
    return false;
  }
}

/**
 * 检查用户是否有阅读历史记录
 *
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否存在记录
 */
export async function hasReadingHistory(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    const record = await prisma.user_reading_history.findFirst({
      where: {
        user_id: BigInt(userId),
        book_id: bookId
      }
    });

    return !!record;
  } catch (error) {
    console.error(`Failed to check reading history for user ${userId}, book ${bookId}:`, error);
    return false;
  }
}

/**
 * 获取用户阅读历史总数
 *
 * @param userId 用户ID
 * @returns Promise<number> 阅读历史总数
 */
export async function getReadingHistoryCount(userId: number): Promise<number> {
  try {
    const count = await prisma.user_reading_history.count({
      where: {
        user_id: BigInt(userId)
      }
    });

    return count;
  } catch (error) {
    console.error(`Failed to get reading history count for user ${userId}:`, error);
    return 0;
  }
}
