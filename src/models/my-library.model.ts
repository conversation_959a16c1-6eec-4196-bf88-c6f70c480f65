'server-only'

/**
 * My Library 页面模型层
 * 负责数据库访问和基本数据操作
 */

import { prisma } from '@prisma/prisma';
import { Prisma } from '@prisma/client';

/**
 * 获取用户音频进度
 * 使用 Promise.all 并行获取数据和总数
 */
export async function getUserAudioProgress(
  userId: number,
  page: number,
  limit: number
): Promise<{ audioProgress: any[]; total: number }> {
  // 并行查询用户音频进度表和总数
  const [audioProgress, total] = await Promise.all([
    prisma.user_audio_progress.findMany({
      where: { user_id: userId },
      orderBy: { last_listened_at: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.user_audio_progress.count({
      where: { user_id: userId }
    })
  ]);

  return { audioProgress, total };
}

/**
 * 获取用户收藏
 * 使用 Promise.all 并行获取数据和总数
 */
export async function getUserFavorites(
  userId: number,
  page: number,
  limit: number
): Promise<{ favorites: any[]; total: number }> {
  // 并行查询用户收藏表和总数
  const [favorites, total] = await Promise.all([
    prisma.user_favorites.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.user_favorites.count({
      where: { user_id: userId }
    })
  ]);

  return { favorites, total };
}

/**
 * 获取用户评分
 * 使用 Promise.all 并行获取数据和总数
 */
export async function getUserRatings(
  userId: number,
  page: number,
  limit: number
): Promise<{ userRatings: any[]; total: number }> {
  // 并行查询用户评分表和总数
  const [userRatings, total] = await Promise.all([
    prisma.ratings.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.ratings.count({
      where: { user_id: userId }
    })
  ]);

  return { userRatings, total };
}

/**
 * 获取用户阅读历史
 * 使用 Promise.all 并行获取数据和总数
 */
export async function getUserReadingHistory(
  userId: number,
  page: number,
  limit: number
): Promise<{ history: any[]; total: number }> {
  // 并行查询用户阅读历史表和总数
  // 注意：user_id 字段在数据库中是 BigInt 类型，需要转换
  const [history, total] = await Promise.all([
    prisma.user_reading_history.findMany({
      where: { user_id: BigInt(userId) },
      orderBy: { last_read_at: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.user_reading_history.count({
      where: { user_id: BigInt(userId) }
    })
  ]);

  return { history, total };
}

/**
 * 根据ID列表获取书籍详情
 * 使用 select 代替 include，只选择需要的字段，减少返回的数据量
 */
export async function getBooksByIds(
  bookIds: number[],
  language: string
): Promise<any[]> {
  if (!bookIds.length) return [];

  // 查询书籍详情
  return prisma.books.findMany({
    where: { id: { in: bookIds } },
    select: {
      id: true,
      rate_score: true, // 添加书籍评分字段
      book_translations: {
        where: { language_id: language },
        take: 1,
        select: {
          title: true,
          subtitle: true
        }
      },
      book_covers: {
        where: { is_primary: 1 },
        take: 1,
        select: {
          image_url: true
        }
      },
      book_authors: {
        orderBy: { author_order: 'asc' },
        take: 1,
        select: {
          author: {
            select: {
              author_translations: {
                where: { language_id: language },
                take: 1,
                select: {
                  name: true
                }
              }
            }
          }
        }
      },
      audio_files: {
        where: { language_id: language },
        take: 1,
        select: {
          duration_seconds: true
        }
      }
    }
  });
}

/**
 * 获取书籍评分统计
 */
export async function getBookRatings(bookIds: number[]): Promise<any[]> {
  if (!bookIds.length) return [];

  // 查询书籍评分
  const results = await prisma.$queryRaw`
    SELECT
      book_id,
      AVG(score) as avg_score
    FROM ratings
    WHERE book_id IN (${Prisma.join(bookIds)})
    GROUP BY book_id
  ` as any[];

  // 转换 Decimal 为 number，确保可以在 Server/Client Components 之间传递
  return results.map((result: any) => ({
    book_id: result.book_id,
    avg_score: Number(result.avg_score) || 0
  }));
}
