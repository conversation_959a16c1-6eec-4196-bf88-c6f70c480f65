/**
 * 分类模型层
 * 负责与数据库交互，提供基础的CRUD操作
 */

import { prisma } from '@prisma/prisma'
import { CategoryBasic, CategoryTranslation } from '@/types/book.types';

/**
 * 分类详情（包含翻译信息）
 */
export interface CategoryDetail extends CategoryBasic {
  translations: CategoryTranslation[];
  bookCount: number;
}

/**
 * 分类列表项
 */
export interface CategoryListItem {
  id: number;
  name: string;
  description?: string | null;
  bookCount: number;
}

/**
 * 获取分类列表
 */
export async function getCategories(language: string = 'en'): Promise<CategoryListItem[]> {
  // 1. 获取所有分类
  const categories = await prisma.categories.findMany();

  // 2. 获取分类ID列表
  const categoryIds = categories.map(category => category.id);

  // 3. 获取这些分类的翻译
  const translations = await prisma.category_translations.findMany({
    where: {
      category_id: { in: categoryIds },
      language_id: language
    }
  });

  // 4. 获取分类关联的书籍数量
  const bookCategories = await prisma.book_categories.findMany({
    where: {
      category_id: { in: categoryIds }
    }
  });

  // 5. 统计每个分类的书籍数量
  const bookCountMap = new Map<number, number>();
  bookCategories.forEach(bc => {
    const categoryId = bc.category_id;
    bookCountMap.set(categoryId, (bookCountMap.get(categoryId) || 0) + 1);
  });

  // 6. 组合数据
  const result = categories.map(category => {
    const translation = translations.find(t => t.category_id === category.id);
    return {
      id: category.id,
      name: translation?.name || `Category ${category.id}`,
      description: translation?.description || null,
      bookCount: bookCountMap.get(category.id) || 0
    };
  });

  // 7. 按名称排序
  return result.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * 获取分类详情
 */
export async function getCategoryById(id: number): Promise<CategoryDetail | null> {
  // 1. 获取分类
  const category = await prisma.categories.findUnique({
    where: { id }
  });

  if (!category) {
    return null;
  }

  // 2. 获取分类的翻译
  const translations = await prisma.category_translations.findMany({
    where: {
      category_id: id
    }
  });

  // 3. 获取分类关联的书籍数量
  const bookCategories = await prisma.book_categories.findMany({
    where: {
      category_id: id
    }
  });

  // 4. 组合数据
  return {
    id: category.id,
    translations: translations.map(translation => ({
      id: translation.id,
      categoryId: translation.category_id,
      languageId: translation.language_id,
      name: translation.name,
      description: translation.description,
      isDefault: translation.is_default
    })),
    bookCount: bookCategories.length
  };
}

/**
 * 获取Browse下拉菜单的分类列表
 * 过滤空分类，按书籍数量降序排列
 */
export async function getCategoriesForBrowse(language: string = 'en'): Promise<CategoryListItem[]> {
  // 复用现有逻辑，但添加过滤和重新排序
  const allCategories = await getCategories(language);

  return allCategories
    .filter(category => category.bookCount > 0) // 过滤空分类
    .sort((a, b) => b.bookCount - a.bookCount); // 按书籍数量降序排列
}

/**
 * 获取热门分类（固定返回指定分类并查询书籍数量）
 */
export async function getPopularCategories(language: string = 'en'): Promise<CategoryListItem[]> {
  // 1. 定义固定的热门分类列表
  const popularCategoryNames = [
    'Business',
    'Psychology',
    'Self-Improvement',
    'Health',
    'Fiction',
    'History',
    'Science',
    'Personal Development',
    "Self Help"
  ];

  // 2. 使用级联查询一次性获取分类信息和关联的书籍数量
  const categoryTranslations = await prisma.category_translations.findMany({
    where: {
      name: { in: popularCategoryNames },
      language_id: language
    },
    include: {
      category: {
        include: {
          book_categories: true
        }
      }
    }
  });

  // 3. 组合数据
  const categoriesMap = new Map<string, CategoryListItem>();
  categoryTranslations.forEach(ct => {
    if (ct.category_id && ct.category) {
      categoriesMap.set(ct.name, {
        id: ct.category_id,
        name: ct.name,
        description: ct.description,
        bookCount: ct.category.book_categories.length
      });
    }
  });

  // 4. 按照固定的顺序返回结果
  const result: CategoryListItem[] = [];
  popularCategoryNames.forEach(name => {
    const category = categoriesMap.get(name);
    if (category) {
      result.push(category);
    }
  });

  return result;
}

/**
 * 将字符串转换为 slug 格式
 * 例如：'Business & Finance' -> 'business-finance'
 */
function convertToSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/-+/g, '-'); // 移除连续的连字符
}

/**
 * 根据 slug 获取分类
 */
export async function getCategoryBySlug(slug: string, language: string = 'en'): Promise<CategoryListItem | null> {
  // 1. 获取所有分类翻译
  const categoryTranslations = await prisma.category_translations.findMany({
    where: {
      language_id: language
    },
    include: {
      category: {
        include: {
          book_categories: true
        }
      }
    }
  });

  // 2. 查找匹配的分类
  const matchedTranslation = categoryTranslations.find(ct => {
    // 将分类名称转换为 slug 格式进行比较
    const categorySlug = convertToSlug(ct.name);
    return categorySlug === slug;
  });

  // 3. 如果找不到匹配的分类，返回 null
  if (!matchedTranslation || !matchedTranslation.category) {
    return null;
  }

  // 4. 返回分类信息
  return {
    id: matchedTranslation.category_id!,
    name: matchedTranslation.name,
    description: matchedTranslation.description,
    bookCount: matchedTranslation.category.book_categories.length
  };
}
