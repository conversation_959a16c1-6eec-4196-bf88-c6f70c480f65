/**
 * 作者模型层
 * 负责与数据库交互，提供基础的CRUD操作
 */

import { prisma } from '@prisma/prisma'
import { AuthorBasic, AuthorTranslation } from '@/types/book.types';

/**
 * 作者详情（包含翻译信息）
 */
export interface AuthorDetail extends AuthorBasic {
  avatar_url?: string | null;
  website?: string | null;
  twitter_account?: string | null;
  translations: AuthorTranslation[];
  books?: {
    id: number;
    title: string;
    coverUrl?: string;
  }[];
  viewCount?: string | number;
}

/**
 * 作者列表项
 */
export interface AuthorListItem {
  id: number;
  name: string;
  biography?: string | null;
  bookCount: number;
  viewCount?: string | number;
  avatar_url?: string | null;
}

/**
 * 作者查询参数
 */
export interface AuthorQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  language?: string;
  sortBy?: 'popular' | 'name';
}

/**
 * 获取作者列表
 */
export async function getAuthors(params: AuthorQueryParams): Promise<{
  data: AuthorListItem[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> {
  const {
    page = 1,
    limit = 10,
    search,
    language = 'en',
    sortBy = 'popular'
  } = params;

  // 1. 获取所有作者（包含头像URL）
  let authors = await prisma.authors.findMany({
    select: {
      id: true,
      avatar_url: true
    }
  });

  // 2. 获取作者ID列表
  const authorIds = authors.map(author => author.id);

  // 3. 获取这些作者的翻译
  let translations = await prisma.author_translations.findMany({
    where: {
      author_id: { in: authorIds },
      language_id: language
    }
  });

  // 4. 如果有搜索关键词，过滤作者
  if (search) {
    // 过滤出匹配搜索条件的翻译
    translations = translations.filter(translation =>
      translation.name?.includes(search) ||
      translation.biography?.includes(search)
    );

    // 获取匹配的作者ID
    const matchedAuthorIds = translations.map(t => t.author_id);

    // 过滤作者列表
    authors = authors.filter(author => matchedAuthorIds.includes(author.id));
  }

  // 5. 计算总数
  const total = authors.length;
  const totalPages = Math.ceil(total / limit);

  // 6. 获取作者的浏览统计
  const viewStatistics = await prisma.author_view_statistics.findMany({
    where: {
      author_id: { in: authors.map(a => a.id) }
    }
  });

  // 7. 获取作者关联的书籍
  const bookAuthors = await prisma.book_authors.findMany({
    where: {
      author_id: { in: authors.map(a => a.id) }
    }
  });

  // 8. 统计每个作者的书籍数量
  const bookCountMap = new Map<number, number>();
  bookAuthors.forEach(ba => {
    const authorId = ba.author_id;
    bookCountMap.set(authorId, (bookCountMap.get(authorId) || 0) + 1);
  });

  // 9. 组合数据
  let result = authors.map(author => {
    const translation = translations.find(t => t.author_id === author.id);
    const viewStat = viewStatistics.find(vs => vs.author_id === author.id);
    return {
      id: author.id,
      name: translation?.name || `Author ${author.id}`,
      biography: translation?.biography || null,
      bookCount: bookCountMap.get(author.id) || 0,
      viewCount: viewStat?.view_count?.toString() || '0',
      avatar_url: author.avatar_url
    };
  });

  // 10. 排序
  if (sortBy === 'popular') {
    // 按浏览量排序
    result.sort((a, b) => {
      const viewCountA = Number(a.viewCount);
      const viewCountB = Number(b.viewCount);
      return viewCountB - viewCountA; // 降序排列
    });
  } else {
    // 按名称排序
    result.sort((a, b) => a.name.localeCompare(b.name)); // 升序排列
  }

  // 11. 分页
  const paginatedResult = result.slice((page - 1) * limit, page * limit);

  // 12. 转换为前端需要的格式
  const data = paginatedResult;

  return {
    data,
    meta: {
      total,
      page,
      limit,
      totalPages
    }
  };
}

/**
 * 获取作者详情
 */
export async function getAuthorById(id: number, language: string = 'en'): Promise<AuthorDetail | null> {
  // 使用一次性查询获取作者及其关联数据
  const author = await prisma.authors.findUnique({
    where: { id },
    select: {
      id: true,
      avatar_url: true,
      website: true,
      twitter_account: true,
      // 获取作者的翻译信息
      author_translations: {
        select: {
          id: true,
          author_id: true,
          language_id: true,
          name: true,
          biography: true,
          is_default: true
        }
      },
      // 获取作者的浏览统计
      author_view_statistics: {
        select: {
          view_count: true
        }
      },
      // 获取作者的书籍关联
      book_authors: {
        select: {
          book_id: true,
          // 嵌套选择书籍信息
          book: {
            select: {
              id: true,
              // 嵌套选择书籍翻译
              book_translations: {
                where: {
                  language_id: language
                },
                select: {
                  title: true
                },
                take: 1
              },
              // 嵌套选择书籍封面
              book_covers: {
                where: {
                  language_id: language,
                  is_primary: 1
                },
                select: {
                  image_url: true
                },
                take: 1
              }
            }
          }
        }
      }
    }
  });

  if (!author) {
    return null;
  }

  // 格式化书籍数据
  const books = author.book_authors
    .filter(ba => ba.book !== null)
    .map(ba => {
      const book = ba.book;
      return {
        id: book.id,
        title: book.book_translations[0]?.title || `Book ${book.id}`,
        coverUrl: book.book_covers[0]?.image_url
      };
    });

  // 格式化作者翻译数据
  const translations = author.author_translations.map(translation => ({
    id: translation.id,
    authorId: translation.author_id,
    languageId: translation.language_id,
    name: translation.name,
    biography: translation.biography,
    isDefault: translation.is_default
  }));

  // 返回结果
  return {
    id: author.id,
    avatar_url: author.avatar_url,
    website: author.website,
    twitter_account: author.twitter_account,
    translations,
    books,
    viewCount: author.author_view_statistics?.view_count?.toString() || '0'
  };
}

/**
 * 获取热门作者
 */
export async function getPopularAuthors(limit: number = 10, language: string = 'en'): Promise<AuthorListItem[]> {
  // 1. 获取所有作者（包含头像URL）
  const authors = await prisma.authors.findMany({
    select: {
      id: true,
      avatar_url: true
    }
  });

  // 2. 获取作者ID列表
  const authorIds = authors.map(author => author.id);

  // 3. 获取这些作者的翻译
  const translations = await prisma.author_translations.findMany({
    where: {
      author_id: { in: authorIds },
      language_id: language
    }
  });

  // 4. 获取作者的浏览统计
  const viewStatistics = await prisma.author_view_statistics.findMany({
    where: {
      author_id: { in: authorIds }
    }
  });

  // 5. 获取作者关联的书籍
  const bookAuthors = await prisma.book_authors.findMany({
    where: {
      author_id: { in: authorIds }
    }
  });

  // 6. 统计每个作者的书籍数量
  const bookCountMap = new Map<number, number>();
  bookAuthors.forEach(ba => {
    const authorId = ba.author_id;
    bookCountMap.set(authorId, (bookCountMap.get(authorId) || 0) + 1);
  });

  // 7. 组合数据
  const result = authors.map(author => {
    const translation = translations.find(t => t.author_id === author.id);
    const viewStat = viewStatistics.find(vs => vs.author_id === author.id);
    return {
      id: author.id,
      name: translation?.name || `Author ${author.id}`,
      biography: translation?.biography || null,
      bookCount: bookCountMap.get(author.id) || 0,
      viewCount: viewStat?.view_count?.toString() || '0',
      avatar_url: author.avatar_url
    };
  });

  // 8. 按浏览量排序
  result.sort((a, b) => {
    const viewCountA = Number(a.viewCount);
    const viewCountB = Number(b.viewCount);
    return viewCountB - viewCountA; // 降序排列
  });

  // 9. 只取前limit个作者
  return result.slice(0, limit);
}

/**
 * 增加作者浏览量
 * 使用 upsert 操作避免并发竞态条件
 */
export async function incrementAuthorViewCount(authorId: number): Promise<void> {
  try {
    await prisma.author_view_statistics.upsert({
      where: { author_id: authorId },
      update: {
        view_count: {
          increment: 1
        },
        last_viewed_at: new Date()
      },
      create: {
        author_id: authorId,
        view_count: 1,
        last_viewed_at: new Date()
      }
    });
  } catch (error) {
    console.error(`Failed to increment view count for author ${authorId}:`, error);
    // 不抛出错误，避免影响用户体验
    // 访问量统计失败不应该阻止页面正常显示
  }
}
