'server-only'

import { prisma } from '@prisma/prisma';
// 用户 model 层

/**
 * 获取用户音频进度
 */
export async function getUserAudioProgress(
  userId: number,
  page: number,
  limit: number
): Promise<{ audioProgress: any[]; total: number }> {
  // 查询用户音频进度表
  const audioProgress = await prisma.user_audio_progress.findMany({
    where: { user_id: userId },
    orderBy: { last_listened_at: 'desc' },
    skip: (page - 1) * limit,
    take: limit
  });

  // 计算总数
  const total = await prisma.user_audio_progress.count({
    where: { user_id: userId }
  });

  return { audioProgress, total };
}

/**
 * 获取用户收藏
 */
export async function getUserFavorites(
  userId: number,
  page: number,
  limit: number
): Promise<{ favorites: any[]; total: number }> {
  // 查询用户收藏表
  const favorites = await prisma.user_favorites.findMany({
    where: { user_id: userId },
    orderBy: { created_at: 'desc' },
    skip: (page - 1) * limit,
    take: limit
  });

  // 计算总数
  const total = await prisma.user_favorites.count({
    where: { user_id: userId }
  });

  return { favorites, total };
}

/**
 * 获取用户评分
 */
export async function getUserRatings(
  userId: number,
  page: number,
  limit: number
): Promise<{ userRatings: any[]; total: number }> {
  // 查询用户评分表
  const userRatings = await prisma.ratings.findMany({
    where: { user_id: userId },
    orderBy: { created_at: 'desc' },
    skip: (page - 1) * limit,
    take: limit
  });

  // 计算总数
  const total = await prisma.ratings.count({
    where: { user_id: userId }
  });

  return { userRatings, total };
}

/**
 * 获取用户阅读历史
 */
export async function getUserReadingHistory(
  userId: number,
  page: number,
  limit: number
): Promise<{ history: any[]; total: number }> {
  // 查询用户阅读历史表
  const history = await prisma.user_reading_history.findMany({
    where: { user_id: userId },
    orderBy: { last_read_at: 'desc' },
    skip: (page - 1) * limit,
    take: limit
  });

  // 计算总数
  const total = await prisma.user_reading_history.count({
    where: { user_id: userId }
  });

  return { history, total };
}
