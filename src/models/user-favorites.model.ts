'server-only'

/**
 * 用户收藏模型层
 * 负责与数据库交互，提供基础的CRUD操作
 */

import { prisma } from '@prisma/prisma'

/**
 * 检查用户是否收藏了指定书籍
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否已收藏
 */
export async function checkUserFavorite(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)
    const bookIdInt = Number(bookId)

    const favorite = await prisma.user_favorites.findUnique({
      where: {
        user_id_book_id: {
          user_id: userIdInt,
          book_id: bookIdInt
        }
      }
    })
    return !!favorite
  } catch (error) {
    console.error('检查用户收藏状态失败:', error)
    return false
  }
}

/**
 * 添加用户收藏
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否添加成功
 */
export async function addUserFavorite(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)
    const bookIdInt = Number(bookId)
    console.log('addUserFavorite', userIdInt, bookIdInt)
    await prisma.user_favorites.create({
      data: {
        user_id: userIdInt,
        book_id: bookIdInt
      }
    })
    return true
  } catch (error) {
    console.error('添加用户收藏失败:', error)
    // 如果是重复插入错误，也认为是成功的
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return true
    }
    return false
  }
}

/**
 * 移除用户收藏
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns Promise<boolean> 是否移除成功
 */
export async function removeUserFavorite(
  userId: number,
  bookId: number
): Promise<boolean> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)
    const bookIdInt = Number(bookId)

    await prisma.user_favorites.delete({
      where: {
        user_id_book_id: {
          user_id: userIdInt,
          book_id: bookIdInt
        }
      }
    })
    return true
  } catch (error) {
    console.error('移除用户收藏失败:', error)
    // 如果记录不存在，也认为是成功的
    if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
      return true
    }
    return false
  }
}

/**
 * 获取用户收藏的书籍ID列表
 * @param userId 用户ID
 * @param limit 限制数量，默认不限制
 * @returns Promise<number[]> 书籍ID列表
 */
export async function getUserFavoriteBookIds(
  userId: number,
  limit?: number
): Promise<number[]> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)

    const favorites = await prisma.user_favorites.findMany({
      where: { user_id: userIdInt },
      select: { book_id: true },
      orderBy: { created_at: 'desc' },
      ...(limit && { take: limit })
    })

    return favorites.map(f => f.book_id)
  } catch (error) {
    console.error('获取用户收藏书籍ID列表失败:', error)
    return []
  }
}

/**
 * 获取用户收藏总数
 * @param userId 用户ID
 * @returns Promise<number> 收藏总数
 */
export async function getUserFavoriteCount(userId: number): Promise<number> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)

    return await prisma.user_favorites.count({
      where: { user_id: userIdInt }
    })
  } catch (error) {
    console.error('获取用户收藏总数失败:', error)
    return 0
  }
}

/**
 * 批量检查用户收藏状态
 * @param userId 用户ID
 * @param bookIds 书籍ID列表
 * @returns Promise<Map<number, boolean>> 书籍ID到收藏状态的映射
 */
export async function batchCheckUserFavorites(
  userId: number,
  bookIds: number[]
): Promise<Map<number, boolean>> {
  const result = new Map<number, boolean>()

  if (bookIds.length === 0) {
    return result
  }

  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)
    const bookIdsInt = bookIds.map(id => Number(id))

    const favorites = await prisma.user_favorites.findMany({
      where: {
        user_id: userIdInt,
        book_id: { in: bookIdsInt }
      },
      select: { book_id: true }
    })

    const favoritedBookIds = new Set(favorites.map(f => f.book_id))

    bookIdsInt.forEach(bookId => {
      result.set(bookId, favoritedBookIds.has(bookId))
    })

    return result
  } catch (error) {
    console.error('批量检查用户收藏状态失败:', error)
    // 出错时返回所有书籍都未收藏
    bookIds.forEach(bookId => {
      result.set(Number(bookId), false)
    })
    return result
  }
}

/**
 * 获取书籍的收藏用户数量
 * @param bookId 书籍ID
 * @returns Promise<number> 收藏用户数量
 */
export async function getBookFavoriteCount(bookId: number): Promise<number> {
  try {
    // 确保类型转换正确
    const bookIdInt = Number(bookId)

    return await prisma.user_favorites.count({
      where: { book_id: bookIdInt }
    })
  } catch (error) {
    console.error('获取书籍收藏数量失败:', error)
    return 0
  }
}

/**
 * 获取最近收藏的书籍
 * @param userId 用户ID
 * @param limit 限制数量
 * @returns Promise<Array<{book_id: number, created_at: Date}>> 最近收藏的书籍
 */
export async function getRecentFavorites(
  userId: number,
  limit: number = 10
): Promise<Array<{ book_id: number; created_at: Date | null }>> {
  try {
    // 确保类型转换正确
    const userIdInt = Number(userId)

    return await prisma.user_favorites.findMany({
      where: { user_id: userIdInt },
      select: {
        book_id: true,
        created_at: true
      },
      orderBy: { created_at: 'desc' },
      take: limit
    })
  } catch (error) {
    console.error('获取最近收藏失败:', error)
    return []
  }
}
