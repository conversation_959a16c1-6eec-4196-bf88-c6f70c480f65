import mongoose, { Schema, Document } from 'mongoose'

export interface ITask extends Document {
  taskId: string
  text: string
  result?: string
  status: 'pending' | 'completed'
  error?: string
  submittedAt: Date
  completedAt?: Date
}

const TaskSchema: Schema = new Schema({
  taskId: { type: String, required: true, unique: true, index: true },
  text: { type: String, required: true },
  result: { type: String },
  status: { type: String, enum: ['pending', 'completed'], default: 'pending' },
  error: { type: String },
  submittedAt: { type: Date, default: Date.now },
  completedAt: { type: Date }
})

// 如果模型已经编译过则使用现有模型，否则创建新模型
export default mongoose.models.Task || mongoose.model<ITask>('Task', TaskSchema)
