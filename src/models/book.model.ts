/**
 * 书籍模型层
 * 负责与数据库交互，提供基础的CRUD操作
 */

import { prisma } from '@prisma/prisma'
import { BookQueryParams, PaginatedResult, BookListItem } from '@/types/book.types';
import {
  BookWhereInput,
  BookOrderByWithRelationInput,
  AuthorTranslation
} from '@/types/prisma.types';
import BookDetailData, { Ratings, Chapter } from '@/app/[locale]/(book)/book-summary/[slug]/types';
import { buildOptimizedAmazonUrl } from '@/utils/amazon.utils';

/**
 * 计算字符串相似度（基于相关性得分）
 * 用于搜索结果排序
 * @param str 要比较的字符串
 * @param query 搜索关键词
 * @returns 相似度得分（0-100）
 */
function calculateRelevanceScore(str: string, query: string): number {
  if (!str || !query) return 0;

  // 转换为小写以进行不区分大小写的比较
  const lowerStr = str.toLowerCase();
  const lowerQuery = query.toLowerCase();

  // 完全匹配得分最高
  if (lowerStr === lowerQuery) return 100;

  // 以查询词开头的得分次之
  if (lowerStr.startsWith(lowerQuery)) return 80;

  // 包含查询词的得分再次
  if (lowerStr.includes(lowerQuery)) {
    // 单词边界匹配得分更高（例如，查询"编程"，"Java编程"比"编程序"得分高）
    const wordBoundaryMatch = new RegExp(`(^|\\s)${lowerQuery}`, 'i').test(str);
    return wordBoundaryMatch ? 70 : 60;
  }

  // 包含查询词的部分单词匹配
  const words = lowerQuery.split(/\s+/);
  let matchCount = 0;

  for (const word of words) {
    if (word.length > 1 && lowerStr.includes(word)) {
      matchCount++;
    }
  }

  if (matchCount > 0) {
    return 40 + (matchCount / words.length) * 20;
  }

  // 最低相关性
  return 0;
}

/**
 * 按相关性对搜索结果进行排序
 * @param books 书籍列表
 * @param query 搜索关键词
 * @returns 排序后的书籍列表
 */
function sortByRelevance(books: BookListItem[], query: string): BookListItem[] {
  return [...books].sort((a, b) => {
    // 计算标题相关性得分
    const titleScoreA = calculateRelevanceScore(a.title, query);
    const titleScoreB = calculateRelevanceScore(b.title, query);

    // 计算作者相关性得分
    const authorScoreA = Math.max(...a.authors.map(author => calculateRelevanceScore(author, query)), 0);
    const authorScoreB = Math.max(...b.authors.map(author => calculateRelevanceScore(author, query)), 0);

    // 计算描述相关性得分
    const descScoreA = calculateRelevanceScore(a.description || '', query);
    const descScoreB = calculateRelevanceScore(b.description || '', query);

    // 计算总得分，标题权重最高，其次是作者，最后是描述
    const totalScoreA = titleScoreA * 3 + authorScoreA * 2 + descScoreA * 1;
    const totalScoreB = titleScoreB * 3 + authorScoreB * 2 + descScoreB * 1;

    // 按总得分降序排序
    return totalScoreB - totalScoreA;
  });
}

/**
 * 获取书籍列表（分页）
 */
export async function getBooks(params: BookQueryParams): Promise<PaginatedResult<BookListItem>> {
  const {
    page = 1,
    limit = 10,
    categoryId,
    authorId,
    search,
    language = 'en',
    sortBy = 'newest'
  } = params;

  // 构建查询条件
  const where: BookWhereInput = {
    is_published: 1
  };

  // 如果有分类ID，添加分类过滤
  if (categoryId) {
    where.book_categories = {
      some: {
        category_id: categoryId
      }
    };
  }

  // 如果有作者ID，添加作者过滤
  if (authorId) {
    where.book_authors = {
      some: {
        author_id: authorId
      }
    };
  }

  // 如果有搜索关键词，添加标题、描述和作者名搜索
  if (search) {
    where.OR = [
      // 搜索书籍标题、副标题和描述
      {
        book_translations: {
          some: {
            OR: [
              { title: { contains: search } },
              { subtitle: { contains: search } },
              { description: { contains: search } }
            ]
          }
        }
      },
      // 搜索作者名
      {
        book_authors: {
          some: {
            author: {
              author_translations: {
                some: {
                  name: { contains: search }
                }
              }
            }
          }
        }
      }
    ];
  }

  // 计算总数
  const total = await prisma.books.count({ where });
  const totalPages = Math.ceil(total / limit);

  // 构建排序条件
  const orderBy = sortBy === 'popular'
    ? { view_statistics: { view_count: 'desc' } as any }
    : { created_at: 'desc' as const };

  // 一次性查询所有关联数据
  const books = await prisma.books.findMany({
    where,
    select: {
      id: true,
      publication_year: true,
      rate_score: true,
      total_ratings: true,
      book_translations: {
        where: {
          language_id: language
        },
        select: {
          title: true,
          subtitle: true,
          description: true
        },
        take: 1
      },
      book_covers: {
        where: {
          language_id: language,
          is_primary: 1
        },
        select: {
          image_url: true
        },
        take: 1
      },
      book_authors: {
        include: {
          author: {
            include: {
              author_translations: {
                where: {
                  language_id: language
                }
              }
            }
          }
        },
        orderBy: {
          author_order: 'asc'
        }
      },
      book_categories: {
        include: {
          category: {
            include: {
              category_translations: {
                where: {
                  language_id: language
                }
              }
            }
          }
        }
      },
      view_statistics: {
        select: {
          view_count: true
        }
      }
    },
    orderBy,
    skip: (page - 1) * limit,
    take: limit
  } as any);

  // 转换为前端需要的格式
  let data = books.map((book: any) => {
    // 提取作者名称
    const authors = book.book_authors
      .map((ba: any) => {
        const authorTranslation = ba.author?.author_translations?.[0];
        return authorTranslation?.name || '';
      })
      .filter((name: string) => name !== '');

    // 提取分类名称
    const categories = book.book_categories
      .map((bc: any) => {
        const categoryTranslation = bc.category?.category_translations?.[0];
        return categoryTranslation?.name || '';
      })
      .filter((name: string) => name !== '');

    // 直接使用数据库中的预计算评分
    const rating = book.rate_score ? Number(book.rate_score) : 0;

    return {
      id: book.id,
      title: book.book_translations[0]?.title || '',
      subtitle: book.book_translations[0]?.subtitle || null,
      description: book.book_translations[0]?.description || null,
      coverUrl: book.book_covers[0]?.image_url || undefined,
      authors,
      categories,
      publicationYear: book.publication_year,
      rating: rating,
      viewCount: book.view_statistics?.view_count?.toString() || '0'
    } as BookListItem;
  });

  // 如果有搜索关键词，按相关性排序
  if (search) {
    // 计算相关性得分并排序
    data = sortByRelevance(data, search);
  }

  return {
    data,
    meta: {
      total,
      page,
      limit,
      totalPages
    }
  };
}

/**
 * 将 BookListItem 转换为 SimpleBook
 */
function convertToSimpleBook(book: BookListItem): any {
  return {
    id: String(book.id),
    title: book.title,
    subtitle: book.subtitle || '', // 添加 subtitle 字段
    coverUrl: book.coverUrl || '',
    author: book.authors[0] || '',
    rating: {
      score: book.rating || 0,
      count: 0 // 数据库中可能没有这个字段
    }
  };
}

/**
 * 获取书籍详情 - 优化版
 * 返回符合 BookDetailData 类型的完整书籍信息
 */
export async function getBookById(id: number, language: string = 'en'): Promise<BookDetailData | null> {
  // 1. 核心查询 - 获取书籍基本信息（包含作者、分类、语言、出版商和音频信息）
  const bookBasic = await getBookBasicInfo(id, language);

  if (!bookBasic) {
    return null;
  }

  // 2. 提取已查询的数据
  const authorData = bookBasic.authorData;
  const categoryData = bookBasic.categoryData;
  const languageInfo = bookBasic.languageInfo;
  const publisherData = bookBasic.publisherData;
  const audioInfo = bookBasic.audioInfo;

  // 3. 并行获取其余必要数据
  const [ratingStats, chapterData] = await Promise.all([
    getBookRatingStats(id, bookBasic),
    getBookChapters(id, language)
  ]) as [Ratings, Chapter[]];

  // 4. 获取相关书籍和热门书籍
  const [relatedBooksRaw, trendingBooksRaw] = await Promise.all([
    getRelatedBooks(id, authorData.authorIds, categoryData.categoryIds, language, 10),
    getPopularBooks(10, language)
  ]);

  // 5. 转换为 SimpleBook 类型
  const relatedBooks = relatedBooksRaw;
  const trendingBooks = trendingBooksRaw.map(convertToSimpleBook);

  // 6. 构建符合 BookDetailData 类型的返回数据
  const result: BookDetailData = {
    book: {
      id: String(bookBasic.id),
      title: bookBasic.book_translations[0]?.title || '',
      subtitle: bookBasic.book_translations[0]?.subtitle || '',
      isbn: bookBasic.isbn || '',
      asin: bookBasic.asin || '',
      isbn13: bookBasic.isbn13 || '',
      issn: bookBasic.issn || '',
      publicationYear: bookBasic.publication_year || 0,
      readingTimeMinutes: bookBasic.reading_time_minutes || 0,
      hasAudio: audioInfo.available,
      keyIdeasCount: chapterData.length,
      fileInfo: {
        pdf: {
          sizeInMb: bookBasic.file_size_pdf || 0,
          url: bookBasic.pdf_url || ''
        },
        epub: {
          sizeInMb: bookBasic.file_size_epub || 0,
          url: bookBasic.epub_url || ''
        }
      },
      ipfs: {
        cid: bookBasic.ipfs_cid || '',
        cidBlake2b: bookBasic.ipfs_cid_blake2b || ''
      },
      cover: {
        id: String(bookBasic.book_covers[0]?.id || ''),
        imageUrl: bookBasic.book_covers[0]?.image_url || '',
        isPrimary: Boolean(bookBasic.book_covers[0]?.is_primary)
      },
      description: bookBasic.book_translations[0]?.description || '',
      plotSummary: bookBasic.book_translations[0]?.plot_summary || '',
      reviewSummary: bookBasic.book_translations[0]?.review_summary || '',
      synopsis: bookBasic.book_translations[0]?.synopsis || '',
      language: languageInfo,
      publisher: publisherData,
      binding: bookBasic.binding || null,
      contentType: bookBasic.content_type || null
    },

    authors: authorData.authors,
    categories: categoryData.categories,
    ratings: ratingStats,
    chapters: chapterData,

    bestQuote: {
      text: bookBasic.book_translations[0]?.best_quote || '' // 假设数据库中没有引用表，使用默认值
    },

    relatedBooks: relatedBooks,
    trendingBooks: trendingBooks,
    audioInfo: audioInfo,

    userInfo: {
      isFavorite: false, // 需要从用户数据中获取
      readingProgress: {
        lastReadAt: new Date().toISOString()
      },
      audioProgress: {
        positionSeconds: 0,
        isCompleted: false,
        lastListenedAt: new Date().toISOString()
      }
    },

    purchaseLinks: {
      amazon: buildOptimizedAmazonUrl(
        bookBasic.book_translations[0]?.title || '',
        authorData.authors[0]?.name
      )
    },

    viewStatistics: {
      viewCount: Number(bookBasic.view_statistics?.view_count || 0),
      lastViewedAt: bookBasic.view_statistics?.last_viewed_at?.toISOString() || new Date().toISOString()
    }
  };

  return result;
}

/**
 * 获取书籍基本信息
 * 返回包含所有必要字段的书籍信息，包括作者、分类、语言、出版商和音频信息
 */
async function getBookBasicInfo(id: number, language: string): Promise<any> {
  // 1. 获取书籍基本信息
  const book = await prisma.books.findUnique({
    where: { id },
    select: {
      id: true,
      isbn: true,
      asin: true,
      isbn13: true,
      issn: true,
      publication_year: true,
      publisher_id: true,
      original_language_id: true,
      is_published: true,
      published_at: true,
      reading_time_minutes: true,
      file_size_pdf: true,
      file_size_epub: true,
      pdf_url: true,
      epub_url: true,
      ipfs_cid: true,
      ipfs_cid_blake2b: true,
      // 添加评分和binding字段
      rate_score: true,
      total_ratings: true,
      binding: true,
      content_type: true,

      book_translations: {
        where: { language_id: language },
        select: {
          id: true,
          title: true,
          subtitle: true,
          description: true,
          plot_summary: true,
          review_summary: true,
          synopsis: true,
          best_quote: true,
          is_default: true
        },
        take: 1
      },

      book_covers: {
        where: { language_id: language },
        select: {
          id: true,
          image_url: true,
          is_primary: true
        }
      },

      book_authors: {
        select: {
          author_id: true,
          author_order: true,
          author: {
            select: {
              id: true,
              avatar_url: true
            }
          }
        },
        orderBy: {
          author_order: 'asc'
        }
      },

      book_categories: {
        select: {
          category_id: true
        }
      },

      audio_files: {
        where: { language_id: language },
        select: {
          id: true,
          file_url: true,
          duration_seconds: true,
          file_size_mb: true
        }
      },

      view_statistics: {
        select: {
          view_count: true,
          last_viewed_at: true
        }
      }
    }
  } as any);

  if (!book) {
    return null;
  }

  // 2. 获取作者信息
  const authorIds = (book as any).book_authors
    .map((ba: any) => ba.author_id)
    .filter((id: any) => id !== null && id !== undefined) as number[];

  const authorTranslations = await prisma.author_translations.findMany({
    where: {
      author_id: { in: authorIds },
      language_id: language
    },
    select: {
      id: true,
      author_id: true,
      name: true,
      biography: true,
    }
  });

  const authors = authorIds.map(id => {
    const translation = authorTranslations.find(at => at.author_id === id);
    const authorData = (book as any).book_authors.find((ba: any) => ba.author_id === id)?.author;
    return {
      id: String(id),
      name: translation?.name || '',
      biography: translation?.biography || '',
      imageUrl: authorData?.avatar_url || ''
    };
  }).filter(author => author.name !== '');

  const authorData = { authorIds, authors };

  // 3. 获取分类信息
  const categoryIds = (book as any).book_categories
    .map((bc: any) => bc.category_id)
    .filter((id: any) => id !== null && id !== undefined) as number[];

  const categoryTranslations = await prisma.category_translations.findMany({
    where: {
      category_id: { in: categoryIds },
      language_id: language
    },
    select: {
      id: true,
      category_id: true,
      name: true
    }
  });

  const categories = categoryIds.map(id => {
    const translation = categoryTranslations.find(ct => ct.category_id === id);
    return {
      id: String(id),
      name: translation?.name || ''
    };
  }).filter(category => category.name !== '');

  const categoryData = { categoryIds, categories };

  // 4. 获取语言信息
  const languageInfo = await getLanguageInfo(language);

  // 5. 获取出版商信息
  const publisherData = await getPublisherInfo(book.publisher_id, language);

  // 6. 获取音频信息
  const audioInfo = await getBookAudioInfo((book as any).audio_files);

  // 7. 返回包含所有信息的结果
  return {
    ...book,
    authorData,
    categoryData,
    languageInfo,
    publisherData,
    audioInfo
  };
}

/**
 * 获取书籍评分统计
 * 优先使用books表中的预计算字段，降级到实时计算
 */
async function getBookRatingStats(bookId: number, bookData?: any) {
  let averageScore = 0;
  let totalCount = 0;

  // 优先使用传入的bookData中的预计算字段
  if (bookData?.rate_score !== null && bookData?.rate_score !== undefined &&
    bookData?.total_ratings !== null && bookData?.total_ratings !== undefined &&
    Number(bookData.total_ratings) > 0) {
    averageScore = Number(bookData.rate_score);
    totalCount = Number(bookData.total_ratings);
  } else {
    // 降级到从books表查询预计算字段
    const book = await prisma.books.findUnique({
      where: { id: bookId },
      select: {
        rate_score: true,
        total_ratings: true
      }
    });

    if (book?.rate_score !== null && book?.rate_score !== undefined &&
      book?.total_ratings !== null && book?.total_ratings !== undefined &&
      Number(book.total_ratings) > 0) {
      averageScore = Number(book.rate_score);
      totalCount = Number(book.total_ratings);
    } else {
      // 最后降级到实时计算
      const stats = await prisma.$queryRaw`
        SELECT
          COALESCE(AVG(score), 0) as average_score,
          COUNT(*) as total_count
        FROM ratings
        WHERE book_id = ${bookId}
      ` as any[];

      averageScore = Number(stats[0]?.average_score || 0);
      totalCount = Number(stats[0]?.total_count || 0);
    }
  }

  // 始终从ratings表获取评分分布（这个数据量不大，实时计算可接受）
  const distributionStats = await prisma.$queryRaw`
    SELECT
      SUM(CASE WHEN score = 1 THEN 1 ELSE 0 END) as rating_1,
      SUM(CASE WHEN score = 2 THEN 1 ELSE 0 END) as rating_2,
      SUM(CASE WHEN score = 3 THEN 1 ELSE 0 END) as rating_3,
      SUM(CASE WHEN score = 4 THEN 1 ELSE 0 END) as rating_4,
      SUM(CASE WHEN score = 5 THEN 1 ELSE 0 END) as rating_5
    FROM ratings
    WHERE book_id = ${bookId}
  ` as any[];

  // 格式化为前端需要的结构
  return {
    averageScore,
    totalCount,
    distribution: {
      "1": Number(distributionStats[0]?.rating_1 || 0),
      "2": Number(distributionStats[0]?.rating_2 || 0),
      "3": Number(distributionStats[0]?.rating_3 || 0),
      "4": Number(distributionStats[0]?.rating_4 || 0),
      "5": Number(distributionStats[0]?.rating_5 || 0)
    }
  };
}

/**
 * 获取书籍章节信息
 */
async function getBookChapters(bookId: number, language: string): Promise<Array<{
  id: string;
  number: number;
  title: string;
  summary: string;
}>> {
  // 从 book_chapters 表获取章节内容
  const bookChapter = await prisma.book_chapters.findFirst({
    where: {
      book_id: bookId,
      language_id: language
    },
    select: {
      content: true
    }
  });

  // 如果找到数据，直接返回 JSON 内容
  if (bookChapter && bookChapter.content) {
    // Prisma 会自动处理 JSON 类型的反序列化，直接返回
    return bookChapter.content as Array<{
      id: string;
      number: number;
      title: string;
      summary: string;
    }>;
  }

  // 如果没有找到数据，返回空数组
  return [];
}

/**
 * 获取语言信息
 */
async function getLanguageInfo(languageCode: string) {
  const language = await prisma.languages.findUnique({
    where: { language_code: languageCode },
    select: {
      language_code: true,
      name: true
    }
  });

  return {
    code: language?.language_code || languageCode,
    name: language?.name || languageCode
  };
}

/**
 * 获取出版商信息
 */
async function getPublisherInfo(publisherId: number | null | undefined, language: string) {
  if (!publisherId) {
    return { id: '', name: '' };
  }

  const publisher = await prisma.publishers.findUnique({
    where: { id: publisherId },
    select: { id: true }
  });

  if (!publisher) {
    return { id: '', name: '' };
  }

  const publisherTranslation = await prisma.publisher_translations.findFirst({
    where: {
      publisher_id: publisherId,
      language_id: language
    },
    select: {
      name: true
    }
  });

  return {
    id: String(publisherId),
    name: publisherTranslation?.name || ''
  };
}

/**
 * 获取书籍音频信息
 */
async function getBookAudioInfo(audioFiles: any[] | undefined) {
  if (!audioFiles || audioFiles.length === 0) {
    return {
      available: false,
      durationSeconds: 0,
      fileUrl: '',
      fileSizeMb: 0
    };
  }

  const audioFile = audioFiles[0];

  return {
    available: true,
    durationSeconds: audioFile.duration_seconds || 0,
    fileUrl: audioFile.file_url || '',
    fileSizeMb: audioFile.file_size_mb || 0
  };
}

/**
 * 获取相关书籍
 */
async function getRelatedBooks(bookId: number, authorIds: number[], categoryIds: number[], language: string, limit: number) {
  if (authorIds.length === 0 && categoryIds.length === 0) {
    return [];
  }

  // 先查询同作者的书籍（通常更相关）
  let relatedBooks: any[] = [];

  if (authorIds.length > 0) {
    const authorBooks = await prisma.books.findMany({
      where: {
        book_authors: {
          some: {
            author_id: { in: authorIds }
          }
        },
        id: { not: bookId },
        is_published: 1
      },
      select: {
        id: true,
        rate_score: true, // 添加预计算评分字段
        book_translations: {
          where: { language_id: language },
          select: {
            title: true,
            subtitle: true // 添加 subtitle 字段
          },
          take: 1
        },
        book_covers: {
          where: {
            language_id: language,
            is_primary: 1
          },
          select: { image_url: true },
          take: 1
        },
        book_authors: {
          include: {
            author: {
              include: {
                author_translations: {
                  where: { language_id: language },
                  select: { name: true },
                  take: 1
                }
              }
            }
          },
          take: 1
        }
      },
      take: limit
    });

    // 直接使用预计算评分，不再实时计算
    relatedBooks = authorBooks.map(formatSimpleBookWithPrecomputedRating);
  }

  return relatedBooks.slice(0, limit);
}

/**
 * 格式化简化版书籍数据（使用预计算评分）
 */
function formatSimpleBookWithPrecomputedRating(book: any) {
  // 获取作者名称
  const authorName = book.book_authors[0]?.author?.author_translations[0]?.name || '';
  const coverUrl = book.book_covers[0]?.image_url || '';

  // 使用预计算评分
  const rating = book.rate_score ? Number(book.rate_score) : 0;

  return {
    id: String(book.id),
    title: book.book_translations[0]?.title || '',
    subtitle: book.book_translations[0]?.subtitle || '', // 添加 subtitle 字段
    coverUrl: coverUrl,
    author: authorName,
    authors: [authorName], // 添加 authors 数组，确保类型兼容
    rating: {
      score: rating,
      count: 0 // 暂时设为0，因为我们主要关注评分分数
    }
  };
}

/**
 * 格式化简化版书籍数据（实时计算评分，保留用于其他地方）
 */
export function formatSimpleBook(book: any) {
  // 计算平均评分
  const ratings = book.ratings || { scores: [], count: 0 };
  const avgRating = ratings.scores.length > 0
    ? ratings.scores.reduce((sum: number, score: number) => sum + score, 0) / ratings.scores.length
    : 0;

  // 获取作者名称
  const authorName = book.book_authors[0]?.author?.author_translations[0]?.name || '';

  const coverUrl = book.book_covers[0]?.image_url || '';

  return {
    id: String(book.id),
    title: book.book_translations[0]?.title || '',
    coverUrl: coverUrl,
    author: authorName,
    authors: [authorName], // 添加 authors 数组，确保类型兼容
    rating: {
      score: avgRating,
      count: ratings.count
    }
  };
}

/**
 * 通用书籍获取函数 - 根据条件获取书籍列表
 * @param options 查询选项
 * @returns 格式化后的书籍列表
 */
async function getBooksByCondition(options: {
  where?: BookWhereInput;                          // 查询条件
  orderBy: BookOrderByWithRelationInput;           // 排序条件
  limit: number;                                   // 结果数量限制
  language: string;                                // 语言
}): Promise<BookListItem[]> {
  const { where = { is_published: 1 }, orderBy, limit, language } = options;

  // 查询书籍列表
  const books = await prisma.books.findMany({
    where,
    select: {
      id: true,
      publication_year: true,
      rate_score: true,
      total_ratings: true,
      book_translations: {
        where: {
          language_id: language
        },
        select: {
          title: true,
          subtitle: true,
          description: true
        },
        take: 1
      },
      book_covers: {
        where: {
          language_id: language,
          is_primary: 1
        },
        select: {
          image_url: true
        },
        take: 1
      },
      book_authors: {
        include: {
          author: true
        },
        orderBy: {
          author_order: 'asc'
        }
      },
      book_categories: {
        include: {
          category: true
        }
      },
      view_statistics: {
        select: {
          view_count: true
        }
      }
    },
    orderBy,
    take: limit
  } as any);

  // 收集所有需要的作者ID
  const allAuthorIds: number[] = [];
  books.forEach((book: any) => {
    const authorIds = book.book_authors
      .map((ba: any) => ba.author_id)
      .filter((id: any): id is number => id !== null && id !== undefined);
    allAuthorIds.push(...authorIds);
  });

  // 去重
  const uniqueAuthorIds = [...new Set(allAuthorIds)];

  // 批量获取所有作者翻译
  const authorTranslations = await prisma.author_translations.findMany({
    where: {
      author_id: { in: uniqueAuthorIds },
      language_id: language
    }
  }) as AuthorTranslation[];

  // 创建查找映射以便快速访问
  const authorTranslationMap = new Map<number, string>();
  authorTranslations.forEach(at => {
    authorTranslationMap.set(at.author_id!, at.name);
  });

  // 转换为前端需要的格式
  return books.map((book: any) => {
    // 获取当前书籍的作者名称
    const authors = book.book_authors
      .map((ba: any) => ba.author_id)
      .filter((id: any): id is number => id !== null && id !== undefined)
      .map((id: any) => authorTranslationMap.get(id) || '')
      .filter((name: any) => name !== '');

    // 直接使用数据库中的预计算评分
    const rating = book.rate_score ? Number(book.rate_score) : 0;

    return {
      id: book.id,
      title: book.book_translations[0]?.title || '',
      subtitle: book.book_translations[0]?.subtitle || null,
      description: book.book_translations[0]?.description || null,
      coverUrl: book.book_covers[0]?.image_url || undefined,
      authors,
      categories: [], // 添加空的分类数组，确保类型兼容
      publicationYear: book.publication_year,
      rating: rating,
      viewCount: book.view_statistics?.view_count?.toString() || '0'
    } as BookListItem;
  });
}

/**
 * 获取热门书籍
 */
export async function getPopularBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return getBooksByCondition({
    where: {
      is_published: 1,
      view_statistics: {
        isNot: null
      }
    },
    orderBy: {
      view_statistics: {
        view_count: 'desc'
      }
    },
    limit,
    language
  });
}

/**
 * 获取最新书籍
 */
export async function getNewestBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return getBooksByCondition({
    where: {
      is_published: 1
    },
    orderBy: {
      created_at: 'desc'
    },
    limit,
    language
  });
}

/**
 * 获取月度精选书籍（前10本书籍，按ID排序）
 */
export async function getMonthlyPickedBooks(limit: number = 10, language: string = 'en'): Promise<BookListItem[]> {
  return getBooksByCondition({
    where: {
      is_published: 1
    },
    orderBy: {
      id: 'asc'
    },
    limit,
    language
  });
}

/**
 * 增加书籍浏览量
 * 使用 upsert 操作避免并发竞态条件
 */
export async function incrementBookViewCount(bookId: number): Promise<void> {
  try {
    await prisma.view_statistics.upsert({
      where: { book_id: bookId },
      update: {
        view_count: {
          increment: 1
        },
        last_viewed_at: new Date()
      },
      create: {
        book_id: bookId,
        view_count: 1,
        last_viewed_at: new Date()
      }
    });
  } catch (error) {
    console.error(`Failed to increment view count for book ${bookId}:`, error);
    // 不抛出错误，避免影响用户体验
    // 访问量统计失败不应该阻止页面正常显示
  }
}
