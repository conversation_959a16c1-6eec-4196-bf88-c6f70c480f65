'server-only'

/**
 * 评分模型层
 * 负责与数据库交互，提供基础的评分相关 CRUD 操作
 */

import { prisma } from '@prisma/prisma'
import { Decimal } from '@prisma/client/runtime/library'

/**
 * 评分数据类型定义
 */
export interface RatingData {
  id: number
  book_id: number
  user_id: bigint
  score: Decimal
  review_text?: string | null
  created_at: Date
  updated_at: Date
}

/**
 * 用户评分结果类型
 */
export interface UserRatingResult {
  id: number
  score: number
  review_text?: string | null
  created_at: Date
  updated_at: Date
}

/**
 * 创建或更新评分的结果类型
 */
export interface CreateOrUpdateRatingResult {
  rating: UserRatingResult
  isNewRating: boolean
}

/**
 * 书籍评分统计类型
 */
export interface BookRatingStats {
  average_score: number
  total_ratings: number
  rating_distribution: {
    [key: number]: number // 1-5星的分布数量
  }
}

/**
 * 获取用户对特定书籍的评分
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @returns 用户评分数据或null（如果未评分）
 */
export async function getUserRating(
  userId: number,
  bookId: number
): Promise<UserRatingResult | null> {
  try {
    const rating = await prisma.ratings.findFirst({
      where: {
        book_id: bookId,
        user_id: BigInt(userId)
      },
      select: {
        id: true,
        score: true,
        review_text: true,
        created_at: true,
        updated_at: true
      }
    })

    if (!rating) {
      return null
    }

    return {
      id: rating.id,
      score: Number(rating.score),
      review_text: rating.review_text,
      created_at: rating.created_at!,
      updated_at: rating.updated_at!
    }
  } catch (error) {
    console.error('Error fetching user rating:', error)
    throw new Error('Failed to fetch user rating')
  }
}

/**
 * 创建或更新用户评分
 * @param userId 用户ID
 * @param bookId 书籍ID
 * @param score 评分（1-5）
 * @param reviewText 评论内容（可选）
 * @returns 创建或更新后的评分数据和是否为新评分的标识
 */
export async function createOrUpdateRating(
  userId: number,
  bookId: number,
  score: number,
  reviewText?: string
): Promise<CreateOrUpdateRatingResult> {
  // 验证评分范围
  if (score < 1 || score > 5) {
    throw new Error('Rating score must be between 1 and 5')
  }

  try {
    // 先查找是否存在现有评分
    const existingRating = await prisma.ratings.findFirst({
      where: {
        book_id: bookId,
        user_id: BigInt(userId)
      }
    })

    let rating
    const isNewRating = !existingRating

    if (existingRating) {
      // 更新现有评分
      rating = await prisma.ratings.update({
        where: {
          id: existingRating.id
        },
        data: {
          score: new Decimal(score),
          review_text: reviewText || null,
          updated_at: new Date()
        },
        select: {
          id: true,
          score: true,
          review_text: true,
          created_at: true,
          updated_at: true
        }
      })
    } else {
      // 创建新评分
      rating = await prisma.ratings.create({
        data: {
          book_id: bookId,
          user_id: BigInt(userId),
          score: new Decimal(score),
          review_text: reviewText || null
        },
        select: {
          id: true,
          score: true,
          review_text: true,
          created_at: true,
          updated_at: true
        }
      })
    }

    return {
      rating: {
        id: rating.id,
        score: Number(rating.score),
        review_text: rating.review_text,
        created_at: rating.created_at!,
        updated_at: rating.updated_at!
      },
      isNewRating
    }
  } catch (error) {
    console.error('Error creating/updating rating:', error)
    throw new Error('Failed to create or update rating')
  }
}

/**
 * 更新书籍的评分统计信息
 * 只在新评分时增加 total_ratings 计数，不更新 rate_score 字段
 * @param bookId 书籍ID
 * @param isNewRating 是否为新评分（只有新评分时才增加计数）
 * @returns 更新后的统计信息
 */
export async function updateBookRatingStats(bookId: number, isNewRating: boolean = false): Promise<BookRatingStats> {
  try {
    // 1. 只在新评分时增加 total_ratings 计数
    if (isNewRating) {
      await prisma.books.update({
        where: {
          id: bookId
        },
        data: {
          total_ratings: { increment: 1 }
          // 不更新 rate_score，保持原始评分不变
        }
      })
    }

    // 2. 获取当前书籍的统计信息用于返回
    const book = await prisma.books.findUnique({
      where: {
        id: bookId
      },
      select: {
        rate_score: true,
        total_ratings: true
      }
    })

    if (!book) {
      throw new Error('Book not found')
    }

    // 3. 获取用户评分分布（实时计算）
    const ratings = await prisma.ratings.findMany({
      where: {
        book_id: bookId
      },
      select: {
        score: true
      }
    })

    const ratingDistribution: { [key: number]: number } = {
      1: 0, 2: 0, 3: 0, 4: 0, 5: 0
    }

    ratings.forEach(rating => {
      const score = Math.round(Number(rating.score))
      if (score >= 1 && score <= 5) {
        ratingDistribution[score]++
      }
    })

    return {
      average_score: book.rate_score ? Number(book.rate_score) : 0,
      total_ratings: book.total_ratings || 0,
      rating_distribution: ratingDistribution
    }
  } catch (error) {
    console.error('Error updating book rating stats:', error)
    throw new Error('Failed to update book rating statistics')
  }
}

/**
 * 获取书籍的评分统计信息
 * @param bookId 书籍ID
 * @returns 书籍评分统计
 */
export async function getBookRatingStats(bookId: number): Promise<BookRatingStats> {
  try {
    // 1. 从 books 表获取缓存的统计信息
    const book = await prisma.books.findUnique({
      where: {
        id: bookId
      },
      select: {
        rate_score: true,
        total_ratings: true
      }
    })

    if (!book) {
      throw new Error('Book not found')
    }

    // 2. 获取评分分布（实时计算）
    const ratings = await prisma.ratings.findMany({
      where: {
        book_id: bookId
      },
      select: {
        score: true
      }
    })

    const ratingDistribution: { [key: number]: number } = {
      1: 0, 2: 0, 3: 0, 4: 0, 5: 0
    }

    ratings.forEach(rating => {
      const score = Math.round(Number(rating.score))
      if (score >= 1 && score <= 5) {
        ratingDistribution[score]++
      }
    })

    return {
      average_score: book.rate_score ? Number(book.rate_score) : 0,
      total_ratings: book.total_ratings || 0,
      rating_distribution: ratingDistribution
    }
  } catch (error) {
    console.error('Error fetching book rating stats:', error)
    throw new Error('Failed to fetch book rating statistics')
  }
}



/**
 * 批量获取用户对多本书籍的评分
 * @param userId 用户ID
 * @param bookIds 书籍ID数组
 * @returns 用户评分映射 { bookId: UserRatingResult }
 */
export async function getUserRatingsForBooks(
  userId: number,
  bookIds: number[]
): Promise<Record<number, UserRatingResult>> {
  try {
    const ratings = await prisma.ratings.findMany({
      where: {
        user_id: BigInt(userId),
        book_id: {
          in: bookIds
        }
      },
      select: {
        id: true,
        book_id: true,
        score: true,
        review_text: true,
        created_at: true,
        updated_at: true
      }
    })

    const result: Record<number, UserRatingResult> = {}

    ratings.forEach(rating => {
      result[rating.book_id!] = {
        id: rating.id,
        score: Number(rating.score),
        review_text: rating.review_text,
        created_at: rating.created_at!,
        updated_at: rating.updated_at!
      }
    })

    return result
  } catch (error) {
    console.error('Error fetching user ratings for books:', error)
    throw new Error('Failed to fetch user ratings for books')
  }
}
