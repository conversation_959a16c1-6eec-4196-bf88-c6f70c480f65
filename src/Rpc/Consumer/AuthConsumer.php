<?php

declare(strict_types=1);

namespace Website\Common\Rpc\Consumer;

use Carbon\Carbon;
use Hyperf\Redis\Redis;
use Website\Common\Constants\ErrorCode;
use Website\Common\Exception\UserException;
use Website\Common\FrontendApi\Auth\Guard\SsoGuard;
use Website\Common\Model\Rank;
use Website\Common\Model\Team;
use Website\Common\Model\TeamMember;
use Website\Common\Model\User;
use Website\Common\Service\UserService;
use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Utils\ApplicationContext;
use Lete\Base\Abstraction\ConsumerMessage;
use PhpAmqpLib\Message\AMQPMessage;
use Qbhy\HyperfAuth\AuthManager;
use Qbhy\SimpleJwt\Exceptions\TokenBlacklistException;
use Qbhy\SimpleJwt\Exceptions\TokenExpiredException;
use Qbhy\SimpleJwt\Exceptions\TokenNotActiveException;
use Qbhy\SimpleJwt\JWT;

/**
 * @Consumer(name="AuthConsumer", nums=10)
 */
class AuthConsumer extends ConsumerMessage
{
    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-frontend-rpc-auth';
        $this->queue = env('APP_NAME') . '-frontend-rpc-auth';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     * @throws TokenExpiredException
     * @throws \Qbhy\SimpleJwt\Exceptions\InvalidTokenException
     * @throws \Qbhy\SimpleJwt\Exceptions\SignatureException
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            $AuthManager = ApplicationContext::getContainer()->get(AuthManager::class);
            $AuthGuard = $AuthManager->guard('frontend-api-sso');
            /* @var SsoGuard $AuthGuard */
            $AuthGuard->setRequester($data);
            $AuthGuard->simulateSessionStart();
            /* @var User $User */
            $User = $AuthGuard->user();
            if (in_array($User->account_status, [User::ACCOUNT_STATUS_DISABLED, User::ACCOUNT_STATUS_DELETED])) {
                throw new UserException(ErrorCode::USER_ACCOUNT_DISABLED);
            }

            // 更改用户语言
            if ($data['language'] && $User->language !== $data['language']) {
                $User->update([
                    'language' => $data['language'],
                ]);
            }

            // 用户的套餐
            $Rank = Rank::findFromCache($User->rank_id);

            // 团队信息
            $team = null;
            if (config('website.team') && $data['team_id']) {
                $Redis = ApplicationContext::getContainer()->get(Redis::class);
                $redis_key = env('APP_NAME') . ":open-api:team-member:{$data['team_id']}-{$User->id}";
                $redis_result = $Redis->get($redis_key);
                if ($redis_result !== false) {
                    $team = $redis_result ? json_decode($redis_result, true) : null;
                } else {
                    $TeamMember = TeamMember::query()
                        ->with('team:id,name,user_id')
                        ->with('role:id,name,permission')
                        ->where('team_id', $data['team_id'])
                        ->where('user_id', $User->id)
                        ->first(['id', 'team_id', 'user_id', 'role_id', 'status']);
                    if ($TeamMember) {
                        // 团队成员
                        $team = [
                            'id' => $TeamMember->team->id,
                            'name' => $TeamMember->team->name,
                            'user_id' => $TeamMember->team->user_id,
                            'rank_name' => $TeamMember->team->owner->rank->rank_name,
                            'permission' => $TeamMember->team->owner->rank->permission,
                            'member' => [
                                'user_id' => $TeamMember->user_id,
                                'status' => $TeamMember->status,
                                'role' => [
                                    'id' => $TeamMember->role->id,
                                    'name' => $TeamMember->role->name,
                                    'permission' => $TeamMember->role->permission,
                                ],
                            ],
                        ];
                    } else {
                        // 团队所有者
                        $Team = Team::query()
                            ->where('id', $data['team_id'])
                            ->where('user_id', $User->id)
                            ->first(['id', 'name', 'user_id']);
                        if ($Team) {
                            $team = [
                                'id' => $Team->id,
                                'name' => $Team->name,
                                'user_id' => $Team->user_id,
                                'rank_name' => $Rank->rank_name,
                                'permission' => $Rank->permission,
                                'member' => [
                                    'user_id' => $Team->user_id,
                                    'status' => TeamMember::STATUS_ACTIVATED,
                                    'role' => [
                                        'id' => 0,
                                        'name' => 'Owner',
                                        'permission' => array_map(function ($permission_value) {
                                            return is_bool($permission_value) ? true : $permission_value;
                                        }, config('website.team.role.permission_structure', [])),
                                    ],
                                ],
                            ];
                        }
                    }

                    if ($team) {
                        $team['last_rank_name'] = UserService::getLastRankNameFromCache($team['user_id']);
                    }

                    // 缓存结果
                    $Redis->set($redis_key, $team ? json_encode($team) : $team, 10);
                }
            }

            $user = $User->toArray();
            $rank = $Rank->toArray();
            foreach (['created_at', 'updated_at'] as $field) {
                $user[$field] && $user[$field] = Carbon::createFromTimeString($user[$field])->timestamp;
                $rank[$field] && $rank[$field] = Carbon::createFromTimeString($rank[$field])->timestamp;
            }

            $user['last_rank_name'] = UserService::getLastRankNameFromCache($User->id);

            $result_data = [
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'user' => $user,
                    'rank' => $rank,
                    'team' => $team,
                ],
            ];
        } catch (\Throwable $e) {
            // 自动登录失败逻辑
            if ($e->getPrevious() instanceof TokenBlacklistException ||
                $e->getPrevious() instanceof TokenExpiredException ||
                $e->getPrevious() instanceof TokenNotActiveException ||
                $e instanceof UserException
            ) {
                /* @var JWT $JWT */
                $JWT = $AuthGuard->getJwtManager()->justParse($data['token']);
                $AuthGuard->markLogin($JWT->getPayload()['uid'], UserService::LOGIN_MODE_AUTOMATIC, false, $e->getMessage());
                $AuthGuard->deletedToken();
            }

            $result_data = [
                'code' => $e->getCode() ?: 400,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }
        $result_data['data']['session_id'] = $AuthGuard ? $AuthGuard->getSessionId() : null;
        $this->reply($result_data, $message);
        return Result::ACK;
    }
}
