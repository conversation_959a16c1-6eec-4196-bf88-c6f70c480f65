<?php

declare(strict_types=1);

namespace Website\Common\Rpc\Consumer;

use Website\Common\Service\UserService;
use Hyperf\Amqp\Result;
use Hyperf\Amqp\Annotation\Consumer;
use Hyperf\Utils\ApplicationContext;
use Lete\Base\Abstraction\ConsumerMessage;
use PhpAmqpLib\Message\AMQPMessage;

/**
 * @Consumer(name="QuotaConsumer", nums=10)
 */
class QuotaConsumer extends ConsumerMessage
{
    public function __construct()
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = env('APP_NAME') . '-frontend-rpc-quota';
        $this->queue = env('APP_NAME') . '-frontend-rpc-quota';
    }

    /**
     * @param $data
     * @param AMQPMessage $message
     * @return string
     * @throws TokenExpiredException
     * @throws \Qbhy\SimpleJwt\Exceptions\InvalidTokenException
     * @throws \Qbhy\SimpleJwt\Exceptions\SignatureException
     */
    public function consumeMessage($data, AMQPMessage $message): string
    {
        try {
            $result = UserService::incrPermissionUsed($data['user_id'], $data['permission_name'], $data['used'], $data['timezone'] ?? null);
            $result_data = [
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'result' => $result,
                ],
            ];
        } catch (\Throwable $e) {
            $result_data = [
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => [
                    'result' => false,
                ],
            ];
        }
        $this->reply($result_data, $message);
        return Result::ACK;
    }
}
