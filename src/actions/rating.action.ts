'use server'

/**
 * 评分相关的 Server Actions
 * 提供客户端组件调用的服务端函数，集成认证和缓存管理
 */

import * as ratingService from '@/services/rating.service'
import { withAuth, withOptionalAuth, getCurrentAuthUser } from '@/lib/auth/withAuth'
import { CacheManager } from '@/lib/cache-manager'

/**
 * 安全的日期序列化函数
 * 处理 Date 对象、字符串日期和 null/undefined 情况
 * @param date 日期值
 * @returns ISO字符串
 */
function safeToISOString(date: Date | string | null | undefined): string {
  if (!date) return new Date().toISOString()
  if (date instanceof Date) return date.toISOString()
  try {
    return new Date(date).toISOString()
  } catch (error) {
    console.warn('Invalid date format, using current date:', date)
    return new Date().toISOString()
  }
}

/**
 * Server Action 返回的通用结果类型
 */
export interface ActionResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * 用户评分数据类型（客户端使用）
 */
export interface UserRatingData {
  id: number
  score: number
  review_text?: string | null
  created_at: string // 序列化为字符串
  updated_at: string // 序列化为字符串
}

/**
 * 书籍评分统计数据类型（客户端使用）
 */
export interface BookRatingStatsData {
  average_score: number
  total_ratings: number
  rating_distribution: {
    [key: number]: number
  }
}

/**
 * 获取用户对书籍的评分（需要认证）
 * @param bookId 书籍ID
 * @returns 用户评分数据或null
 */
export const getUserRating = withAuth(async (bookId: number): Promise<ActionResult<UserRatingData | null>> => {
  try {
    // 获取当前认证用户
    const { userId } = getCurrentAuthUser()

    // 验证参数
    if (!bookId || bookId <= 0) {
      return {
        success: false,
        error: 'Invalid book ID'
      }
    }

    // 调用服务层获取评分
    const rating = await ratingService.getUserBookRating(userId, bookId)

    if (!rating) {
      return {
        success: true,
        data: null
      }
    }

    // 序列化日期字段 - 使用安全的日期处理
    const serializedRating: UserRatingData = {
      id: rating.id,
      score: rating.score,
      review_text: rating.review_text,
      created_at: safeToISOString(rating.created_at),
      updated_at: safeToISOString(rating.updated_at)
    }

    return {
      success: true,
      data: serializedRating
    }
  } catch (error) {
    console.error('Action: Error getting user rating:', error)
    return {
      success: false,
      error: 'Failed to get user rating'
    }
  }
})

/**
 * 获取书籍评分统计（无需认证）
 * @param bookId 书籍ID
 * @returns 书籍评分统计
 */
export const getBookRatingStats = withOptionalAuth(async (bookId: number): Promise<ActionResult<BookRatingStatsData>> => {
  try {
    // 验证参数
    if (!bookId || bookId <= 0) {
      return {
        success: false,
        error: 'Invalid book ID'
      }
    }

    // 调用服务层获取统计
    const stats = await ratingService.getBookRatingStatistics(bookId)

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Action: Error getting book rating stats:', error)
    return {
      success: false,
      error: 'Failed to get book rating statistics'
    }
  }
})

/**
 * 提交用户评分（需要认证）
 * @param bookId 书籍ID
 * @param score 评分（1-5）
 * @param reviewText 评论内容（可选）
 * @returns 操作结果
 */
export const submitRating = withAuth(async (
  bookId: number,
  score: number,
  reviewText?: string
): Promise<ActionResult<{
  rating: UserRatingData
  stats: BookRatingStatsData
}>> => {
  try {
    // 获取当前认证用户
    const { userId } = getCurrentAuthUser()

    // 验证参数
    if (!bookId || bookId <= 0) {
      return {
        success: false,
        error: 'Invalid book ID'
      }
    }

    if (!score || score < 1 || score > 5) {
      return {
        success: false,
        error: 'Rating score must be between 1 and 5'
      }
    }

    // 调用服务层提交评分
    const result = await ratingService.submitRating(userId, bookId, score, reviewText)

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'Failed to submit rating'
      }
    }

    // 缓存失效处理
    await invalidateRatingCaches(userId, bookId)

    // 序列化返回数据 - 使用安全的日期处理
    const serializedRating: UserRatingData = {
      id: result.data!.id,
      score: result.data!.score,
      review_text: result.data!.review_text,
      created_at: safeToISOString(result.data!.created_at),
      updated_at: safeToISOString(result.data!.updated_at)
    }

    return {
      success: true,
      data: {
        rating: serializedRating,
        stats: result.stats!
      },
      message: 'Rating submitted successfully'
    }
  } catch (error) {
    console.error('Action: Error submitting rating:', error)
    return {
      success: false,
      error: 'Failed to submit rating'
    }
  }
})



/**
 * 检查用户是否已对书籍评分（需要认证）
 * @param bookId 书籍ID
 * @returns 是否已评分
 */
export const hasUserRatedBook = withAuth(async (bookId: number): Promise<ActionResult<boolean>> => {
  try {
    // 获取当前认证用户
    const { userId } = getCurrentAuthUser()

    // 验证参数
    if (!bookId || bookId <= 0) {
      return {
        success: false,
        error: 'Invalid book ID'
      }
    }

    // 调用服务层检查评分状态
    const hasRated = await ratingService.hasUserRatedBook(userId, bookId)

    return {
      success: true,
      data: hasRated
    }
  } catch (error) {
    console.error('Action: Error checking user rating status:', error)
    return {
      success: false,
      error: 'Failed to check rating status'
    }
  }
})

/**
 * 批量获取书籍的平均评分和统计信息（无需认证）
 * 用于书籍列表、搜索结果等场景，获取书籍本身的评分数据
 * @param bookIds 书籍ID数组
 * @returns 书籍评分统计映射
 */
export const getBooksRatingStats = withOptionalAuth(async (
  bookIds: number[]
): Promise<ActionResult<Record<number, BookRatingStatsData>>> => {
  try {
    // 验证参数
    if (!bookIds || bookIds.length === 0) {
      return {
        success: true,
        data: {}
      }
    }

    // 验证所有书籍ID
    const validBookIds = bookIds.filter(id => id && id > 0)
    if (validBookIds.length === 0) {
      return {
        success: false,
        error: 'No valid book IDs provided'
      }
    }

    // 批量获取书籍评分统计
    const statsPromises = validBookIds.map(bookId =>
      ratingService.getBookRatingStatistics(bookId)
    )
    const statsResults = await Promise.all(statsPromises)

    // 构建返回数据
    const booksStats: Record<number, BookRatingStatsData> = {}
    validBookIds.forEach((bookId, index) => {
      booksStats[bookId] = statsResults[index]
    })

    return {
      success: true,
      data: booksStats
    }
  } catch (error) {
    console.error('Action: Error getting books rating stats:', error)
    return {
      success: false,
      error: 'Failed to get books rating statistics'
    }
  }
})

/**
 * 批量获取用户对多本书籍的个人评分（需要认证）
 * 仅在需要显示用户个人评分的场景使用，如用户图书馆的评分页面
 * @param bookIds 书籍ID数组
 * @returns 用户个人评分映射
 */
export const getUserPersonalRatings = withAuth(async (
  bookIds: number[]
): Promise<ActionResult<Record<number, UserRatingData>>> => {
  try {
    // 获取当前认证用户
    const { userId } = getCurrentAuthUser()

    // 验证参数
    if (!bookIds || bookIds.length === 0) {
      return {
        success: true,
        data: {}
      }
    }

    // 验证所有书籍ID
    const validBookIds = bookIds.filter(id => id && id > 0)
    if (validBookIds.length === 0) {
      return {
        success: false,
        error: 'No valid book IDs provided'
      }
    }

    // 调用服务层获取用户个人评分
    const ratings = await ratingService.getUserRatingsForBooks(userId, validBookIds)

    // 序列化日期字段 - 使用安全的日期处理
    const serializedRatings: Record<number, UserRatingData> = {}
    Object.entries(ratings).forEach(([bookId, rating]) => {
      serializedRatings[Number(bookId)] = {
        id: rating.id,
        score: rating.score,
        review_text: rating.review_text,
        created_at: safeToISOString(rating.created_at),
        updated_at: safeToISOString(rating.updated_at)
      }
    })

    return {
      success: true,
      data: serializedRatings
    }
  } catch (error) {
    console.error('Action: Error getting user personal ratings:', error)
    return {
      success: false,
      error: 'Failed to get user personal ratings'
    }
  }
})

/**
 * 缓存失效处理函数
 * 在评分操作后失效相关缓存
 * @param userId 用户ID
 * @param bookId 书籍ID
 */
async function invalidateRatingCaches(userId: number, bookId: number): Promise<void> {
  try {
    // 使用统一的缓存管理器进行缓存失效
    CacheManager.rating.revalidateAfterRating(userId, bookId)
  } catch (error) {
    console.error('Error invalidating rating caches:', error)
    // 缓存失效失败不应该影响主要操作
  }
}
