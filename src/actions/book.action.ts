'use server'

import { getBookById } from '@/services/book.service';
import * as BookModel from '@/models/book.model';
import { CacheManager } from '@/lib/cache-manager';
import BookDetailData from '@/app/[locale]/(book)/book-summary/[slug]/types';

/**
 * 获取书籍详情数据（带缓存）
 * 这个函数只负责获取数据，不更新访问量
 */
export async function getBookData(id: number, language: string = 'en'): Promise<BookDetailData | null> {
  return getBookById(id, language);
}

/**
 * 更新书籍访问量（不缓存）
 * 这个函数只负责更新访问量，并重新验证相关缓存
 */
export async function incrementBookView(id: number, language: string = 'en'): Promise<boolean> {
  try {
    // 1. 更新访问量
    await BookModel.incrementBookViewCount(id);

    // 2. 重新验证相关缓存
    CacheManager.book.revalidatePopular(language);

    return true;
  } catch (error) {
    console.error('Error incrementing book view count:', error);
    return false;
  }
}

/**
 * 组合函数：获取书籍详情并增加浏览量
 * 这个函数用于页面组件中，组合了上面两个独立的操作
 */
export async function getBookAndIncrementView(id: number, language: string = 'en'): Promise<{
  success: boolean;
  data: BookDetailData | null;
  error?: string;
}> {
  try {
    // 1. 获取书籍数据（带缓存）
    const bookData = await getBookData(id, language);

    // 2. 如果找到书籍，增加浏览量（不缓存）
    if (bookData) {
      await incrementBookView(id, language);
    }

    return {
      success: true,
      data: bookData
    };
  } catch (error) {
    console.error('Error in getBookAndIncrementView:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    };
  }
}
