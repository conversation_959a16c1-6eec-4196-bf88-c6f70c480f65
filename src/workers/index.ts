/** 启动消费者的入口 */
import { Consumer, ProcessStatus } from '../lib/rabbitmq/consumer'
import { createQueueService } from '../services/server/queueService'
import { startUppercaseWorker } from './uppercaseWorker'

// 定义消息类型接口
interface OrderMessage {
  id: string
  product: string
  quantity: number
  customer: string
  createdAt: string
}

interface NotificationMessage {
  type: 'email' | 'sms' | 'push'
  recipient: string
  subject?: string
  content: string
}

// 处理订单消息
async function processOrderMessages(): Promise<void> {
  const orderConsumer = new Consumer<OrderMessage>('orders')

  await orderConsumer.consume(async (orderData) => {
    try {
      console.log(`处理订单 ${orderData.id} 客户: ${orderData.customer}`)

      // 订单处理逻辑示例
      // await processOrder(orderData);

      // 模拟处理时间
      await new Promise((resolve) => setTimeout(resolve, 500))

      console.log(`订单 ${orderData.id} 处理成功`)
      return ProcessStatus.SUCCESS
    } catch (error) {
      console.error(`处理订单 ${orderData.id} 失败:`, error)
      return ProcessStatus.FAILURE
    }
  })
}

// 处理通知消息
async function processNotifications(): Promise<void> {
  const notificationService = createQueueService<NotificationMessage>('notifications')

  await notificationService.processMessages(async (notification: NotificationMessage) => {
    try {
      console.log(`发送 ${notification.type} 通知到 ${notification.recipient}`)

      // 根据类型发送通知的示例
      switch (notification.type) {
        case 'email':
          // await sendEmail(notification.recipient, notification.subject!, notification.content);
          break
        case 'sms':
          // await sendSMS(notification.recipient, notification.content);
          break
        case 'push':
          // await sendPushNotification(notification.recipient, notification.content);
          break
      }

      console.log(`通知已发送到 ${notification.recipient}`)
      return ProcessStatus.SUCCESS
    } catch (error) {
      console.error(`发送通知失败:`, error)
      // 重试通知发送
      return ProcessStatus.RETRY
    }
  })
}

// 启动所有工作进程
export async function startWorkers(): Promise<void> {
  console.log('启动 RabbitMQ 工作进程...')

  // 启动订单处理
  processOrderMessages().catch((error) => console.error('订单消费者错误:', error))

  // 启动通知处理
  processNotifications().catch((error) => console.error('通知消费者错误:', error))

  // 启动大写转换处理
  startUppercaseWorker().catch((error) => console.error('大写转换消费者错误:', error))

  console.log('所有工作进程已启动!')
}

// 只有当直接执行此文件时才启动工作进程
if (require.main === module) {
  startWorkers().catch((error) => {
    console.error('启动工作进程失败:', error)
    process.exit(1)
  })
}
