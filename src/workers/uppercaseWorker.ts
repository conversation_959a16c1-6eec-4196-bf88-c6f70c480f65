import { Consumer, ProcessStatus } from '../lib/rabbitmq/consumer'
import Task from '../models/Task'

// 定义大写转换消息类型
interface UppercaseMessage {
  taskId: string
  text: string
}

// 处理大写转换消息的工作进程
export async function startUppercaseWorker(): Promise<void> {
  console.log('启动大写转换工作进程...')

  const consumer = new Consumer<UppercaseMessage>('uppercase-tasks')

  await consumer.consume(async (message) => {
    try {
      console.log(`处理大写转换任务: ${message.taskId}`)
      console.log(`原始文本: "${message.text}"`)

      // 查找任务
      const task = await Task.findOne({ taskId: message.taskId })

      if (!task) {
        console.error(`找不到任务ID: ${message.taskId}`)
        return ProcessStatus.FAILURE
      }

      // 模拟处理时间 (2-8秒)
      const processingTime = Math.floor(Math.random() * 6000) + 2000
      console.log(`任务将处理 ${processingTime / 1000} 秒`)

      await new Promise((resolve) => setTimeout(resolve, processingTime))

      // 执行转换
      const result = message.text.toUpperCase()
      console.log(`转换结果: "${result}"`)

      // 更新数据库中的任务状态
      task.result = result
      task.status = 'completed'
      task.completedAt = new Date()
      await task.save()

      console.log(`任务 ${message.taskId} 处理完成，已更新数据库`)

      return ProcessStatus.SUCCESS
    } catch (error) {
      console.error(`处理大写转换任务失败:`, error)
      return ProcessStatus.FAILURE
    }
  })

  console.log('大写转换工作进程已启动，等待消息...')
}

// 如果直接执行此文件，启动工作进程
if (require.main === module) {
  startUppercaseWorker().catch((error) => {
    console.error('启动大写转换工作进程失败:', error)
    process.exit(1)
  })
}
