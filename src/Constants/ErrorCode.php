<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace Website\Common\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

/**
 * @Constants
 */
class ErrorCode extends AbstractConstants
{
    /**
     * @Message("Server Error！")
     */
    public const SERVER_ERROR = 500;

    /**
     * 账号已禁用
     * @Message("Your account is temporarily unavailable, please contact us.")
     */
    public const USER_ACCOUNT_DISABLED = 401001;

    /**
     * 邮箱已注册
     * @Message("The email has already been taken.")
     */
    public const USER_EMAIL_EXISTS = 400001;

    /**
     * 邮箱账号未激活
     * @Message("The email account is not activated.")
     */
    public const USER_EMAIL_IS_NOT_ACTIVATED = 400002;

    /**
     * 邮箱账号已激活
     * @Message("The email account is activated.")
     */
    public const USER_EMAIL_IS_ACTIVATED = 400003;

    /**
     * 邮箱未注册
     * @Message("The email account not registered.")
     */
    public const USER_EMAIL_NOT_REGISTERED = 400004;

    /**
     * 账号密码错误
     * @Message("The password does not match.")
     */
    public const USER_PASSWORD_NOT_MATCH = 400005;

    /**
     * 重置密码-验证码不正确
     * @Message("Invalid code.")
     */
    public const RESET_PASSWORD_INVALID_CODE = 400006;

    /**
     * 账号激活-验证码不正确
     * @Message("Invalid activate code.")
     */
    public const ACCOUNT_ACTIVATE_CODE_INVALID = 400007;

    /**
     * 重置密码-账号未设置密码
     * @Message("Account password not set.")
     */
    public const RESET_PASSWORD_EMPTY_PASSWORD = 400009;

    /**
     * 请求过快
     * @Message("request too fast")
     */
    public const REQUEST_TOO_FAST = 400010;

    /**
     * 发送邮件-操作频繁
     * @Message("Frequent operations.")
     */
    public const SEND_EMAIL_FREQUENT = 400014;

    /**
     * 订阅-购买的套餐和现在订阅的套餐相同，不能购买
     * @Message("You need to unsubscribe before purchasing.")
     */
    public const SUBSCRIPTION_SAME_PLAN = 400015;

    /**
     * 新建分销-分销码判断
     * @Message("The distribution code can only be 6-10 English letters and numbers.")
     */
    public const CREATE_DISTRIBUTION_CODE = 400016;

    /**
     * 新建分销-分销码唯一
     * @Message("code already exists, please change the code.")
     */
    public const DISTRIBUTION_CODE_ALREADY_EXISTS = 400017;

    /**
     * 新建分销-会员已经有分销码
     * @Message("Already have a distribution code.")
     */
    public const DISTRIBUTION_CODE_ALREADY_HAVE = 400018;

    /**
     * 团队-团队人数达到上限
     * @Message("The number of team members has reached the maximum limit.")
     */
    public const TEAM_MEMBERS_REACHED_MAXIMUM_LIMIT = 400019;

    /**
     * 团队-无效的邀请码
     * @Message("Invalid code.")
     */
    public const TEAM_INVITE_CODE_INVALID = 400020;

    /**
     * 团队-邀请码和邮箱不匹配
     * @Message("The code and email do not match.")
     */
    public const TEAM_INVITE_CODE_AND_EMAIL_NOT_MATCH = 400021;

    /**
     * 团队-没有权限创建团队
     * @Message("No permission to create team.")
     */
    public const TEAM_NO_PERMISSION_TO_CREATE = 400022;

    /**
     * 团队-未创建团队
     * @Message("No team created.")
     */
    public const TEAM_NOT_CREATE = 400023;

    /**
     * 团队-不是该团队的成员
     * @Message("Not a member of this team.")
     */
    public const TEAM_NOT_MEMBER = 400024;

    /**
     * 团队-已经是该团队的成员
     * @Message("Already a member of the team.")
     */
    public const TEAM_ALREADY_A_MEMBER = 400025;

    /**
     * 团队-无效的角色id
     * @Message("Invalid role id.")
     */
    public const TEAM_ROLE_ID_INVALID = 400026;

    /**
     * 团队-必须是管理员
     * @Message("Must be an administrator.")
     */
    public const TEAM_MUST_BE_ADMIN = 400027;

    /**
     * 用户反馈-已经达到提交次数的上限
     * @Message("The maximum number of submissions has been reached.")
     */
    public const USER_FEEDBACK_SUBMIT_MAXIMUM = 400028;

    /**
     * 团队-不能邀请自己，你是团队的拥有者
     * @Message("You are the owner of the team.")
     */
    public const TEAM_YOUR_ARE_OWNER = 400029;

    /**
     * 团队-未满x天不能删除成员
     * @Message("Members cannot be deleted before x days have passed.")
     */
    public const TEAM_REMOVE_MEMBER_DAYS_LIMIT = 400030;

    /**
     * 团队-邮箱未注册
     * @Message("The email account not registered.")
     */
    public const TEAM_EMAIL_NOT_REGISTERED = 400031;

    /**
     * 删除账号-验证码不正确
     * @Message("Invalid verification code.")
     */
    public const DELETE_ACCOUNT_VERIFICATION_CODE_INVALID = 400036;

    /**
     * stripe-接口返回异常
     * @Message("Temporarily unavailable. Please wait patiently for restoration.")
     */
    public const STRIPE_API_ERROR = 400037;
}
