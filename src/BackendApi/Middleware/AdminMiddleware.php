<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Middleware;

use Hyperf\Contract\TranslatorInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Context\Context;
use Lete\Base\Utils\IP;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class AdminMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var TranslatorInterface
     */
    protected $translator;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $this->translator->setLocale('zh_CN');

        $request = Context::set(ServerRequestInterface::class, $request->withAttribute('client-ip', IP::get($this->request)));

        return $handler->handle($request);
    }
}
