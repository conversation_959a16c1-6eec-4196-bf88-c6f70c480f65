<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Middleware;

use Hyperf\Redis\Redis;
use Website\Common\BackendApi\Rpc\Message\AuthMessage;
use Hyperf\Amqp\RpcClient;
use Hyperf\Context\Context;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\ApplicationContext;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

class AuthMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var \Hyperf\HttpServer\Contract\ResponseInterface
     */
    protected $response;

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        try {
            $token = $this->request->header('Authorization', '');

            $redis_key = env('APP_NAME') . ":backend-auth:{$token}";
            $Redis = ApplicationContext::getContainer()->get(Redis::class);
            $redis_result = $Redis->get($redis_key);
            if (!$redis_result) {
                $RpcClient = ApplicationContext::getContainer()->get(RpcClient::class);
                $result = $RpcClient->call(new AuthMessage([
                    'token' => $token,
                ]));

                $Redis->setex($redis_key, 60, json_encode($result));
            } else {
                $result = json_decode($redis_result, true);
            }

            if ($result['code'] !== 200) {
                throw new \Exception($result['message'], $result['code']);
            }
            Context::set('AdminUSER', $result['data']['admin_user']);
        } catch (\Throwable $e) {
           return $this->response->json([
               'code' => $e->getCode() ?: 400,
               'message' => $e->getMessage(),
               'data' => [],
           ]);
        }

        return $handler->handle($request);
    }
}
