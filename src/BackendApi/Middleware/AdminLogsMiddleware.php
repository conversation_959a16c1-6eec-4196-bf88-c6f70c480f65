<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Middleware;

use Carbon\Carbon;
use FastRoute\Dispatcher;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Router\Dispatched;
use Hyperf\Context\Context;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Lete\Base\Service\RouterData;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Lete\MongoDB\MongoClient\MongoDb;

class AdminLogsMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var ConfigInterface
     */
    protected $config;

    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $shouldLogOperation = $this->shouldLogOperation();
        if ($shouldLogOperation) {
            $admin_user = Context::get('AdminUSER');
            $data = [
                'user_id' => $admin_user ? $admin_user['id'] : 0,
                'username' => $admin_user ? $admin_user['username'] : '',
                'ip' => $this->request->getAttribute('client-ip'),
                'path' => $this->request->getPathInfo(),
                'method' => $this->request->getMethod(),
                'input' => $this->request->all(),
                'created_at' => Carbon::now('PRC')->timestamp,
            ];
            [$data['module'], $data['function']] = $this->prepareMoudleFunction($request);
            Context::set('AdminOperationLog', $data);
        }

        $response = $handler->handle($request);

        if ($shouldLogOperation) {
            $output = json_decode($response->getBody()->getContents(), true);
            !is_null($output) && Arr::forget($output, 'data');
            $data = Context::get('AdminOperationLog');
            $data['output'] = is_null($output) ? (object) [] : $output;

            MongoDb::connection($this->config->get('admin.log.connection'))
                ->collection($this->config->get('admin.log.collection'))
                ->insertOne($data);
        }

        return $response;
    }

    /**
     * 判断是否记录日志
     * @return bool
     */
    protected function shouldLogOperation()
    {
        return $this->config->get('admin.log.enable', false)
            &&
            ((!$this->isExcepted() && $this->isAllowedMethods())
                || $this->isIncluded());
    }

    /**
     * 判断当期请求方式是否记录日志
     * @return bool
     */
    protected function isAllowedMethods()
    {
        $method = $this->request->getMethod();
        $allowedMethods = collect(config('admin.log.allowed_methods'))->filter();

        if ($allowedMethods->isEmpty()) {
            return true;
        }

        return $allowedMethods->map(function ($method) {
            return strtoupper($method);
        })->contains($method);
    }

    /**
     * 判断当前uri是否不记录日志
     * @return bool
     */
    protected function isExcepted()
    {
        $uri_path = $this->request->getUri()->getPath();
        foreach ($this->config->get('admin.log.except') as $except) {
            $methods = [];
            if (Str::contains($except, ':')) {
                list($methods, $except) = explode(':', $except);
                $methods = explode(',', $methods);
            }
            $methods = array_map('strtoupper', $methods);

            if (($uri_path === $except || @preg_match("/{$except}/", $uri_path)) &&
                (empty($methods) || in_array($this->request->getMethod(), $methods))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断当前uri是否必须记录日志
     * @return bool
     */
    protected function isIncluded()
    {
        $uri_path = $this->request->getUri()->getPath();
        foreach ($this->config->get('admin.log.includes') as $except) {
            $methods = [];
            if (Str::contains($except, ':')) {
                list($methods, $except) = explode(':', $except);
                $methods = explode(',', $methods);
            }
            $methods = array_map('strtoupper', $methods);

            if (($uri_path === $except || @preg_match("/{$except}/", $uri_path)) &&
                (empty($methods) || in_array($this->request->getMethod(), $methods))) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param ServerRequestInterface $request
     * @return array
     * @throws \Doctrine\Common\Annotations\AnnotationException
     */
    protected function prepareMoudleFunction(ServerRequestInterface $request): array
    {
        /** @var Dispatched $dispatched */
        $dispatched = $request->getAttribute(Dispatched::class);
        if ($dispatched->status !== Dispatcher::FOUND || $dispatched->handler->callback instanceof \Closure) {
            return ['', ''];
        }

        return RouterData::prepareMoudleFunction($dispatched->handler->callback);
    }
}
