<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Middleware;

use Hyperf\Context\Context;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Redis\Redis;
use Hyperf\Utils\Str;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Website\Common\Utils\Website;

class ReSubmitMiddleware implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var \Hyperf\HttpServer\Contract\ResponseInterface
     */
    protected $response;

    /**
     * @Inject
     * @var Redis
     */
    protected $redis;

    /**
     * @param ServerRequestInterface $request
     * @param RequestHandlerInterface $handler
     * @return ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        if ($this->isExcepted()) {
            return $handler->handle($request);
        }

        $admin_user = Context::get('AdminUSER');
        if ($admin_user && in_array($this->request->getMethod(), ['POST', 'PUT', 'DELETE'])) {
            $key =  env('APP_NAME') . ':admin:resubmit:' . md5($this->request->getPathInfo() . ':' . $admin_user['id'] . ':' . $this->request->getAttribute('client-ip') . ':' . json_encode($this->request->all()));
            if (!$this->redis->set($key, 1, ['NX', 'EX' => 1])) {
                return $this->response->json([
                    'code' => 400,
                    'message' => 'request too fast',
                    'data' => [],
                ]);
            }
        }

        return $handler->handle($request);
    }

    /**
     * @return bool
     */
    protected function isExcepted()
    {
        $arr = array_merge([
        ], Website::config()->get('website.backend.middlewares.resubmit.except', []));
        $uri_path = $this->request->getUri()->getPath();
        foreach ($arr as $except) {
            $methods = [];
            if (Str::contains($except, ':')) {
                list($methods, $except) = explode(':', $except);
                $methods = explode(',', $methods);
            }
            $methods = array_map('strtoupper', $methods);

            if (($uri_path === $except || @preg_match("/^{$except}$/", $uri_path)) &&
                (empty($methods) || in_array($this->request->getMethod(), $methods))) {
                return true;
            }
        }

        return false;
    }
}
