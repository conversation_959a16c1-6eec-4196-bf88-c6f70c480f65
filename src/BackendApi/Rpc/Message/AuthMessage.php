<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Rpc\Message;

use Hyperf\Amqp\Message\RpcMessage;

class AuthMessage extends RpcMessage
{
    /**
     * @var string
     */
    protected $poolName = 'center';

    /**
     * @param $data
     */
    public function __construct($data)
    {
        $this->exchange = env('CENTER_NAME');
        $this->routingKey = 'backend-rpc-auth';

        $this->payload = $data;
    }
}
