<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Website\Common\Constants\ErrorCode;
use Website\Common\Model\Order;
use Website\Common\Service\OrderService;
use Website\Common\Service\RankService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\Header;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Paginator\LengthAwarePaginator;
use Hyperf\Utils\Arr;
use Lete\MongoDB\MongoClient\MongoDb;
use Lete\Base\Utils\Timestamp;
use Lete\Base\Utils\Data;

/**
 * @ApiController(prefix="orders", tag="订单管理", server="website-common-backend-api")
 */
class OrdersController extends AdminController
{
    /**
     * @GetApi(path="overview/renew", description="续费看板")
     * @Query(key="month|月份", rule="required|date_format:Y-m")
     */
    public function renew()
    {
        $validator_data = Context::get('validator.data');
        $CurrentMonth = Carbon::createFromTimeString("{$validator_data['month']}-01 00:00:00", 'PRC');
        $month_start_time = $CurrentMonth->timestamp;
        $month_end_time = $CurrentMonth->addMonth()->timestamp - 1;

        // 整月订单
        // 支付时间 = 当前选择月份； 2.支付状态 = 已支付；
        $PaidOrders = Db::table('orders')->where([
                ['paid_at', '>=', $month_start_time],
                ['paid_at', '<=', $month_end_time],
                ['order_status', OrderService::STATUS_PAID],
            ])
            ->get(['user_id']);
        $paid_count = $PaidOrders->count();

        // 续费订单
        // 支付时间=当前选择月份； 存在支付时间=上个月的订单且订单支付状态=已支付
        $last_month_start_time = Carbon::createFromTimestamp($month_start_time, 'PRC')->subMonth()->timestamp;
        $last_month_end_time = $month_start_time - 1;
        $user_ids = $PaidOrders->groupBy('user_id')->keys()->toArray();
        $renew_count = Db::table('orders')->where([
                ['paid_at', '>=', $last_month_start_time],
                ['paid_at', '<=', $last_month_end_time],
                ['order_status', OrderService::STATUS_PAID],
            ])
            ->whereIn('user_id', $user_ids)
            ->count();

        // 续费率
        // 续费率 = 当前选择月份的续费订单数 / 当前月份的整月订单。 结果保留2位小数
        $renew_rate = $paid_count > 0 ? round($renew_count / $paid_count, 2): 0;

        // 已支付金额
        $paid_amount = (float) Db::table('orders')
            ->where('paid_at', '>=', $month_start_time)
            ->where('paid_at', '<=', $month_end_time)
            ->sum('paid_amount');

        // 已退款金额
        $refunded_amount = (float) Db::table('orders')
            ->where('refunded_at', '>=', $month_start_time)
            ->where('refunded_at', '<=', $month_end_time)
            ->sum('refunded_amount');

        // 利润额
        $profit_amount = (float) Db::table('orders')
            ->where('paid_at', '>=', $month_start_time)
            ->where('paid_at', '<=', $month_end_time)
            ->sum('profit_amount');

        $data = [
            'paid_count' => $paid_count,
            'renew_count' => $renew_count,
            'renew_rate' => $renew_rate,
            'paid_amount' => $paid_amount,
            'refunded_amount' => $refunded_amount,
            'profit_amount' => $profit_amount
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="overview/time", description="时间看板")
     * @Query(key="start_time|开始时间", rule="date_format:Y-m-d")
     * @Query(key="end_time|结束时间", rule="date_format:Y-m-d")
     */
    public function overview()
    {
        $validator_data = Context::get('validator.data');
        $start_time = (string) Arr::get($validator_data,'start_time', '');
        $end_time = (string) Arr::get($validator_data,'end_time', '');

        // 如果没有传时间，所有记录中找到最小和最大时间
        if (!$start_time && !$end_time) {
            $all_orders = Order::query()
                ->get(['order_status', 'created_at', 'paid_at', 'refunded_at', 'rank_duration', 'paid_amount', 'refunded_amount', 'rank_id', 'order_amount'])
                ->toArray();

            if ($all_orders) {
                $start_time = explode(' ', $all_orders[0]['created_at'])[0];
                $max_created_at = Carbon::createFromTimeString(array_reverse($all_orders)[0]['created_at'], 'PRC')->timestamp;
                $max_paid_at = Db::table('orders')
                    ->where('order_status', OrderService::STATUS_PAID)
                    ->max('paid_at');
                $max_refunded_at = Db::table('orders')
                    ->where('order_status', OrderService::STATUS_REFUNDED)
                    ->max('refunded_at');
                $end_time = Carbon::createFromTimestamp(max($max_created_at, $max_paid_at, $max_refunded_at), 'PRC')->format('Y-m-d');
            }
        } else {
            !$start_time && $start_time = Carbon::createFromTimestamp(Db::table('orders')->min('created_at'), 'PRC')->format('Y-m-d');
            if (!$end_time) {
                $max_created_at = Db::table('orders')
                    ->max('created_at');
                $max_paid_at = Db::table('orders')
                    ->where('order_status', OrderService::STATUS_PAID)
                    ->max('paid_at');
                $max_refunded_at = Db::table('orders')
                    ->where('order_status', OrderService::STATUS_REFUNDED)
                    ->max('refunded_at');
                $end_time = Carbon::createFromTimestamp(max($max_created_at, $max_paid_at, $max_refunded_at), 'PRC')->format('Y-m-d');
            }
        }
        $start_timestamp = Timestamp::dateStart($start_time, 'PRC');
        $end_timestamp = Timestamp::dateEnd($end_time, 'PRC');

        // 新增支付订单
        // 支付时间 = 【所选时间范围】
        if (isset($all_orders)) {
            $paid_data = array_filter($all_orders, function ($order) use ($start_timestamp, $end_timestamp) {
                if (!$order['paid_at']) {
                    return false;
                }
                return $start_timestamp <= $order['paid_at'] && $order['paid_at'] <= $end_timestamp;
            });
        } else {
            $where = [];
            $start_time && $where[] = ['paid_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['paid_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $paid_data = Order::query()
                ->where($where)
                ->get(['paid_at', 'paid_amount', 'rank_id'])->toArray();
        }

        // 新增xxx（会员等级）支付订单
        // 支付时间 = 【所选时间范围】，会员等级=xxx
        $rank_paid = [];
        foreach (RankService::integration() as $rank) {
            $rank_paid_data = array_filter($paid_data, function ($order) use ($rank) {
                return in_array($order['rank_id'], explode(',', $rank['id']));
            });
            $rank_paid[] = array_merge([
                'title' => $rank['rank_name'],
            ], Data::toTimeline($start_time, $end_time, 'paid_at', $rank_paid_data));
        }

        // 新增未支付订单
        // 下单时间 = 【所选时间范围】，订单状态 = 未支付
        if (isset($all_orders)) {
            $unpaid_data = array_filter($all_orders, function ($order) {
                return $order['order_status'] === OrderService::STATUS_UNPAID;
            });
        } else {
            $where = [
                ['order_status', OrderService::STATUS_UNPAID],
            ];
            $start_time && $where[] = ['created_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['created_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $unpaid_data = Order::query()->where($where)->get(['created_at', 'order_amount'])->toArray();
        }

        // 新增已退款订单
        // 退款时间 = 【所选时间范围】
        if (isset($all_orders)) {
            $refunded_data = array_filter($all_orders, function ($order) use ($start_timestamp, $end_timestamp) {
                if (!$order['refunded_at']) {
                    return false;
                }
                return $start_timestamp <= $order['refunded_at'] && $order['refunded_at'] <= $end_timestamp;
            });
        } else {
            $where = [];
            $start_time && $where[] = ['refunded_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['refunded_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $refunded_data = Order::query()->where($where)->get(['refunded_at', 'refunded_amount'])->toArray();
        }

        // 新增月支付订单
        // 支付时间 = 【所选时间范围】，订单状态 = 已支付，套餐时长 = 一个月
        if (isset($all_orders)) {
            $paid_1_month_data = array_filter($all_orders, function ($order) {
                return $order['order_status'] === OrderService::STATUS_PAID && $order['rank_duration'] === '1 months';
            });
        } else {
            $where = [
                ['order_status', OrderService::STATUS_PAID],
                ['rank_duration', '1 months'],
            ];
            $start_time && $where[] = ['paid_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['paid_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $paid_1_month_data = Order::query()->where($where)->get(['paid_at'])->toArray();
        }

        // 新增年支付订单
        // 支付时间 = 【所选时间范围】，订单状态 = 已支付，套餐时长 = 一年
        if (isset($all_orders)) {
            $paid_1_year_data = array_filter($all_orders, function ($order) {
                return $order['order_status'] === OrderService::STATUS_PAID && $order['rank_duration'] === '1 years';
            });
        } else {
            $where = [
                ['order_status', OrderService::STATUS_PAID],
                ['rank_duration', '1 years'],
            ];
            $start_time && $where[] = ['paid_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['paid_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $paid_1_year_data = Order::query()->where($where)->get(['paid_at'])->toArray();
        }

        // 已支付金额
        // 支付时间 = 【所选时间范围】
        $paid_amount = 0;
        foreach ($paid_data as $order) {
            $paid_amount += $order['paid_amount'];
        }
        $paid_amount = round($paid_amount, 2);

        // 未支付金额
        // 下单时间 = 【所选时间范围】，订单状态 = 未支付
        $unpaid_amount = 0;
        foreach ($unpaid_data as $order) {
            $unpaid_amount += $order['order_amount'];
        }
        $unpaid_amount = round($unpaid_amount, 2);

        // 已退款金额
        // 退款时间 = 【所选时间范围】
        $refunded_amount = 0;
        foreach ($refunded_data as $order) {
            $refunded_amount += $order['refunded_amount'];
        }
        $refunded_amount = round($refunded_amount, 2);

        // 利润额
        // 支付时间 = 【所选时间范围】
        $where = [];
        $start_time && $where[] = ['paid_at', '>=', Timestamp::dateStart($start_time,'PRC')];
        $end_time && $where[] = ['paid_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
        $profit_amount = round(Order::query()->where($where)->sum('profit_amount'), 2);

        // 新增xxx（订单类型）支付订单
        $type_paid = [];
        $Builder = Order::query()
            ->select(['type', Db::raw("count(*) as count")])
            ->whereNotNull('paid_at');
        $start_time && $Builder->where('paid_at', '>=', Timestamp::dateStart($start_time));
        $end_time && $Builder->where('paid_at', '<=', Timestamp::dateEnd($end_time));
        $type_paid_result = array_column($Builder->groupBy('type')->get()->toArray(), null, 'type');
        foreach (OrderService::typeList() as $type) {
            $type_paid[] = [
                'title' => $type['text'],
                'total' => $type_paid_result[$type['value']]['count'] ?? 0,
            ];
        }

        $data = [
            'paid' => Data::toTimeline($start_time, $end_time, 'paid_at', $paid_data),
            'unpaid' => Data::toTimeline($start_time, $end_time, 'created_at', $unpaid_data),
            'refunded' => Data::toTimeline($start_time, $end_time, 'refunded_at', $refunded_data),
            'paid_1_month' => Data::toTimeline($start_time, $end_time, 'paid_at', $paid_1_month_data),
            'paid_1_year' => Data::toTimeline($start_time, $end_time, 'paid_at', $paid_1_year_data),
            'paid_amount' => $paid_amount,
            'unpaid_amount' => $unpaid_amount,
            'refunded_amount' => $refunded_amount,
            'profit_amount' => $profit_amount,
            'rank_paid' => $rank_paid,
            'type_paid' => $type_paid,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="", description="订单列表")
     * @Query(key="user_id|会员ID", rule="integer")
     * @Query(key="order_sn|订单号", rule="")
     * @Query(key="email|会员账号", rule="")
     * @Query(key="transaction_number|交易号", rule="")
     * @Query(key="rank_id|等级套餐", rule="")
     * @Query(key="rank_duration|套餐时长", rule="cb_ruleCheckDuration")
     * @Query(key="first_time|是否首次", rule="boolean")
     * @Query(key="order_status|订单状态", rule="cb_ruleCheckOrderStatus")
     * @Query(key="remark|备注", rule="")
     * @Query(key="payment_platform|支付方式", rule="in:100,1")
     * @Query(key="created_at_start|下单时间（起）", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|下单时间（止）", rule="date_format:Y-m-d")
     * @Query(key="paid_at_start|支付时间（起）", rule="date_format:Y-m-d")
     * @Query(key="paid_at_end|支付时间（止）", rule="date_format:Y-m-d")
     * @Query(key="refunded_at_start|退款时间（起）", rule="date_format:Y-m-d")
     * @Query(key="refunded_at_end|退款时间（止）", rule="date_format:Y-m-d")
     * @Query(key="refunded_amount_start|退款金额（起）", rule="numeric")
     * @Query(key="refunded_amount_end|退款金额（止）", rule="numeric")
     * @Query(key="profit_amount_start|利润额（起）", rule="numeric")
     * @Query(key="profit_amount_end|利润额（止）", rule="numeric")
     * @Query(key="order_by|排序字段", rule="in:created_at,paid_at,refunded_at")
     * @Query(key="direction|排序方式[desc,asc]", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     * @Query(key="distribution_code|分销码", rule="")
     * @Query(key="type|类型", rule="integer")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');

        $Builder = $this->getBuilder($validator_data);

        $data = $Builder->paginate(intval(($validator_data['per_page'] ?? 10)))
            ->toArray();
        if ($data['data']) {
            $data['data'] = array_map(function ($item) {
                $item['order_status_text'] = OrderService::statusText($item['order_status']);
                $item['payment_platform_text'] = OrderService::paymentPlatformText($item['payment_platform']);
                return $item;
            }, $data['data']);
        }
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckOrderStatus($attribute, $value)
    {
        return is_null(OrderService::statusText($value)) ? '无效的订单状态' : true;
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckDuration($attribute, $value)
    {
        return RankService::ruleCheckDuration($attribute, $value);
    }

    /**
     * @GetApi(path="options", description="选项")
     */
    public function options()
    {
        $data = [
            'ranks' => RankService::integration(),
            'order_status' => OrderService::statusList(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PostApi(path="", description="创建订单")
     * @FormData(key="email|会员账号", rule="required|email")
     * @FormData(key="type|订单类型", rule="sometimes|required|integer")
     * @FormData(key="rank_id|套餐", rule="sometimes|required|exists:ranks,id")
     * @FormData(key="rank_duration|时长", rule="")
     * @FormData(key="paid_amount|支付金额", rule="required|numeric|min:0|max:99999999.99")
     * @FormData(key="paid_at|支付时间", rule="required|date_format:Y-m-d H:i:s")
     * @FormData(key="remark|备注内容", rule="")
     */
    public function store()
    {
        try {
            $validator_data = Context::get('validator.data');

            $type = $validator_data['type'] ?? OrderService::TYPE_SUBSCRIPTION;
            $others = [];
            switch ($type) {
                case OrderService::TYPE_SUBSCRIPTION:
                    $others['rank_id'] = $validator_data['rank_id'] ?? RankService::FREE;
                    $others['rank_duration'] = $validator_data['rank_duration'] ?? null;
                    break;
                default:
                    break;
            }

            OrderService::manualCreate($validator_data['email'],
                $type,
                OrderService::PAYMENT_PLATFORM_OFFLINE,
                $validator_data['paid_amount'],
                Carbon::createFromFormat('Y-m-d H:i:s', $validator_data['paid_at'], 'PRC')->timestamp,
                $validator_data['remark'] ?? '',
                $others
            );
            return $this->adminResponse(200, '请求成功');
        } catch(\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|订单ID", rule="required|integer|exists:orders")
     * @FormData(key="remark|备注内容", rule="sometimes|required")
     */
    public function update()
    {
        $validator_data = Context::get('validator.data');
        if (!(new OrderService($validator_data['id']))->save(['remark' => $validator_data['remark']])) {
            return $this->adminResponse(400, '请求失败');
        }
        return $this->adminResponse(200, '请求成功');
    }

    /**
     * @PostApi(path="{id:\d+}/refund", description="退款")
     * @Path(key="id|订单ID", rule="required|integer|exists:orders")
     * @FormData(key="amount|退款金额", rule="required|numeric|min:0")
     * @FormData(key="method|退款方式", rule="required|in:1,100")
     * @FormData(key="cancel_vip|是否退会员", rule="sometimes|required|boolean")
     * @FormData(key="cancel_subscription|是否取消订阅", rule="sometimes|required|boolean")
     */
    public function refund()
    {
        try {
            $validator_data = Context::get('validator.data');
            (new OrderService($this->request->route('id')))
                ->refund($validator_data['amount'], $validator_data['method'], $validator_data['cancel_vip'] ?? false, $validator_data['cancel_subscription'] ?? false, Context::get('AdminUSER')['id']);
            return $this->adminResponse(200, '请求成功', []);
        } catch(\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @GetApi(path="invoice-template-data/{id:\d+}", description="获取发票模板数据")
     * @Path(key="id|订单ID", rule="required|integer")
     */
    public function getInvoiceTemplateData()
    {
        $validator_data = Context::get('validator.data');
        $data = OrderService::getInvoiceTemplateData((int) $validator_data['id']);
        if (!$data) {
            return $this->adminResponse(500, ErrorCode::getMessage(ErrorCode::SERVER_ERROR));
        }
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="invoice-template-data-logs", description="发票模板下载日志")
     * @Query(key="email|邮箱", rule="")
     * @Query(key="order_sn|订单编号", rule="")
     * @Query(key="status|状态", rule="integer|in:0,1")
     * @Query(key="created_at_start|下载时间（起）", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|下载时间（止）", rule="date_format:Y-m-d")
     * @Query(key="order_by|排序字段", rule="in:created_at")
     * @Query(key="direction|排序方式", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function getInvoiceTemplateDataLogs()
    {
        $validator_data = Context::get('validator.data');
        $InvoiceTemplateDataLogs = MongoDb::collection('invoice_template_data_logs');
        $pipeline = [];
        $match = [
            '$and' => [],
        ];

        ($email = trim(Arr::get($validator_data,'email', ''))) &&
        $match['$and'][] = ['email' => $email];

        ($order_sn = trim(Arr::get($validator_data,'order_sn', ''))) &&
        $match['$and'][] = ['order_sn' => $order_sn];

        $status = Arr::get($validator_data,'status', '');
        if ($status !== '') {
            $match['$and'][] = ['status' => (int) $status];
        }

        // 时间范围筛选
        $datetime_keys = ['created_at'];
        foreach ($datetime_keys as $key) {
            $field = $key;
            $field_value = [];
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value) : Timestamp::dateEnd($key_value);
                    $field_value[$tail === 'start' ? '$gte' : '$lte'] = $key_value;
                }
            }
            $field_value && $match['$and'][] = [$field => $field_value];
        }

        $page = (int) (Arr::get($validator_data,'page') ?: 1);
        $per_page = (int) (Arr::get($validator_data,'per_page') ?: 10);

        $match['$and'] && array_unshift($pipeline, ['$match' => $match]);
        $pipeline = array_merge($pipeline, [
            ['$project' => [
                'result' => 0,
            ]],
            ['$sort' => [
                Arr::get($validator_data,'order_by') ?: 'created_at' => Arr::get($validator_data,'direction', 'desc') === 'desc' ? -1 : 1,
            ]],
            ['$skip' => ($page - 1) * $per_page],
            ['$limit' => $per_page],
        ]);
        $data = (new LengthAwarePaginator(
            array_map(function ($item) {
                $item['_id'] = $item['_id']->jsonSerialize()['$oid'];
                $item['created_at'] = Carbon::createFromTimestamp($item['created_at'])->toDateTimeString();
                return $item;
            }, $InvoiceTemplateDataLogs->aggregate($pipeline)),
            $InvoiceTemplateDataLogs->countDocuments($match['$and'] ? $match : []),
            $per_page,
            $page,
            [
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'pageName' => 'page',
                'query' => $this->request->all()
            ]
        ))->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @param array $validator_data
     * @return \Hyperf\Database\Model\Builder
     */
    public function getBuilder($validator_data)
    {
        $Builder = Order::query(false, true)
            ->with('user:id,account,username,email')
            ->with('rank:id,rank_name');

        ($user_id = Arr::get($validator_data, 'user_id', '')) &&
        $Builder->where('user_id', $user_id);

        ($order_sn = Arr::get($validator_data, 'order_sn', '')) &&
        $Builder->where('order_sn', 'like', "{$order_sn}%");

        $email = Arr::get($validator_data, 'email', '');
        if ($email) {
            $user_ids = Db::table('users')
                ->where('account', 'like', "%{$email}%")
                ->get(['id'])
                ->groupBy('id')->keys();
            $Builder->whereIn('user_id', $user_ids);
        }

        ($transaction_number = Arr::get($validator_data, 'transaction_number', '')) &&
        $Builder->where('transaction_number', 'like', "{$transaction_number}%");

        ($rank_id = Arr::get($validator_data, 'rank_id', '')) &&
        $Builder->whereIn('rank_id', explode(',', $rank_id));

        ($rank_duration = Arr::get($validator_data, 'rank_duration', '')) &&
        $Builder->where('rank_duration', $rank_duration);

        ($distribution_code = Arr::get($validator_data, 'distribution_code', '')) &&
        $Builder->where('distribution_code', strtoupper($distribution_code));

        $first_time = Arr::get($validator_data, 'first_time', '');
        ($first_time !== '') &&
        $Builder->where('first_time', $first_time);

        ($order_status = Arr::get($validator_data, 'order_status', '')) &&
        $Builder->where('order_status', $order_status);

        ($remark = Arr::get($validator_data, 'remark', '')) &&
        $Builder->where('remark', 'like', "%{$remark}%");

        ($payment_platform = Arr::get($validator_data, 'payment_platform', '')) &&
        $Builder->where('payment_platform', $payment_platform);

        ($type = Arr::get($validator_data, 'type', '')) &&
        $Builder->where('type', $type);

        // 时间范围筛选
        $datetime_keys = ['created_at', 'paid_at', 'refunded_at'];
        foreach ($datetime_keys as $key) {
            $field = $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value, 'PRC') : Timestamp::dateEnd($key_value, 'PRC');
                    $Builder->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 数值范围筛选
        $numeric_keys = ['refunded_amount', 'profit_amount'];
        foreach ($numeric_keys as $key) {
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $Builder->where($key, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 排序方式
        $direction = Arr::get($validator_data, 'direction') ?: 'desc';
        $order_by = Arr::get($validator_data, 'order_by') ?: 'id';
        $order_by === 'created_at' && $order_by = 'id';
        $Builder->orderBy($order_by, $direction);

        return $Builder;
    }
}
