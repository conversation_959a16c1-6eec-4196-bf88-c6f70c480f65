<?php
declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Website\Common\Model\DistributionCode;
use Website\Common\Model\DistributionCommission;
use Website\Common\Model\DistributionInfo;
use Website\Common\Model\DistributionSettlement;
use Website\Common\Model\Order;
use Website\Common\Model\User;
use Website\Common\Service\DistributionService;
use Website\Common\Service\OrderService;
use Website\Common\Utils\Tool;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\Arr;
use Lete\Base\Utils\Timestamp;
use Website\Common\Utils\Website;

/**
 * @ApiController(prefix="distribution", tag="后台分销系统", server="website-common-backend-api")
 */
class DistributionController extends AdminController
{
    /**
     * @PostApi(path="", description="新建分销")
     * @FormData(key="account|账号", rule="required")
     * @FormData(key="customize_code|定制码", rule="min:3|max:50|alpha_num")
     * @FormData(key="application_reason|申请理由", rule="nullable")
     * @FormData(key="paypal|paypal账号", rule="nullable")
     */
    public function store()
    {
        try {
            $validator_data = Context::get('validator.data');

            // 会员账号是否存在校验
            $user_id = User::query()->where('account', '=', $validator_data['account'])->value('id');
            if (!$user_id) {
                return $this->adminResponse(400, '会员账号不存在');
            }

            // 会员是否已经分销校验
            $distribution_info_id = DistributionInfo::query()->where('user_id', '=', $user_id)->value('id');
            if ($distribution_info_id) {
                return $this->adminResponse(400, '会员账号申请过分销');
            }

            // 分销码保障唯一
            if (!empty($validator_data['customize_code'])) {
                $customize_code = strtoupper($validator_data['customize_code']);
                $distribution_info_id = DistributionCode::query()->where('distribution_code', '=', $customize_code)->value('id');
                if ($distribution_info_id) {
                    return $this->adminResponse(400, '定制码不能在分销码中存在');
                }
            } else {
                start:
                $customize_code = Tool::getRandomDistributionCode(6, 50);
                $distribution_info_id = DistributionCode::query()->where('distribution_code', '=', strtoupper($customize_code))->value('id');
                if ($distribution_info_id) {
                    goto start;
                }
            }

            Db::beginTransaction();
            $insert = [
                'account' => $validator_data['account'],
                'user_id' => $user_id,
                'customize_code' => $customize_code,
                'distribution_code' => $customize_code,
                'application_time' => time(),
                'review_time' => time(),
                'status' => DistributionInfo::STATUS_DISTRIBUTION,
                'commission_rate' => DistributionCode::COMMISSION_RATE,
                'application_reason' => $validator_data['application_reason'] ?? '',
                'paypal' => $validator_data['paypal'] ?? '',
            ];
            DistributionInfo::create($insert);

            $code_insert = [
                'user_id' => $user_id,
                'distribution_code' => $customize_code,
                'commission_rate' => DistributionCode::COMMISSION_RATE,
            ];
            DistributionCode::create($code_insert);
            Db::commit();
            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            Db::rollBack();
            Website::logger()->error('后台新建分销异常，异常：' . $e->getMessage() . ',line' . $e->getLine() . ',file' . $e->getFile());
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|分销id", rule="required|exists:distribution_info")
     * @FormData(key="customize_code|定制码", rule="min:3|max:50|alpha_num")
     * @FormData(key="application_reason|申请理由", rule="nullable")
     * @FormData(key="paypal|paypal账号", rule="nullable")
     * @FormData(key="remark|备注", rule="nullable")
     */
    public function update()
    {
        try {
            $validator_data = Context::get('validator.data');
            $update = [
                'customize_code' => $validator_data['customize_code'],
                'application_reason' => $validator_data['application_reason'] ?? '',
                'paypal' => $validator_data['paypal'] ?? '',
                'remark' => $validator_data['remark'] ?? '',
            ];
            DistributionInfo::query()->find($validator_data['id'])->update($update);
            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @GetApi(path="overview", description="看板")
     */
    public function overview()
    {
        $data = DistributionInfo::query()->selectRaw("
        count(*) as distribution_num, 
        sum(assets) as assets, 
        sum(confirmed_commission) as confirmed_commission, 
        sum(not_confirmed_commission) as not_confirmed_commission, 
        sum(cancel_commission) as cancel_commission, 
        sum(settlement_commission) as settlement_commission, 
        sum(pay_customer) as pay_customer, 
        sum(click_num) as click_num, 
        sum(register_num) as register_num, 
        sum(income_num) as income_num, 
        sum(order_effect_num) as order_effect_num, 
        sum(order_invalid_num) as order_invalid_num
        ")->limit(1)->get()->toArray();
        if (!empty($data)) {
            $data = current($data);
        }
        return $this->adminResponse(200, '请求成功', $data);
    }


    /**
     * @GetApi(path="", description="分销管理列表")
     * @Query(key="account|分销账号", rule="nullable")
     * @Query(key="status|状态", rule="nullable")
     * @Query(key="customize_code|定制码", rule="nullable")
     * @Query(key="distribution_code|分销码", rule="nullable")
     * @Query(key="paypal|PayPal账号", rule="nullable")
     * @Query(key="application_reason|申请理由", rule="nullable")
     * @Query(key="remark|备注", rule="nullable")
     * @Query(key="application_time_start|申请时间起", rule="date_format:Y-m-d")
     * @Query(key="application_time_end|申请时间止", rule="date_format:Y-m-d")
     * @Query(key="review_time_start|审核时间起", rule="date_format:Y-m-d")
     * @Query(key="review_time_end|审核时间止", rule="date_format:Y-m-d")
     * @Query(key="order_effect_num_start|订单有效数最小", rule="integer")
     * @Query(key="order_effect_num_end|订单有效数最大", rule="integer")
     * @Query(key="assets_start|当前资产(最小)", rule="nullable")
     * @Query(key="assets_end|当前资产(最大)", rule="nullable")
     * @Query(key="confirmed_commission_start|已确认佣金(最小)", rule="nullable")
     * @Query(key="confirmed_commission_end|已确认佣金(最大)", rule="nullable")
     * @Query(key="not_confirmed_commission_start|未确认佣金(最小)", rule="nullable")
     * @Query(key="not_confirmed_commission_end|未确认佣金(最大)", rule="nullable")
     * @Query(key="order_invalid_num_start|订单无效数(最小)", rule="integer")
     * @Query(key="order_invalid_num_end|订单无效数(最大)", rule="integer")
     * @Query(key="cancel_commission_start|已取消佣金(最小)", rule="nullable")
     * @Query(key="cancel_commission_end|已取消佣金(最大)", rule="nullable")
     * @Query(key="settlement_commission_start|已结算佣金(最小)", rule="nullable")
     * @Query(key="settlement_commission_end|已结算佣金(最大)", rule="nullable")
     * @Query(key="pay_customer_start|付费客户数(最小)", rule="nullable")
     * @Query(key="pay_customer_end|付费客户数(最大)", rule="nullable")
     * @Query(key="click_num_start|点击数(最小)", rule="nullable")
     * @Query(key="click_num_end|点击数(最大)", rule="nullable")
     * @Query(key="register_num_start|注册数(最小)", rule="nullable")
     * @Query(key="register_num_end|注册数(最大)", rule="nullable")
     * @Query(key="income_num_start|收入数(最小)", rule="nullable")
     * @Query(key="income_num_end|收入数(最大)", rule="nullable")
     * @Query(key="order_by|排序字段", rule="in:application_time,review_time,order_effect_num,order_invalid_num,pay_customer,assets,not_confirmed_commission,confirmed_commission,cancel_commission,settlement_commission,click_num,register_num,income_num")
     * @Query(key="direction|排序方法", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $Distribution = DistributionInfo::query();

        /* 填充条件 */
        ($tmp = Arr::get($validator_data, 'account', '')) &&
        $Distribution->where('account', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'status', '')) &&
        $Distribution->where('status', '=', $tmp);

        // 定制码和分销码 共同搜索
        $tmp = Arr::get($validator_data, 'customize_code', '');
        if ($tmp) {
            $user_ids = DistributionCode::query()->where('distribution_code', 'like', "%{$tmp}%")->pluck('user_id')->toArray();
            if (empty($user_ids)) {
                $user_ids = [0];
            }
            $Distribution->where(function ($query) use ($user_ids, $tmp) {
                $query->whereIn('user_id', $user_ids)->orWhere('customize_code', 'like', "%{$tmp}%");
            });
        }

        ($tmp = Arr::get($validator_data, 'distribution_code', '')) &&
        $Distribution->where('distribution_code', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'paypal', '')) &&
        $Distribution->where('paypal', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'application_reason', '')) &&
        $Distribution->where('application_reason', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'remark', '')) &&
        $Distribution->where('remark', 'like', "%{$tmp}%");

        // 时间范围筛选
        $datetime_keys = ['application_time', 'review_time'];
        foreach ($datetime_keys as $key) {
            $field = $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value, 'PRC') : Timestamp::dateEnd($key_value, 'PRC');
                    $Distribution->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 数值范围筛选
        $numeric_keys = ['order_effect_num', 'assets', 'confirmed_commission', 'not_confirmed_commission', 'order_invalid_num', 'cancel_commission', 'settlement_commission', 'pay_customer', 'click_num', 'register_num', 'income_num'];
        foreach ($numeric_keys as $key) {
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $Distribution->where($key, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        $data = $Distribution->orderBy(Arr::get($validator_data, 'order_by') ?: 'id', Arr::get($validator_data, 'direction') ?: 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();

        return $this->adminResponse(200, '请求成功', $data);

    }

    /**
     * @PostApi(path="update-status", description="编辑状态")
     * @FormData(key="id|分销id", rule="required|exists:distribution_info")
     * @FormData(key="status|status", rule="required|in:1,2,3")
     */
    public function updateStatus()
    {
        try {
            $validator_data = Context::get('validator.data');
            $update = [
                'status' => $validator_data['status'],
            ];
            DistributionInfo::query()->find($validator_data['id'])->update($update);
            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }


    /**
     * @GetApi(path="{id:\d+}", description="查看详情")
     * @Path(key="id|id", rule="required|integer")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');

        $return = DistributionService::confirmCommission($validator_data['id']);
        if ($return['type'] === false) {
            return $this->adminResponse(400, $return['msg']);
        }

        $data = DistributionInfo::query()->find($validator_data['id'])->toArray();

        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="record", description="分销记录表")
     * @Query(key="id|分销id", rule="required")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function record()
    {
        $validator_data = Context::get('validator.data');
        $id = Arr::get($validator_data, 'id', '');
        $per_page = (int)Arr::get($validator_data, 'per_page', 10);

        $distribution_user_id = DistributionInfo::query()->where('id', '=', $id)->value('user_id');
        if (empty($distribution_user_id)) {
            return $this->adminResponse(400, '该分销的user_id异常');
        }
        $distribution_code_arr = DistributionCode::query()->where('user_id', '=', $distribution_user_id)->pluck('distribution_code')->toArray();


        $data = Order::query(false, true)
            ->with('rank:id,rank_name')
            ->with('user:id,email')
            ->with('distribution:order_id,pay_status,sure_time,commission_rate,commission')
            ->whereIn('distribution_code', $distribution_code_arr)
            ->whereIn('order_status', [OrderService::STATUS_PAID, OrderService::STATUS_REFUNDED])
            ->select("order_sn", "rank_duration", "user_id", "order_amount", "paid_amount", "paid_at", "distribution_code", "id", "rank_id")
            ->orderBy('id', 'desc')
            ->paginate($per_page)
            ->toArray();

        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PostApi(path="settle", description="新建结算")
     * @FormData(key="id|分销id", rule="required|exists:distribution_info")
     * @FormData(key="pay_money|结算金额", rule="numeric|required|min:1")
     * @FormData(key="pay_time|结算时间", rule="date_format:Y-m-d H:i:s")
     * @FormData(key="remark|备注", rule="")
     * @FormData(key="pay_account|付款账号", rule="")
     */
    public function settle()
    {
        $validator_data = Context::get('validator.data');
        $id = Arr::get($validator_data, 'id', '');
        $distribution_info = DistributionInfo::query()->find($id);

        // 结算金额
        $money = $validator_data['pay_money'];
        $tmp = explode('.', $money);
        if (isset($tmp[1]) && strlen($tmp[1]) > 2) {
            return $this->adminResponse(400, '结算金额只能精确到2位小数点');
        }
        if ($distribution_info->assets < $money) {
            return $this->adminResponse(400, '付款金额必须小于当前资产, 分销者的当前资产为:' . $distribution_info->assets);
        }
        $user_id = $distribution_info->user_id;

        // 结算佣金
        $result = DistributionService::settleCommission($money, $user_id, $validator_data);
        if ($result['type'] === false) {
            return $this->adminResponse(400, $result['msg']);
        }

        return $this->adminResponse(200, '请求成功');

    }

    /**
     * @GetApi(path="settle-list", description="结算记录列表")
     * @Query(key="id|分销id", rule="required")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function settleList()
    {
        $validator_data = Context::get('validator.data');
        $id = Arr::get($validator_data, 'id', '');

        $user_id = DistributionInfo::query()->where('id', '=', $id)->value('user_id');
        if (empty($user_id)) {
            return $this->adminResponse(400, '分销id对应的用户id异常');
        }

        $data = DistributionSettlement::query()
            ->where('user_id', '=', $user_id)
            ->orderBy('id', 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();
        return $this->adminResponse(200, '请求成功', $data);

    }

    /**
     * @PostApi(path="cancel-commission", description="取消结算记录")
     * @FormData(key="id|id", rule="required")
     */
    public function cancelCommission()
    {
        $validator_data = Context::get('validator.data');
        $id = $validator_data['id'];
        $return = DistributionService::cancelCommission($id);
        if($return['type'] === false){
            return $this->adminResponse(400, $return['msg']);
        }

        return $this->adminResponse(200, '请求成功');
    }


    /**
     * @GetApi(path="commission", description="佣金记录列表")
     * @Query(key="id|分销id", rule="required")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function commission()
    {
        $validator_data = Context::get('validator.data');
        $id = Arr::get($validator_data, 'id', '');

        $user_id = DistributionInfo::query()->where('id', '=', $id)->value('user_id');
        if (empty($user_id)) {
            return $this->adminResponse(400, '分销id对应的用户id异常');
        }

        $data = DistributionCommission::query()
            ->where('user_id', '=', $user_id)
            ->orderBy('id', 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();

        return $this->adminResponse(200, '请求成功', $data);
    }


    /**
     * @GetApi(path="record-list", description="分销结算列表")
     * @Query(key="email|分销账号", rule="nullable")
     * @Query(key="status|状态", rule="nullable")
     * @Query(key="pay_time_start|结算时间起", rule="date_format:Y-m-d")
     * @Query(key="pay_time_end|结算时间止", rule="date_format:Y-m-d")
     * @Query(key="created_at_start|创建时间起", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|创建时间止", rule="date_format:Y-m-d")
     * @Query(key="paypal|付款账号", rule="nullable")
     * @Query(key="pay_money_start|付款金额最小值", rule="nullable")
     * @Query(key="pay_money_end|付款金额最大值", rule="nullable")
     * @Query(key="remark|备注", rule="nullable")
     * @Query(key="order_by|排序字段", rule="in:pay_time,created_at,pay_money,void_time")
     * @Query(key="direction|排序方法", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function recordList()
    {
        $validator_data = Context::get('validator.data');
        $Distribution = DistributionSettlement::query()
            ->join('distribution_info', 'distribution_info.user_id', '=', 'distribution_settlement.user_id')
            ->selectRaw("distribution_info.id as distribution_info_id,distribution_info.account as email,distribution_settlement.*");

        /* 填充条件 */
        ($tmp = Arr::get($validator_data, 'email', '')) &&
        $Distribution->where('distribution_info.account', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'status', '')) &&
        $Distribution->where('distribution_settlement.status', '=', $tmp);

        ($tmp = Arr::get($validator_data, 'paypal', '')) &&
        $Distribution->where('distribution_settlement.pay_account', 'like', "%{$tmp}%");

        ($tmp = Arr::get($validator_data, 'remark', '')) &&
        $Distribution->where('distribution_settlement.remark', 'like', "%{$tmp}%");

        // 时间范围筛选
        $datetime_keys = ['pay_time', 'created_at'];
        foreach ($datetime_keys as $key) {
            $field = "distribution_settlement." . $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value, 'PRC') : Timestamp::dateEnd($key_value, 'PRC');
                    $Distribution->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 数值范围筛选
        $numeric_keys = ['pay_money'];
        foreach ($numeric_keys as $key) {
            $field = "distribution_settlement." . $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $Distribution->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        $data = $Distribution->orderBy('distribution_settlement.' . (Arr::get($validator_data, 'order_by') ?: 'id'), Arr::get($validator_data, 'direction') ?: 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();

        return $this->adminResponse(200, '请求成功', $data);

    }

    /**
     * @GetApi(path="record-overview", description="分销看板")
     * @Query(key="start_time|开始时间", rule="date_format:Y-m-d")
     * @Query(key="end_time|结束时间", rule="date_format:Y-m-d")
     */
    public function recordOverview()
    {
        $validator_data = Context::get('validator.data');
        $start_time = $validator_data['start_time'] ?? '';
        $end_time = $validator_data['end_time'] ?? '';

        $DistributionSettlement = DistributionSettlement::query()->selectRaw("
        count(*) as total, 
        count(distinct(user_id)) as user_count,
        sum(pay_money) as pay_money
        ");
        $start_time && $DistributionSettlement->where('created_at', '>=', Timestamp::dateStart($start_time, 'PRC'));
        $end_time && $DistributionSettlement->where('created_at', '<=', Timestamp::dateEnd($end_time, 'PRC'));

        $data = $DistributionSettlement->limit(1)->get()->toArray();
        if (!empty($data)) {
            $data = current($data);
        }
        return $this->adminResponse(200, '请求成功', $data);

    }

    /**
     * @GetApi(path="get-rank-data", description="获取套餐初始数据")
     */
    public function getRankData()
    {
        $data = [
            'data' => DistributionService::defaultRankData(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }


    /**
     * @PostApi(path="create-code", description="新建子分销码")
     * @FormData(key="id|分销id", rule="required")
     * @FormData(key="distribution_code|分销码", rule="required")
     * @FormData(key="remark|备注", rule="")
     * @FormData(key="rank_data|套餐数据", rule="array")
     * @FormData(key="rank_data.*.rank_id|rank_id", rule="integer|required")
     * @FormData(key="rank_data.*.rank_name|rank_name", rule="required")
     * @FormData(key="rank_data.*.duration|duration", rule="required")
     * @FormData(key="rank_data.*.check|check", rule="required")
     * @FormData(key="rank_data.*.commission_rate|commission_rate", rule="required|integer|min:0")
     */
    public function createCode()
    {
        $validator_data = Context::get('validator.data');
        $distribution_code = $validator_data['distribution_code'];
        $id = $validator_data['id'];
        $rank_data = $validator_data['rank_data'];
        $remark = $validator_data['remark'];

        $distribution_code = strtoupper($distribution_code);
        $distribution_info_id = DistributionCode::query()->where('distribution_code', '=', $distribution_code)->value('id');
        if ($distribution_info_id) {
            return $this->adminResponse(400, '定制码不能在分销码中存在');
        }

        $user_id = DistributionInfo::query()->where('id', '=', $id)->value('user_id');
        if (empty($user_id)) {
            return $this->adminResponse(400, '分销id对应的用户id异常');
        }

        $code_insert = [
            'user_id' => $user_id,
            'distribution_code' => $distribution_code,
            'commission_rate' => DistributionCode::COMMISSION_RATE,
            'rank_data' => $rank_data,
            'remark' => $remark,
        ];
        DistributionCode::create($code_insert);
        return $this->adminResponse(200, '请求成功');
    }

    /**
     * @PostApi(path="update-code", description="修改子分销码的")
     * @FormData(key="id|id", rule="required")
     * @FormData(key="remark|备注", rule="")
     * @FormData(key="rank_data|套餐数据", rule="array")
     * @FormData(key="rank_data.*.rank_id|rank_id", rule="integer|required")
     * @FormData(key="rank_data.*.rank_name|rank_name", rule="required")
     * @FormData(key="rank_data.*.duration|duration", rule="required")
     * @FormData(key="rank_data.*.check|check", rule="required")
     * @FormData(key="rank_data.*.commission_rate|commission_rate", rule="required|integer|min:0|max:100")
     */
    public function updateCode()
    {
        $validator_data = Context::get('validator.data');
        $id = $validator_data['id'];
        $rank_data = $validator_data['rank_data'];
        $remark = $validator_data['remark'];

        $code_update = [
            'rank_data' => $rank_data,
            'remark' => $remark,
        ];
        DistributionCode::find($id)->update($code_update);
        return $this->adminResponse(200, '请求成功');
    }


    /**
     * @GetApi(path="get-code", description="获取分销码详情")
     * @Query(key="id|id", rule="")
     */
    public function showCode()
    {
        $validator_data = Context::get('validator.data');
        $id = $validator_data['id'];
        $distribution_code_data = DistributionCode::find($id)->toArray();

        return $this->adminResponse(200, '请求成功', $distribution_code_data);
    }

    /**
     * @GetApi(path="get-code-list", description="获取分销码列表")
     * @Query(key="id|分销id", rule="")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function showCodeList()
    {
        $validator_data = Context::get('validator.data');
        $id = Arr::get($validator_data, 'id', '');

        $user_id = DistributionInfo::query()->where('id', '=', $id)->value('user_id');
        if (empty($user_id)) {
            return $this->adminResponse(400, '分销id对应的用户id异常');
        }

        $distribution_code_list = DistributionCode::query()
            ->where('user_id', '=', $user_id)
            ->orderBy('id', 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();

        return $this->adminResponse(200, '请求成功', $distribution_code_list);

    }


}
