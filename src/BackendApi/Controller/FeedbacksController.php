<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Website\Common\Model\Feedback;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\DeleteApi;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\Header;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\Arr;

/**
 * @ApiController(prefix="feedbacks", tag="用户反馈", server="website-common-backend-api")
 */
class FeedbacksController extends AdminController
{
    /**
     * @GetApi(path="overview", description="看板")
     * @Header(key="Authorization|接口访问凭证", rule="required")
     * @Query(key="time_start|开始时间", rule="integer")
     * @Query(key="time_end|结束时间", rule="integer")
     */
    public function overview()
    {
        $validator_data = Context::get('validator.data');
        $time_start = (int) Arr::get($validator_data,'time_start', 0);
        !$time_start && $time_start = Carbon::now()->subDays(30)->timestamp;
        $time_end = (int) Arr::get($validator_data,'time_end', 0);
        !$time_end && $time_end = time();

        $count = Feedback::query()
            ->where('created_at', '>=', $time_start)
            ->where('created_at', '<=', $time_end)
            ->count();

        $data = [
            'count' => $count,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="", description="列表")
     * @Header(key="Authorization|接口访问凭证", rule="required")
     * @Query(key="user_id|用户ID", rule="")
     * @Query(key="email|用户账号", rule="")
     * @Query(key="name|用户名", rule="")
     * @Query(key="level|会员等级", rule="")
     * @Query(key="content|反馈内容", rule="")
     * @Query(key="time_start|反馈时间（起）", rule="integer")
     * @Query(key="time_end|反馈时间（止）", rule="integer")
     * @Query(key="order_by|排序字段", rule="in:create_time")
     * @Query(key="direction|排序方式", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $Feedback = Feedback::query()
            ->select(Db::raw('feedbacks.id, feedbacks.content, feedbacks.created_at as create_time, feedbacks.user_id as account_id, users.account as email, users.username as name, ranks.rank_name as level'))
            ->join('users', 'feedbacks.user_id', '=', 'users.id')
            ->join('ranks', 'users.rank_id', '=', 'ranks.id');

        ($user_id = Arr::get($validator_data, 'user_id', '')) &&
        $Feedback->where('feedbacks.user_id', $user_id);

        ($email = Arr::get($validator_data, 'email', '')) &&
        $Feedback->where('users.account', $email);

        ($name = Arr::get($validator_data, 'name', '')) &&
        $Feedback->where('users.username', 'like', "%{$name}%");

        ($level = Arr::get($validator_data, 'level', '')) &&
        $Feedback->where('ranks.rank_name', $level);

        ($content = Arr::get($validator_data, 'content', '')) &&
        $Feedback->where('feedbacks.content', 'like', "%{$content}%");

        ($time_start = (int) Arr::get($validator_data, 'time_start', 0)) &&
        $Feedback->where('feedbacks.created_at', '>=', $time_start);

        ($time_end = (int) Arr::get($validator_data, 'time_end', 0)) &&
        $Feedback->where('feedbacks.created_at', '<=', $time_end);

        // 排序方式
        $direction = Arr::get($validator_data, 'direction') ?: 'desc';
        $order_by = Arr::get($validator_data, 'order_by') ?: 'id';
        $order_by === 'create_time' && $order_by = 'created_at';
        if ($order_by) {
            $Feedback->orderBy("feedbacks.{$order_by}", $direction);
        }

        $data = $Feedback->paginate((int) Arr::get($validator_data, 'per_page', 10))->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @DeleteApi(path="", description="删除")
     * @FormData(key="id|ids", rule="required|array")
     */
    public function destory()
    {
        $validator_data = Context::get('validator.data');

        Feedback::query()
            ->whereIn('id', $validator_data['id'])
            ->delete();
        return $this->adminResponse(200, '请求成功');
    }
}
