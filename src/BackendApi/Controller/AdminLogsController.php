<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Carbon\Carbon;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Router\DispatcherFactory;
use Hyperf\HttpServer\Router\Handler;
use Hyperf\Paginator\LengthAwarePaginator;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Arr;
use Hyperf\Context\Context;
use Lete\Base\Service\RouterData;
use Lete\Base\Utils\Timestamp;
use Lete\MongoDB\MongoClient\MongoDb;

/**
 * @ApiController(prefix="admin/logs", tag="操作日志", server="website-common-backend-api")
 */
class AdminLogsController extends AdminController
{
    /**
     * @Inject
     * @var ConfigInterface
     */
    protected $config;

    /**
     * @GetApi(path="", description="列表")
     * @Query(key="user_id|账号id", rule="integer")
     * @Query(key="username|账号", rule="")
     * @Query(key="module|操作项目", rule="")
     * @Query(key="function|功能", rule="")
     * @Query(key="method|请求方式", rule="")
     * @Query(key="path|路径", rule="")
     * @Query(key="path_regex|路径（正则）", rule="")
     * @Query(key="ip|IP地址", rule="ip")
     * @Query(key="start_date|开始日期(Y-m-d)", rule="date_format:Y-m-d")
     * @Query(key="end_date|结束日期(Y-m-d)", rule="date_format:Y-m-d")
     * @Query(key="log_status|状态[0,1]", rule="in:0,1")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|分页大小", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $adminLogs = MongoDb::connection($this->config->get('admin.log.connection'))
            ->collection($this->config->get('admin.log.collection'));
        $pipeline = [];
        $match = [
            '$and' => [],
        ];

        ($user_id = (int)Arr::get($validator_data,'user_id', 0)) &&
        $match['$and'][] = ['user_id' => $user_id];

        ($username = Arr::get($validator_data,'username', '')) &&
        $match['$and'][] = ['username' => $username];

        ($module = Arr::get($validator_data,'module', '')) &&
        $match['$and'][] = ['module' => $module];

        ($function = Arr::get($validator_data,'function', '')) &&
        $match['$and'][] = ['function' => $function];

        ($method = Arr::get($validator_data,'method', '')) &&
        $match['$and'][] = ['method' => $method];

        ($path = Arr::get($validator_data,'path', '')) &&
        $match['$and'][] = ['path' => $path ];

        ($path_regex = Arr::get($validator_data,'path_regex', '')) &&
        $match['$and'][] = ['path' => ['$regex' => $path_regex]];

        ($ip = Arr::get($validator_data,'ip', '')) &&
        $match['$and'][] = ['ip' => $ip];

        $start_date = Arr::get($validator_data,'start_date');
        $end_date = Arr::get($validator_data,'end_date');
        if ($start_date || $end_date) {
            $created_at = [];
            $start_date && $created_at['$gte'] = Timestamp::dateStart($start_date,'PRC');
            $end_date && $created_at['$lte'] = Timestamp::dateEnd($end_date,'PRC');
            $match['$and'][] = ['created_at' => $created_at];
        }

        $log_status = Arr::get($validator_data,'log_status', '');
        if ($log_status !== '') {
            $log_status == 1 &&
            $match['$and'][] = ['output.code' => 200];
            $log_status == 0 &&
            $match['$and'][] = ['output.code' => [
                '$ne' => 200
            ]];
        }

        $page = (int) Arr::get($validator_data,'page') ?: 1;
        $per_page = (int) Arr::get($validator_data,'per_page') ?: 10;

        $match['$and'] && array_unshift($pipeline, ['$match' => $match]);
        $pipeline = array_merge($pipeline, [
            ['$sort' => ['_id' => -1]],
            ['$skip' => ($page - 1) * $per_page],
            ['$limit' => $per_page],
        ]);
        $data = (new LengthAwarePaginator(
            array_map(function ($log) {
                $log['_id'] = $log['_id']->jsonSerialize()['$oid'];
                $log['log_status'] = $log['output']['code'] === 200 ? 1 : 0;
                $log['created_at'] = Carbon::createFromTimestamp($log['created_at'], 'PRC')->toDateTimeString();
                return $log;
            }, $adminLogs->aggregate($pipeline)),
            $adminLogs->countDocuments($match['$and'] ? $match : []),
            $per_page,
            $page,
            [
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'pageName' => 'page',
                'query' => $this->request->all()
            ]
        ))->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="options", description="选项")
     */
    public function options()
    {
        $data = [
            'methods' => [
                ['value' => '', 'label' => '全部'],
                ['value' => 'GET', 'label' => 'GET'],
                ['value' => 'POST', 'label' => 'POST'],
                ['value' => 'DELETE', 'label' => 'DELETE'],
                ['value' => 'PUT', 'label' => 'PUT'],
            ],
            'modules' => $this->getModules(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getModules()
    {
        $data = [];
        foreach (['backend-api', 'website-common-backend-api', 'website-self-backend-api'] as $server_name) {
            $data = array_merge($data, RouterData::modules($server_name));
        }
        return $data;
    }
}
