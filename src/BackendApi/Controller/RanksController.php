<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Website\Common\Model\Rank;
use Website\Common\Service\RankService;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Apidog\Validation\Validation;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Arr;
use Lete\Base\Utils\Security;
use Website\Common\Utils\Website;

/**
 * @ApiController(prefix="ranks", tag="等级套餐", server="website-common-backend-api")
 */
class RanksController extends AdminController
{
    /**
     * @GetApi(path="", description="列表")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');

        $per_page = (int) ($validator_data['per_page'] ?? 10);
        $data = Rank::query()
            ->paginate($per_page)
            ->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PostApi(path="", description="创建")
     * @FormData(key="rank_name|套餐名称", rule="required")
     * @FormData(key="duration|套餐时长", rule="required|cb_ruleCheckDuration")
     * @FormData(key="price|价格", rule="required|numeric|min:0|max:99999999.99")
     * @FormData(key="original_price|原价", rule="required|numeric|min:0|max:99999999.99")
     * @FormData(key="first_price|首购价格", rule="nullable|numeric|min:0|max:99999999.99")
     * @FormData(key="trial_days|试用天数（首次购买）", rule="nullable|integer|min:0")
     * @FormData(key="allowed_buy|允许购买（前台）", rule="required|boolean")
     * @FormData(key="is_visibled|前台展示", rule="required|boolean")
     * @FormData(key="max_online_users|最大同时在线人数", rule="nullable|integer|min:0")
     * @FormData(key="max_team_members|团队人数上限", rule="nullable|integer|min:0")
     * @FormData(key="remark|备注", rule="nullable|string")
     */
    public function store()
    {
        return $this->save();
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|等级套餐ID", rule="required|integer")
     * @FormData(key="price|价格", rule="sometimes|required|numeric|min:0|max:99999999.99")
     * @FormData(key="original_price|原价", rule="sometimes|required|numeric|min:0|max:99999999.99")
     * @FormData(key="first_price|首购价格", rule="nullable|numeric|min:0|max:99999999.99")
     * @FormData(key="trial_days|试用天数（首次购买）", rule="nullable|integer|min:0")
     * @FormData(key="allowed_buy|允许购买（前台）", rule="boolean")
     * @FormData(key="is_visibled|前台展示", rule="boolean")
     * @FormData(key="max_online_users|最大同时在线人数", rule="nullable|integer|min:0")
     * @FormData(key="max_team_members|团队人数上限", rule="nullable|integer|min:0")
     * @FormData(key="permission|权限", rule="sometimes|required|json|cb_ruleCheckPermission")
     * @FormData(key="remark|备注", rule="nullable|string")
     */
    public function update()
    {
        return $this->save();
    }

    /**
     * 验证规则-时长
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckDuration($attribute, $value)
    {
        return RankService::ruleCheckDuration($attribute, $value);
    }

    /**
     * 验证规则-权限
     * @param $attribute
     * @param $value
     * @return bool|string
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function ruleCheckPermission($attribute, $value)
    {
        $error = Security::validJsonKeys($value, array_keys(RankService::structurePermission()), '套餐权限');
        if (!is_bool($error)) {
            return $error;
        }

        $rules = [];
        foreach (Arr::dot(RankService::structurePermission()) as $key => $key_value) {
            $rules["{$key}|{$key}"] = 'required|' . gettype($key_value);
        }
        $result = ApplicationContext::getContainer()
            ->get(Validation::class)
            ->check($rules, $value, $this);
        if ($result[0] === null) {
            return implode(PHP_EOL, $result[1]);
        }

        return true;
    }

    /**
     * 保存
     * @return array
     */
    protected function save()
    {
        $validator_data = Context::get('validator.data');
        if (isset($validator_data['id'])) {
            $Rank = Rank::query()->find($validator_data['id']);
            if (!$Rank) {
                return $this->adminResponse(404, '套餐不存在');
            }
        }

        $attributes = $validator_data;

        // 相同套餐名称，权限一致
        if (empty($attributes['id']) && empty($attributes['permission']) && !empty($attributes['rank_name'])) {
            $SameRank = Rank::query()
                ->where('rank_name', $attributes['rank_name'])
                ->first(['permission']);
            $attributes['permission'] = $SameRank ? $SameRank->permission : RankService::structurePermission();
        }

        try {
            Db::beginTransaction();

            if (isset($validator_data['id'])) {
                $Rank->update($attributes);
            } else {
                $Rank = Rank::create($attributes);
            }

            RankService::syncStripeProduct($Rank->id);

            Db::commit();
            return $this->adminResponse(200, '请求成功', [
                'rank' => $Rank->refresh()->toArray()
            ]);
        } catch (\Throwable $e) {
            Db::rollBack();
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @GetApi(path="{id:\d+}", description="查看")
     * @Path(key="id|等级套餐ID", rule="required|integer|exists:ranks")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');

        $data = [
            'rank' => Rank::query()
                ->find($validator_data['id'])
                ->toArray(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="permissions-structure", description="权限结构")
     */
    public function permissionsStructure()
    {
        $data = [
            'structure' => Website::config()->get('website.rank.structure', []),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="integration", description="按套餐名称重新整合")
     */
    public function integration()
    {
        $data = [
            'ranks' => RankService::integration(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="quota-permissions", description="次数权限")
     */
    public function quotaPermissions()
    {
        $quota_permissions = [];
        foreach (RankService::getPermissions() as $permission_name => $permission) {
            $quota_permissions[] = [
                'permission_name' => $permission_name,
                'title' => $permission['title'],
            ];
        }
        $data = [
            'quota_permissions' => $quota_permissions,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }
}
