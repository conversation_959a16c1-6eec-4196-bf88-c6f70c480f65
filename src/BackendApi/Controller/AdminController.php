<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerInterface;
use Qbhy\HyperfAuth\AuthManager;

abstract class AdminController
{
    /**
     * @Inject
     * @var ContainerInterface
     */
    protected $container;
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;
    /**
     * @Inject
     * @var ResponseInterface
     */
    protected $response;

    /**
     * 定义Controller默认查询的 Model
     *
     * @var string
     */
    protected string $model;

    /**
     * @param int $code
     * @param string $message
     * @param array $data
     * @return array
     */
    protected function adminResponse(int $code, string $message, array $data = [])
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => (object) $data,
        ];
    }
}
