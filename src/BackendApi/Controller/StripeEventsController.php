<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Carbon\Carbon;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\Header;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\Paginator\LengthAwarePaginator;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Str;
use Lete\MongoDB\MongoClient\MongoDb;

/**
 * @ApiController(prefix="stripe/events", tag="sripe事件管理", server="website-common-backend-api")
 */
class StripeEventsController extends AdminController
{
    /**
     * @GetApi(path="", description="事件列表")
     * @Query(key="user_id|会员ID", rule="")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $StripeEvents = MongoDb::collection('stripe_events');
        $pipeline = [];
        $match = [
            '$and' => [],
        ];

        ($user_id = (int)Arr::get($validator_data,'user_id', 0)) &&
        $match['$and'][] = ['user_id' => $user_id];

        $page = (int) (Arr::get($validator_data,'page') ?: 1);
        $per_page = (int) (Arr::get($validator_data,'per_page') ?: 10);

        $match['$and'] && array_unshift($pipeline, ['$match' => $match]);
        $pipeline = array_merge($pipeline, [
            ['$project' => [
                '_id' => 0,
                'user_id' => 1,
				'id' => 1,
                'type' => 1,
                'created' => 1,
                'data' => 1,
            ]],
            ['$sort' => [
                'created' => -1,
                '_id' => -1,
            ]],
            ['$skip' => ($page - 1) * $per_page],
            ['$limit' => $per_page],
        ]);
        $data = (new LengthAwarePaginator(
            array_map(function ($event) {
                $event['created'] = Carbon::createFromTimestamp($event['created'], 'PRC')->toDateTimeString();
                $method = 'handle'.Str::studly(str_replace('.', '_', $event['type'])).'Content';
                $event['content'] = method_exists($this, $method) ? $this->{$method}($event['data']['object']) : '';
                unset($event['data']);
                return $event;
            }, $StripeEvents->aggregate($pipeline)),
            $StripeEvents->countDocuments($match['$and'] ? $match : []),
            $per_page,
            $page,
            [
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'pageName' => 'page',
                'query' => $this->request->all()
            ]
        ))->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoiceCreatedContent($object)
    {
        return "创建了一个草稿账单 {$object['id']}";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoiceFinalizedContent($object)
    {
        $total = $object['total'] / 100;
        return "确定了给 {$object['id']} 开具的一个金额为 {$object['currency']}{$total} 的草稿账单";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoiceFinalizedFailedContent($object)
    {
        return "草稿账单 {$object['id']} 确定失败，原因是 {$object['last_finalization_error']}";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoiceDeletedContent($object)
    {
        return "删除了一个草稿账单 {$object['id']}";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoiceUpdatedContent($object)
    {
        return "更改了 {$object['id']} 的账单";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleInvoicePaidContent($object)
    {
        $amount_paid = $object['amount_paid'] / 100;
        return "支付了{$object['id']} {$object['currency']}{$amount_paid} 的账单";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleCustomerSubscriptionCreatedContent($object)
    {
        return "尝试订阅 {$object['id']}";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleCustomerSubscriptionDeletedContent($object)
    {
        return "取消了 {$object['id']} 订阅";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handleCustomerSubscriptionUpdatedContent($object)
    {
        return "更改了 {$object['id']} 订阅";
    }

    /**
     * @param $event
     * @return string
     */
    protected function handlePaymentMethodAttachedContent($object)
    {
        $content = "添加了一个 {$object['type']} 的支付方式";
        $object['type'] === 'card' && $content .= "，尾号为 {$object['card']['last4']}";
        return $content;
    }
}
