<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\DbConnection\Db;
use Website\Common\Exception\TeamException;
use Website\Common\Model\DailyUser;
use Website\Common\Model\FrontendApiSecretKey;
use Website\Common\Model\User;
use Website\Common\Model\UserQuota;
use Website\Common\Model\UserSocialite;
use Website\Common\Service\OrderService;
use Website\Common\Service\RankService;
use Website\Common\Service\TeamService;
use Website\Common\Service\UserService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\FormData;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\Database\Model\Builder;
use Hyperf\Paginator\LengthAwarePaginator;
use Hyperf\Utils\Arr;
use Hyperf\Utils\Collection;
use Hyperf\Utils\Coroutine;
use Hyperf\Utils\Exception\ParallelExecutionException;
use Hyperf\Utils\Parallel;
use Hyperf\Utils\Str;
use Lete\Base\Utils\Data;
use Lete\Base\Utils\Timestamp;
use Lete\MongoDB\MongoClient\MongoDb;

/**
 * @ApiController(prefix="users", tag="会员管理", server="website-common-backend-api")
 */
class UsersController extends AdminController
{
    protected string $model = User::class;

    /**
     * @GetApi(path="overview", description="看板")
     * @Query(key="start_time|开始时间", rule="date_format:Y-m-d")
     * @Query(key="end_time|结束时间", rule="date_format:Y-m-d")
     */
    public function overview()
    {
        $validator_data = Context::get('validator.data');
        $start_time = (string) Arr::get($validator_data,'start_time', '');
        if (!$start_time) {
            $min_created_at = User::query()->min('created_at');
            $start_time = $min_created_at ? Carbon::createFromTimestamp($min_created_at)->format('Y-m-d') : Carbon::today()->format('Y-m-d');
        }
        $end_time = (string) Arr::get($validator_data,'end_time', '');
        !$end_time && $end_time = Carbon::today()->format('Y-m-d');
        $start_timetamp = Timestamp::dateStart($start_time);
        $end_timetamp = Timestamp::dateEnd($end_time);

        $Parallel = new Parallel(5);

        // 新增会员
        $new_users = null;
        // 每日用户统计的数据
        $daily_users = [];
        $today = Carbon::today()->format('Y-m-d');
        $Parallel->add(function () use (&$new_users, &$daily_users, $today, $start_time, $end_time) {
            $DailyUser = DailyUser::query()
                ->where('date', '>=', $start_time)
                ->where('date', '<=', $end_time)
                ->get(['date', 'total', 'new_users', 'active_users', 'active_user_ids', 'inactive_users', 'active_seniors', 'active_seniors_rate']);
            if ($end_time === $today) {
                $daily_users_today = array_merge(['date' => $today], UserService::dailyStatistics($today));
                $DailyUser->add(new Collection($daily_users_today));
            }
            $daily_users = $DailyUser->toArray();
            $new_users = Data::toTimeline($start_time, $end_time, 'date', $daily_users, null, 'new_users');
            return Coroutine::id();
        });

        // 新增xxx（会员等级）会员
        // 注册时间 = 【所选时间范围】，等级 = xxx，统计所有的会员数
        $new_users_rank = [];
        $Parallel->add(function () use (&$new_users_rank, $start_time, $end_time, $start_timetamp, $end_timetamp) {
            foreach (RankService::integration() as $rank) {
                $new_users_rank[] = array_merge([
                    'title' => $rank['rank_name'],
                ], Data::toTimeline($start_time, $end_time, 'vip_started_at', User::query()
                    ->where('vip_started_at', '>=', $start_timetamp)
                    ->where('vip_started_at', '<=', $end_timetamp)
                    ->whereIn('rank_id', explode(',', $rank['id']))
                    ->get(['vip_started_at'])
                    ->toArray()
                ));
            }
        });

        // 新增会员订阅
        // 订阅开始时间 = 【所选时间范围】， 统计所有的会员数
        $new_subscription = 0;
        $Parallel->add(function () use (&$new_subscription, $start_timetamp, $end_timetamp) {
            $new_subscription = User::query()
                ->where('subscription_started_at', '>=', $start_timetamp)
                ->where('subscription_started_at', '<=', $end_timetamp)
                ->count();
        });

        // 新增xxx（会员等级）订阅
        // 订阅开始时间 = 【所选时间范围】，等级=xxx，统计所有的会员数
        $new_subscription_rank = [];
        $Parallel->add(function () use (&$new_subscription_rank, $start_timetamp, $end_timetamp) {
            foreach (RankService::integration() as $rank) {
                $new_subscription_rank[] = [
                    'title' => $rank['rank_name'],
                    'value' => User::query()
                        ->where('subscription_started_at', '>=', $start_timetamp)
                        ->where('subscription_started_at', '<=', $end_timetamp)
                        ->whereIn('rank_id', explode(',', $rank['id']))
                        ->count(),
                ];
            }
        });

        // 到期会员数
        // 会员到期时间 = 【所选时间范围】， 统计所有的会员数
        $expired_users = 0;
        $Parallel->add(function () use (&$expired_users, $start_timetamp, $end_timetamp) {
            $expired_users = User::query()
                ->where('vip_expired_at', '>=', $start_timetamp)
                ->where('vip_expired_at', '<=', $end_timetamp)
                ->count();
        });

        try {
            $Parallel->wait();
        } catch (ParallelExecutionException $e) {
            return $this->adminResponse(400, $e->getMessage());
        }

        // 已活跃会员
        // 每日统计当天有使用网站会员记录数。并记录活跃用户id，统计多少天内活跃用户时，根据id去重计算活跃总数。
        $active_users = Data::toTimeline($start_time, $end_time, 'date', $daily_users, null, 'active_users');
        if (count($daily_users) === 1) {
            $active_users['total'] = $daily_users[0]['active_users'];
        } elseif (count($daily_users) > 1) {
            $active_user_ids = [];
            foreach ($daily_users as $item) {
                $active_user_ids = array_merge($active_user_ids, $item['active_user_ids']);
            }
            $active_users['total'] = count(array_unique($active_user_ids));
        }

        // 未活跃会员数
        // 每日统计当天累计会员数和位活跃会员数。未活跃会员=累计会员数-已活跃会员数。所选时间段内未活跃会员统计=所选时间段最晚那天累计会员数-已活跃会员数。
        $inactive_users = 0;
        if (count($daily_users) === 1) {
            $inactive_users = $daily_users[0]['inactive_users'];
        } elseif (count($daily_users) > 1) {
            $inactive_users = $daily_users[count($daily_users) - 1]['total'] - $active_users['total'];
        }

        // 老会员活跃数
        // 每日统计老会员活跃=已活跃用户-今天注册的活跃用户。所选时间范围老会员活跃=所选时间段活跃用户-所选时间段注册的活跃用户。
        $active_seniors = 0;
        if (count($daily_users) === 1) {
            $active_seniors = $daily_users[0]['active_seniors'];
        } elseif (count($daily_users) > 1) {
            $new_users_quantity = 0;
            foreach ($daily_users as $item) {
                $new_users_quantity += $item['new_users'];
            }
            $active_seniors = $active_users['total'] - $new_users_quantity;
        }

        // 老会员活跃率
        // 老会员活跃率 = 老会员活跃数 / 已活跃会员 ，保留两位小数即可
        $active_seniors_rate = 0;
        if (count($daily_users) === 1) {
            $active_seniors_rate = $daily_users[0]['active_seniors_rate'];
        } elseif (count($daily_users) > 1) {
            $active_seniors_rate = $active_users['total'] > 0 ? round($active_seniors / $active_users['total'], 2) : 0;
        }

        $data = [
            'new_users' => $new_users,
            'new_users_rank' => $new_users_rank,
            'new_subscription' => $new_subscription,
            'new_subscription_rank' => $new_subscription_rank,
            'expired_users' => $expired_users,
            'active_users' => $active_users,
            'inactive_users' => $inactive_users,
            'active_seniors' => $active_seniors,
            'active_seniors_rate' => $active_seniors_rate,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="", description="列表")
     * @Query(key="account|账号", rule="")
     * @Query(key="account_status|账号状态", rule="integer")
     * @Query(key="username|用户名", rule="")
     * @Query(key="email|邮箱", rule="")
     * @Query(key="phone_region|手机归属地", rule="")
     * @Query(key="last_ip|登录IP", rule="")
     * @Query(key="last_location|IP地址", rule="")
     * @Query(key="source|来源", rule="")
     * @Query(key="language|语言", rule="")
     * @Query(key="timezone|时区", rule="")
     * @Query(key="device_language|设备语言", rule="")
     * @Query(key="device_timezone|设备时区", rule="")
     * @Query(key="rank_id|等级套餐", rule="")
     * @Query(key="remark|备注", rule="")
     * @Query(key="subscription_status|当前订阅状态", rule="")
     * @Query(key="card_last4|付款卡号", rule="")
     * @Query(key="distribution_code|分销码", rule="")
     * @Query(key="created_at_start|注册时间（起）", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|注册时间（止）", rule="date_format:Y-m-d")
     * @Query(key="last_at_start|最后一次登录时间（起）", rule="date_format:Y-m-d")
     * @Query(key="last_at_end|最后一次登录时间（止）", rule="date_format:Y-m-d")
     * @Query(key="vip_started_at_start|会员开通时间（起）", rule="date_format:Y-m-d")
     * @Query(key="vip_started_at_end|会员开通时间（止）", rule="date_format:Y-m-d")
     * @Query(key="vip_expired_at_start|会员到期时间（起）", rule="date_format:Y-m-d")
     * @Query(key="vip_expired_at_end|会员到期时间（止）", rule="date_format:Y-m-d")
     * @Query(key="subscription_started_at_start|订阅开始时间（起）", rule="date_format:Y-m-d")
     * @Query(key="subscription_started_at_end|订阅开始时间（止）", rule="date_format:Y-m-d")
     * @Query(key="subscription_next_deduct_at_start|下次付款时间（起）", rule="date_format:Y-m-d")
     * @Query(key="subscription_next_deduct_at_end|下次付款时间（止）", rule="date_format:Y-m-d")
     * @Query(key="user_value_start|会员价值（起）", rule="numeric|min:0")
     * @Query(key="user_value_end|会员价值（止）", rule="numeric|min:0")
     * @Query(key="order_by|排序字段", rule="")
     * @Query(key="direction|排序方式", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|分页大小", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');

        try {
            $Builder = $this->beforeBuilder($validator_data);
            $validator_data = $this->beforeIndex($validator_data);
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }

        $data = $Builder->paginate(intval(($validator_data['per_page'] ?? 10)))
            ->toArray();
        if ($data['data']) {
            $data['data'] = array_map(function ($item) {
                $item['subscription_platform_text'] = OrderService::paymentPlatformText($item['subscription_platform']);

                // 次数权限
                $quota = [];
                $permissions = RankService::flattenPermission($item['rank']['permission'], '');
                $quota_permission_obj = array_column($item['quota'], null, 'permission_name');
                foreach (RankService::getPermissions() as $permission_name => $permission) {
                    $quota_item = [
                        'user_id' => $item['id'],
                        'title' => $permission['title'],
                        'permission_name' => $permission_name,
                        'remaining' => 0,
                        'used' => $quota_permission_obj[$permission_name]['used'] ?? 0,
                        'limit' => $permissions[$permission_name],
                    ];
                    $quota_item['remaining'] = intval($quota_item['limit'] - $quota_item['used']);
                    $quota_item['remaining'] < 0 && $quota_item['remaining'] = 0;
                    $quota[] = $quota_item;
                }
                $item['quota'] = $quota;

                $item['socialites'] = array_map(function ($socialite_item) {
                    $socialite_item['title'] = UserSocialite::getTitle($socialite_item['type']);
                    return $socialite_item;
                }, $item['socialites']);
                return $item;
            }, $data['data']);
        }

        $data = $this->afterIndex($data);

        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * 前置 Builder
     * @param $validator_data
     * @return Builder
     * @throws \Exception
     */
    protected function beforeBuilder($validator_data): Builder
    {
        $Builder = $this->getBuilder($validator_data);

        return $Builder;
    }

    /**
     * index 前置操作
     * @param $validator_data
     * @return mixed
     */
    protected function beforeIndex($validator_data)
    {
        return $validator_data;
    }

    /**
     * index 后置操作
     * @param array $data
     * @return array
     */
    protected function afterIndex(array $data): array
    {
        return $data;
    }

    /**
     * @PutApi(path="{id:\d+}", description="编辑")
     * @Path(key="id|Id", rule="required|integer")
     * @FormData(key="username|用户名", rule="sometimes|required")
     * @FormData(key="remark|备注信息", rule="")
     * @FormData(key="account_status|账号状态", rule="sometimes|required|integer|in:0,1,2")
     * @FormData(key="password|密码", rule="sometimes|required|string|min:32")
     * @FormData(key="comfirm_password|确认密码", rule="required_with:password|same:password")
     */
    public function update()
    {
        return $this->save();
    }

    /**
     * 保存
     * @return array
     */
    protected function save()
    {
        $validator_data = Context::get('validator.data');

        if (isset($validator_data['id'])) {
            $User = User::query()->find($validator_data['id']);
            if (!$User) {
                return $this->adminResponse(404, '用户不存在');
            }
        }

        $attributes = $validator_data;
        if (isset($attributes['comfirm_password'])) {
            unset($attributes['comfirm_password']);
        }

        if (isset($validator_data['id'])) {
            $User->update($attributes);
        } else {
            $User = User::create($attributes);
        }

        // 激活账号
        if ((isset($validator_data['account_status']) && $validator_data['account_status'] === User::ACCOUNT_STATUS_ACTIVATED) ||
            (isset($validator_data['password']) && $User->account_status === User::ACCOUNT_STATUS_ACTIVATED)) {
            UserSocialite::updateOrCreate([
                'type' => UserSocialite::TYPE_EMAIL,
                'socialite_id' => $User->email ?: $User->account,
                'user_id' => $User->id,
            ]);
        }

        return $this->adminResponse(200, '请求成功', [
            'user' => User::query(false, true)
                ->with('rank:id,rank_name')
                ->with('socialites:user_id,type')
                ->find($User->id)
                ->toArray(),
        ]);
    }

    /**
     * @GetApi(path="{id:\d+}", description="查看")
     * @Path(key="id|Id", rule="required|integer")
     */
    public function show()
    {
        $validator_data = Context::get('validator.data');

         $User = User::query(false, true)
             ->with('rank:id,rank_name')
             ->with('socialites:user_id,type')
             ->with('quota:user_id,permission_name,limit,remaining,used')
             ->find($validator_data['id']);

        if (!$User) {
            return $this->adminResponse(404, '账号不存在');
        }

        $user = $User->toArray();
        $user['quota'] = array_map(function ($quota_item) {
            $quota_item['title'] = RankService::getPermissionTitle($quota_item['permission_name']);
            return $quota_item;
        }, $user['quota']);
        $user['socialites'] = array_map(function ($socialite_item) {
            $socialite_item['title'] = UserSocialite::getTitle($socialite_item['type']);
            return $socialite_item;
        }, $user['socialites']);

        /** 用户订阅信息 */
        $subcription = UserService::getLastSubcription($User->id);
        if ($subcription) {
            foreach (['next_period_start', 'start_date'] as $field) {
                $subcription[$field] = Carbon::createFromTimestamp($subcription[$field])->toDateTimeString();
            }
        }

        $my_team_members = [];
        $teams = [];
        if (config('website.team')) {
            /** 我的团队成员 */
            try {
                $MyTeam = TeamService::myTeam($user['id']);
                $my_team_members = TeamService::getMembers($MyTeam->id);
                if ($my_team_members) {
                    $my_team_members = array_slice($my_team_members, 1);
                }
            } catch (TeamException $e) {
            }

            /** 我加入的团队 */
            $teams = TeamService::getTeams($user['id']);
        }


        $data = [
            'user' => $user,
            'subcription' => $subcription,
            'my_team_members' => $my_team_members,
            'teams' => $teams,
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="{id:\d+}/login-logs", description="登录日志")
     * @Path(key="id|Id", rule="required|integer")
     * @Query(key="login_status|状态", rule="integer|in:0,1")
     * @Query(key="ip|登录IP", rule="string|min:3")
     * @Query(key="ip_location|IP地址", rule="string|min:2")
     * @Query(key="created_at_start|登录时间（起）", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|登录时间（止）", rule="date_format:Y-m-d")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function loginLogs()
    {
        $validator_data = Context::get('validator.data');
        $UserLoginLogs = MongoDb::collection('user_login_logs');
        $pipeline = [];
        $match = [
            '$and' => [
                ['user_id' => (int) $validator_data['id']],
            ],
        ];

        $login_status = Arr::get($validator_data, 'login_status', '');
        $login_status !== '' && $match['$and'][] = ['login_status' => (bool) $login_status];

        ($ip = Arr::get($validator_data,'ip')) &&
        $match['$and'][] = ['ip' => ['$regex' => $ip]];

        ($ip_location = Arr::get($validator_data,'ip_location')) &&
        $match['$and'][] = ['ip_location' => ['$regex' => $ip_location, '$options' => 'i']];

        $created_at_start = Arr::get($validator_data,'created_at_start');
        $created_at_end = Arr::get($validator_data,'created_at_end');
        if ($created_at_start || $created_at_end) {
            $created_at = [];
            $created_at_start && $created_at['$gte'] = Timestamp::dateStart($created_at_start,'PRC');
            $created_at_end && $created_at['$lte'] = Timestamp::dateEnd($created_at_end,'PRC');
            $match['$and'][] = ['created_at' => $created_at];
        }

        $page = (int) Arr::get($validator_data,'page') ?: 1;
        $per_page = (int) Arr::get($validator_data,'per_page') ?: 10;

        $match['$and'] && array_unshift($pipeline, ['$match' => $match]);
        $pipeline = array_merge($pipeline, [
            ['$sort' => ['created_at' => -1]],
            ['$skip' => ($page - 1) * $per_page],
            ['$limit' => $per_page],
        ]);
        $data = (new LengthAwarePaginator(
            array_map(function ($log) {
                $log['_id'] = $log['_id']->jsonSerialize()['$oid'];
                $log['created_at'] = Carbon::createFromTimestamp($log['created_at'])->toDateTimeString();
                return $log;
            }, $UserLoginLogs->aggregate($pipeline)),
            $UserLoginLogs->countDocuments($match['$and'] ? $match : []),
            $per_page,
            $page,
            [
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'pageName' => 'page',
                'query' => $this->request->all()
            ]
        ))->toArray();
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PostApi(path="generate-frontend-api-secret-key", description="创建前台接口密钥")
     * @FormData(key="user_id|用户id", rule="required|exists:users,id")
     */
    public function generateFrontendApiSecretKey()
    {
        $validator_data = Context::get('validator.data');

        $secret_key = FrontendApiSecretKey::query()
            ->where('user_id', $validator_data['user_id'])
            ->value('secret_key');
        if (!$secret_key) {
            $secret_key = FrontendApiSecretKey::generateSecretKey($validator_data['user_id']);
        }

        return $this->adminResponse(200, '请求成功', [
            'secret_key' => $secret_key,
        ]);
    }

    /**
     * @PostApi(path="create-account", description="创建前台账号")
     * @FormData(key="account|账号", rule="required")
     * @FormData(key="password|密码", rule="required")
     * @FormData(key="username|用户名", rule="")
     * @FormData(key="company|公司", rule="")
     * @FormData(key="phone_number|手机号码", rule="numeric")
     * @FormData(key="remark|备注信息", rule="")
     */
    public function createAccount()
    {
        $validator_data = Context::get('validator.data');
        $others = [];

        if (!filter_var($validator_data['account'], FILTER_VALIDATE_EMAIL)) {
            return $this->adminResponse(400, '会员账号必须是邮箱地址');
        }
        if (!empty($validator_data['username'])) {
            $others['username'] = $validator_data['username'];
        }
        if (!empty($validator_data['company'])) {
            $others['company'] = $validator_data['company'];
        }
        if (!empty($validator_data['phone_number'])) {
            $others['phone_number'] = $validator_data['phone_number'];
        }
        if (!empty($validator_data['remark'])) {
            $others['remark'] = $validator_data['remark'];
        }
        $return = UserService::backendSignupByEmail($validator_data['account'], $validator_data['password'], $others);
        if ($return['type'] == false) {
            return $this->adminResponse(400, $return['msg']);
        }
        return $this->adminResponse(200, 'success');
    }

    /**
     * @param array $validator_data
     * @return \Hyperf\Database\Model\Builder
     * @throws \Exception
     */
    public function getBuilder($validator_data)
    {
        $Builder= $this->model::query(false, true)
            ->with('rank:id,rank_name,permission')
            ->with('socialites:user_id,type')
            ->with('quota:user_id,permission_name,limit,remaining,used');

        // 排序字段
        $order_by = Arr::get($validator_data, 'order_by') ?: 'id';
        $order_by === 'created_at' && $order_by = 'id';
        if (!in_array($order_by, ['id', 'created_at', 'vip_started_at', 'vip_expired_at', 'last_at', 'subscription_started_at', 'subscription_next_deduct_at', 'user_value']) &&
            !Str::startsWith($order_by, ['user_quota_remaining_', 'user_quota_used_', 'user_quota_limit_'])) {
            throw new \Exception('选定的 排序字段 是无效的');
        }
        if (Str::startsWith($order_by, ['user_quota_remaining_', 'user_quota_used_', 'user_quota_limit_'])) {
            if (Str::startsWith($order_by, 'user_quota_remaining_')) {
                $permission_name = str_replace('user_quota_remaining_', '', $order_by);
                $order_by = 'user_quota.remaining';
            }
            if (Str::startsWith($order_by, 'user_quota_used_')) {
                $permission_name = str_replace('user_quota_used_', '', $order_by);
                $order_by = 'user_quota.used';
            }
            if (Str::startsWith($order_by, 'user_quota_limit_')) {
                $permission_name = str_replace('user_quota_limit_', '', $order_by);
                $order_by = 'user_quota.limit';
            }

            if (is_null(RankService::getPermissionTitle($permission_name))) {
                throw new \Exception('选定的 排序字段 是无效的');
            }

            $Builder->leftJoinSub(UserQuota::query()
                ->select(['user_id', 'remaining', 'used', 'limit'])
                ->where('permission_name', $permission_name), 'user_quota', 'user_quota.user_id', '=', 'users.id');
        }

        ($account = Arr::get($validator_data, 'account', '')) &&
        $Builder->where(function (Builder $Query) use ($account) {
            $Query->where('account', 'like', "%{$account}%", 'or')
                ->where('email', 'like', "%{$account}%", 'or')
                ->where('phone_number', 'like', "%{$account}%", 'or');
        });

        $account_status = Arr::get($validator_data, 'account_status', '');
        $account_status !== '' &&
        $Builder->where('account_status', $account_status);

        ($username = Arr::get($validator_data, 'username', '')) &&
        $Builder->where('username', 'like', "%{$username}%");

        ($email = Arr::get($validator_data, 'email', '')) &&
        $Builder->where('email', 'like', "%{$email}%");

        ($phone_region = Arr::get($validator_data, 'phone_region', '')) &&
        $Builder->where('phone_region', $phone_region);

        ($last_ip = Arr::get($validator_data, 'last_ip', '')) &&
        $Builder->where('last_ip', $last_ip);

        ($last_location = Arr::get($validator_data, 'last_location', '')) &&
        $Builder->where('last_location', $last_location);

        ($source = Arr::get($validator_data, 'source', '')) &&
        $Builder->where('source', 'like', "%{$source}%");

        ($language = Arr::get($validator_data, 'language', '')) &&
        $Builder->where('language', $language);

        ($timezone = Arr::get($validator_data, 'timezone', '')) &&
        $Builder->where('timezone', $timezone);

        ($device_language = Arr::get($validator_data, 'device_language', '')) &&
        $Builder->where('device_language', $device_language);

        ($device_timezone = Arr::get($validator_data, 'device_timezone', '')) &&
        $Builder->where('device_timezone', $device_timezone);

        ($rank_id = Arr::get($validator_data, 'rank_id', '')) &&
        $Builder->whereIn('rank_id', explode(',', $rank_id));

        $subscription_status = Arr::get($validator_data, 'subscription_status', '');
        $subscription_status !== '' &&
        $Builder->where('subscription_status', $subscription_status == 2 ? null : $subscription_status);

        ($card_last4 = Arr::get($validator_data, 'card_last4', '')) &&
        $Builder->where('card_last4', $card_last4);

        ($distribution_code = Arr::get($validator_data, 'distribution_code', '')) &&
        $Builder->where('distribution_code', strtoupper($distribution_code));

        ($remark = Arr::get($validator_data, 'remark', '')) &&
        $Builder->where('remark', 'like', "%{$remark}%");

        // 权限次数筛选
        foreach (array_keys(RankService::getPermissions()) as $permission_name) {
            foreach (['remaining', 'used', 'limit'] as $user_quota_field) {
                foreach (['start', 'end'] as $tail) {
                    $key_value = $this->request->input("user_quota_{$user_quota_field}_{$permission_name}_{$tail}", '');
                    if ($key_value !== '') {
                        $Builder->whereExists(function (\Hyperf\Database\Query\Builder $Query) use ($permission_name, $user_quota_field, $tail, $key_value) {
                            $Query->selectRaw(Db::raw(1))
                                ->from('user_quota')
                                ->whereColumn('user_quota.user_id', 'users.id')
                                ->where('permission_name', $permission_name)
                                ->where($user_quota_field, $tail === 'start' ? '>=' : '<=', $key_value);
                        });
                    }
                }
            }
        }

        // 数值范围筛选
        $keys = ['user_value'];
        foreach ($keys as $key) {
            $field = $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $Builder->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 日期范围筛选
        $keys = ['created_at', 'last_at', 'vip_started_at', 'vip_expired_at', 'subscription_started_at', 'subscription_next_deduct_at'];
        foreach ($keys as $key) {
            $field = $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value) : Timestamp::dateEnd($key_value);
                    $Builder->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        // 排序方式
        $direction = Arr::get($validator_data, 'direction') ?: 'desc';
        $Builder->orderBy($order_by, $direction);

        return $Builder;
    }

    /**
     * @PostApi(path="join-deletion-queue", description="加入删除队列")
     * @FormData(key="id|用户ID", rule="required|integer")
     */
    public function joinDeletionQueue()
    {
        $validator_data = Context::get('validator.data');
        try {
            UserService::deleteAccountJoinDeletionQueue($validator_data['id'], null, [
                'handler_type' => 'admin',
                'handler' => Context::get('AdminUSER')['username'],
            ]);

            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @PostApi(path="remove-deletion-queue", description="移出删除队列")
     * @FormData(key="id|用户ID", rule="required|integer")
     */
    public function removeDeletionQueue()
    {
        $validator_data = Context::get('validator.data');
        try {
            UserService::deleteAccountRemoveDeletionQueue($validator_data['id'], [
                'handler_type' => 'admin',
                'handler' => Context::get('AdminUSER')['username'],
            ]);

            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @PostApi(path="delete-account", description="删除账号")
     * @FormData(key="id|用户ID", rule="required|integer")
     * @FormData(key="verification_code|验证码", rule="required|date_format:Y-m-d")
     */
    public function deleteAccount()
    {
        $validator_data = Context::get('validator.data');
        if ($validator_data['verification_code'] !== Carbon::today()->format('Y-m-d')) {
            return $this->adminResponse(400, '验证码错误');
        }

        try {
            UserService::deleteAccount($validator_data['id'], [
                'handler_type' => 'admin',
                'handler' => Context::get('AdminUSER')['username'],
            ]);

            return $this->adminResponse(200, '请求成功');
        } catch (\Throwable $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }
}
