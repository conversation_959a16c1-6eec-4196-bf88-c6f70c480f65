<?php

declare(strict_types=1);

namespace Website\Common\BackendApi\Controller;

use Lete\Base\Utils\Timestamp;
use Website\Common\Model\StripePaymentIntent;
use Website\Common\Model\Subscription;
use Website\Common\Service\OrderService;
use Website\Common\Service\SubscriptionService;
use Carbon\Carbon;
use Hyperf\Apidog\Annotation\GetApi;
use Hyperf\Apidog\Annotation\Path;
use Hyperf\Apidog\Annotation\ApiController;
use Hyperf\Apidog\Annotation\Header;
use Hyperf\Apidog\Annotation\PostApi;
use Hyperf\Apidog\Annotation\PutApi;
use Hyperf\Apidog\Annotation\Query;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\Arr;
use Lete\Pay\Cashier;
use Lete\Base\Utils\Data;

/**
 * @ApiController(prefix="subscriptions", tag="订阅管理", server="website-common-backend-api")
 */
class SubscriptionsController extends AdminController
{
    /**
     * @GetApi(path="overview", description="数据看板")
     * @Query(key="start_time|开始日期", rule="date_format:Y-m-d")
     * @Query(key="end_time|结束日期", rule="date_format:Y-m-d")
     */
    public function overview()
    {
        $validator_data = Context::get('validator.data');
        $start_time = (string) Arr::get($validator_data,'start_time', '');
        $end_time = (string) Arr::get($validator_data,'end_time', '');

        // 如果没有传时间，所有记录中找到最小和最大时间
        if (!$start_time && !$end_time) {
            $all_subscriptions = Subscription::query()
                ->get(['subscription_status', 'created_at', 'canceled_at', 'next_period_start'])
                ->toArray();

            if ($all_subscriptions) {
                $start_time = explode(' ', $all_subscriptions[0]['created_at'])[0];
                $max_created_at = Carbon::createFromFormat('Y-m-d H:i:s', array_reverse($all_subscriptions)[0]['created_at'])->timestamp;
                $max_canceled_at = Db::table('subscriptions')->max('canceled_at');
                $max_next_period_start = Db::table('subscriptions')->max('next_period_start');
                $end_time = Carbon::createFromTimestamp(max($max_created_at, $max_canceled_at, $max_next_period_start), 'PRC')->format('Y-m-d');
            }
        } else {
            !$start_time && $start_time = Carbon::createFromTimestamp(Db::table('subscriptions')->min('created_at'), 'PRC')->format('Y-m-d');
            if (!$end_time) {
                $max_created_at = Db::table('subscriptions')->max('created_at');
                $max_canceled_at = Db::table('subscriptions')->max('canceled_at');
                $max_next_period_start = Db::table('subscriptions')->max('next_period_start');
                $end_time = Carbon::createFromTimestamp(max($max_created_at, $max_canceled_at, $max_next_period_start), 'PRC')->format('Y-m-d');
            }
        }

        // 新增订阅
        // 创建时间 = 【所选时间范围】
        if (isset($all_subscriptions)) {
            $new_subscriptions_data = $all_subscriptions;
        } else {
            $where = [];
            $start_time && $where[] = ['created_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['created_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $new_subscriptions_data = Subscription::query()->where($where)->get(['created_at'])->toArray();
        }

        // 生效中订阅
        // 下创建时间 = 【所选时间范围】，订阅状态 = 已生效
        if (isset($all_subscriptions)) {
            $active_subscriptions_data = array_filter($all_subscriptions, function ($subscription) {
                return $subscription['subscription_status'] === SubscriptionService::STATUS_ACTIVE;
            });
        } else {
            $where = [
                ['subscription_status', SubscriptionService::STATUS_ACTIVE],
            ];
            $start_time && $where[] = ['created_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['created_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $active_subscriptions_data = Subscription::query()->where($where)->get(['created_at'])->toArray();
        }

        // 已取消订阅
        // 取消时间 = 【所选时间范围】，订阅状态 = 已取消
        if (isset($all_subscriptions)) {
            $canceled_subscriptions_data = array_filter($all_subscriptions, function ($subscription) {
                return $subscription['subscription_status'] === SubscriptionService::STATUS_CANCELED;
            });
        } else {
            $where = [
                ['subscription_status', SubscriptionService::STATUS_CANCELED],
            ];
            $start_time && $where[] = ['canceled_at', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['canceled_at', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $canceled_subscriptions_data = Subscription::query()->where($where)->get(['canceled_at'])->toArray();
        }

        // 预计扣款订阅
        // 订阅下一账单日期 = 【所选时间范围】，订单状态 = 已生效
        if (isset($all_subscriptions)) {
            $next_period_subscriptions_data = array_filter($all_subscriptions, function ($subscription) {
                return $subscription['subscription_status'] === SubscriptionService::STATUS_ACTIVE;
            });
        } else {
            $where = [
                ['subscription_status', SubscriptionService::STATUS_ACTIVE],
            ];
            $start_time && $where[] = ['next_period_start', '>=', Timestamp::dateStart($start_time,'PRC')];
            $end_time && $where[] = ['next_period_start', '<=', Timestamp::dateEnd($end_time,'PRC')];
            $next_period_subscriptions_data = Subscription::query()->where($where)->get(['next_period_start'])->toArray();
        }

        $data = [
            'new_subscriptions' => Data::toTimeline($start_time, $end_time, 'created_at', $new_subscriptions_data),
            'active_subscriptions' => Data::toTimeline($start_time, $end_time, 'created_at', $active_subscriptions_data),
            'canceled_subscriptions' => Data::toTimeline($start_time, $end_time, 'canceled_at', $canceled_subscriptions_data),
            'next_period_subscriptions' => Data::toTimeline($start_time, $end_time, 'next_period_start', $next_period_subscriptions_data),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @GetApi(path="", description="订阅列表")
     * @Query(key="user_id|会员ID", rule="integer")
     * @Query(key="subscription_sn|订阅编号", rule="")
     * @Query(key="subscription_status|订阅状态", rule="cb_ruleCheckSubscriptionStatus")
     * @Query(key="canceled_handler|取消来源", rule="cb_ruleCheckCanceledHandler")
     * @Query(key="email|会员账号", rule="")
     * @Query(key="start_date_start|订阅开始时间（起）", rule="date_format:Y-m-d")
     * @Query(key="start_date_end|订阅开始时间（止）", rule="date_format:Y-m-d")
     * @Query(key="next_period_start_start|订阅下一账单日期（起）", rule="date_format:Y-m-d")
     * @Query(key="next_period_start_end|订阅下一账单日期（止）", rule="date_format:Y-m-d")
     * @Query(key="created_at_start|创建时间（起）", rule="date_format:Y-m-d")
     * @Query(key="created_at_end|创建时间（止）", rule="date_format:Y-m-d")
     * @Query(key="updated_at_start|更新时间（起）", rule="date_format:Y-m-d")
     * @Query(key="updated_at_end|更新时间（止）", rule="date_format:Y-m-d")
     * @Query(key="canceled_at_start|取消时间（起）", rule="date_format:Y-m-d")
     * @Query(key="canceled_at_end|取消时间（止）", rule="date_format:Y-m-d")
     * @Query(key="order_by|排序字段", rule="in:start_date,next_period_start,created_at,updated_at,canceled_at")
     * @Query(key="direction|排序方式[desc,asc]", rule="in:desc,asc")
     * @Query(key="page|页码", rule="integer|min:1")
     * @Query(key="per_page|x条/页", rule="integer|max:10000")
     */
    public function index()
    {
        $validator_data = Context::get('validator.data');
        $Subscription = Subscription::query(false, true)
            ->with('user:id,account,username,email');

        ($user_id = Arr::get($validator_data, 'user_id', '')) &&
        $Subscription->where('user_id', $user_id);

        ($subscription_sn = Arr::get($validator_data, 'subscription_sn', '')) &&
        $Subscription->where('subscription_sn', 'like', "{$subscription_sn}%");

        ($subscription_status = Arr::get($validator_data, 'subscription_status', '')) &&
        $Subscription->where('subscription_status', $subscription_status);

        ($canceled_handler = Arr::get($validator_data, 'canceled_handler', '')) &&
        $Subscription->where('canceled_handler', $canceled_handler);

        $email = Arr::get($validator_data, 'email', '');
        if ($email) {
            $user_ids = Db::table('users')
                ->where('account', 'like', "%{$email}%")
                ->get(['id'])
                ->groupBy('id')->keys();
            $Subscription->whereIn('user_id', $user_ids);
        }

        // 时间范围筛选
        $datetime_keys = ['start_date', 'next_period_start', 'created_at', 'updated_at', 'canceled_at'];
        foreach ($datetime_keys as $key) {
            $field = $key;
            foreach (['start', 'end'] as $tail) {
                $key_value = Arr::get($validator_data, "{$key}_$tail", '');
                if ($key_value !== '') {
                    $key_value = $tail === 'start' ? Timestamp::dateStart($key_value, 'PRC') : Timestamp::dateEnd($key_value, 'PRC');
                    $Subscription->where($field, $tail === 'start' ? '>=' : '<=', $key_value);
                }
            }
        }

        $data = $Subscription->orderBy(Arr::get($validator_data, 'order_by') ?: 'id', Arr::get($validator_data, 'direction') ?: 'desc')
            ->paginate((int)$this->request->input('per_page', 10))
            ->toArray();
        $data['data'] = array_map(function ($item) {
            $item['subscription_status_text'] = SubscriptionService::statusText($item['subscription_status']);
            $item['payment_platform_text'] = OrderService::paymentPlatformText($item['payment_platform']);
            return $item;
        }, $data['data']);
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool|string
     */
    public function ruleCheckSubscriptionStatus($attribute, $value)
    {
        return is_null(SubscriptionService::statusText((int) $value)) ? '无效的订单状态' : true;
    }

    public function ruleCheckCanceledHandler($attribute, $value)
    {
        $result = Arr::where(SubscriptionService::canceledHandlerList(), function ($v) use ($value) {
            return $v['value'] == $value;
        });
        return $result ? true : '无效的取消来源';
    }

    /**
     * @GetApi(path="options", description="选项")
     */
    public function options()
    {
        $data = [
            'subscription_status' => SubscriptionService::statusList(),
            'canceled_handlers' => SubscriptionService::canceledHandlerList(),
        ];
        return $this->adminResponse(200, '请求成功', $data);
    }

    /**
     * @PutApi(path="{id:\d+}/cancel", description="取消订阅")
     * @Path(key="id|订阅ID", rule="required|integer|exists:subscriptions")
     */
    public function cancel()
    {
        try {
            (new SubscriptionService($this->request->route('id')))->cancel(SubscriptionService::CANCELED_HANDLER_ADMIN);
            return $this->adminResponse(200, '请求成功', []);
        } catch(\Exception $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }

    /**
     * @GetApi(path="{id:\d+}/invoices", description="账单")
     * @Path(key="id|订阅ID", rule="required|integer|exists:subscriptions")
     */
    public function invoices()
    {
        try {
            $Subscription = Db::table('subscriptions')->find($this->request->route('id'), ['subscription_sn', 'product_name']);
            $invoices = [];
            $starting_after = '';
            do {
                $params = [
                    'subscription' => $Subscription->subscription_sn,
                    'limit' => 100,
                ];
                $starting_after && $params['starting_after'] = $starting_after;

                $StripeCollection = Cashier::stripe()->invoices->all($params);
                foreach ($StripeCollection->data as $InvoiceObject) {
                    if (!isset($user)) {
                        $user = Db::table('users')
                            ->join('stripe_customer', 'users.id', '=', 'stripe_customer.user_id')
                            ->where('stripe_customer.stripe_id', $InvoiceObject->customer)
                            ->first(['users.id', 'users.account', 'users.username', 'users.email']);
                    }

                    $StripePaymentIntent = StripePaymentIntent::where('payment_intent', $InvoiceObject->payment_intent)->first();
                    $payment_method = [
                        'type' => $StripePaymentIntent ? ($StripePaymentIntent->payment_method_details['type'] ?? '') : '',
                        'last4' => $StripePaymentIntent && ($StripePaymentIntent->payment_method_details['type'] ?? '') ? ($StripePaymentIntent->payment_method_details[$StripePaymentIntent->payment_method_details['type']]['last4'] ?? '') : '',
                    ];
                    if ($payment_method['type'] === 'card') {
                        $payment_method['card_brand'] = $StripePaymentIntent->payment_method_details['card']['brand'];
                    }

                    $invoice = [
                        'id' => $InvoiceObject->id,
                        'currency' => $InvoiceObject->currency,
                        'total' => $InvoiceObject->total / 100,
                        'status' => $InvoiceObject->status,
                        'number' => $InvoiceObject->number,
                        'user' => $user,
                        'due_date' => $InvoiceObject->due_date ? Carbon::createFromTimestamp($InvoiceObject->due_date, 'PRC')->toDateTimeString() : '',
                        'created' => $InvoiceObject->created ? Carbon::createFromTimestamp($InvoiceObject->created, 'PRC')->toDateTimeString() : '',
                        'paid_at' => $InvoiceObject->status_transitions->paid_at ? Carbon::createFromTimestamp($InvoiceObject->status_transitions->paid_at, 'PRC')->toDateTimeString() : '',
                        'hosted_invoice_url' => $InvoiceObject->hosted_invoice_url,
                        'invoice_pdf' => $InvoiceObject->invoice_pdf,
                        'payment_method' => $payment_method,
                        'product_name' => $Subscription->product_name,
                    ];
                    $invoices[] = $invoice;
                }
            } while ($StripeCollection->has_more);

            $data = [
                'invoices' => $invoices,
            ];
            return $this->adminResponse(200, '请求成功', $data);
        } catch(\Exception $e) {
            return $this->adminResponse(400, $e->getMessage());
        }
    }
}
