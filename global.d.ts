import mongoose from 'mongoose'

declare global {
  var mongoose: {
    conn: typeof mongoose | null
    promise: Promise<typeof mongoose> | null
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  type SafeAny = any

  /** API Routes 上下文 */
  type RequestContext = {
    t: (key: string, values?: Record<string, string>) => string
    params: {
      [key: string]: string | string[] | undefined
    }
  }

  interface IconProps {
    height?: number
    width?: number
  }

  interface User {
    id: string
    email: string
    username: string
    avatar: string | null
    /** Optional */
    account?: string
    language?: string
    company?: string
    country?: string
    province?: string
    city?: string
    postal?: string
    address?: string
    phone?: string
    vat?: string
    distribution_code?: string
    created_at?: number
    vip_expired_at?: number
    delete_task_plan_executed_at?: number | null
    last_rank_name?: string | null
    rank_name?: string
  }

  interface Rank {
    rank_name: 'Free'
    permissions: []
  }

  interface PermissionQuota {
    limit: number
    remaining: number
    reset_at: number
    permission_name: string
    used: number
  }

  interface ApiResponse<T = SafeAny> {
    code: number
    message: string
    data: T
  }

  interface PageResponse<T = SafeAny> extends ApiResponse<T[]> {
    meta: {
      pagination: {
        page: number
        pageSize: number
        total: number
        totalPages: number
      }
    }
  }
}
