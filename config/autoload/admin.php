<?php

declare(strict_types=1);

return [
    /**
     * 权限认证
     */
    'auth' => [
        // 排除以下uri
        'except' => [
            'POST:/passport/login',
        ],
        'ip_guard' => false,
        // 以下账号的jwt不会过期，也不需要验证ip
        'credible_account' => [],
    ],
    /**
     * 操作日志
     */
    'log' => [
        'connection' => env('ADMIN_LOG_CONNECTION', 'default'),
        'collection' =>  env('ADMIN_LOG_COLLECTION_NAME', 'admin_logs'),
        // 开关
        'enable' => env('ADMIN_LOG_ENABLE', false),
        // 只记录以下请求方式
        'allowed_methods' => ['POST', 'PUT', 'DELETE'],
        /**
         * 排除以下uri
         */
        'except' => [
            '\/admin\/logs.+',
        ],
        /**
         * 以下uri必定记录
         */
        'includes' => [],
        /**
         * 跟踪数据变化
         * 不支持批量更新
         * 不支持通过关联模型更新
         */
        'updated' => [
            // 开关
            'enable' => env('ADMIN_LOG_UPDATED_ENABLE', false),
            // 只记录以下请求方式
            'allowed_methods' => ['POST', 'PUT', 'DELETE'],
            // 排除以下表
            'except' => [],
        ]
    ],
];
