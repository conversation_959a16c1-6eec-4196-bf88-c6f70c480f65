<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
return [
    'website-common-backend-api' => [
        \Website\Common\BackendApi\Middleware\AdminMiddleware::class,
        \Website\Common\BackendApi\Middleware\AuthMiddleware::class,
        \Website\Common\BackendApi\Middleware\AdminLogsMiddleware::class,
        \Website\Common\BackendApi\Middleware\ReSubmitMiddleware::class,
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
    'website-self-backend-api' => [
        \Website\Common\BackendApi\Middleware\AdminMiddleware::class,
        \Website\Common\BackendApi\Middleware\AuthMiddleware::class,
        \Website\Common\BackendApi\Middleware\AdminLogsMiddleware::class,
        \Website\Common\BackendApi\Middleware\ReSubmitMiddleware::class,
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
    'website-common-frontend-api' => [
        \Website\Common\FrontendApi\Middleware\CoreMiddleware::class,
        \Website\Common\FrontendApi\Middleware\AuthMiddleware::class,
        \Website\Common\FrontendApi\Middleware\ReSubmitMiddleware::class,
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
    'website-self-frontend-api' => [
        \Website\Common\FrontendApi\Middleware\CoreMiddleware::class,
        \Website\Common\FrontendApi\Middleware\AuthMiddleware::class,
        \Website\Common\FrontendApi\Middleware\ReSubmitMiddleware::class,
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
    'website-frontend' => [
        \App\Frontend\Middleware\CoreMiddleware::class,
        \Website\Common\FrontendApi\Middleware\AuthMiddleware::class,
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
    'website-common-open-api' => [
        \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class,
    ],
];
