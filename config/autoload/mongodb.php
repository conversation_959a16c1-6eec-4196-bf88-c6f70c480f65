<?php

$mongodbs = [
    'default' => [
        'socket_address' => env('MONGODB_SOCKET_ADDRESS', '127.0.0.1:6001'),
        'uri' => env('MONGODB_URI', ''),
        'database' =>  env('MONGODB_DATABASE', 'admin'),
        'connect_timeout' => env('MONGODB_CONNECT_TIMEOUT', '3s'),
        "read_write_timeout" => env('MONGODB_READ_WRITE_TIMEOUT', '60s'),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => 30,
            'connect_timeout' => 10.0,
            'wait_timeout' => 30.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('GOTASK_MAX_IDLE_TIME', 60),
        ],
    ],
];

return array_filter($mongodbs, function ($mongodb) {
    return $mongodb['uri'];
});
