<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
$processes = [
    Hyperf\Crontab\Process\CrontabDispatcherProcess::class,
];
if (env('ASYNC_QUEUE_PROCESS_ENABLE', false)) {
    $processes[] = Hyperf\AsyncQueue\Process\ConsumerProcess::class;
}
return $processes;
