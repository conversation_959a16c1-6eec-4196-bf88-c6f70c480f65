<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
use Hyperf\Server\Event;
use Hyperf\Server\Server;
use Swoole\Constant;

$servers = [
    [
        'name' => 'website-common-backend-api',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_COMMON_BACKEND_API_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteCommonBackendApiServer', 'onRequest'],
        ],
    ],
    [
        'name' => 'website-self-backend-api',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_SELF_BACKEND_API_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteSelfBackendApiServer', 'onRequest'],
        ],
    ],
    [
        'name' => 'website-common-frontend-api',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_COMMON_FRONTEND_API_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteCommonFrontendApiServer', 'onRequest'],
        ],
    ],
    [
        'name' => 'website-self-frontend-api',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_SELF_FRONTEND_API_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteSelfFrontendApiServer', 'onRequest'],
        ],
    ],
    [
        'name' => 'website-frontend',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_FRONTEND_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteFrontendServer', 'onRequest'],
        ],
    ],
    [
        'name' => 'website-common-open-api',
        'type' => Server::SERVER_HTTP,
        'host' => '0.0.0.0',
        'port' => (int) env('WEBSITE_COMMON_OPEN_API_SERVER_PORT', 0),
        'sock_type' => SWOOLE_SOCK_TCP,
        'callbacks' => [
            Event::ON_REQUEST => ['WebsiteCommonOpenApiServer', 'onRequest'],
        ],
    ],
];

return [
    'mode' => SWOOLE_PROCESS,
    'servers' => array_filter($servers, function ($server) {
        return $server['port'] > 0;
    }),
    'settings' => [
        Constant::OPTION_ENABLE_COROUTINE => true,
        Constant::OPTION_WORKER_NUM => swoole_cpu_num(),
        Constant::OPTION_PID_FILE => BASE_PATH . '/runtime/hyperf.pid',
        Constant::OPTION_OPEN_TCP_NODELAY => true,
        Constant::OPTION_MAX_COROUTINE => 100000,
        Constant::OPTION_OPEN_HTTP2_PROTOCOL => true,
        Constant::OPTION_MAX_REQUEST => 0,
        Constant::OPTION_SOCKET_BUFFER_SIZE => 2 * 1024 * 1024,
        Constant::OPTION_BUFFER_OUTPUT_SIZE => 2 * 1024 * 1024,
        Constant::OPTION_PACKAGE_MAX_LENGTH => 7 * 1024 * 1024,
    ],
    'callbacks' => [
        Event::ON_WORKER_START => [Hyperf\Framework\Bootstrap\WorkerStartCallback::class, 'onWorkerStart'],
        Event::ON_PIPE_MESSAGE => [Hyperf\Framework\Bootstrap\PipeMessageCallback::class, 'onPipeMessage'],
        Event::ON_WORKER_EXIT => [Hyperf\Framework\Bootstrap\WorkerExitCallback::class, 'onWorkerExit'],
    ],
];
