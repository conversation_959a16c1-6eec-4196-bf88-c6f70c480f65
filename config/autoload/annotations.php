<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
return [
    'scan' => [
        'paths' => [
            BASE_PATH . '/app',
        ],
        'ignore_annotations' => [
            'mixin',
        ],
        'class_map' => [
            // 需要映射的类名 => 类所在的文件地址
            \Hyperf\Apidog\Middleware\ApiValidationMiddleware::class => BASE_PATH . '/vendor/lete/base/src/class_map/daodao97/apidog/src/Middleware/ApiValidationMiddleware.php',
            \Hyperf\Apidog\Validation\ValidationApi::class => BASE_PATH . '/vendor/lete/base/src/class_map/daodao97/apidog/src/Validation/ValidationApi.php',
            \Hyperf\Apidog\Swagger\SwaggerJson::class => BASE_PATH . '/vendor/lete/base/src/class_map/daodao97/apidog/src/Swagger/SwaggerJson.php',
            \Hyperf\Crontab\Annotation\Crontab::class => BASE_PATH . '/vendor/lete/base/src/class_map/hyperf/crontab/src/Annotation/Crontab.php',
            \Stripe\HttpClient\CurlClient::class => BASE_PATH . '/vendor/lete/base/src/class_map/stripe/stripe-php/lib/HttpClient/CurlClient.php',
        ],
    ],
];
