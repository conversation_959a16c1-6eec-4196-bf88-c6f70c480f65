<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
return [
    'default' => [
        'driver' => \Hyperf\AsyncQueue\Driver\RedisDriver::class,
        'redis' => [
            'pool' => 'default',
        ],
        'channel' => strtolower(env('APP_NAME')) . ':queue',
        'timeout' => 2,
        'retry_seconds' => 5,
        'handle_timeout' => 300,
        'processes' => 1,
        'concurrent' => [
            'limit' => 20,
        ],
    ],
];
