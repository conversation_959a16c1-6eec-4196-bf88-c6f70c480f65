<?php

declare(strict_types=1);
/**
 * This file is part of lete/mail.
 *
 */
return [
    /*
    |--------------------------------------------------------------------------
    | 默认驱动
    |--------------------------------------------------------------------------
    |
    */

    'default' => env('MAIL_MAILER', 'smtp'),

    /*
    |--------------------------------------------------------------------------
    | 全部驱动
    |--------------------------------------------------------------------------
    |
    */

    'mailers' => [
        'smtp' => [
            'transport' => \Lete\Mail\Transport\SmtpTransport::class,
            'options' => [
                'host' => env('MAIL_SMTP_HOST', 'smtp.qq.com'),
                'port' => env('MAIL_SMTP_PORT', 465),
                'username' => env('MAIL_SMTP_USERNAME', '发件邮箱'),
                'password' => env('MAIL_SMTP_PASSWORD', '发件密码'),
            ],
        ],

        'aws_ses' => [
            'transport' => \Lete\Mail\Transport\AwsSesTransport::class,
            'options' => [
                'username' => env('MAIL_AWS_SES_USERNAME', 'AKIAZMUIVNIGEC3LH5MW'),
                'password' => env('MAIL_AWS_SES_PASSWORD', 'BD5fBK4WJ/t82uXwkKwkDrrchqU70vL0vMDay5fzML1p'),
                'region' => env('MAIL_AWS_SES_REGION', 'us-east-1'),
            ],
        ],

        'aliyun' => [
            'transport' => \Lete\Mail\Transport\AliyunTransport::class,
            'options' => [
                'username' => env('MAIL_ALIYUN_USERNAME', '<EMAIL>'),
                'password' => env('MAIL_ALIYUN_PASSWORD', '123321sunSUN'),
                'region' => env('MAIL_ALIYUN_REGION', ''),
            ],
        ],

        'sendgrid' => [
            'transport' => \Lete\Mail\Transport\SendGridTransport::class,
            'options' => [
                'username' => env('MAIL_SENDGRID_USERNAME', 'apikey'),
                'password' => env('MAIL_SENDGRID_PASSWORD', ''),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 全局地址
    |--------------------------------------------------------------------------
    |
    */

    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'Lete'),
    ],

    'to' => [
        'address' => '',
        'name' => '',
    ],

    'replyTo' => [
        'address' => '',
        'name' => '',
    ],

    'returnPath' => [
        'address' => '',
        'name' => '',
    ],

];
