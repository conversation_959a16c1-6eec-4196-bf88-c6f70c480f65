CENTER_NAME=pipiads-system
APP_NAME=Minutes
APP_ENV=prod
SCAN_CACHEABLE=true
CRONTAB_ENABLE=true
ASYNC_QUEUE_PROCESS_ENABLE=true

MONGODB_URI=***************************************************************************************************************
MONGODB_DATABASE=15minutes

DB_DRIVER=mysql
DB_HOST=**********
DB_PORT=3306
DB_DATABASE=15minutes
DB_USERNAME=15minutes
DB_PASSWORD=mZMXpny8tymjpYKY
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

REDIS_HOST=***********
REDIS_AUTH=REDIS!123SDFdfsfe
REDIS_PORT=6379
REDIS_DB=0

AMQP_HOST=**********
AMQP_PORT=5672
AMQP_USER=admin
AMQP_PASSWORD=MQdsf!fde3315472
AMQP_HTTP_PORT=15672
AMQP_ENABLE=true

AMQP_CENTER_HOST=*************
AMQP_CENTER_PORT=5673
AMQP_CENTER_USER=System
AMQP_CENTER_PASSWORD=System_MQ_2023MQ
AMQP_CENTER_HTTP_PORT=15673
AMQP_CENTER_ENABLE=True

# AMQP_CENTER_HOST=*************
# AMQP_CENTER_PORT=5673
# AMQP_CENTER_USER=ppspy
# AMQP_CENTER_PASSWORD=ppspy_MQ_2023
# AMQP_CENTER_HTTP_PORT=15673
# AMQP_CENTER_ENABLE=true

WEBSITE_FRONTEND_BASE_URI=https://15minutes.ai
WEBSITE_FRONTEND_COOKIE_DOMAIN=.15minutes.ai

WEBSITE_COMMON_BACKEND_API_SERVER_PORT=9501
WEBSITE_SELF_BACKEND_API_SERVER_PORT=9502
WEBSITE_COMMON_FRONTEND_API_SERVER_PORT=9503
WEBSITE_SELF_FRONTEND_API_SERVER_PORT=9504
WEBSITE_FRONTEND_SERVER_PORT=9505
WEBSITE_COMMON_OPEN_API_SERVER_PORT=9506

ADMIN_LOG_ENABLE=true
ADMIN_LOG_UPDATED_ENABLE=true

FEI_SHU_BOT_SECRET=
FEI_SHU_BOT_WEBHOOK=

# GOOGLE_CLIENT_SECRET=
# GOOGLE_HTTP_PROXY=dl.louhu.com:10001
# GOOGLE_CLIENT_SECRET={"web":*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}
GOOGLE_CLIENT_SECRET={"web":****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}
GOOGLE_HTTP_PROXY=
GOOGLE_RECAPTCHA_SECRET=6LdSKgYqAAAAAOpaOmXGqbkPLJSCE0hNOUlEdSLY

CONSUMER_E_MAIL_ENABLE=true

MAIL_MAILER=aws_ses
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Zafu.ai
MAIL_AWS_SES_USERNAME=********************
MAIL_AWS_SES_PASSWORD=BAZ7PDUN8GELUf74A8eUoWbL/n9PyqeWBv683fAlcNPK
MAIL_AWS_SES_REGION=us-west-1

# MAIL_MAILER=
# MAIL_FROM_ADDRESS=
# MAIL_FROM_NAME=
# MAIL_AWS_SES_USERNAME=
# MAIL_AWS_SES_PASSWORD=
# MAIL_AWS_SES_REGION=

STRIPE_KEY=pk_live_51PKuJSDCec5sEmI22iZi5LRLb3S80syfSKoZCkoqfiCmM5yKKROLJkrQlOsrAjETXsA3SzdL2gPZXPx01EHo5p8U00wZs7vEyA
STRIPE_SECRET=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_NjLwDOW1ANnZZZtR7tn6W3VxIYE2E5ai
