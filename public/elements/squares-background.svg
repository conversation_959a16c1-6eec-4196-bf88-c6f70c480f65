<svg width="1440" height="768" viewBox="0 0 1440 768" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="-2" y="-517.951" width="907.925" height="907.925" transform="rotate(45 -2 -517.951)" fill="url(#paint0_linear)"/>
<rect width="907.925" height="907.925" transform="matrix(-0.707107 0.707107 0.707107 0.707107 1440 -517.951)" fill="url(#paint1_linear)"/>
<rect width="907.925" height="907.925" transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 720 1026)" fill="url(#paint2_linear)"/>
<defs>
<linearGradient id="paint0_linear" x1="451.963" y1="-517.951" x2="451.963" y2="389.974" gradientUnits="userSpaceOnUse">
<stop stop-color="#006251"/>
<stop offset="1" stop-color="#007460"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="453.963" y1="0" x2="453.963" y2="907.925" gradientUnits="userSpaceOnUse">
<stop stop-color="#006251"/>
<stop offset="1" stop-color="#007460"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="907.925" y1="0" x2="0" y2="907.925" gradientUnits="userSpaceOnUse">
<stop stop-color="#006251"/>
<stop offset="1" stop-color="#007460"/>
</linearGradient>
</defs>
</svg>
