CENTER_NAME=pipiads-system
APP_NAME=pupu_email
APP_ENV=prod
SCAN_CACHEABLE=true
CRONTAB_ENABLE=false
ASYNC_QUEUE_PROCESS_ENABLE=true

MONGODB_URI=**************************************************************************************************************************
MONGODB_SOCKET_ADDRESS=127.0.0.1:6017
MONGODB_DATABASE=pipiads_email

DB_DRIVER=mysql
DB_HOST=**********
DB_PORT=3306
DB_DATABASE=system_pipiads
DB_USERNAME=system_pipiads
DB_PASSWORD=pFeLm8KkX!PByE7G7D
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

REDIS_HOST=**********
REDIS_AUTH=Redis!c7a9c148b8Ghs
REDIS_PORT=6379
REDIS_DB=0

AMQP_HOST=**********
AMQP_PORT=5672
AMQP_USER=Toolify
AMQP_PASSWORD=Toolify_MQ_202302
AMQP_HTTP_PORT=15673
AMQP_ENABLE=false

AMQP_CENTER_HOST=*********
AMQP_CENTER_PORT=5673
AMQP_CENTER_USER=System
AMQP_CENTER_PASSWORD=System_MQ_2023MQ
AMQP_CENTER_HTTP_PORT=15673
AMQP_CENTER_ENABLE=true

WEBSITE_FRONTEND_BASE_URI=
WEBSITE_FRONTEND_COOKIE_DOMAIN=

WEBSITE_COMMON_BACKEND_API_SERVER_PORT=9501
WEBSITE_SELF_BACKEND_API_SERVER_PORT=
WEBSITE_COMMON_FRONTEND_API_SERVER_PORT=
WEBSITE_SELF_FRONTEND_API_SERVER_PORT=
WEBSITE_FRONTEND_SERVER_PORT=
WEBSITE_COMMON_OPEN_API_SERVER_PORT=

ADMIN_LOG_ENABLE=true
ADMIN_LOG_UPDATED_ENABLE=true

FEI_SHU_BOT_SECRET=
FEI_SHU_BOT_WEBHOOK=

GOOGLE_CLIENT_SECRET=

MAIL_MAILER=aws_ses
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=
MAIL_AWS_SES_USERNAME=
MAIL_AWS_SES_PASSWORD=
MAIL_AWS_SES_REGION=us-west-1

STRIPE_KEY=
STRIPE_SECRET=
STRIPE_WEBHOOK_SECRET=
