FROM phpshop/php-7.4:v1
LABEL maintainer="PiPiADS" version="1.0" app.name="skeleton"

##
# ---------- env settings ----------
##
# --build-arg env_file=.env.dev
ARG env_file
ENV ENV_FILE=${env_file:-".env.dev"}

WORKDIR /www
COPY . /www

RUN  rm -rf /www/runtime \
    && chmod -R +x /www/vendor/bin \
    && cp ${ENV_FILE} .env

EXPOSE 9501
EXPOSE 9502
EXPOSE 9503
EXPOSE 9504
EXPOSE 9505
EXPOSE 9506

ENTRYPOINT ["php", "/www/bin/hyperf.php", "start"]
