{"name": "website/skeleton", "type": "project", "keywords": [], "description": "", "license": "Apache-2.0", "require": {"php": ">=7.4", "ext-dom": "*", "ext-json": "*", "96qbhy/hyperf-auth": "^2.6", "daodao97/apidog": "^1.5", "google/apiclient": "^2.13", "hyperf/amqp": "^2.2", "hyperf/async-queue": "^2.2", "hyperf/cache": "~2.2.0", "hyperf/command": "~2.2.0", "hyperf/config": "~2.2.0", "hyperf/constants": "~2.2.0", "hyperf/crontab": "^2.2", "hyperf/database": "~2.2.0", "hyperf/db-connection": "~2.2.0", "hyperf/elasticsearch": "^2.2", "hyperf/framework": "~2.2.0", "hyperf/http-server": "~2.2.0", "hyperf/logger": "~2.2.0", "hyperf/memory": "~2.2.0", "hyperf/model-cache": "~2.2.0", "hyperf/paginator": "^2.2", "hyperf/redis": "~2.2.0", "hyperf/view": "^2.2", "hyperf/view-engine": "^2.2", "lete/base": "9999999-dev", "lete/mail": "9999999-dev", "lete/mongodb": "9999999-dev", "lete/pay": "9999999-dev", "website/common": "9999999-dev"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/devtool": "~2.2.0", "hyperf/ide-helper": "~2.2.0", "hyperf/testing": "~2.2.0", "hyperf/watcher": "^2.2", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12", "swoole/ide-helper": "^4.5"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}, "files": []}, "autoload-dev": {"psr-4": {"HyperfTest\\": "./test/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": [], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "test": "co-phpunit --prepend test/bootstrap.php -c phpunit.xml --colors=always", "cs-fix": "php-cs-fixer fix $1", "analyse": "phpstan analyse --memory-limit 300M -l 0 -c phpstan.neon ./app ./config", "start": ["Composer\\Config::disableProcessTimeout", "php ./bin/hyperf.php start"]}, "repositories": {"lete/base": {"type": "vcs", "url": "*********************:5f2b9e94df9df74e36afc9a8/PHP/lete-base.git"}, "lete/mongodb": {"type": "vcs", "url": "*********************:5f2b9e94df9df74e36afc9a8/PHP/mongodb.git"}, "lete/mail": {"type": "vcs", "url": "*********************:5f2b9e94df9df74e36afc9a8/PHP/lete_mail.git"}, "lete/pay": {"type": "vcs", "url": "*********************:5f2b9e94df9df74e36afc9a8/PHP/lete_pay.git"}, "website/common": {"type": "vcs", "url": "*********************:5f2b9e94df9df74e36afc9a8/PHP/website-common.git"}}}