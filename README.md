# lufe.ai 官方网站

这是 [lufe.ai](https://www.lufe.ai) 浏览器翻译插件的官方网站项目。使用 Next.js 15 构建，采用 App Router 模式开发的现代化网站。

## 🚀 特性

- 🌐 多语言支持 (使用 next-intl)
- 🎨 现代化 UI 设计 (Tailwind CSS + Shadcn/UI)
- 📱 响应式布局
- ⚡ 服务端渲染 (SSR)
- 🔄 状态管理 (Zustand)
- 🎭 流畅动画效果 (Framer Motion)

## 🛠️ 技术栈

### 核心框架

- Next.js 15 (App Router)
- React 19
- TypeScript

### UI 框架和组件

- Tailwind CSS
- Shadcn/UI
- Framer Motion 动画

### 状态管理和工具

- Zustand
- React Hook Form
- Ky (HTTP 请求)
- next-intl (国际化)

## 📦 安装

确保你的环境中已安装 Node.js 和 pnpm。

```bash
# 安装依赖
pnpm install

# 开发环境运行
pnpm dev

# 构建项目
pnpm build

# 生产环境运行
pnpm start
```

## 脚本命令

### 数据源导入

```bash
# 导入book-data目录下的所有书籍JSON数据
pnpm db:import:all
```

## 🔧 开发

项目运行在 http://localhost:3333

### 音频播放器流程

![alt text](audio-player.png)

### 开发规范

1. 使用 ESNext 特性
2. 采用函数式编程实践
3. 确保类型声明完整
4. 避免嵌套三元运算符

## 生产环境

### 日志

- logs [`/opt/logs`]

## 📝 许可证

私有项目 - © lufe.ai

```
graph TD
    A[用户点击播放] --> B{检查S3缓存}
    B -->|存在| C[返回S3 URL]
    B -->|不存在| D[获取书籍plotSummary]
    D --> E[调用ElevenLabs TTS API]
    E --> F[生成MP3音频文件]
    F --> G[上传到S3]
    G --> H[返回S3 URL]
    C --> I[AudioPlayer播放]
    H --> I[AudioPlayer播放]
```
