/** 独立工作进程启动脚本 */
import dotenv from 'dotenv'
import { startWorkers } from './src/workers'

// 加载环境变量
dotenv.config()

// 启动所有工作进程
console.log('初始化 RabbitMQ 工作进程...')
startWorkers()
  .then(() => {
    console.log('RabbitMQ 工作进程初始化成功')
  })
  .catch((error) => {
    console.error('初始化工作进程失败:', error)
    process.exit(1)
  })

// 处理进程信号
process.on('SIGINT', () => {
  console.log('工作进程正在关闭...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('工作进程已终止')
  process.exit(0)
})

// 保持进程运行
process.stdin.resume()
