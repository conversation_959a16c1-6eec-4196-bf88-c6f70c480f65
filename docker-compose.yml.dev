# syntax version.
version: "3.7"

# services list.
services:
  server:
    image: "phpshop/php-7.4:v1"
    volumes:
#      - "C:/Users/<USER>/.ssh:/ssh"
      - ".:/www"
    ports:
      - "9501:9501"
      - "9502:9502"
      - "9503:9503"
      - "9504:9504"
#      - "9505:9505"
#      - "9506:9506"
    stdin_open: true
    tty: true
#    entrypoint: bash -c "mkdir -p /root/.ssh && cp /ssh/* /root/.ssh && chmod -R 700 /root/.ssh && bash"
    entrypoint: bash -c "bash"
    command: bash
