<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <style>
            html,
            body,
            div,
            p {
                padding: 0;
                margin: 0;
                font-family: 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            }

            .page-wrap {
                margin: 20px auto;
                max-width: 668px;
                width: 100%;
                padding: 36px 18px;
                border: 1px solid #eee;
                background: #f8f8f8;
                box-sizing: border-box;
            }

            .page-container {
                max-width: 618px;
                width: 100%;
                margin: 0 auto;
            }

            .page-header {
                 padding: 16px 20px;
                 border-top-left-radius: 8px;
                 border-top-right-radius: 8px;
                 background-color: #FFFFFF;
                 border-bottom: 1px solid #eee;
                 text-align: center;
                 display: flex;
                 justify-content: center;
                 align-items: center;
             }

            .page-header .logo {
                display: inline-block;
                height: 70px;
                width: auto;
                position: relative;
                top: 2px;
            }
            
            .page-header .brand-text {
                display: inline-block;
                color: #000;
                font-size: 40px;
                font-weight: bold;
                margin-left: 15px;
            }

            .page-main {
                padding: 35px 30px;
                background-color: #fff;
                border: 1px solid #eee;
                text-align: center;
            }

            .page-main .desc {
                font-size: 20px;
                font-weight: 600;
                color: #191919;
                margin: 0 0 20px;
                padding: 0;
                text-align: center;
            }

            .page-main .v-code {
                font-size: 64px;
                font-weight: 600;
                line-height: 80px;
                color: #191919;
                margin: 20px 0;
                padding: 0;
                text-align: left;
                letter-spacing: 10px;
            }

            .link {
                text-decoration: none;
                color: #004fff;
            }

            .link:hover {
                color: #004fff;
            }

            .mt10 {
                margin-top: 10px;
            }

            .small {
                padding: 0;
                margin: 0;
                font-size: 16px;
                color: #999;
                text-align: left;
                word-wrap: break-word;
            }
            .main-content{
                color: #191919;
            }
            .btn-to-website {
                display: block;
                margin: 46px auto;
                border: 1px solid #2184fd;
                color: #fff!important;
                background: #2184fd;
                border-radius: 4px;
                padding: 8px 10px;
                width: 100%;
                max-width: 150px;
                text-align: center;
                text-decoration: none;
                box-sizing: border-box;
            }

            .btn-to-website:hover {
                background: #4d9dfd;
                border-color: #4d9dfd;
                color: #fff;
            }
            .bottom-box{
                text-align: left;
            }
            .bottom-box p{
                color: #666;
                word-wrap: break-word;
                margin: 0;
                padding: 0;
                font-size: 16px;
            }
            .bottom-box .link{
                display: block;
                margin-top: 15px;
                color: #2184fd;
                word-wrap: break-word;
                word-break: break-all;
            }
            .bottom-box .link:hover {
                color: #4d9dfd;
                opacity: 1;
            }
        </style>
    </head>
    <body class="qmbox">
        <div class="page-wrap">
            
            <div class="page-container">
                <div class="page-header">
                     <img class="logo" src="https://cdn.15minutes.ai/images/logo.png">
                </div>
                <div class="page-main">
                    <p class="desc">Email Verification</p>
                    <p class="small main-content">Your email verification code is :</p>
                    <p class="v-code">
                        {{ $activate_code }}
                    </p>
                    <div class="bottom-box">
                        <p>Please copy this activation code and enter this code in the page to activate your account.</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
