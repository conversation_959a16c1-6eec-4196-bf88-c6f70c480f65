@extends('frontend.layouts.base')

@section('title', 'PPSPY: Shopify Store Spy & Shopify Dropshipping Monitor Tool')

@section('content')
    <div class="pricing-main" id="plan">
      <h1 class="pricing-title">
        {{ __('home.pricing_simplePricing') }}
      </h1>
      <div class="pricing-way-box">
        <div class="pricing-way">
          <div class="pricing-way-item" onclick="changePlan('year')" id="yearPlan">
            <span>{{ __('home.pricing_Yearly') }}</span>
            <em>- 30%</em>
          </div>
          <div class="pricing-way-item pricing-actived" onclick="changePlan('month')" id="monthPlan">
            <span>{{ __('home.pricing_Monthly') }}</span>
          </div>
        </div>
      </div>
      <div class="pricing-list-box">
        <div class="pricing-list">
          @foreach($groups as $rank_name => $rank)
            @if($rank_name !== 'STARTER')
              <div class="pricing-item {{ strtoupper($rank_name) === 'UNLIMITED' ? 'pricing-popular' : '' }}">
                @if(strtoupper($rank_name) === 'UNLIMITED')
                <div class="popular-top">
                  {{ __('home.pricing_mostPopular') }}
                </div>
                @endif
                <div class="pricing-item-content">
                  <div class="pricing-rank">
                    <img src="/static/images/{{ $rank_name }}.svg" srcset="" alt="{{ $rank_name }}" class="{{ strtoupper($rank_name) === 'ENTERPRISE' ? 'size3' : (strtoupper($rank_name) === 'UNLIMITED' ? 'size2' : 'size1') }}" />
                    <span>{{ \Hyperf\Utils\Str::title($rank_name) }}</span>
                  </div>
                  <p class="rank-desc">
                    {{ __('home.pricing_' .strtolower($rank_name). '_desc') }}
                  </p>
                  <div class="rank-coast">
                    <em>$</em>
                    <span class="month-price">{{ $rank['1 months']['price'] }}</span>
                    <span class="year-price">{{ $rank['1 years']['price'] }}</span>
                  </div>
                  <div class="rank-duration month-duration">
                    {{ __('home.pricing_perMonth') }}
                  </div>
                  <div class="rank-duration year-duration">
                   {{ __('home.pricing_perYear') }}
                  </div>
                  <a href="{{ env('FRONTEND_BASE_URI') }}/rank" class="go-rank month-link">
                    {{ __('home.pricing_go' . \Hyperf\Utils\Str::title($rank_name)) }}
                  </a>
                  <a href="{{ env('FRONTEND_BASE_URI') }}/rank" class="go-rank year-link">
                    {{ __('home.pricing_go' . \Hyperf\Utils\Str::title($rank_name)) }}
                  </a>
                  <div class="rank-desc-box">
                    <h3 class="rank-desc-title">{{ __('home.pricing_featuresYoullLove') }}</h3>
                    <ul>
                      <li>
                        <img src="/static/images/check_blue.svg" srcset="" alt="BASIC" class="size1" />
                        <span>
                          {{ __('home.pricing_saleTracking') }}:<em>{{ $rank['1 months']['permission']['site']['monthly']['track'] === -1 ? __('home.pricing_unlimited') : $rank['1 months']['permission']['site']['monthly']['track'] }}</em>{{ __('home.pricing_monthly_date') }}
                        </span>
                      </li>
                      <li>
                        <img src="/static/images/check_blue.svg" srcset="" alt="BASIC" class="size1" />
                        <span>
                          {{ __('home.pricing_trackLiveOrdersExtension') }}:<em>{{ $rank['1 months']['permission']['extension']['daily']['track'] === -1 ? __('home.pricing_unlimited') : $rank['1 months']['permission']['extension']['daily']['track'] }}</em>{{ __('home.pricing_daily_date') }}
                        </span>
                      </li>
                      <li>
                        <img src="/static/images/check_blue.svg" srcset="" alt="BASIC" class="size1" />
                        <span>
                          {{ __('home.pricing_exportProductReviewExtension') }}:<em>{{ $rank['1 months']['permission']['extension']['daily']['exprot_product_review'] === -1 ? __('home.pricing_unlimited') : $rank['1 months']['permission']['extension']['daily']['exprot_product_review'] }}</em>{{ __('home.pricing_daily_date') }}
                        </span>
                      </li>
                      <li>
                        <img src="/static/images/check_blue.svg" srcset="" alt="BASIC" class="size1" />
                        <span>
                          {{ __('home.pricing_shopifyAnalysis') }}:<em>{{ $rank['1 months']['permission']['search_permissions']['monthly']['track'] === -1 ? __('home.pricing_unlimited') : $rank['1 months']['permission']['search_permissions']['monthly']['track'] }}</em>{{ __('home.pricing_credits') }}
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            @endif
          @endforeach
        </div>
      </div>
    </div>

    <div class="subscriber-section">
      <h2 class="subscriber-section-title">{{ __('home.pricing_subscribeReviewsTitle') }}</h2>
      <div class="subscriber-reviews">
        <div class="subscriber-review-item">
          <div class="subscriber-review-desc">
            {{ __('home.pricing_subscribeReviews01') }}
          </div>
          <div class="subscriber-reviews-info">
            <div class="subscriber-reviews-pic">
              <img src="/static/images/pricing_picture1.png" alt="subscriber" srcset="">
            </div>
            <div class="subscriber-reviews-right">
              <div class="subscriber-reviews-name">
                {{ __('home.pricing_subscribeAuthor01') }}
              </div>
              <div class="subscriber-reviews-job">
                {{ __('home.pricing_subscribeAuthorjob01') }}
              </div>
            </div>
          </div>
        </div>
        <div class="subscriber-review-item">
          <div class="subscriber-review-desc">
            {{ __('home.pricing_subscribeReviews02') }}
          </div>
          <div class="subscriber-reviews-info">
            <div class="subscriber-reviews-pic">
              <img src="/static/images/pricing_picture2.png" alt="subscriber" srcset="">
            </div>
            <div class="subscriber-reviews-right">
              <div class="subscriber-reviews-name">
                {{ __('home.pricing_subscribeAuthor02') }}
              </div>
              <div class="subscriber-reviews-job">
                {{ __('home.pricing_subscribeAuthorjob02') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="need-a-bigger-plan">
        <h3>{{ __('home.pricing_needABiggerPlan') }}</h3>
        <p>{{ __('home.pricing_askUsAboutCustom') }}</p>
        <a href="mailto:<EMAIL>" class="ppspy-btn">
          <span>{{ __('home.pricing_contactUs') }}</span>
          <img src="/static/images/arrowright_white.svg" srcset="" alt="arrow" class="arrow" />
        </a>
      </div>
    </div>

    <div class="faq-section">
      <h2 class="faq-section-title">
        <img src="/static/images/ppspy_questions.png" srcset="" alt="arrow" class="arrow" />
        <span>{{ __('home.pricing_faqTitle') }}</span>
      </h2>
      <div class="faq-content">
        <div class="faq-list">
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.pricing_faqTitle01') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              {{ __('home.pricing_faqDetail01') }}
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.pricing_faqTitle02') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              {{ __('home.pricing_faqDetail02') }}
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.pricing_faqTitle03') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              {{ __('home.pricing_faqDetail03') }}
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.pricing_faqTitle04') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              {{ __('home.pricing_faqDetail04') }}
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.pricing_faqTitle05') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              {{ __('home.pricing_faqDetail05') }}
            </div>
          </div>
        </div>
      </div>
    </div>



@endsection

@section('script')
  @parent

  <script type="text/javascript">
    function intoView(bId) {
      var node = document.getElementById('nav');
      var nodeDrawer = document.getElementById('menuDrawer');
      var nodeBody = document.getElementsByTagName("body")[0];
      var nodeContainer = document.getElementById('container');
      document.getElementById(bId).scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      node.classList.remove("visible")
      nodeBody.classList.remove("bOverHide")
      nodeDrawer.style.display="none"
      nodeContainer.style.paddingRight= 0
    }
    function changePlan(type) {
      var yearDom = document.getElementById('yearPlan')
      var monthDom = document.getElementById('monthPlan')
      var planDom =  document.getElementById('plan')
      if(type==='month') {
        yearDom.classList.remove("pricing-actived")
        monthDom.classList.add("pricing-actived")
        planDom.classList.remove("year-rank")
      } else {
        yearDom.classList.add("pricing-actived")
        monthDom.classList.remove("pricing-actived")
        planDom.classList.add("year-rank")
      }
    }

    //FAQ
    var list = document.getElementsByClassName("faq-title");
    for(var i = 0;i<list.length;i++){
      list[i].onclick = function(){
        if(this.parentNode) {
          if(this.parentNode.classList.contains('faq-actived')) {
            this.parentNode.classList.remove("faq-actived")
          } else {
            this.parentNode.classList.add("faq-actived")
          }
        }
      }
    }


  </script>
@endsection
