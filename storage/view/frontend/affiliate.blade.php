@extends('frontend.layouts.base')

@section('title', 'PPSPY: Shopify Store Spy & Shopify Dropshipping Monitor Tool')

@section('content')
    <div class="affiliate-content">
      <div class="a-header">
        <h1 class="a-header-title">
        {{ __('home.affiliateHome_titleOur') }} <span style="font-size: 96px; color: #191919; background: linear-gradient(116deg, #FF6C17 0%, #FFC91B 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">30 %</span> {{ __('home.affiateHome_titleAffiliateProgram') }}
        </h1>
        <p class="a-header-desc">{{ __('home.affiliateHome_titleTips') }}</p>
        <a href="{{ env('FRONTEND_BASE_URI') }}/affiliate-apply" class="affiliate-btn">
        {{ __('home.affiliateHome_startBtn') }}
        </a>
        <div class="affiliate-advantage">
          <div class="affiliate-advantage-item">
            <img src="/static/images/Join.svg" srcset="" alt="Join" />
            <h3>{{ __('home.affiliateHome_joinText') }}</h3>
            <p class="a-desc">{{ __('home.affiliateHome_joinTip1') }}</p>
            <p class="a-tips">{{ __('home.affiliateHome_joinTip2') }}</p>
          </div>
          <div class="affiliate-advantage-item">
            <img src="/static/images/Promote.svg" srcset="" alt="Promote" />
            <h3>{{ __('home.affiliateHome_PrompteText') }}</h3>
            <p class="a-desc">{{ __('home.affiliateHome_PromoteTip1') }}</p>
            <p class="a-tips">{{ __('home.affiliateHome_PromoteTip2') }}</p>
          </div>
          <div class="affiliate-advantage-item">
            <img src="/static/images/Earn.svg" srcset="" alt="Earn" />
            <h3>{{ __('home.affiliateHome_earnText') }}</h3>
            <p class="a-desc">{{ __('home.affiliateHome_earnTip1') }}</p>
            <p class="a-tips">{{ __('home.affiliateHome_earnTip2') }}</p>
          </div>
        </div>
      </div>
      <div class="affiliate-how">
        <h2 class="affiliate-how-title">
        {{ __('home.affiliateHome_howWorkTitle') }}
        </h2>
        <div class="affiliate-how-list">
          <div class="affiliate-how-item">
            <div class="text">
              <div class="quot">"</div>
              <p>{{ __('home.affiliateHome_howWorkTip1') }}</p>
              <div class="quot">"</div>
            </div>
            <div class="how-pic-box">
              <img src="/static/images/ppspy_affiliate_banner1.png" srcset="" alt="affiliate_banner1" />
            </div>
          </div>
          <div class="affiliate-how-item">
            <div class="text">
              <div class="quot">"</div>
              <p>{{ __('home.affiliateHome_howWorkTip2') }}</p>
              <div class="quot">"</div>
            </div>
            <div class="how-pic-box">
              <img src="/static/images/ppspy_affiliate_banner2.png" srcset="" alt="affiliate_banner2" />
            </div>
          </div>
          <div class="affiliate-how-item">
            <div class="text">
              <div class="quot">"</div>
              <p>{{ __('home.affiliateHome_howWorkTip3') }}</p>
              <div class="quot">"</div>
            </div>
            <div class="how-pic-box">
              <img src="/static/images/ppspy_affiliate_banner3.png" srcset="" alt="affiliate_banner3" />
            </div>
          </div>
        </div>
      </div>
      <div class="affiliate-how-much">
        <h2 class="affiliate-how-much-title">
        {{ __('home.affiliateHome_howMuchTitle') }}
        </h2>
        <div class="affiliate-paid-box">
          <div class="paid-box-table">
            <div class="paid-box-col1">
              <div class="paid-box-th">{{ __('home.affiliateHome_howMuchHead1') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchRef1') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchRef2') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchRef3') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchRef4') }}</div>
            </div>
            <div class="paid-box-col1">
              <div class="paid-box-th" style="text-align: center;">{{ __('home.affiliateHome_howMuchHead2') }}</div>
              <div class="paid-box-td with-icon">
                <img src="/static/images/ADVANCED.svg" style="margin-right: 16px;" srcset="" alt="vip_pricing_Basic" />
                <span>{{ __('home.affiliateHome_orderMonthStarter') }}</span>
              </div>
              <div class="paid-box-td with-icon">
                <img src="/static/images/UNLIMITED.svg" style="width: 24px;" srcset="" alt="Basic" />
                <span>{{ __('home.affiliateHome_orderMonthVip') }}</span>
              </div>
              <div class="paid-box-td with-icon">
                <img src="/static/images/ENTERPRISE.svg" style="width: 24px;" srcset="" alt="ADVANCED" />
                <span>{{ __('home.affiliateHome_orderMonthPro') }}</span>
              </div>
              <div class="paid-box-td with-icon">
                <img src="/static/images/UNLIMITED.svg" style="width: 24px;" srcset="" alt="ADVANCED" />
                <span>{{ __('home.affiliateHome_orderYearPro') }}</span>
              </div>
            </div>
            <div class="paid-box-col1">
              <div class="paid-box-th">{{ __('home.affiliateHome_howMuchHead3') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchEarn1') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchEarn2') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchEarn3') }}</div>
              <div class="paid-box-td">{{ __('home.affiliateHome_howMuchEarn4') }}</div>
            </div>
          </div>
          <div class="more-tip">
          {{ __('home.affiliateHome_moreTip') }}...
          </div>
        </div>
      </div>
      <div class="affiliate-exceptional-section">
        <div class="affiliate-exceptional">
          <h2 class="affiliate-exceptional-title">{{ __('home.affiliateHome_eceptionalTitle') }}</h2>
          <div class="exceptional-box">
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip1') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText1') }}</p>
              <div class="img-com">
                <img src="/static/images/adspytool.svg" srcset="" alt="adspytool" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip2') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText2') }}</p>
              <div class="img-com">
                <img src="/static/images/interface.svg" srcset="" alt="interface" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip3') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText3') }}</p>
              <div class="img-com">
                <img src="/static/images/24.svg" srcset="" alt="24suport" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip4') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText4') }}</p>
              <div class="img-com">
                <img src="/static/images/tiktokadspy.svg" srcset="" alt="TikTok Adspy" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip5') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText5') }}</p>
              <div class="img-com">
                <img src="/static/images/popularadspytool.svg" srcset="" alt="popular Adspy" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip6') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText6') }}</p>
              <div class="img-com">
                <img src="/static/images/monthlyupdates.svg" srcset="" alt="Monthly Updates" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip7') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText7') }}</p>
              <div class="img-com">
                <img src="/static/images/experience.svg" srcset="" alt="experience" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip8') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText8') }}</p>
              <div class="img-com">
                <img src="/static/images/dropshipping.svg" srcset="" alt="Dropshipping" />
              </div>
            </div>
            <div class="exceptional-item">
              <h4>{{ __('home.affiliateHome_eceptionalTip9') }}</h4>
              <p>{{ __('home.affiliateHome_eceptionalText9') }}</p>
              <div class="img-com">
                <img src="/static/images/winningproducts.svg" srcset="" alt="winning products" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="affiliate-faq-section">
        <h2 class="affiliate-faq">{{ __('home.affiliateHome_faqTitle') }}</h2>
        <div class="affiliate-faq-list">
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ1') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              <p>{{ __('home.affiliateHome_faqA1P1') }}</p>
              <p>{{ __('home.affiliateHome_faqA1P2') }}</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ2') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              <p>{{ __('home.affiliateHome_faqA2P1') }}</p>
              <p>{{ __('home.affiliateHome_faqA2P2') }}</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ3') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">{{ __('home.affiliateHome_faqA3') }}</div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ4') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">{{ __('home.affiliateHome_faqA4') }}</div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ5') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              <p>{{ __('home.affiliateHome_faqA5P1') }}</p>
              <p>{{ __('home.affiliateHome_faqA5P2') }}</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ6') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">{{ __('home.affiliateHome_faqA6') }}</div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ7') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">
              <ul class="faq-ul">
                <li>{{ __('home.affiliateHome_faqA7P1') }}</li>
                <li>{{ __('home.affiliateHome_faqA7P2') }}</li>
                <li>{{ __('home.affiliateHome_faqA7P3') }}</li>
                <li>{{ __('home.affiliateHome_faqA7P4') }}</li>
                <li>{{ __('home.affiliateHome_faqA7P5') }}</li>
                <li>{{ __('home.affiliateHome_faqA7P6') }}</li>
              </ul>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-title">
              <span>{{ __('home.affiliateHome_faqQ8') }}</span>
              <div class="faq-icon">
                <img src="/static/images/ppspy_minus_default.svg" srcset="" alt="minus" class="minus" />
                <img src="/static/images/ppspy_minus_selected.svg" srcset="" alt="minus" class="minus-actived" />
                <img src="/static/images/ppspy_plus_default.svg" srcset="" alt="plus" class="plus" />
                <img src="/static/images/ppspy_plus_selected.svg" srcset="" alt="plus" class="plus-actived" />
              </div>
            </div>
            <div class="faq-detail">{{ __('home.affiliateHome_faqA8') }}</div>
          </div>
        </div>
      </div>
    </div>
@endsection

@section('script')
  @parent

  <script type="text/javascript">
    function intoView(bId) {
      var node = document.getElementById('nav');
      var nodeDrawer = document.getElementById('menuDrawer');
      var nodeBody = document.getElementsByTagName("body")[0];
      var nodeContainer = document.getElementById('container');
      document.getElementById(bId).scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      node.classList.remove("visible")
      nodeBody.classList.remove("bOverHide")
      nodeDrawer.style.display="none"
      nodeContainer.style.paddingRight= 0
    }

    //FAQ
    var list = document.getElementsByClassName("faq-title");
    for(var i = 0;i<list.length;i++){
      list[i].onclick = function(){
        if(this.parentNode) {
          if(this.parentNode.classList.contains('faq-actived')) {
            this.parentNode.classList.remove("faq-actived")
          } else {
            this.parentNode.classList.add("faq-actived")
          }
        }
      }
    }
  </script>
@endsection
