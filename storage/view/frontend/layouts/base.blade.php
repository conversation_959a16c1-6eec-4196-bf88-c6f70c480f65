<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="icon" href="/favicon.ico" />
  <link rel="stylesheet" href="/static/style/style.css?v={{ $static_version }}" />
  <title>@yield('title', 'PPSPY')</title>
  <meta name='ir-site-verification-token' value='50488554'>
  <meta name="description"
    content="@yield('description', 'PPSPY provides the best shopify analytics tool, supporting shopify dropshipping, shopify sale monitor, bestseller product, live sales, shopify app, shopify download and shopify reviews download.')">
  <meta name='og:title' content="@yield('title', 'PPSPY')">
  <meta name="og:description"
  content="@yield('description', 'PPSPY provides the best shopify analytics tool, supporting shopify dropshipping, shopify sale monitor, bestseller product, live sales, shopify app, shopify download and shopify reviews download.')">
  <link rel="canonical" href="{{ $current_uri }}" />
  <script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
        'gtm.start':
          new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-NNVKM7Q');</script>
  @section('headStylesheet')
  @show
</head>

<body>
  <noscript>
    <strong>We're sorry but ppspy doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
  </noscript>
  <div class="page-container" id="container">
    <div class="header">
      <div class="header-wrap">
        <a href="/" class="logo">
          <img src="/static/images/logo.png" srcset="" alt="logo" />
        </a>
        <div class="menu top-menu">
          <div class="menu-box">
            <div class="menu-top">
              <span>{{ __('home.header_products') }}</span>
              <svg viewBox="0 0 170 97" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill-rule="evenodd">
                      <g transform="translate(-789.000000, -760.000000)" fill-rule="nonzero">
                          <path d="M919.321338,802.798099 L840.565581,726.413483 C837.178236,723.195506 831.758485,723.195506 828.540508,726.413483 C825.153164,729.63146 825.153164,734.881844 828.540508,738.099821 L901.199045,808.725951 L828.540508,879.182715 C825.153164,882.400692 825.153164,887.651076 828.540508,890.869053 C831.927853,894.087031 837.347604,894.087031 840.565581,890.869053 L919.321338,814.484437 C922.708682,811.435827 922.708682,806.016076 919.321338,802.798099 Z" transform="translate(873.930923, 808.641268) rotate(90.000000) translate(-873.930923, -808.641268) "></path>
                      </g>
                  </g>
              </svg>
            </div>
            <div class="menu-list">
              <a href="{{ env('FRONTEND_BASE_URI') }}/sales-tracking" class="menu-item">
                <svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g stroke="none" stroke-width="1" fill-rule="evenodd">
                        <g transform="translate(-1281.000000, -54.000000)" fill-rule="nonzero">
                            <g transform="translate(1281.000000, 54.000000)">
                                <g >
                                    <path d="M50,0 C52.6530613,0 54.9981779,1.23177843 55.3196585,3.12343815 L55.3571429,3.57142857 L55.3571429,8.21428571 C71.7857143,10 86.0714286,21.4285714 91.0714286,37.8571429 C91.6428572,39.5714286 91.9857143,41.5142857 92.2828571,43.5028571 L92.5,45 L96.4285714,45 C98.2142857,45 100,47.1428571 100,49.6428571 C100,52.2959184 98.7682216,54.641035 96.8765618,54.9625156 L96.4285714,55 L92.5,55 C90.7142857,70 81.4285714,83.2142857 66.7857143,89.2857143 C63.6607143,90.8482143 60.2622768,91.8638393 56.8296596,92.3325893 L55.3571429,92.5 L55.3571429,96.4285714 C55.3571429,98.5714286 52.8571429,100 50,100 C47.3469387,100 45.0018221,98.7682216 44.6803415,96.8765618 L44.6428571,96.4285714 L44.6428571,92.5 C25.7674772,90.4027356 10.6552739,75.6475134 7.67694951,56.6050359 L7.5,55.3571429 L3.57142857,55.3571429 C1.42857143,55.3571429 0,52.8571429 0,50 C0,47.3469387 1.23177843,45.0018221 3.12343815,44.6803415 L3.57142857,44.6428571 L7.5,44.6428571 C9.64285714,26.7857143 23.2142857,11.7857143 41.4285714,8.21428571 C42.1428571,8.21428571 42.8571429,8.05555555 43.5714286,7.94973545 L44.6428571,7.85714286 L44.6428571,3.57142857 C44.6428571,1.42857143 47.1428571,0 50,0 Z M51.6818182,15.1272727 L44.673619,15.1316473 L43.727087,15.2914106 L42.8775103,15.409228 L42.3272727,15.4545455 L41.624571,15.6090664 C27.678971,18.8554643 17.0654566,30.3399869 14.8868815,44.3063107 L14.7209225,45.5093678 L14.1636364,50.1227273 L14.7145303,54.43893 C16.742828,70.3755549 28.9074203,82.8793747 44.2866351,85.1228396 L45.4459955,85.2717547 L49.5363636,85.7227273 L54.698701,85.2571403 C57.3589567,85.0152989 59.9712287,84.3578007 62.3589318,83.3270643 L64.0001505,82.5675898 C75.4595872,77.816116 83.2246152,67.6378844 85.1048428,55.4121647 L85.2782671,54.1402699 L85.8136364,49.6318182 L85.1652217,45.0955863 L84.9241583,43.531706 L84.7046982,42.2908325 C84.668764,42.1056285 84.6328616,41.9299014 84.5965569,41.7616603 L84.3704269,40.8261194 L84.1138004,39.9746819 C80.1511999,26.9547088 69.0328144,17.5258408 55.8162988,15.6026205 L54.5712579,15.4444273 L51.6818182,15.1272727 Z"></path>
                                    <path d="M50,9.09090909 C72.593467,9.09090909 90.9090909,27.406533 90.9090909,50 C90.9090909,72.593467 72.593467,90.9090909 50,90.9090909 C27.406533,90.9090909 9.09090909,72.593467 9.09090909,50 C9.09090909,27.406533 27.406533,9.09090909 50,9.09090909 Z M50,15.4545455 C30.9210723,15.4545455 15.4545455,30.9210723 15.4545455,50 C15.4545455,69.0789277 30.9210723,84.5454545 50,84.5454545 C69.0789277,84.5454545 84.5454545,69.0789277 84.5454545,50 C84.5454545,30.9210723 69.0789277,15.4545455 50,15.4545455 Z"></path>
                                </g>
                                <path d="M50,27.2727273 C37.4896147,27.2727273 27.2727273,37.4896147 27.2727273,50 C27.2727273,62.5103853 37.4896147,72.7272727 50,72.7272727 C62.5103853,72.7272727 72.7272727,62.5103853 72.7272727,50 C72.7272727,37.4896147 62.5103853,27.2727273 50,27.2727273 Z M50,36.3636364 C57.4896148,36.3636364 63.6363636,42.5103852 63.6363636,50 C63.6363636,57.4896148 57.4896148,63.6363636 50,63.6363636 C42.5103852,63.6363636 36.3636364,57.4896148 36.3636364,50 C36.3636364,42.5103852 42.5103852,36.3636364 50,36.3636364 Z"></path>
                            </g>
                        </g>
                    </g>
                </svg>
                <span>{{ __('home.header_salesTracking') }}</span>
              </a>
              <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-product" class="menu-item">
                <svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill-rule="evenodd">
                      <g transform="translate(-1281.000000, -290.000000)">
                          <g id="Product_default" transform="translate(1281.000000, 290.000000)">
                              <path d="M88,25 C91.8659932,25 95,28.1340068 95,32 L95,93 C95,96.8659932 91.8659932,100 88,100 L12,100 C8.13400675,100 5,96.8659932 5,93 L5,32 C5,28.1340068 8.13400675,25 12,25 L88,25 Z M87,33 L13,33 L13,92 L87,92 L87,33 Z" fill-rule="nonzero"></path>
                              <rect x="27.2727273" y="45" width="45.4545455" height="12.5" rx="6.25"></rect>
                              <path d="M93,0 C96.8659932,0 100,3.13400675 100,7 L100,28 C100,31.8659932 96.8659932,35 93,35 L7,35 C3.13400675,35 0,31.8659932 0,28 L0,7 C0,3.13400675 3.13400675,0 7,0 L93,0 Z M92,8 L8,8 L8,27 L92,27 L92,8 Z" fill-rule="nonzero"></path>
                          </g>
                      </g>
                  </g>
                </svg>
                <span>{{ __('home.header_shopifyProduct') }}</span>
              </a>
              <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-store" class="menu-item">
                <svg width="102px" height="100px" viewBox="0 0 102 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <title>store_default</title>
                    <g stroke="none" stroke-width="1" fill-rule="evenodd">
                        <g transform="translate(-1280.000000, -172.000000)" fill-rule="nonzero">
                            <path d="M1330.82486,172 L1331.01786,172.171 L1331.17514,172 L1381.17514,216.444444 L1374.47186,219.626 L1374.47565,262.47619 C1374.47565,267.736045 1370.21169,272 1364.95184,272 L1298.28517,272 C1293.02532,272 1288.76136,267.736045 1288.76136,262.47619 L1288.75786,220.21 L1280.82486,216.444444 L1330.82486,172 L1330.82486,172 Z M1330.99986,186.704 L1296.37286,217.742 L1296.38041,262.47619 C1296.38041,263.396665 1297.03333,264.164642 1297.9013,264.342254 L1298.28517,264.380952 L1364.95184,264.380952 C1365.87231,264.380952 1366.64029,263.728034 1366.8179,262.860417 L1366.8566,262.476714 L1366.85286,218.841 L1330.99986,186.704 Z M1337.18251,205.357779 C1343.6877,205.357779 1347.11072,206.89872 1347.11072,206.89872 L1347.11072,206.89872 L1343.89889,218.605863 C1343.89889,218.605863 1339.49207,216.763731 1334.38494,216.763731 C1326.89436,216.763731 1327.0275,220.653017 1327.15694,221.630863 C1327.86478,226.978017 1344.98413,229.025273 1346.64135,241.544321 C1347.94552,251.396339 1341.23274,257.738731 1331.04434,257.738731 C1318.82326,257.738731 1311.73766,251.444321 1311.73766,251.444321 L1311.73766,251.444321 L1313.83013,242.504672 C1313.83013,242.504672 1320.86164,247.066112 1326.2783,247.066112 C1329.82234,247.066112 1330.96174,244.761452 1330.73868,243.076464 C1329.81531,236.101101 1315.94887,235.791112 1314.43206,224.332779 C1313.15564,214.690387 1320.28775,205.357779 1337.18251,205.357779 Z"></path>
                        </g>
                    </g>
                </svg>
                <span>{{ __('home.header_shopifyStore') }}</span>
              </a>
              <a href="{{ env('FRONTEND_BASE_URI') }}/traffic-research" class="menu-item">
                <svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill-rule="evenodd">
                      <g transform="translate(-256.000000, -1213.000000)" fill-rule="nonzero">
                          <g transform="translate(256.000000, 1213.000000)">
                              <path d="M50,0 C77.6142375,0 100,22.3857625 100,50 C100,77.6142375 77.6142375,100 50,100 C22.3857625,100 0,77.6142375 0,50 C0,22.3857625 22.3857625,0 50,0 Z M50,8 C26.8040405,8 8,26.8040405 8,50 C8,73.1959595 26.8040405,92 50,92 C73.1959595,92 92,73.1959595 92,50 C92,26.8040405 73.1959595,8 50,8 Z"></path>
                              <path d="M54.5,2.33430618 L54.5,45.0463062 L96.0989688,36.0896266 L97.7829873,43.9103734 L47.5344393,54.7293062 L46.4994393,52.9693062 L46.5,2.33430618 L54.5,2.33430618 Z"></path>
                              <polygon points="53.9252156 49.9341108 75.9252156 86.4098049 69.0747844 90.5415833 47.0747844 54.0658892"></polygon>
                          </g>
                      </g>
                  </g>
                </svg>
                <span>{{ __('home.header_trafficResearch') }}</span>
              </a>
              <a href="{{ env('FRONTEND_BASE_URI') }}/one-product-store" class="menu-item">
                <svg width="106px" height="100px" viewBox="0 0 106 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill-rule="evenodd">
                      <g transform="translate(-1278.000000, -408.000000)">
                          <g transform="translate(1278.619048, 408.000000)">
                              <path d="M50,1.69176842e-14 L100,44.2735043 L93.647619,44.2809524 L93.6507937,90.4761905 C93.6507937,95.7360452 89.3868389,100 84.1269841,100 L17.4603175,100 C12.2004627,100 7.93650794,95.7360452 7.93650794,90.4761905 L7.93333333,44.4285714 L8.6618543e-12,44.4444444 L50,1.69176842e-14 Z M50.0047619,10.1809524 L15.547619,40.8142857 L15.5555556,90.4761905 C15.5555556,91.3966651 16.2084736,92.1646425 17.0764415,92.3422544 L17.4603175,92.3809524 L84.1269841,92.3809524 C85.0474587,92.3809524 85.8154361,91.7280343 85.9930481,90.8604172 L86.031746,90.4767141 L86.0285714,42.0761905 L50.0047619,10.1809524 Z" fill-rule="nonzero"></path>
                              <polygon points="50 -1.2265321e-13 4.25655599e-12 44.4444444 7.93650794 51.6446159 57.4931458 8.1454591"></polygon>
                              <polygon transform="translate(71.603716, 25.822308) scale(-1, 1) translate(-71.603716, -25.822308) " points="92.8571429 -1.2265321e-13 42.8571429 44.4444444 50.7936508 51.6446159 100.350289 8.1454591"></polygon>
                              <polygon points="47.5942372 45.7964345 47.5942372 82.0443243 61.9051906 70.3687221 76.1656658 82.0443243 76.1656658 45.7964345"></polygon>
                          </g>
                      </g>
                  </g>
                </svg>
                <span>{{ __('home.header_oneProductStore') }}</span>
              </a>
              <a href="{{ env('FRONTEND_BASE_URI') }}/store-theme" class="menu-item">
                <svg width="110px" height="111px" viewBox="0 0 110 111" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g stroke="none" stroke-width="1" fill-rule="evenodd">
                        <g transform="translate(-1276.000000, -526.000000)" fill-rule="nonzero">
                            <g transform="translate(1276.000000, 526.000000)">
                                <g transform="translate(0.000000, 0.390185)">
                                    <path d="M55.0526316,0.578947368 C87.7909901,0.578947368 109.526316,26.3901241 109.526316,49.7277961 C109.526316,65.1064444 99.3283312,77.0021898 87.1176681,81.7089825 C84.1850863,82.8422357 80.7706323,83.4847568 76.8508939,83.8100243 C74.0633174,84.0413428 71.3340297,84.0984737 68.3573692,84.042976 C68.3918607,84.1126719 68.4313997,84.1878134 68.4730966,84.2669074 C69.990334,86.7342216 70.7310855,89.6545577 70.7310855,93.5497533 C70.7310855,99.8542267 67.5209665,104.651085 62.5543675,107.20243 C59.7448124,108.6457 56.9627518,109.16549 54.3137306,109.226614 L54.3137306,109.226614 L52.4273588,109.206078 C20.2151975,108.330118 0.578947368,81.8257213 0.578947368,54.754523 C0.578947368,27.3852891 21.4646223,0.578947368 55.0526316,0.578947368 Z M55.0526316,9 C21.4383224,9 9,38.3174342 9,54.754523 C9,71.1916118 20.9551809,100.807356 54.0760691,100.807356 L54.0760691,100.807356 L54.6613483,100.785493 L55.296156,100.722773 L56.0924616,100.594378 C58.5301878,100.115487 62.3100329,98.5644129 62.3100329,93.5497533 C62.3100329,90.0438669 61.4796914,88.7291595 60.6056477,87.7504733 L60.6056477,87.7504733 L60.2352794,87.3473489 C59.8001098,86.9666459 59.3903549,86.6917354 58.6359962,86.1770828 C57.0229018,85.0765675 56,83.4387557 56,81.6098148 C56,78.2961063 59.5810029,75.4673804 63.7231386,75.4673804 C63.9575642,75.4673804 64.1782414,75.4776345 64.3859754,75.4975828 L66.4994735,75.569785 L68.90903,75.6289661 L70.7925034,75.637523 L72.8281559,75.6016326 C76.6632767,75.4847559 80.9652818,75.0585312 84.0822368,73.8540296 C90.589227,71.3458059 101.105263,63.348273 101.105263,49.7277961 L101.105263,49.7277961 L101.058945,48.4051813 C100.206328,35.7506241 87.4874913,9 55.0526316,9 Z M26.9790296,38.9136513 C31.3992599,38.9136513 34.9765625,42.4909539 34.9765625,46.9111842 C34.9662829,51.3314145 31.3889803,54.9087171 26.9790296,54.9087171 C22.5587993,54.9087171 18.9814967,51.3314145 18.9814967,46.9111842 C18.9814967,42.4909539 22.5587993,38.9136513 26.9790296,38.9136513 Z M82.9309211,38.9136513 C87.3511513,38.9136513 90.928476,42.4909539 90.928476,46.9111842 C90.9387336,51.3314145 87.3511513,54.9087171 82.9309211,54.9087171 C78.5209704,54.9087171 74.9333882,51.3314145 74.9333882,46.9111842 C74.9333882,42.4909539 78.5106908,38.9136513 82.9309211,38.9136513 Z M42.0592105,19.1254112 C46.4691612,19.1254112 50.0567434,22.7027138 50.0567434,27.1229441 C50.0567434,31.5431743 46.4794408,35.120477 42.0592105,35.120477 C37.6389803,35.120477 34.0616776,31.5431743 34.0616776,27.1229441 C34.0616776,22.7027138 37.6389803,19.1254112 42.0592105,19.1254112 Z M67.7068257,19.1254112 C72.1167763,19.1254112 75.7043586,22.7027138 75.7043586,27.1229441 C75.7043586,31.5431743 72.1270559,35.120477 67.7068257,35.120477 C63.296875,35.120477 59.7092928,31.5431743 59.7092928,27.1229441 C59.7092928,22.7027138 63.2865954,19.1254112 67.7068257,19.1254112 Z"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
                <span>{{ __('home.header_storeTheme') }}</span>
              </a>
            </div>
          </div>
          <div class="menu-box">
            <div class="menu-top">
              <span>{{ __('home.header_resources') }}</span>
              <svg viewBox="0 0 170 97" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill-rule="evenodd">
                      <g transform="translate(-789.000000, -760.000000)" fill-rule="nonzero">
                          <path d="M919.321338,802.798099 L840.565581,726.413483 C837.178236,723.195506 831.758485,723.195506 828.540508,726.413483 C825.153164,729.63146 825.153164,734.881844 828.540508,738.099821 L901.199045,808.725951 L828.540508,879.182715 C825.153164,882.400692 825.153164,887.651076 828.540508,890.869053 C831.927853,894.087031 837.347604,894.087031 840.565581,890.869053 L919.321338,814.484437 C922.708682,811.435827 922.708682,806.016076 919.321338,802.798099 Z" transform="translate(873.930923, 808.641268) rotate(90.000000) translate(-873.930923, -808.641268) "></path>
                      </g>
                  </g>
              </svg>
            </div>
            <div class="menu-list">
              <a href="/blog" class="menu-item">
                <span>{{ __('home.header_blog') }}</span>
              </a>
              <a href="/affiliate" class="menu-item">
                <span>{{ __('home.header_affiliate') }}</span>
              </a>
            </div>
          </div>
          <div class="menu-box">
            <a href="/rank" class="menu-link">{{ __('home.header_rank') }}</a>
          </div>
        </div>
        <div class="r-menu">
          <!-- <a href="https://chrome.google.com/webstore/detail/ppspy-shopify-spy-dropshi/lppbajkahdbbadheilijoeegnfndhlab"
            class="to-install" target="_blank" title="to install ppspy">
            <img src="/static/images/ppspy_logo.svg" srcset="" alt="logo" />
            <span>{{ __('home.InstallPPSPY') }}</span>
          </a> -->
          <a href="{{ env('FRONTEND_BASE_URI') }}/login" class="to-login-in">
            <!-- <img src="/static/images/google_Whitebackground.svg" srcset="" alt="logo" /> -->
            <span>{{ __('home.header_loginIn') }}</span>
          </a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/register" class="to-register-in">
            <!-- <img src="/static/images/google_Whitebackground.svg" srcset="" alt="logo" /> -->
            <span>{{ __('home.header_startFreeTrial') }}</span>
          </a>
          <div class="menu-btn" onclick="navClick()" id="nav">
            <img src="/static/images/guanbi3.svg" class="nav navHide" alt="nav" />
            <img src="/static/images/gengduo.svg" class="nav navShow" alt="nav" />
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      @section('content')
      @show
    </div>

    @section('footer')
          <div class="common-footer">
              <div class="bg-white">
                  <div
                          class="max-w-8xl mx-auto pt-16 pb-6 px-4 sm:px-6 lg:pt-16 lg:pb-6 lg:px-8"
                  >
                      <div class="xl:grid xl:grid-cols-5 xl:gap-2">
                          <div class="space-y-8 xl:col-span-1">
                              <a href="/" class="flex-shrink-0">
                                  <img src="/static/images/logo.png" alt="logo" srcset="" class="h-10">
                              </a>

                              <h3
                                      class="text-base font-semibold text-black tracking-wider"
                              >
                                {{ __('home.footer_AllInOneTruly') }}
                              </h3>
                              <p class="text-gray-500 text-sm">
                                {{ __('home.footer_TheMost') }}
                              </p>
                          </div>
                          <div
                                  class="mt-12 grid md:grid-cols-9 gap-3 xl:mt-0 md:col-span-2 xl:col-span-4"
                          >
                              <div class="md:gap-8 md:col-span-2 xl:col-span-2">
                                  <h3
                                          class="text-base font-semibold text-black tracking-wider"
                                  >
                                      {{ __('home.footer_Product') }}
                                  </h3>
                                  <ul role="list" class="mt-4 space-y-3">
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/sales-tracking"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_salesTracking') }}</a>
                                      </li>
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-product"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_shopifyProduct') }}</a>
                                      </li>
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-store"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_shopifyStore') }}</a>
                                      </li>
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/traffic-research"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_trafficResearch') }}</a>
                                      </li>
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/one-product-store"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_oneProductStore') }}</a>
                                      </li>
                                      <li>
                                          <a href="{{ env('FRONTEND_BASE_URI') }}/store-theme"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_storeTheme') }}</a>
                                      </li>
                                      <li>
                                          <a href="/rank"class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline">{{ __('home.header_rank') }}</a>
                                      </li>
                                  </ul>
                              </div>
                              <div class="md:gap-8 md:col-span-2 xl:col-span-2">
                                  <h3
                                          class="text-base font-semibold text-black tracking-wider"
                                  >
                                    {{ __('home.footer_Resourse') }}
                                  </h3>
                                  <ul role="list" class="mt-4 space-y-3">
                                      @if(isset($footer))
                                          <li>
                                              <a
                                                      href="{{ "{$seo_base_uri}/blog" }}"
                                                      class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                              >
                                                  Blog
                                              </a>
                                          </li>
                                          @foreach($footer['article_types'] as $article_type)
                                              <li>
                                                  <a
                                                          href="{{ "{$seo_base_uri}/{$article_type['handle']}" }}"
                                                          class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                                  >
                                                      {{ $article_type['name'] }}
                                                  </a>
                                              </li>
                                          @endforeach
                                          <li>
                                              <a
                                                      href="{{ "{$seo_base_uri}/author" }}"
                                                      class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                              >
                                                  Author
                                              </a>
                                          </li>
                                      @endif
                                  </ul>

                                  <h3
                                          class="text-base font-semibold text-black tracking-wider mt-6"
                                  >
                                    {{ __('home.footer_BrowseByAlphabet') }}
                                  </h3>
                                  <ul class="flex items-center flex-wrap mt-2">
                                      @if(isset($footer))
                                          @foreach($footer['alphabet_urls'] as $item)
                                              <li class="py-1 mr-2 xl:mr-0">
                                                  <a
                                                          href="{{ $base_uri . $item['url'] }}"
                                                          class="text-sm text-gray-500 px-1 py-1 hover:font-bold hover:text-blue-400 hover:underline"
                                                  >{{ $item['title'] }}</a
                                                  >
                                              </li>
                                          @endforeach
                                      @endif
                                  </ul>

                                  <h3
                                          class="text-base font-semibold text-black tracking-wider mt-6"
                                  >
                                    {{ __('home.footer_Top1000ShopifySpy') }}
                                  </h3>
                                  <ul class="flex items-center flex-wrap mt-2">
                                      @if(isset($footer))
                                          @foreach($footer['top1000_dates'] as $top1000_date)
                                              <li class="py-1 mr-2 md:mr-5 xl:mr-0">
                                                  <a
                                                          href="{{ $base_uri . $top1000_date['url'] }}"
                                                          class="text-sm text-gray-500 px-1 py-1 hover:font-bold hover:text-blue-400 hover:underline"
                                                  >{{ $top1000_date['title'] }}</a
                                                  >
                                              </li>
                                          @endforeach
                                      @endif
                                  </ul>
                              </div>
                              <div class="md:gap-8 md:col-span-3 xl:col-span-3">
                                  <h3
                                          class="text-base font-semibold text-black tracking-wider"
                                  >
                                    {{ __('home.footer_ReadMore') }}
                                  </h3>
                                  <ul role="list" class="mt-4 space-y-3">
                                      @if(isset($footer))
                                          @foreach($footer['latest_articles'] as $article)
                                              <li>
                                                  <a
                                                          href="{{ "{$seo_base_uri}/{$article['article_type_handle']}/{$article['handle']}" }}"
                                                          class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                                  >
                                                      {{ $article['title'] }}
                                                  </a>
                                              </li>
                                          @endforeach
                                      @endif
                                  </ul>
                              </div>
                              <div class="md:gap-8 md:col-span-2 xl:col-span-2">
                                  <h3
                                          class="text-base font-semibold text-black tracking-wider"
                                  >
                                    {{ __('home.footer_About') }}
                                  </h3>
                                  <ul role="list" class="mt-4 space-y-3">
                                      <li>
                                          <a
                                                  href="/privacy-policy"
                                                  class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                          >
                                              {{ __('home.PrivacyPolicy') }}
                                          </a>
                                      </li>

                                      <li>
                                          <a
                                                  href="/terms-of-service"
                                                  class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                          >
                                              {{ __('home.TermOfServices') }}
                                          </a>
                                      </li>

                                      <li>
                                          <a
                                                  href="/subscription-refund-policy"
                                                  class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                          >
                                              {{ __('home.SubscriptionRefundPolicy') }}
                                          </a>
                                      </li>

                                      <li>
                                          <a
                                                  href="/affiliate"
                                                  class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                          >
                                              {{ __('home.distributionAlliance') }}
                                          </a>
                                      </li>
                                  </ul>

                                  <h3
                                          class="text-base font-semibold text-black tracking-wider mt-6"
                                  >
                                      Contact us
                                  </h3>
                                  <ul role="list" class="mt-4 space-y-3">
                                      <li>
                                          <a href="mailto:<EMAIL>"
                                             class="text-sm text-gray-500 hover:font-bold hover:text-blue-400 hover:underline"
                                          >
                                              <EMAIL>
                                          </a>
                                      </li>
                                  </ul>
                              </div>
                          </div>
                      </div>
                      <div class="language-list">
                          @foreach($languages as $language)
                              <a href="{{ "/{$language['value']}" . ($current_path === '/' ? '' : $current_path) }}" @if($language['value'] === $locale)class="language-actived"@endif>
                                  {{ $language['title'] }}
                              </a>
                          @endforeach
                      </div>
                      <div class="mt-5">
                          <p class="text-base text-gray-400">Copyright &copy;2023 PPSPY</p>
                      </div>
                  </div>
              </div>
          </div>
    @show

    @section('menu-drawer')
    <div class="menu-drawer" id="menuDrawer">
      <div class="menu-drawer-bg" onclick="navClick()"></div>
      <div class="nav-box">
        <div class="menu">
          <a href="{{ env('FRONTEND_BASE_URI') }}/sales-tracking" class="nav-item">{{ __('home.header_salesTracking') }}</a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-product" class="nav-item">{{ __('home.header_shopifyProduct') }}</a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/shopify-store" class="nav-item">{{ __('home.header_shopifyStore') }}</a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/traffic-research" class="nav-item">{{ __('home.header_trafficResearch') }}</a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/one-product-store" class="nav-item">{{ __('home.header_oneProductStore') }}</a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/store-theme" class="nav-item">{{ __('home.header_storeTheme') }}</a>
          <a href="/blog" class="nav-item">{{ __('home.header_blog') }}</a>
          <a href="/affiliate" class="nav-item">{{ __('home.header_affiliate') }}</a>
          <a href="/rank" class="nav-item">{{ __('home.header_rank') }}</a>
        </div>
        <div class="d-ctrl">
          <a href="{{ env('FRONTEND_BASE_URI') }}/login" class="to-login-in">
            <span>{{ __('home.header_loginIn') }}</span>
          </a>
          <a href="{{ env('FRONTEND_BASE_URI') }}/register"  class="to-register-in">
            <span>{{ __('home.header_startFreeTrial') }}</span>
          </a>
        </div>
      </div>
    </div>
    @show

    <button type="button" onclick="toTop()" class="toTop">
      <img src="/static/images/arrow_up_blue.svg" />
    </button>
  </div>
</body>
<!-- <link rel="stylesheet" href="/static/style/tailwind.min.css?v=********" /> -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNVKM7Q" height="0" width="0"
    style="display:none;visibility:hidden"></iframe></noscript>
<script src="/static/js/axios.min.js"></script>
<script src="/static/js/commonFn.js?v={{ $static_version }}"></script>
@if(!$is_logged_in)
<div id="g_id_onload"
     data-client_id="{{ $google['client_id'] }}"
     data-context="signin"
     data-callback="handleGoogleCredentialResponse">
</div>
<script src="https://accounts.google.com/gsi/client" async defer></script>
<script type="text/javascript">
  function handleGoogleCredentialResponse(content) {
    const data = {
      credential: content.credential,
      source: getCookie('utm') || 'ppspy'
    }
    axiosPostRequst('/v1/google/auth/login', data, '{{ $app_env }}').then(res => {
      if (parseInt(res.data.code, 10) === 200) {
        let url
        if (res.data && res.data.data && res.data.data.my && res.data.data.my.language) {
          url = `{{ env('FRONTEND_BASE_URI') }}/${res.data.data.my.language}/dashboard`
        } else {
          url = "{{ env('FRONTEND_BASE_URI') }}/dashboard"
        }
        if (window.opener) {
          window.opener.postMessage(
              {
                eventName: 'loginEvent',
              },
              '*'
          )
        } else {
          console.log('无opener')
        }

        location.href = url;
      } else {
        if (window.opener) {
          window.opener.postMessage(
            {
              eventName: 'loginEventFail',
            },
            '*'
          )
        } else {
          console.log('无opener')
        }

        /* error tips */
        var message = res.data.text || res.data.message || res.data.msg || "error"
        var dom = document.createElement("div");
        dom.className = "ppspy-message ppspy-message-actived";
        var innerErrorHtml = '<div class="ppspy-message--content">'+message+'</div>'
        dom.innerHTML = innerErrorHtml
        document.body.appendChild(dom);

        setTimeout(function() {
          if(document.querySelector(".ppspy-message")) {
            document.querySelector(".ppspy-message").remove();
          }
        }, 3000);
        /* error tips end */
      }
    })
  }
</script>
@endif
@section('script')
  <script type="text/javascript">
    function navClick() {
      var node = document.getElementById('nav');
      var nodeDrawer = document.getElementById('menuDrawer');
      var nodeBody = document.getElementsByTagName("body")[0];
      var nodeContainer = document.getElementById('container');

      if(node.getAttribute('class').indexOf('visible') > -1){
        node.classList.remove("visible")
        nodeBody.classList.remove("bOverHide")
        nodeDrawer.style.display="none"
        nodeContainer.style.paddingRight= 0
      } else {
        var sWidth = getScrollbarWidth();
        node.classList.add("visible")
        nodeBody.classList.add("bOverHide")
        nodeDrawer.style.display="block"
        nodeContainer.style.paddingRight= sWidth + 'px'
      }
    }

    function getScrollbarWidth() {
      var scrollDiv = document.createElement("div");
      scrollDiv.style.cssText = 'width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;';
      document.body.appendChild(scrollDiv);
      var scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
      document.body.removeChild(scrollDiv);
      return scrollbarWidth;
    }

    function setCookie(cname, cvalue, exdays, cdomain) {
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toGMTString();
        var domain = cdomain ? cdomain : '{{ env('APP_ENV') === 'prod' ? '.ppspy.com' : (env('APP_ENV') === 'stage' ? '.shenbihuyu.com' : '') }}';
        document.cookie = cname + "=" + cvalue + "; " + expires + ";path=/;" + (domain ? `domain=${domain}` : '');
    };

    window.onload = function () {
      var req = getRequest('utm');
      if(document.referrer || req) {
        setCookie('utm', req ? req : document.referrer, 1)
      }

      var refcode = getRequest('ref');
      if (refcode && refcode.length) {
        axiosPostRequst('/v1/distribution/click', {
          distribution_code: refcode
        }, '{{ $app_env }}')
        var preKey = '{{ env('APP_ENV') === 'prod' ? '' : env('APP_ENV') }}'
        preKey = preKey.length > 0 ? '-' + preKey : preKey
        var cookieName = "PPSPY_REF_CODE" + preKey
        var curCode = getCookie(cookieName)
        if (!curCode) {
          setCookie(cookieName, refcode, 90, '.ppspy.com')
        }
      }

      window.addEventListener('scroll',function () {
        var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        var dom = document.querySelector('.toTop')
        var headerDom = document.querySelector('.header')
        if(scrollTop > 0) {
          headerDom.classList.add("header-shadow")
        } else {
          headerDom.classList.remove("header-shadow")
        }
        if(scrollTop > 150) {
          dom.style.display = 'flex'
        } else {
          dom.style.display = 'none'
        }
      })


      var videoDom = document.querySelector("video")
      var isMobile = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if(videoDom && isMobile) {
        var videoDom = document.querySelector("video")
        var playBtn = document.querySelector(".playBtn")
        playBtn.style.display = 'none'
        videoDom.play();
      }
    }

    function getRequest(key) {
      var url = location.search
      var theRequest = new Object()
      if (url.indexOf('?') != -1) {
        var str = url.substr(1)
        strs = str.split('&')
        for (var i = 0; i < strs.length; i++) {
          theRequest[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1])
        }
      }
      var value = theRequest[key]
      return value
    }

    function hideNav(){
      var node = document.getElementById('nav');
      var nodeDrawer = document.getElementById('menuDrawer');
      var nodeBody = document.getElementsByTagName("body")[0];
      var nodeContainer = document.getElementById('container');

      node.classList.remove("visible")
      nodeBody.classList.remove("bOverHide")
      nodeDrawer.style.display="none"
      nodeContainer.style.paddingRight= 0
    }


    function toTop() {
      window.scrollTo(0,0)
    }
    if (window.addEventListener) {
      window.addEventListener("resize", function() {
        hideNav();
      });
    } else if (window.attachEvent) {
      window.attachEvent("onresize", function() {
        hideNav();
      });
    }

    /* var menuDom = document.quertSelecto
    var list = document.querySelector(".top-menu").children;
    for (var i = 0; i < list.length; i++) {
      list[i].onmouseover = function (){
        this.classList.add("visible")
      }
      list[i].onmouseout = function (){
        this.classList.remove("visible")
      }
    } */
  </script>
@show
</html>
