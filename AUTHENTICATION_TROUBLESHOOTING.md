# 生产环境身份验证问题排查指南

## 🔍 问题概述

在生产环境 https://www.15minutes.ai/ 上，用户登录后出现以下异常：
1. 网站header没有显示用户登录信息和头像
2. 后台出现401未授权错误
3. 但是直接访问用户API端点 `/common-api/v1/user` 可以正常返回用户数据

## 🎯 根本原因分析

通过代码分析，发现了以下关键问题：

### 1. Redis数据库配置不一致
- **前端Next.js配置**: `REDIS_DB=1` (`.env.production`)
- **后端PHP配置**: `REDIS_DB=0` (`.env.production`)

### 2. Token验证流程差异
- **直接访问**: `/common-api/v1/user` 使用PHP后端的认证中间件
- **内部API路由**: 通过 `apiServer` 调用，存在Cookie传递问题

### 3. 环境配置差异
- **生产环境**: 使用 `MINUTES_ACCESS_TOKEN`
- **开发环境**: 使用 `MINUTES_ACCESS_TOKEN-dev`

## 🛠️ 解决方案

### 第一步：验证当前状态

1. 访问调试页面：`https://www.15minutes.ai/debug-api-logs`
2. 启用调试日志
3. 依次点击以下测试按钮：
   - **测试认证调试** - 查看基本认证状态
   - **测试Redis配置** - 检查Redis数据库配置
   - **测试环境配置** - 检查环境变量配置
   - **测试内部API** - 测试内部API调用
   - **测试直接API** - 测试直接API调用

### 第二步：修复Redis数据库配置

根据测试结果，需要统一Redis数据库配置：

#### 方案A：修改前端配置（推荐）
```bash
# 修改前端 .env.production
REDIS_DB=0
```

#### 方案B：修改后端配置
```bash
# 修改后端 .env.production
REDIS_DB=1
```

### 第三步：验证Token存储位置

使用Redis配置测试确认Token实际存储在哪个数据库：
- 如果Token在数据库0，使用方案A
- 如果Token在数据库1，使用方案B

### 第四步：重启服务

修改配置后需要重启相关服务：
1. 重启Next.js前端服务
2. 重启PHP后端服务
3. 清除Redis缓存（可选）

## 🔧 调试工具

### 新增的调试API端点

1. **`/api/debug/auth`** - 基本认证状态调试
2. **`/api/debug/redis-config`** - Redis配置和Token存储检查
3. **`/api/debug/env-config`** - 环境配置对比分析
4. **`/api/test-user-info`** - 内部API调用测试

### 调试页面功能

访问 `/debug-api-logs` 页面可以：
- 启用/关闭API调试日志
- 测试不同的API调用路径
- 实时查看请求和响应日志
- 对比内部API和直接API的差异

## 📊 预期结果

修复后应该看到：
1. **Header显示**: 用户头像和登录信息正常显示
2. **API调用**: 内部API调用不再返回401错误
3. **用户体验**: 所有需要认证的功能正常工作

## 🚨 注意事项

### 生产环境操作
- 在生产环境修改配置前，建议先在测试环境验证
- 修改配置可能需要短暂的服务重启
- 建议在低峰时段进行修改

### 配置备份
- 修改前备份当前配置文件
- 记录修改的具体内容和时间
- 准备回滚方案

### 监控验证
- 修改后监控错误日志
- 验证用户登录流程
- 检查相关功能是否正常

## 🔄 回滚方案

如果修改后出现问题：
1. 立即恢复原始配置文件
2. 重启相关服务
3. 验证基本功能恢复
4. 分析失败原因

## 📞 联系支持

如果问题持续存在：
1. 收集调试页面的完整测试结果
2. 提供错误日志和配置信息
3. 描述具体的问题现象
4. 联系技术支持团队

---

**最后更新**: 2024年12月19日
**状态**: 待验证和实施
