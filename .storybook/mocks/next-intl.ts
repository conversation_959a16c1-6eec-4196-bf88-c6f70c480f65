import { useLocale, useMessages, useTranslations } from 'next-intl'

export const getLocale = async () => {
  console.log('getLocale called')
  return useLocale()
}

export const getTranslations = async (...args: Parameters<typeof useTranslations>) => {
  //console.log('getTranslations called with args:', args);
  return useTranslations(args)
}

export const getMessages = async () => {
  console.log('getMessages called')
  return useMessages()
}
