<?php

declare(strict_types=1);

return [
    // 网址名称
    'name' => '',
    /**
     * 会员账号
     */
    'user_account' => [
        /**
         * 注册相关
         */
        'register' => [
            // 是否需要邮箱验证
            'validate_email' => true,
            // 是否开启无密码的邮箱注册登录
            'email_without_password' => false,
            // 是否发送注册成功邮件
            'send_registration_successful_email' => false,
            // 新用户送试用套餐，填套餐的名称，如果有多个相同名称的套餐，默认取最新的
            'trial_name' => '',
        ],
    ],
    /**
     * 等级套餐
     */
    'rank' => [
        /**
         * 权限结构（前端用来输出）
         */
        'structure' => [
//            [
//                'label' => '搜索权限',
//                'dKey' => 'search',
//                'children' => [
//                    [
//                        'label' => '搜索&筛选次数/月',
//                        'dKey' => 'times',
//                        'type' => 'number'
//                    ]
//                ],
//            ],
        ],
        /**
         * 权限管理的基础数据结构
         */
        'structure_permissions' => [
//            // 搜索权限
//            'search' => [
//                // 搜索&筛选次数/月
//                'times' => 0,
//            ],
        ],
        /**
         * 所有次数权限
         */
        'quota_permissions' => [
//            'search_times' => [
//                'title' => '搜索&筛选次数/月',
//                'reset_days' => 30,
//            ],
        ],
    ],
    /**
     * 登录鉴权
     */
    'auth' => [
        // 排除以下uri
        'except' => [
//            'POST:/v1/stripe-webhook',
//            '\/v1\/signup',
        ],
    ],
    /**
     * 团队
     */
    'team' => [
        // 角色
        'role' => [
            // 类型（default：默认的；custom：用户可以自定义；）
            'type' => 'default',
            // 角色权限结构
            'permission_structure' => [],
            // 默认的角色数据
            'default_data' => [
                /*[
                    // 角色名称
                    'name' => 'Editor',
                    // 权限
                    'permission' => [],
                    // 默认角色（可选）
                    'is_default' => 1,
                    // 管理员（可选）
                    'is_admin' => 1,
                ]*/
            ],
        ]
    ],
    /**
     * 事件通知开关
     */
    'events' => [
        'auth' => [
            // 用户注册事件
            'signup' => false,
            // 用户注销登录事件
            'logout' => false,
        ],
        'user' => [
            // 会员到期事件
            'expired' => false,
            // 账号删除事件
            'deleted' => false,
        ],
    ],
    /**
     * 订单
     */
    'order' => [
        'types' => [],
        'refund' => [
            // 退款时，原路退回（通过api直接退款）
            'retracement' => false,
        ],
    ],
    /**
     * 前台配置
     */
    'frontend' => [
        'middlewares' => [
            'resubmit' => [
                'except' => []
            ]
        ]
    ],
    /**
     * 后台台配置
     */
    'backend' => [
        'middlewares' => [
            'resubmit' => [
                'except' => []
            ]
        ]
    ],
];
